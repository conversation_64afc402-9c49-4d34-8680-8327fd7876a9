<?xml version="1.0" encoding="utf-8"?>
<!-- 
	item:主要有二种 /::| /ue436
-->
<emoji-panel> 
  <item><![CDATA[/::)]]></item>  
  <item><![CDATA[/::~]]></item>  
  <item><![CDATA[/::B]]></item>  
  <item><![CDATA[/::|]]></item>  
  <item><![CDATA[/:8-)]]></item>  
  <item><![CDATA[/::<]]></item>  
  <item><![CDATA[/::$]]></item>  
  <item><![CDATA[/::X]]></item>  
  <item><![CDATA[/::Z]]></item>  
  <item><![CDATA[/::'(]]></item>  
  <item><![CDATA[/::-|]]></item>  
  <item><![CDATA[/::@]]></item>  
  <item><![CDATA[/::P]]></item>  
  <item><![CDATA[/::D]]></item>  
  <item><![CDATA[/::O]]></item>  
  <item><![CDATA[/::(]]></item>  
  <item><![CDATA[[Blush]]]></item>
  <item><![CDATA[/::Q]]></item>  
  <item><![CDATA[/::T]]></item>  
  <item><![CDATA[/:,@P]]></item>  
  <item><![CDATA[/:,@-D]]></item>  
  <item><![CDATA[/::d]]></item>  
  <item><![CDATA[/:,@o]]></item>  
  <item><![CDATA[/:|-)]]></item>  
  <item><![CDATA[/::!]]></item>  
  <item><![CDATA[/::>]]></item>  
  <item><![CDATA[/::,@]]></item>  
  <item><![CDATA[/::-S]]></item>  
  <item><![CDATA[/:?]]></item>  
  <item><![CDATA[/:,@x]]></item>  
  <item><![CDATA[/:,@@]]></item>  
  <item><![CDATA[/:,@!]]></item>  
  <item><![CDATA[/:!!!]]></item>  
  <item><![CDATA[/:xx]]></item>  
  <item><![CDATA[[Bye]]]></item>
  <item><![CDATA[/:wipe]]></item>  
  <item><![CDATA[/:dig]]></item>  
  <item><![CDATA[/:handclap]]></item>  
  <item><![CDATA[/:B-)]]></item>  
  <item><![CDATA[/:@>]]></item>  
  <item><![CDATA[/:>-|]]></item>  
  <item><![CDATA[/:P-(]]></item>  
  <item><![CDATA[/::'|]]></item>  
  <item><![CDATA[/:X-)]]></item>  
  <item><![CDATA[/::*]]></item>  
  <item><![CDATA[/:8*]]></item>  
  <item><![CDATA[[Happy]]]></item>
  <item><![CDATA[[Sick]]]></item>
  <item><![CDATA[[Flushed]]]></item>
  <item><![CDATA[[Lol]]]></item>
  <item><![CDATA[[Terror]]]></item>
  <item><![CDATA[[LetDown]]]></item>
  <item><![CDATA[[Duh]]]></item>
  <item><![CDATA[[Hey]]]></item>
  <item><![CDATA[[Facepalm]]]></item>
  <item><![CDATA[[Smirk]]]></item>
  <item><![CDATA[[Smart]]]></item>
  <item><![CDATA[[Concerned]]]></item>
  <item><![CDATA[[Yeah!]]]></item>
  <item><![CDATA[[Onlooker]]]></item> 
  <item><![CDATA[[GoForIt]]]></item>
  <item><![CDATA[[Sweats]]]></item>
  <item><![CDATA[[OMG]]]></item> 
  <item><![CDATA[[Emm]]]></item>
  <item><![CDATA[[Respect]]]></item>
  <item><![CDATA[[Doge]]]></item>
  <item><![CDATA[[NoProb]]]></item> 
  <item><![CDATA[[MyBad]]]></item>
  <item><![CDATA[[Wow]]]></item>
  <item><![CDATA[[Boring]]]></item>
  <item><![CDATA[[666]]]></item>
  <item><![CDATA[[LetMeSee]]]></item>
  <item><![CDATA[[Sigh]]]></item>
  <item><![CDATA[[Hurt]]]></item>
  <item><![CDATA[[Broken]]]></item>

  <item><![CDATA[/:showlove]]></item>  
  <item><![CDATA[/:heart]]></item>  
  <item><![CDATA[/:break]]></item>  
  <item><![CDATA[/:hug]]></item>  
  <item><![CDATA[/:strong]]></item>  
  <item><![CDATA[/:weak]]></item>  
  <item><![CDATA[/:share]]></item>  
  <item><![CDATA[/:v]]></item>  
  <item><![CDATA[[Salute]]]></item>
  <item><![CDATA[/:jj]]></item>  
  <item><![CDATA[/:@@]]></item>  

  <item><![CDATA[/:ok]]></item>  
  <item><![CDATA[[Worship]]]></item>

  <item><![CDATA[/:beer]]></item>  
  <item><![CDATA[/:coffee]]></item>
  <item><![CDATA[/:cake]]></item>  

  <item><![CDATA[/:rose]]></item>  
  <item><![CDATA[/:fade]]></item> 
  <item><![CDATA[/:pd]]></item>  
  <item><![CDATA[/:bome]]></item>  
  <item><![CDATA[/:shit]]></item>  
  <item><![CDATA[/:moon]]></item>  
  <item><![CDATA[/:sun]]></item> 
  <item><![CDATA[[Party]]]></item><!-- SWITCHED -->
  <item><![CDATA[[gift]]]></item>
  <item><![CDATA[[Packet]]]></item>
  <item><![CDATA[[Rich]]]></item>
  <item><![CDATA[[Blessing]]]></item>
  <item><![CDATA[[Fireworks]]]></item>
  <item><![CDATA[[Firecracker]]]></item>
  
  <item><![CDATA[/:pig]]></item>  
  <item><![CDATA[/:jump]]></item>  
  <item><![CDATA[/:shake]]></item>  
  <item><![CDATA[/:circle]]></item>

  <item><![CDATA[8J+Ygw==]]></item>
  <item><![CDATA[8J+YgA==]]></item>
  <item><![CDATA[8J+Yig==]]></item>
  <item><![CDATA[4pi677iP]]></item>
  <item><![CDATA[8J+YiQ==]]></item>
  <item><![CDATA[8J+YjQ==]]></item>
  <item><![CDATA[8J+YmA==]]></item>
  <item><![CDATA[8J+Ymg==]]></item>
  <item><![CDATA[8J+Ylw==]]></item>
  <item><![CDATA[8J+YmQ==]]></item>
  <item><![CDATA[8J+YnA==]]></item>
  <item><![CDATA[8J+Ymw==]]></item>
  <item><![CDATA[8J+YgQ==]]></item>
  <item><![CDATA[8J+YjA==]]></item>
  <item><![CDATA[8J+Yng==]]></item>
  <item><![CDATA[8J+Yow==]]></item>
  <item><![CDATA[8J+Yog==]]></item>
  <item><![CDATA[8J+YrQ==]]></item>
  <item><![CDATA[8J+Yqg==]]></item>
  <item><![CDATA[8J+YpQ==]]></item>
  <item><![CDATA[8J+YsA==]]></item>
  <item><![CDATA[8J+YhQ==]]></item>
  <item><![CDATA[8J+Ykw==]]></item>
  <item><![CDATA[8J+YqQ==]]></item>
  <item><![CDATA[8J+Yqw==]]></item>
  <item><![CDATA[8J+YqA==]]></item>
  <item><![CDATA[8J+YoA==]]></item>
  <item><![CDATA[8J+YoQ==]]></item>
  <item><![CDATA[8J+YpA==]]></item>
  <item><![CDATA[8J+Ylg==]]></item>
  <item><![CDATA[8J+Yhg==]]></item>
  <item><![CDATA[8J+Yiw==]]></item>
  <item><![CDATA[8J+Yjg==]]></item>
  <item><![CDATA[8J+YtA==]]></item>
  <item><![CDATA[8J+YtQ==]]></item>
  <item><![CDATA[8J+Ysg==]]></item>
  <item><![CDATA[8J+Ynw==]]></item>
  <item><![CDATA[8J+Ypg==]]></item>
  <item><![CDATA[8J+Ypw==]]></item>
  <item><![CDATA[8J+YiA==]]></item>
  <item><![CDATA[8J+Rvw==]]></item>
  <item><![CDATA[8J+Yrg==]]></item>
  <item><![CDATA[8J+YrA==]]></item>
  <item><![CDATA[8J+YkA==]]></item>
  <item><![CDATA[8J+YlQ==]]></item>
  <item><![CDATA[8J+Yrw==]]></item>
  <item><![CDATA[8J+Ytg==]]></item>
  <item><![CDATA[8J+Yhw==]]></item>
  <item><![CDATA[8J+Yjw==]]></item>
  <item><![CDATA[8J+YkQ==]]></item>
  <item><![CDATA[8J+Rsg==]]></item>
  <item><![CDATA[8J+Rsw==]]></item>
  <item><![CDATA[8J+Rrg==]]></item>
  <item><![CDATA[8J+Rtw==]]></item>
  <item><![CDATA[8J+Sgg==]]></item>
  <item><![CDATA[8J+Rtg==]]></item>
  <item><![CDATA[8J+Rpg==]]></item>
  <item><![CDATA[8J+Rpw==]]></item>
  <item><![CDATA[8J+RqA==]]></item>
  <item><![CDATA[8J+RqQ==]]></item>
  <item><![CDATA[8J+RtA==]]></item>
  <item><![CDATA[8J+RtQ==]]></item>
  <item><![CDATA[8J+RsQ==]]></item>
  <item><![CDATA[8J+RvA==]]></item>
  <item><![CDATA[8J+RuA==]]></item>
  <item><![CDATA[8J+Yug==]]></item>
  <item><![CDATA[8J+YuA==]]></item>
  <item><![CDATA[8J+Yuw==]]></item>
  <item><![CDATA[8J+YvQ==]]></item>
  <item><![CDATA[8J+YvA==]]></item>
  <item><![CDATA[8J+ZgA==]]></item>
  <item><![CDATA[8J+Yvw==]]></item>
  <item><![CDATA[8J+YuQ==]]></item>
  <item><![CDATA[8J+Yvg==]]></item>
  <item><![CDATA[8J+RuQ==]]></item>
  <item><![CDATA[8J+Rug==]]></item>
  <item><![CDATA[8J+ZiA==]]></item>
  <item><![CDATA[8J+ZiQ==]]></item>
  <item><![CDATA[8J+Zig==]]></item>
  <item><![CDATA[8J+SgA==]]></item>
  <item><![CDATA[8J+RvQ==]]></item>
  <item><![CDATA[8J+SqQ==]]></item>
  <item><![CDATA[8J+UpQ==]]></item>
  <item><![CDATA[4pyo]]></item>
  <item><![CDATA[8J+Mnw==]]></item>
  <item><![CDATA[8J+Sqw==]]></item>
  <item><![CDATA[8J+SpQ==]]></item>
  <item><![CDATA[8J+Sog==]]></item>
  <item><![CDATA[8J+Spg==]]></item>
  <item><![CDATA[8J+Spw==]]></item>
  <item><![CDATA[8J+SpA==]]></item>
  <item><![CDATA[8J+SqA==]]></item>
  <item><![CDATA[8J+Rgg==]]></item>
  <item><![CDATA[8J+RgA==]]></item>
  <item><![CDATA[8J+Rgw==]]></item>
  <item><![CDATA[8J+RhQ==]]></item>
  <item><![CDATA[8J+RhA==]]></item>
  <item><![CDATA[8J+RjQ==]]></item>
  <item><![CDATA[8J+Rjg==]]></item>
  <item><![CDATA[8J+RjA==]]></item>
  <item><![CDATA[8J+Rig==]]></item>
  <item><![CDATA[4pyK]]></item>
  <item><![CDATA[4pyM77iP]]></item>
  <item><![CDATA[8J+Riw==]]></item>
  <item><![CDATA[4pyL]]></item>
  <item><![CDATA[8J+RkA==]]></item>
  <item><![CDATA[8J+Rhg==]]></item>
  <item><![CDATA[8J+Rhw==]]></item>
  <item><![CDATA[8J+RiQ==]]></item>
  <item><![CDATA[8J+RiA==]]></item>
  <item><![CDATA[8J+ZjA==]]></item>
  <item><![CDATA[8J+Zjw==]]></item>
  <item><![CDATA[4pid77iP]]></item>
  <item><![CDATA[8J+Rjw==]]></item>
  <item><![CDATA[8J+Sqg==]]></item>
  <item><![CDATA[8J+atg==]]></item>
  <item><![CDATA[8J+Pgw==]]></item>
  <item><![CDATA[8J+Sgw==]]></item>
  <item><![CDATA[8J+Rqw==]]></item>
  <item><![CDATA[8J+Rqg==]]></item>
  <item><![CDATA[8J+RrA==]]></item>
  <item><![CDATA[8J+RrQ==]]></item>
  <item><![CDATA[8J+Sjw==]]></item>
  <item><![CDATA[8J+SkQ==]]></item>
  <item><![CDATA[8J+Rrw==]]></item>
  <item><![CDATA[8J+Zhg==]]></item>
  <item><![CDATA[8J+ZhQ==]]></item>
  <item><![CDATA[8J+SgQ==]]></item>
  <item><![CDATA[8J+Ziw==]]></item>
  <item><![CDATA[8J+Shg==]]></item>
  <item><![CDATA[8J+Shw==]]></item>
  <item><![CDATA[8J+ShQ==]]></item>
  <item><![CDATA[8J+RsA==]]></item>
  <item><![CDATA[8J+Zjg==]]></item>
  <item><![CDATA[8J+ZjQ==]]></item>
  <item><![CDATA[8J+Zhw==]]></item>
  <item><![CDATA[8J+OqQ==]]></item>
  <item><![CDATA[8J+RkQ==]]></item>
  <item><![CDATA[8J+Rkg==]]></item>
  <item><![CDATA[8J+Rnw==]]></item>
  <item><![CDATA[8J+Rng==]]></item>
  <item><![CDATA[8J+RoQ==]]></item>
  <item><![CDATA[8J+RoA==]]></item>
  <item><![CDATA[8J+Rog==]]></item>
  <item><![CDATA[8J+RlQ==]]></item>
  <item><![CDATA[8J+RlA==]]></item>
  <item><![CDATA[8J+Rmg==]]></item>
  <item><![CDATA[8J+Rlw==]]></item>
  <item><![CDATA[8J+OvQ==]]></item>
  <item><![CDATA[8J+Rlg==]]></item>
  <item><![CDATA[8J+RmA==]]></item>
  <item><![CDATA[8J+RmQ==]]></item>
  <item><![CDATA[8J+SvA==]]></item>
  <item><![CDATA[8J+RnA==]]></item>
  <item><![CDATA[8J+RnQ==]]></item>
  <item><![CDATA[8J+Rmw==]]></item>
  <item><![CDATA[8J+Rkw==]]></item>
  <item><![CDATA[8J+OgA==]]></item>
  <item><![CDATA[8J+Mgg==]]></item>
  <item><![CDATA[8J+ShA==]]></item>
  <item><![CDATA[8J+Smw==]]></item>
  <item><![CDATA[8J+SmQ==]]></item>
  <item><![CDATA[8J+SnA==]]></item>
  <item><![CDATA[8J+Smg==]]></item>
  <item><![CDATA[4p2k77iP]]></item>
  <item><![CDATA[8J+SlA==]]></item>
  <item><![CDATA[8J+Slw==]]></item>
  <item><![CDATA[8J+Skw==]]></item>
  <item><![CDATA[8J+SlQ==]]></item>
  <item><![CDATA[8J+Slg==]]></item>
  <item><![CDATA[8J+Sng==]]></item>
  <item><![CDATA[8J+SmA==]]></item>
  <item><![CDATA[8J+SjA==]]></item>
  <item><![CDATA[8J+Siw==]]></item>
  <item><![CDATA[8J+SjQ==]]></item>
  <item><![CDATA[8J+Sjg==]]></item>
  <item><![CDATA[8J+RpA==]]></item>
  <item><![CDATA[8J+RpQ==]]></item>
  <item><![CDATA[8J+SrA==]]></item>
  <item><![CDATA[8J+Row==]]></item>
  <item><![CDATA[8J+SrQ==]]></item>
</emoji-panel>
