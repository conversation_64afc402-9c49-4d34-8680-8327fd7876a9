"use strict";(self.webpackChunkweixin_search=self.webpackChunkweixin_search||[]).push([["90-prize-awarding"],{566470:function(t,e,r){r.r(e);var i=r(132766),n=r(181927),a=r(551900),o=r(427598),s=(0,a.Z)(n.Z,i.s,i.x,!1,null,"258f0e82",null);"function"==typeof o.Z&&(0,o.Z)(s),e.default=s.exports},325918:function(t,e,r){r.d(e,{Z:function(){return i}});function i(t){t.options.__wxs_id="5107debf"}},427598:function(t,e,r){var i=r(325918);e.Z=i.Z},181927:function(t,e,r){var i=r(759738);e.Z=i.Z},132766:function(t,e,r){r.d(e,{s:function(){return i},x:function(){return n}});var i=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"prize-awarding",attrs:{"data-fcn-prize-awarding":"","data-fr-1c30e4910":""}},t._l(t.showList,function(e,i){return r("div",{directives:[{name:"active",rawName:"v-active.stop",value:!!e.jumpInfo,expression:"!!item.jumpInfo",modifiers:{stop:!0}}],key:i,staticClass:"prize active__mask",class:"rank-"+e.rank,attrs:{"data-report-id":t.ignoreReport?"":t.M_itemReportId(e,i+1),"data-new-teach-report-id":e.teachReportId,"data-cli":""},on:{click:function(r){return r.stopPropagation(),t.onTapItem(e,i)}}},[r("div",{directives:[{name:"active",rawName:"v-active.stop",value:!!e.liveInfo,expression:"!!item.liveInfo",modifiers:{stop:!0}}],staticClass:"thumb-wrap",class:{live:!!e.liveInfo},attrs:{"data-cli":""},on:{click:function(r){return t.onTapAvatar(r,e,i)}}},[e.iconUrl?r("online-image",{staticClass:"thumb",class:{"thumb--national-flag":e.isNationalFlag},attrs:{url:e.iconUrl,mode:"avatar","data-fc-130a293b9":""}}):t._e()],1),r("div",{staticClass:"text"},[r("p",{staticClass:"title"},[t._v(t._s(e.title))]),e.subTitle?r("p",{staticClass:"sub-title"},[t._v(t._s(e.subTitle))]):t._e(),r("p",{staticClass:"desc"},[e.rank<=3?r("ui-image",{staticClass:"trophy",attrs:{size:16,url:"https://res.wx.qq.com/t/fed_upload/3b5ee12c-6004-4175-a570-36e5102e44f5/traffic.svg","data-fc-130a293ba":""}}):t._e(),r("span",[t._v(t._s(e.honor||e.rank))])],1)])])}),0)},n=[]},759738:function(t,e,r){var i=r(798509);function n(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{},i=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(r).filter(function(t){return Object.getOwnPropertyDescriptor(r,t).enumerable}))),i.forEach(function(e){var i,n,a;i=t,n=e,a=r[e],n in i?Object.defineProperty(i,n,{value:a,enumerable:!0,configurable:!0,writable:!0}):i[n]=a})}return t}e.Z={name:"PrizeAwarding",mixins:[i.jB,i.uW,i.Sx],props:{honorMap:{type:Object,default:function(){return{1:"冠军",2:"亚军",3:"季军"}}},ignoreReport:{type:Boolean}},data:function(){return{}},computed:{showList:function(){var t=this,e=["2","1","3"],r=[];return this.itemInfo.list.forEach(function(i){var a,o;r[e.indexOf(i.rank)]=(a=n({},i),o=(o={honor:t.honorMap[i.rank]},o),Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(o)):(function(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);r.push.apply(r,i)}return r})(Object(o)).forEach(function(t){Object.defineProperty(a,t,Object.getOwnPropertyDescriptor(o,t))}),a)}),r}},methods:{onTapItem:function(t,e){this.M_serviceSearchGo(t)&&!this.ignoreReport&&this.M_clickReport({clickContent:t.title},t),this.$emit("tap:item",t,e)},onTapAvatar:function(t,e,r){if(e.liveInfo){this.M_serviceSearchGo(n({},e,e.liveInfo))&&!this.ignoreReport&&this.M_clickReport({clickContent:e.title},e),this.$emit("tap:avatar",n({},e,e.liveInfo),r),t.stopPropagation(),t.preventDefault();return}}}}}}]);