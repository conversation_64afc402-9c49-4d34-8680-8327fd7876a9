(self.webpackChunkweixin_search=self.webpackChunkweixin_search||[]).push([["pc-picker"],{966611:function(e){e.exports='<div class="picker-pc <%= className %>"> <div class="weui-mask mask"></div> <div class="weui-half-screen-dialog weui-picker picker" role=dialog aria-modal=true tabindex=-1> <div class=weui-half-screen-dialog__hd> <div class=weui-half-screen-dialog__hd__main> <strong class="weui-half-screen-dialog__title title"><%= title %></strong> <span class=weui-half-screen-dialog__subtitle><%= desc %></span> </div> </div> <div class="weui-half-screen-dialog__bd body"> <div class=weui-picker__bd></div> </div> <div class="weui-half-screen-dialog__ft footer"> <div class=weui-hidden_abs id=weui-picker-aria-content></div> <% if (mac) { %> <a href=javascript:; class="weui-btn weui-btn_default weui-picker__btn btn" id=weui-picker-cancel data-action=select role=button><%= closeText %></a> <a href=javascript:; class="weui-btn weui-btn_primary weui-picker__btn btn" id=weui-picker-confirm data-action=select role=button><%= confirmText %></a> <% } else { %> <a href=javascript:; class="weui-btn weui-btn_primary weui-picker__btn btn" id=weui-picker-confirm data-action=select role=button><%= confirmText %></a> <a href=javascript:; class="weui-btn weui-btn_default weui-picker__btn btn" id=weui-picker-cancel data-action=select role=button><%= closeText %></a> <% } %> </div> </div> </div> '},225206:function(e,t,n){"use strict";n.r(t);var i=n(787806),a=n.n(i),r=n(168065);a().picker=r.g,a().datePicker=r.O},168065:function(e,t,n){"use strict";n.d(t,{O:function(){return m},g:function(){return h}});var i,a=n(336733),r=n(439074);n(45406);var o=n(316999),l=n(966611),c=n.n(l),u=n(482471),s=n.n(u),f=n(798509);function d(e){"object"!=typeof e&&(e={label:e,value:e}),a.Z.extend(this,e)}d.prototype.toString=function(){return this.value},d.prototype.valueOf=function(){return this.value};var p={};function h(){if(i)return i;var e,t,n=arguments[arguments.length-1],r=a.Z.extend({id:"default",className:"",container:"body",title:"",desc:"",confirmText:f.Zr.L("确定"),closeText:f.Zr.L("取消"),mac:Global.os.mac,onChange:a.Z.noop,onConfirm:a.Z.noop,onClose:a.Z.noop},n),l=!1;if(arguments.length>2){var u=0;for(e=[];u<arguments.length-1;)e.push(arguments[u++]);l=!0}else e=arguments[0];p[r.id]=p[r.id]||[];var h=[],m=p[r.id],v=(0,a.Z)(a.Z.render(c(),r)),w=v.find("#weui-picker-confirm"),g=v.find(".weui-mask"),b=n.depth||(l?e.length:o.v(e[0])),_="";function k(e){k=a.Z.noop,v.find(".weui-mask").addClass("weui-animate-fade-out"),v.find(".weui-picker").addClass(n.animateOut||"picker-pc-leave").on("animationend webkitAnimationEnd",function(){v.remove(),i=!1,r.onClose(),e&&e()}),f.Gc.$emit(f.U3.PC_SHOW_SCROLLBAR,!0)}function y(e){k(e)}function C(e,n){if(void 0===m[n]&&r.defaultValue&&void 0!==r.defaultValue[n]){var i=r.defaultValue[n],o=0,c=e.length;if("object"==typeof e[o])for(;o<c&&i!=e[o].value;++o);else for(;o<c&&i!=e[o];++o);o<c?m[n]=o:console.warn("Picker has not match defaultValue: ".concat(i))}v.find(".weui-picker__group").eq(n).scroll({items:e,temp:m[n],onChange:function(e,i){if(e){var o=v.find(".weui-picker__group").eq(n);o.find(".weui-picker__item").attr("aria-hidden","true"),a.Z.os.android?(o.attr("title","按住上下可调"),o.attr("aria-label",e.label)):(o.find(".weui-picker__item").eq(i).attr("aria-hidden","false"),o.find(".weui-picker__item").eq(i)[0].focus()),h[n]=new d(e)}else h[n]=null;m[n]=i,l?h.length==b&&r.onChange(h):e.children&&e.children.length>0?(v.find(".weui-picker__group").eq(n+1).show(),C(e.children,n+1)):(v.find(".weui-picker__group").forEach(function(e,t){t>n&&(0,a.Z)(e).hide()}),h.splice(n+1),r.onChange(h)),v.find(".weui-picker__group").eq(n)[0].focus(),clearTimeout(t),t=setTimeout(function(){v.find("#weui-picker-aria-content").html("")},100)},onScroll:function(e,i){if(e){var r=v.find(".weui-picker__group").eq(n);r.find(".weui-picker__item").attr("aria-hidden","true"),a.Z.os.android?(r.attr("title","按住上下可调"),r.attr("aria-label",e.label)):(r.find(".weui-picker__item").eq(i).attr("aria-hidden","false"),r.find(".weui-picker__item").eq(i)[0].focus()),h[n]=new d(e)}else h[n]=null;m[n]=i,a.Z.os.android&&(clearTimeout(t),t=setTimeout(function(){v.find("#weui-picker-aria-content").html(e.label).attr("role","alert")},50))},onConfirm:r.onConfirm})}for(var Z=b;Z--;)_+=s();return v.find(".weui-picker__bd").html(_),(0,a.Z)(r.container).append(v),a.Z.getStyle(v[0],"transform"),v.find(".weui-mask").addClass("weui-animate-fade-in"),v.find(".weui-picker").addClass(n.animateIn||"picker-pc-enter").on("animationend webkitAnimationEnd",function(e){e.target.focus()}),f.Gc.$emit(f.U3.PC_SHOW_SCROLLBAR,!1),l?e.forEach(function(e,t){C(e,t)}):C(e,0),v.on("click",".weui-mask",function(){var e;k(void 0)}).on("click",".weui-picker__btn",function(){var e;k(void 0)}).on("mousewheel",function(e){return e.preventDefault()}),g.on("click",function(){var e;k(void 0)}).on("touchmove",function(e){e.preventDefault()}).on("mousewheel",function(e){e.preventDefault()}),w.on("click",function(){r.onConfirm(h)}),(i=v[0]).hide=y,i}function m(e){var t,n=new Date,i=a.Z.extend({id:"datePicker",onChange:a.Z.noop,onConfirm:a.Z.noop,start:n.getFullYear()-20,end:n.getFullYear()+20,defaultValue:[n.getFullYear(),n.getMonth()+1,n.getDate()],cron:"* * *"},e);"number"==typeof i.start?i.start=new Date("".concat(i.start,"/01/01")):"string"==typeof i.start&&(i.start=new Date(i.start.replace(/-/g,"/"))),"number"==typeof i.end?i.end=new Date("".concat(i.end,"/12/31")):"string"==typeof i.end&&(i.end=new Date(i.end.replace(/-/g,"/")));var o=function(e,t,n){for(var i=0,a=e.length;i<a;i++){var r=e[i];if(r[t]==n)return r}},l=[],c=r.Z.parse(i.cron,i.start,i.end);do{var u=(t=c.next()).value.getFullYear(),s=t.value.getMonth()+1,f=t.value.getDate(),d=o(l,"value",u);!d&&(d={label:"".concat(u,"年"),value:u,children:[]},l.push(d));var p=o(d.children,"value",s);!p&&(p={label:"".concat(s,"月"),value:s,children:[]},d.children.push(p)),p.children.push({label:"".concat(f,"日"),value:f})}while(!t.done);return h(l,i)}},45406:function(e,t,n){"use strict";var i=n(336733);function a(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=Array(t);n<t;n++)i[n]=e[n];return i}n(798509);var r=function(e,t){return e.css({"-webkit-transition":"all ".concat(t,"s"),transition:"all ".concat(t,"s")})},o=function(e,t){return e.css({"-webkit-transform":"translate3d(0, ".concat(t,"px, 0)"),transform:"translate3d(0, ".concat(t,"px, 0)")})},l=function(e){for(var t=Math.floor(e.length/2),n=0;e[t]&&e[t].disabled;)if(t=++t%e.length,++n>e.length)throw Error("No selectable item.");return t},c=function(e,t){return e*t},u=function(e,t,n){return-(t*(n-e-1))};i.Z.fn.scroll=function(e){var t,n,s,f,d=this,p=i.Z.extend({items:[],scrollable:".weui-picker__content",offset:2,rowHeight:48,onChange:i.Z.noop,onScroll:i.Z.noop,temp:null,bodyHeight:240},e),h=p.items.map(function(e){return'<div role="option" title="按住上下可调" tabindex="0" class="weui-picker__item'.concat(e.disabled?" weui-picker__item_disabled":"",'">').concat("object"==typeof e?e.label:e,"</div>")}).join(""),m=(0,i.Z)(this);m.find(".weui-picker__content").html(h);var v=m.find(p.scrollable),w=null,g=[];if(null!==p.temp&&p.temp<p.items.length){var b=p.temp;p.onChange.call(this,p.items[b],b),f=(p.offset-b)*p.rowHeight}else{var _,k,y=l(p.items);p.onChange.call(this,p.items[y],y),_=p.offset,k=p.rowHeight,f=(_-l(p.items))*k}o(v,f);var C=function(e){f+=e,f=Math.round(f/p.rowHeight)*p.rowHeight;var t=c(p.offset,p.rowHeight),n=u(p.offset,p.rowHeight,p.items.length);f>t&&(f=t),f<n&&(f=n);for(var i=p.offset-f/p.rowHeight;p.items[i]&&p.items[i].disabled;)e>0?++i:--i;f=(p.offset-i)*p.rowHeight,r(v,.3),o(v,f),i!==w&&(p.onScroll.call(d,p.items[i],i),p.onChange.call(d,p.items[i],i)),w=null};function Z(e){t=e,s=+new Date}function x(e){var i=f+((n=e)-t);r(v,0),o(v,i),s=+new Date,g.push({time:s,y:n}),g.length>40&&g.shift(),i=Math.round(i/p.rowHeight)*p.rowHeight;var a=c(p.offset,p.rowHeight),l=u(p.offset,p.rowHeight,p.items.length);if(!(i>a)&&!(i<l)){var d=p.offset-i/p.rowHeight;(!p.items[d]||!p.items[d].disabled)&&d!==w&&p.onScroll.call(this,p.items[d],d)}}function D(e){if(t){var i=new Date().getTime(),a=m[0].getBoundingClientRect().top+p.bodyHeight/2;if(n=e,i-s>100)Math.abs(n-t)>10?C(n-t):C(a-n);else if(Math.abs(n-t)>10){for(var r=g.length-1,o=r,l=r;l>0&&s-g[l].time<100;l--)o=l;if(o!==r){var c=g[r],u=g[o],f=c.time-u.time,d=c.y-u.y;C(d/f*150+(n-t))}else C(0)}else C(a-n);t=null}}v=m.offAll().on("touchstart",function(e){Z(e.changedTouches[0].pageY)}).on("touchmove",function(e){x(e.changedTouches[0].pageY),e.preventDefault()}).on("touchend",function(e){D(e.changedTouches[0].pageY)}).find(p.scrollable),m.on("mousedown",function(e){Z(e.pageY),e.stopPropagation(),e.preventDefault()}).on("mousemove",function(e){t&&(x(e.pageY),e.stopPropagation(),e.preventDefault())}).on("mouseup mouseleave",function(e){D(e.pageY),e.stopPropagation(),e.preventDefault()});var H=!1,T=0,S=0;m.on("mousewheel",function(e){if(e.stopPropagation(),e.preventDefault(),H)T&&clearTimeout(T),T=setTimeout(function(){H=!1,D(S)},150),x(S-=.2*e.deltaY);else{H=!0;var t,n,i=(t=m.find(".weui-picker__indicator")||[],n=1,function(e){if(Array.isArray(e))return e}(t)||function(e,t){var n,i,a=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=a){var r=[],o=!0,l=!1;try{for(a=a.call(e);!(o=(n=a.next()).done)&&(r.push(n.value),!t||r.length!==t);o=!0);}catch(e){l=!0,i=e}finally{try{!o&&null!=a.return&&a.return()}finally{if(l)throw i}}return r}}(t,1)||function(e,t){if(e){if("string"==typeof e)return a(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return a(e,t)}}(t,n)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())[0];i&&Z(S=i.getBoundingClientRect().y)}})}}}]);