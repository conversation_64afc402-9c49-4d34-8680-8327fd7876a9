"use strict";(self.webpackChunkweixin_search=self.webpackChunkweixin_search||[]).push([["95-miniprogram-filter"],{902385:function(t,e,a){a.r(e);var r=a(627970),i=a(33035),n=a(551900),s=a(16501),c=(0,n.Z)(i.Z,r.s,r.x,!1,null,"279ed302",null);"function"==typeof s.Z&&(0,s.Z)(c),e.default=c.exports},927971:function(t,e,a){a.d(e,{Z:function(){return r}});function r(t){t.options.__wxs_id="ef72e716"}},16501:function(t,e,a){var r=a(927971);e.Z=r.Z},33035:function(t,e,a){var r=a(722012);e.Z=r.Z},627970:function(t,e,a){a.d(e,{s:function(){return r},x:function(){return i}});var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return t.filter&&t.filter.tabs&&t.filter.tabs.length?a("div",{staticClass:"miniprogram-filter",class:{"bottom-margin":t.bottomMargin},attrs:{"data-fcn-miniprogram-filter":"","data-fr-14aa26952":""}},[a("div",{staticClass:"miniprogram-filter-tabs"},t._l(t.filter.tabs,function(e,r){return a("div",{key:r,staticClass:"tab-item",class:{selected:r===t.selectTabIdx},attrs:{"data-report-id":t.M_itemReportId(e,r+1+"|filter")},on:{click:function(a){return t.onTap(e,r)}}},[e.withIcon?a("svg-icon",{attrs:{"aria-hidden":"true","class-name":"tab-item-icon",name:"magnifier","data-fc-109aac11f":""}}):t._e(),a("span",{staticClass:"tab-item-text"},[t._v(t._s(e.title))])],1)}),0)]):t._e()},i=[]},722012:function(t,e,a){var r=a(798509),i=a(462474),n=a(984928);e.Z={mixins:[r.uW,r.jB],props:{filter:{type:Object,default:function(){return{}}},resultObj:{type:Object,default:function(){return{}}},bottomMargin:Boolean},data:function(){return{selectTabIdx:0,categoryData:[],typePos:-1,data:{boxID:"miniProgram-filter;".concat(10000010,";").concat(r.xB.type)}}},computed:{query:function(){var t;return null===(t=this.$store.state.result.self.resultObj)||void 0===t?void 0:t.query}},watch:{query:function(){this.selectTabIdx=0}},methods:{onTap:function(t,e){if(this.selectTabIdx!=e){this.selectTabIdx=e;var a=t.query,s=this.M_composeParentSid({t:n.X$.FAKE_STICKY_HINT,s:r.xB.searchId,did:a,rid:this.$store.state.result.previousRid});this.M_clickReport({reqBusinessType:10000010,businessType:r.xB.type,actionType:n.At.VERTICAL,itemPos:"".concat(e+1,"|filter")},t),this.M_checkSpecialSearchBeforeLaunchNewSearch({query:a,callback:function(){r.Zr.setSearchInputWord({query:a,isInputChange:!1}),i.Z.$emit(i.U.GOTO,{page:n.kO.RESULT,query:a,searchId:"",extReqParams:{key:"parentSearchID",textValue:s}})},extParams:{crossExtReqParams:[{key:"parentSearchID",textValue:s}]}})}}}}}}]);