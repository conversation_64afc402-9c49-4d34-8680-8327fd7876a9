"use strict";(self.webpackChunkweixin_search=self.webpackChunkweixin_search||[]).push([["97-image-view"],{294234:function(t,e,i){i.r(e);var r=i(37826),n=i(389083),a=i(551900),o=i(266244),l=(0,a.Z)(n.Z,r.s,r.x,!1,null,"d13e2c1c",null);"function"==typeof o.Z&&(0,o.Z)(l),e.default=l.exports},800582:function(t,e,i){i.d(e,{Z:function(){return r}});function r(t){t.options.__wxs_id="e5cb37d5"}},266244:function(t,e,i){var r=i(800582);e.Z=r.Z},389083:function(t,e,i){var r=i(77190);e.Z=r.Z},37826:function(t,e,i){i.d(e,{s:function(){return r},x:function(){return n}});var r=function(){var t=this,e=t.$createElement,i=t._self._c||e;return t.itemInfo.lightImageUrl?i("div",{staticClass:"image-view",attrs:{"data-fcn-image-view":"","data-fr-1d3211152":""}},[i("ui-scroll",{directives:[{name:"arrow-scroll",rawName:"v-arrow-scroll"}],ref:"horScroll",staticClass:"image-list",attrs:{padding:0,items:t.imageUrls,wait:50,role:"listbox",start:0,"data-fc-1377f73ba":""},on:{scrollEnd:t.onUiScrollEnd},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.item,n=e.index;return[i("div",{directives:[{name:"active",rawName:"v-active.stop",modifiers:{stop:!0}}],staticClass:"image-wrap",class:{active__absolute:r.jumpInfo},attrs:{"data-report-id":t.M_itemReportId(r,n+1),"data-expose-width-ratio":"0.001","data-cli":""},on:{click:function(e){return e.stopPropagation(),t.onTapImage(r)}}},[i("div",{directives:[{name:"image",rawName:"v-image.raw",value:t.isDarkMode?r.darkImageUrl:r.lightImageUrl,expression:"isDarkMode ? item.darkImageUrl : item.lightImageUrl",modifiers:{raw:!0}}],staticClass:"image-cont"})])]}}],null,!1,3080018151)}),t.itemInfo.link?i("div",{directives:[{name:"active",rawName:"v-active.stop",modifiers:{stop:!0}}],staticClass:"image-link active__mask",attrs:{role:"link","data-report-id":t.M_itemReportId(t.itemInfo.link),"data-cli":""},on:{click:function(e){return e.stopPropagation(),t.onTapLink(t.itemInfo.link)}}},[t._v("\n      "+t._s(t.itemInfo.link.title)+"\n    ")]):t._e()],1):t._e()},n=[]},77190:function(t,e,i){var r=i(737133),n=i(984928),a=i(798509);function o(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,r=Array(e);i<e;i++)r[i]=t[i];return r}e.Z={name:"ImageView",components:{UiScroll:r.Z},mixins:[a.jB,a.uW,a.Sx],data:function(){return{isDarkMode:a.Zr.isDarkMode()}},computed:{imageUrls:function(){return this.itemInfo.lightImageUrl?[{reportId:this.itemInfo.reportId,jumpInfo:this.itemInfo.jumpInfo,lightImageUrl:this.itemInfo.lightImageUrl,darkImageUrl:this.itemInfo.darkImageUrl||this.itemInfo.lightImageUrl}]:[]}},watch:{imageUrls:{handler:function(){var t=this;this.$nextTick(function(){var e,i,r;null===(r=t.$refs)||void 0===r||null===(i=r.horScroll)||void 0===i||null===(e=i.$el)||void 0===e||e.scroll(0,0),a.Gc.$emit(a.U3.resultExposeAnalysis)})}}},created:function(){var t=this;a.Zr.watchMatchMediaColor(function(e){t.isDarkMode=e})},methods:{onUiScrollEnd:function(t){var e,i,r,a=(i=1,function(t){if(Array.isArray(t))return t}(e=t)||function(t,e){var i,r,n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var a=[],o=!0,l=!1;try{for(n=n.call(t);!(o=(i=n.next()).done)&&(a.push(i.value),!e||a.length!==e);o=!0);}catch(t){l=!0,r=t}finally{try{!o&&null!=n.return&&n.return()}finally{if(l)throw r}}return a}}(e,1)||function(t,e){if(t){if("string"==typeof t)return o(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);if("Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i)return Array.from(i);if("Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return o(t,e)}}(e,i)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())[0],l=((null==a?void 0:null===(r=a.el)||void 0===r?void 0:r.dataset)||{}).scrollItemIndex,c=void 0===l?-1:l;if(+c>-1){var s=this.imageUrls[+c];this.M_clickReport({clickContent:"图片滑动"},function(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{},r=Object.keys(i);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(i).filter(function(t){return Object.getOwnPropertyDescriptor(i,t).enumerable}))),r.forEach(function(e){var r,n,a;r=t,n=e,a=i[e],n in r?Object.defineProperty(r,n,{value:a,enumerable:!0,configurable:!0,writable:!0}):r[n]=a})}return t}({actionType:n.At.HOR_TOUCHMOVE},s))}},onTapImage:function(t){t.jumpInfo&&(this.M_go(t),this.M_clickReport({clickContent:"图片"},t))},onTapLink:function(t){this.M_go(t),this.M_clickReport({clickContent:t.title},t)}}}}}]);