"use strict";(self.webpackChunkweixin_search=self.webpackChunkweixin_search||[]).push([["src_components_general_traffic_traffic-info_vue"],{66119:function(t,e,i){var n=i(2932),a=i(263688),r=i(551900),o=i(60030),s=(0,r.Z)(a.Z,n.s,n.x,!1,null,"ca589aae",null);"function"==typeof o.Z&&(0,o.Z)(s),e.Z=s.exports},483972:function(t,e,i){i.d(e,{Z:function(){return n}});function n(t){t.options.__wxs_id="cef9a5ba"}},16843:function(t,e,i){var n=i(21185),a=i(77253),r=i(551900),o=i(308038),s=(0,r.Z)(a.Z,n.s,n.x,!1,null,"04181503",null);"function"==typeof o.Z&&(0,o.Z)(s),e.Z=s.exports},558219:function(t,e,i){i.d(e,{Z:function(){return n}});function n(t){t.options.__wxs_id="851f83b7"}},30379:function(t,e,i){var n=i(328585),a=i(89333),r=i(551900),o=i(217799),s=(0,r.Z)(a.Z,n.s,n.x,!1,null,"21f5ade2",null);"function"==typeof o.Z&&(0,o.Z)(s),e.Z=s.exports},881812:function(t,e,i){i.d(e,{Z:function(){return n}});function n(t){t.options.__wxs_id="e635139d"}},152734:function(t,e,i){i.r(e);var n=i(656779),a=i(817665),r=i(551900),o=i(380270),s=(0,r.Z)(a.Z,n.s,n.x,!1,null,"10bec1af",null);"function"==typeof o.Z&&(0,o.Z)(s),e.default=s.exports},862767:function(t,e,i){i.d(e,{Z:function(){return n}});function n(t){t.options.__wxs_id="9404769a"}},445823:function(t,e,i){var n=i(344902),a=i(829498),r=i(551900),o=i(628829),s=(0,r.Z)(a.Z,n.s,n.x,!1,null,null,null);"function"==typeof o.Z&&(0,o.Z)(s),e.Z=s.exports},200721:function(t,e,i){i.d(e,{Z:function(){return n}});function n(t){t.options.__wxs_id="40aee3e6"}},60030:function(t,e,i){var n=i(483972);e.Z=n.Z},308038:function(t,e,i){var n=i(558219);e.Z=n.Z},217799:function(t,e,i){var n=i(881812);e.Z=n.Z},380270:function(t,e,i){var n=i(862767);e.Z=n.Z},628829:function(t,e,i){var n=i(200721);e.Z=n.Z},263688:function(t,e,i){var n=i(39442);e.Z=n.Z},77253:function(t,e,i){var n=i(840537);e.Z=n.Z},89333:function(t,e,i){var n=i(298714);e.Z=n.Z},817665:function(t,e,i){var n=i(760078);e.Z=n.Z},829498:function(t,e,i){var n=i(172745);e.Z=n.Z},2932:function(t,e,i){i.d(e,{s:function(){return n},x:function(){return a}});var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{directives:[{name:"active",rawName:"v-active.stop",modifiers:{stop:!0}}],staticClass:"voice-icon-wrapper active__mask",attrs:{role:"button","data-cli":"","data-fcn-TransformIcon":"","data-fr-1339f20e0":""},on:{click:function(e){return e.stopPropagation(),t.onTapIcon(e)}}},[i("i",{staticClass:"voice-icon voice-once",class:{playing:!0===t.play}},[i("span",{staticClass:"ui-aria-hidden"},[t._v(t._s(t.UIAriaHiddenText))])])])},a=[]},21185:function(t,e,i){i.d(e,{s:function(){return n},x:function(){return a}});var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"ticket",class:t.classWrapper,style:{zoom:t.scaleFactor},attrs:{"data-fcn-ticket":"","data-fr-1230c8464":""}},[t.item.title?i("div",{staticClass:"ticket__header"},[t.item.iconUrl?i("ui-image",{staticClass:"mr-4",attrs:{url:t.item.iconUrl,height:20,width:20,"no-rpx":"","data-fc-13e84084d":""}}):t._e(),i("span",{staticClass:"ticket__title"},[t._v(t._s(t.item.title))])],1):t._e(),i("div",{staticClass:"ticket__body",class:{"left-gap":t.item.iconUrl}},[t.item.fromCity?i("p",{staticClass:"recommend__ftcity"},[t._v("\n        "+t._s(t.item.fromCity)+" - "+t._s(t.item.toCity)+"\n      ")]):t._e(),i("div",{staticClass:"ticket-main",class:{"is-bus":t.isBusTicket}},[t.isBusTicket?[i("div",{ref:"timeInfo",staticClass:"time-info",style:{width:t.dynammicMaxWidth.timeInfo}},[i("p",{staticClass:"ticket__time"},[t._v(t._s(t.item.fromTime))]),i("p",{staticClass:"journey-info__cost"},[t._v(t._s(t.item.timeCost))])]),i("div",{ref:"stationInfo",staticClass:"station-info"},[i("ui-image",{staticClass:"arrow-line vertical",attrs:{url:"https://res.wx.qq.com/t/fed_upload/9240b60f-7c26-4437-9d17-df62a0456c55/arrow_2.svg",width:5.5,color:"var(--FG-4)","no-rpx":"","data-fc-13e84084e":""}}),i("div",{staticClass:"station-info__right"},[i("p",{staticClass:"ticket__station"},[t._v(t._s(t.item.fromStation))]),i("p",{staticClass:"ticket__station"},[t._v(t._s(t.item.toStation))])])],1)]:t.isTrafficLine?[i("p",{ref:"fromInfo",staticClass:"ticket__station_simple",style:{width:t.dynammicMaxWidth.fromInfo}},[t._v(t._s(t.item.fromStation))]),i("ui-image",{staticClass:"arrow-line horizontal small",class:{"arrow-line--behind-center":t.item.transferDesc},attrs:{url:"https://search.wxqcloud.qq.com/t/fed_upload/c04d69836a2c95729fbd70f03d313cba/traffic-direction-arrow-small.svg",height:5.5,width:32,"no-rpx":"","data-fc-13e84084f":""}}),i("p",{ref:"toInfo",staticClass:"ticket__station_simple",style:{width:t.dynammicMaxWidth.toInfo}},[t._v(t._s(t.item.toStation))])]:[i("div",{ref:"fromInfo",staticClass:"from-info",style:{width:t.dynammicMaxWidth.fromInfo}},[i("p",{staticClass:"ticket__time"},[t._v(t._s(t.item.fromTime))]),i("p",{staticClass:"ticket__station"},[t._v(t._s(t.item.fromStation))])]),i("div",{ref:"journeyInfo",staticClass:"journey-info",style:{width:t.dynammicMaxWidth.journeyInfo}},[i("p",{staticClass:"journey-info__cost"},[t._v(t._s(t.item.timeCost))]),i("ui-image",{staticClass:"arrow-line horizontal",class:{"arrow-line--behind-center":t.item.transferDesc},attrs:{url:"https://res.wx.qq.com/t/fed_upload/b310e2da-429a-4ba3-8256-6669f9546ab1/arrow.svg",height:5.5,width:64,color:"var(--FG-4)","no-rpx":"","data-fc-13e840850":""}}),t.item.transferDesc?i("mini-text-tag",{staticClass:"journey-info__transfer",attrs:{text:t.item.transferDesc,"font-size":10,"font-weight":500,"bg-color":"transparent","fg-color":"var(--FG-1)","padding-l-r":8,"padding-t-b":2,"forbid-scale":!0,"data-fc-13e840851":""}}):t._e(),i("p",{staticClass:"journey-info__name"},[t._v(t._s(t.item.transferDesc?t.item.viaPlace:t.item.name))])],1),i("div",{ref:"toInfo",staticClass:"to-info",style:{width:t.dynammicMaxWidth.toInfo}},[i("p",{staticClass:"ticket__time"},[i("span",{staticClass:"ticket__toTime"},[t._v(t._s(t.item.toTime))]),i("sup",[t._v(t._s(t.item.toTimeDesc))])]),i("p",{staticClass:"ticket__station"},[t._v(t._s(t.item.toStation))])])],t.item.price?i("div",{staticClass:"price-info"},[i("p",{staticClass:"ticket__price"},[i("span",{staticClass:"ticket__price__value"},[t._v("￥"+t._s(t.item.price))]),t.isBusTicket?t._e():i("span",{staticClass:"ticket__price__start"},[t._v("起")])]),t.item.ticketNum?i("p",{staticClass:"ticket__remain"},[t._v(t._s(t.item.ticketNum))]):t._e()]):t._e()],2),t.item.ticketDescs?i("div",{staticClass:"ticket__descs"},t._l(t.item.ticketDescs,function(e,n){return i("span",{key:n,staticClass:"ticket__desc",domProps:{innerHTML:t._s(t.xss(e))}})}),0):t._e(),t.item.action?i("div",{attrs:{role:"link"}},[i("span",{directives:[{name:"active",rawName:"v-active.stop",modifiers:{stop:!0}}],staticClass:"link-more active__link",attrs:{"data-report-id":t.M_itemReportId(t.item.action),"data-cli":""},on:{click:function(e){return e.stopPropagation(),t.M_commonJump(t.item.action)}}},[t._v(t._s(t.item.action.title))])]):t._e()])])},a=[]},328585:function(t,e,i){i.d(e,{s:function(){return n},x:function(){return a}});var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"traffic-line-graph-container",attrs:{"data-fcn-traffic-line-graph":"","data-fr-14578ca58":""},on:{scroll:t.reportScroll}},[i("div",{staticClass:"traffic-line-graph",style:t.getContainerStyle},t._l(t.list,function(e,n){return i("div",{key:""+encodeURIComponent(e.name)+n,staticClass:"station"},[i("div",{staticClass:"station-line",style:t.getCircleItemStyle(n)},[i("div",{staticClass:"line",style:{width:t.lineLength/2+"px",backgroundColor:0===n&&"transparent"}}),i("div",{staticClass:"circle",class:{"circle-selected":t.hasAnchorFlag?e.isAnchor:t.initIndex===n},style:{width:t.circleWidth+"px",height:t.circleWidth+"px"}}),i("div",{staticClass:"line",style:{width:t.lineLength/2+"px",backgroundColor:n===t.list.length-1&&"transparent"}})]),i("div",{staticClass:"shadow"},[i("div",{staticClass:"text vertical"},[t._v("\n            "+t._s(t.stationNameDecorator(e.name))+"\n          ")]),i("div",{staticClass:"subway-text vertical"},t._l(e.subwayList,function(e,n){return i("span",{key:n,staticClass:"subway-text-item"},[i("span",{domProps:{innerHTML:t._s(t.subwayNameDecorator(e.lineName))}})])}),0)]),i("div",{staticClass:"absolute",style:{width:t.lineLength+"px"}},[i("div",{staticClass:"text vertical"},[t._v(" "+t._s(t.stationNameDecorator(e.name))+"\n            "),i("div",{staticClass:"subway-text vertical"},t._l(e.subwayList,function(e,n){return i("span",{key:n,staticClass:"subway-text-item",style:{color:e.color}},[i("span",{staticClass:"subway-text-container",domProps:{innerHTML:t._s(t.subwayNameDecorator(e.lineName))}})])}),0)])])])}),0)])},a=[]},656779:function(t,e,i){i.d(e,{s:function(){return n},x:function(){return a}});var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{directives:[{name:"active",rawName:"v-active.stop",modifiers:{stop:!0}}],staticClass:"traffic-info active__mask",attrs:{"data-report-id":t.M_itemReportId(t.getCurrentInfo.reportId),"data-cli":"","data-fcn-traffic-info":"","data-fr-16df7e2b8":""},on:{click:function(e){return e.stopPropagation(),t.tapTrafficMoreInfo(Object.assign({},t.getTrafficLineInfo.moreInfo,{reportId:t.getCurrentInfo.reportId}))}}},[i("div",{staticClass:"traffic-info-line"},[t._v(t._s(t.getTrafficCommonInfo.line))]),i("div",{staticClass:"traffic-info-station"},[i("ticket",{attrs:{item:{fromStation:t.getTrafficCommonInfo.fromStation,toStation:t.getTrafficCommonInfo.toStation},"show-type":9,"is-small-arrow":"","is-title-length-limit":"","data-fc-151047479":""}}),t.isOnlyOneDirection?t._e():i("div",{directives:[{name:"active",rawName:"v-active.stop",modifiers:{stop:!0}}],staticClass:"traffic-info-station-swap active__mask",attrs:{"data-report-id":t.M_itemReportId(t.swapReportId),"data-cli":""},on:{click:function(e){return e.stopPropagation(),t.swap(e)}}},[i("ui-image",{attrs:{size:24,url:"https://res.wx.qq.com/t/components/icons/base/transfer_circle_regular.svg","data-fc-15104747a":""}})],1)],1),i("div",{staticClass:"traffic-info-desc"},[t.getTrafficCommonInfo.startTime?i("div",{staticClass:"startTime"},[t._v(t._s(t.getTrafficCommonInfo.startTime))]):t._e(),t.getTrafficCommonInfo.endTime?i("div",{staticClass:"endTime"},[t._v(t._s(t.getTrafficCommonInfo.endTime))]):t._e(),t.getTrafficCommonInfo.moreInfo?i("div",{directives:[{name:"active",rawName:"v-active.stop",modifiers:{stop:!0}}],staticClass:"traffic-info-more active__mask",attrs:{"data-report-id":t.M_itemReportId(t.getTrafficCommonInfo.moreInfo),"data-cli":""},on:{click:function(e){return e.stopPropagation(),t.tapTrafficMoreInfo(t.getTrafficCommonInfo.moreInfo)}}},[t._v(t._s(t.getTrafficCommonInfo.moreInfo.title))]):t._e()]),i("div",{staticClass:"space"}),i("div",{staticClass:"traffic-line"},[t.getTrafficLineInfo.desc||t.getTrafficLineInfo.moreInfo&&t.getTrafficLineInfo.moreInfo.title?i("div",{staticClass:"traffic-line-desc"},[t.getTrafficLineInfo.desc?i("div",{staticClass:"traffic-desc"},[t._v(t._s(t.getTrafficLineInfo.desc))]):t._e(),t.getTrafficLineInfo.moreInfo&&t.getTrafficLineInfo.moreInfo.title?i("div",{directives:[{name:"active",rawName:"v-active.stop",modifiers:{stop:!0}}],staticClass:"traffic-line-more active__mask",attrs:{"data-report-id":t.M_itemReportId(t.getTrafficLineInfo.moreInfo),"data-cli":""},on:{click:function(e){return e.stopPropagation(),t.tapTrafficMoreInfo(t.getTrafficLineInfo.moreInfo)}}},[t._v(t._s(t.getTrafficLineInfo.moreInfo.title)+"\n          "),i("ui-transform-icon",{staticClass:"icon",attrs:{"is-infinite":"","data-fc-15104747b":""}})],1):t._e()]):t._e(),i("div",{staticClass:"trafic-graph"},[i("traffic-line-graph",t._b({attrs:{"data-report-id":t.M_itemReportId(t.getTrafficLineInfo.reportId),list:t.getTrafficLineInfo.list,"update-key":t.currentDirection+"_"+t.tabIndex,"data-fc-15104747c":""},on:{"scroll-report":t.scrollReport}},"traffic-line-graph",t.$props,!1))],1)])])},a=[]},344902:function(t,e,i){i.d(e,{s:function(){return n},x:function(){return a}});var n=function(){var t=this.$createElement,e=this._self._c||t;return e("svg",{ref:"svgRef",attrs:{"data-fc-164305162":"","data-fcn-index":"","data-fr-1875c65bc":""}},[e("rect",{ref:"rectRef",attrs:{"data-fc-164305160":""}}),e("text",{ref:"textRef",attrs:{"font-family":"'PingFang SC','Helvetica Neue'","font-size":this.renderFontSize,"font-weight":"700","dominant-baseline":"middle",x:"0",y:"0","data-fc-16430515e":""}},[this._v(this._s(this.text))])])},a=[]},39442:function(t,e,i){var n=i(798509);function a(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=Array(e);i<e;i++)n[i]=t[i];return n}e.Z={name:"",mixins:[n.jB,n.uW],props:{isInfinite:{type:Boolean,default:!1},duration:{type:Number,default:1e3},autoPlay:{type:Boolean,default:!0},UIAriaHiddenText:{type:String,default:""}},data:function(){return{play:!1}},created:function(){this.autoPlay&&(this.play=!0)},mounted:function(){},methods:{onTapIcon:function(){for(var t,e=this,i=arguments.length,n=Array(i),r=0;r<i;r++)n[r]=arguments[r];if(!this.play){;if(this.play=!0,this.$emit.apply(this,["tap"].concat(function(t){if(Array.isArray(t))return a(t)}(t=n)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return a(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);if("Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i)return Array.from(i);if("Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return a(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())),!this.isInfinite)return;setTimeout(function(){requestAnimationFrame(function(){e.play=!1})},this.duration)}}}}},840537:function(t,e,i){var n=i(798509),a=i(445823);e.Z={name:"FlightTrain",components:{MiniTextTag:a.Z},mixins:[n.uW,n.jB],props:{item:{type:Object,default:function(){return{}}},dynammicMaxWidth:{type:Object,default:function(){return{}}},showType:{type:Number}},data:function(){return{scaleFactor:1}},computed:{isBusTicket:function(){return 8===this.showType},isTrafficLine:function(){return 9===this.showType},classWrapper:function(){return{"traffic-line-optim":9===this.showType,"reset-font-size":9!==this.showType}}},mounted:function(){var t=this.isBusTicket?["timeInfo","stationInfo"]:["fromInfo","journeyInfo","toInfo"];this.dynamicCalcOffsetWidth(t)},methods:{dynamicCalcOffsetWidth:function(t){var e=this;t.forEach(function(t){var i=e.$refs[t];if(i){var n=i.offsetWidth;e.$emit("update-width",{refName:t,width:n+1})}})},resetFontSize:function(){var t=document.createElement("div");t.style="font-size:10px;",document.body.appendChild(t);var e=parseInt(window.getComputedStyle(t,null).getPropertyValue("font-size"));document.body.removeChild(t),this.scaleFactor=10/e}}}},298714:function(t,e,i){var n=i(798509);function a(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=Array(e);i<e;i++)n[i]=t[i];return n}i(984928);e.Z={name:"",mixins:[n.jB,n.uW,n.Sx],props:{initIndex:{type:Number,default:0},list:{type:Array,default:function(){return[]}},updateKey:{type:String,default:""}},data:function(){return{circleWidth:10,lineLength:42,shouldReportFlag:!0}},computed:{getCircleItemStyle:function(){return function(t){var e=this.circleWidth,i=this.lineLength;return{width:"".concat(e+i,"px")}}},getContainerStyle:function(){var t=this.circleWidth,e=this.lineLength,i=this.list.length;return{width:"".concat(i*(e+t),"px")}},subwayNameDecorator:function(){return function(t){if(!t)return"";var e,i=t.match(/^(\d+)(.*)$/);if(i){var n,r,o=(r=3,function(t){if(Array.isArray(t))return t}(n=i)||function(t,e){var i,n,a=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=a){var r=[],o=!0,s=!1;try{for(a=a.call(t);!(o=(i=a.next()).done)&&(r.push(i.value),!e||r.length!==e);o=!0);}catch(t){s=!0,n=t}finally{try{!o&&null!=a.return&&a.return()}finally{if(s)throw n}}return r}}(n,3)||function(t,e){if(t){if("string"==typeof t)return a(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);if("Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i)return Array.from(i);if("Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return a(t,e)}}(n,r)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),s=(o[0],o[1]),c=o[2],f="",l="";s&&(f='<span class="horizon">'.concat(s,"</span>")),c&&(l='<span class="vertical">'.concat(c,"</span>")),e=[f,l].join("")}return e}},stationNameDecorator:function(){return function(t){return t.replace(/[(]/g,"（").replace(/[)]/g,"）")}},hasAnchorFlag:function(){var t;return null===(t=this.list)||void 0===t?void 0:t.some(function(t){return t.isAnchor})}},watch:{updateKey:{handler:function(){var t=this;this.$nextTick(function(){var e=t.$el.getBoundingClientRect().width,i=t.$el.querySelector(".circle-selected");if(i){var n=i.getBoundingClientRect(),a=n.left,r=n.width,o=a+t.$el.scrollLeft-r/2-e/2;o&&t.$el.scrollTo({left:o})}})},immediate:!0}},mounted:function(){},methods:{reportScroll:function(){this.$emit("scroll-report")}}}},760078:function(t,e,i){var n=i(798509),a=i(16843),r=i(30379),o=i(66119),s=i(744812),c={swap:"swap:swap",panel:"panel:panel"};e.Z={name:"TrafficInfo",components:{Ticket:a.Z,TrafficLineGraph:r.Z,UiTransformIcon:o.Z},mixins:[n.jB,n.uW,n.Sx,s.Z],props:{data:{type:Object,default:function(){return{}}},source:{type:Object,default:function(){return{}}},item:{type:Object,default:function(){return{}}},pos:{type:Number,default:0},typePos:{type:Number,default:0},d:{type:Object,default:function(){return{}}},tabIndex:{type:Number,default:0}},data:function(){return{constReportId:c,currentDirection:0}},computed:{isOnlyOneDirection:function(){var t,e;return(null===(e=this.source)||void 0===e?void 0:null===(t=e.infos)||void 0===t?void 0:t.length)<=1},getCurrentInfo:function(){var t,e,i,n=this.currentDirection;return(null===(e=this.source)||void 0===e?void 0:null===(t=e.infos)||void 0===t?void 0:t.find(function(t){return t.direction===n}))||(null===(i=this.source)||void 0===i?void 0:i.infos[0])||{}},getTrafficCommonInfo:function(){var t;return(null===(t=this.getCurrentInfo)||void 0===t?void 0:t.trafficCommonInfo)||{}},getTrafficLineInfo:function(){var t;return(null===(t=this.getCurrentInfo)||void 0===t?void 0:t.trafficLineInfo)||{}},swapReportId:function(){return this.getTabPanelCacheReportId("swap","swap")},panelReportId:function(){return"".concat(this.constReportId.panel,":").concat(n.Zr.getRandom(6))}},mounted:function(){},methods:{scrollReport:function(){if(!this.getIsTabIndexVisited(this.getTrafficLineInfo.reportId))this.updateTabIndexItemVisited(this.getTrafficLineInfo.reportId),this.M_clickReport({},{actionType:n.At.HOR_TOUCHMOVE,reportId:this.getTrafficLineInfo.reportId})},changeDirection:function(t){this.currentDirection=t},tapTrafficMoreInfo:function(t){t&&(this.M_serviceSearchGo(t),this.M_clickReport({},t))},swap:function(){this.M_clickReport({clickContent:"swap",actionType:n.At.CLICK_TRANSFER,reportId:this.M_getReportId(!0)}),this.changeDirection(Number(!this.currentDirection)),this.$nextTick(function(){n.Gc.$emit(n.U3.exposeAnalysis)})}}}},172745:function(t,e,i){var n=i(798509);e.Z={props:{text:{type:String,default:""},fontSize:{type:Number,default:10},fontWeight:{default:700},bgColor:{type:String,default:"red"},fgColor:{type:String,default:"white"},paddingLR:{type:Number,default:5},paddingTB:{type:Number,default:2},forbidScale:{type:Boolean,default:!1}},computed:{renderFontSize:function(){return 2*Math.round(this.fontSize*(this.forbidScale?1:n.xB.fontRatio))}},watch:{text:function(){var t=this;this.$nextTick(function(){t.updateBBox()})}},mounted:function(){this.updateBBox()},methods:{updateBBox:function(){var t=this.$refs,e=t.svgRef,i=t.textRef,n=t.rectRef;if(e&&i&&n){var a=i.getBoundingClientRect(),r=Math.round(a.width),o=Math.round(a.height),s=2*this.paddingLR+r+2*this.paddingLR;s%2!=0&&(s+=s%2);var c=2*this.paddingTB+o+2*this.paddingTB;c%2!=0&&(c+=c%2),e.setAttribute("width",s/2),e.setAttribute("height",c/2),e.setAttribute("viewBox","0 0 ".concat(s," ").concat(c)),n.setAttribute("x",1),n.setAttribute("width",s-2),n.setAttribute("height",c),n.setAttribute("rx",c/2),n.setAttribute("fill",this.bgColor),i.setAttribute("x",2*this.paddingLR),i.setAttribute("y",c/2+2),i.setAttribute("fill",this.fgColor),i.setAttribute("font-weight",this.fontWeight)}}}}},744812:function(t,e,i){var n=i(798509);e.Z={data:function(){return{tabRandomIdCacheMap:{},tabReportIdCacheMap:{}}},props:{tabIndex:{type:Number,default:0}},computed:{getTabPanelCacheReportId:function(){return function(t,e){var i=this.tabIndex,a="".concat(t).concat(e).concat(i),r=this.tabRandomIdCacheMap[a];if(r)return r;var o="".concat(t,":").concat(e,":").concat(n.Zr.getRandom(6));return this.tabRandomIdCacheMap[a]=o,o}}},methods:{updateTabIndexItemVisited:function(t){var e=this.tabIndex,i="".concat(t).concat(e);this.tabReportIdCacheMap[i]=!0},getIsTabIndexVisited:function(t){var e="".concat(t).concat(this.tabIndex);return!!this.tabReportIdCacheMap[e]}}}}}]);