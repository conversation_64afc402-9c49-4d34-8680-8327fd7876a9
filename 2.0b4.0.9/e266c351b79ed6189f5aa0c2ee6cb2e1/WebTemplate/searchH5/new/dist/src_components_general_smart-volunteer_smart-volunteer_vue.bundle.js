"use strict";(self.webpackChunkweixin_search=self.webpackChunkweixin_search||[]).push([["src_components_general_smart-volunteer_smart-volunteer_vue"],{603043:function(t,e,i){i.r(e);var o=i(264058),r=i(972065),n=i(551900),a=i(358283),c=(0,n.Z)(r.Z,o.s,o.x,!1,null,"71898dfe",null);"function"==typeof a.Z&&(0,a.Z)(c),e.default=c.exports},679352:function(t,e,i){i.d(e,{Z:function(){return o}});function o(t){t.options.__wxs_id="106e8e8b"}},358283:function(t,e,i){var o=i(679352);e.Z=o.Z},972065:function(t,e,i){var o=i(516737);e.Z=o.Z},264058:function(t,e,i){i.d(e,{s:function(){return o},x:function(){return r}});var o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"smart-volunteer",attrs:{"data-fcn-smart-volunteer":"","data-fr-1586e6f76":""}},[t.itemInfo.titleInfo?i("div",{staticClass:"title-wrap",attrs:{role:"option"}},[i("span",{domProps:{innerHTML:t._s(t.xss(t.itemInfo.titleInfo.title))}}),i("div",{directives:[{name:"active",rawName:"v-active.stop",modifiers:{stop:!0}}],staticClass:"icon-wrap active__opacity",attrs:{"data-report-id":t.M_itemReportId(t.itemInfo.titleInfo.reportId),"data-cli":""},on:{click:function(e){return e.stopPropagation(),t.onTapTitleIcon(t.itemInfo.titleInfo)}}},[i("svg-icon",{staticClass:"icon-info",attrs:{name:"info","data-fc-1b55f7ee8":""}})],1)]):t._e(),t.formList&&t.formList.length?i("form",{directives:[{name:"active",rawName:"v-active.stop",modifiers:{stop:!0}}],staticClass:"form-list",attrs:{action:"#","data-cli":""},on:{submit:function(e){return e.preventDefault(),t.onTapConfirm(t.confirmButton)},click:function(t){t.stopPropagation()}}},t._l(t.formList,function(e,o){return i("div",{key:e.label,staticClass:"form-item",attrs:{role:"button","data-report-id":t.M_itemReportId(e.reportId,"0:list|"+(o+1))}},[i("p",{staticClass:"form-item-label",domProps:{innerHTML:t._s(t.xss(e.label))}}),i("div",{staticClass:"form-item-line"}),e.input?i("input",{staticClass:"weui-input",attrs:{type:"text",placeholder:e.selector.placeholder||""},domProps:{value:e.selector.value||""},on:{input:function(i){return t.onInput(i,e)},focus:function(i){return t.onFocus(i,e)}}}):e.location?i("poi",{attrs:{"default-slot":"",source:{location:e.location},"is-confirm-report":!1,"data-fc-1b55f7ee6":""},on:{"source:city-clicked":function(i){return t.onCityClicked(e)},"source:city-changed":function(i){return t.onCityChanged(i,e)}}},[i("p",{staticClass:"form-item-cont",class:{placeholder:!e.selector.value&&e.selector.placeholder}},[t._v(t._s(e.selector.value||e.selector.placeholder||""))]),e.showArrow?i("ui-arrow",{attrs:{align:"flex",gap:12,direction:"down",size:"big","data-fc-1b55f7ee4":""}}):t._e()],1):i("div",{staticClass:"form-action",on:{click:function(i){return i.stopPropagation(),t.onClickSelector(e)}}},[i("p",{staticClass:"form-item-cont",class:{placeholder:!e.selector.value&&e.selector.placeholder}},[t._v(t._s(e.selector.value||e.selector.placeholder||""))]),e.showArrow?i("ui-arrow",{attrs:{align:"flex",gap:12,direction:"down",size:"big","data-fc-1b55f7ee2":""}}):t._e()],1)],1)}),0):t._e(),t.confirmButton?i("ui-button",{staticClass:"confirm",attrs:{type:0,title:t.confirmButton.title,loading:t.btnLoading,"data-report-id":t.M_itemReportId(t.confirmButton.reportId),"data-fc-1b55f7ee0":""},nativeOn:{click:function(e){return e.stopPropagation(),t.onTapConfirm(t.confirmButton)}}}):t._e()],1)},r=[]},516737:function(t,e,i){var o=i(798509),r=i(984928),n=i(625500),a=i(787806),c=i.n(a);function s(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{},o=Object.keys(i);"function"==typeof Object.getOwnPropertySymbols&&(o=o.concat(Object.getOwnPropertySymbols(i).filter(function(t){return Object.getOwnPropertyDescriptor(i,t).enumerable}))),o.forEach(function(e){var o,r,n;o=t,r=e,n=i[e],r in o?Object.defineProperty(o,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):o[r]=n})}return t}function l(t,e){return e=null!=e?e:{},Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):(function(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);i.push.apply(i,o)}return i})(Object(e)).forEach(function(i){Object.defineProperty(t,i,Object.getOwnPropertyDescriptor(e,i))}),t}e.Z={name:"SmartVolunteer",components:{poi:n.Z},mixins:[o.jB,o.uW],props:{source:{type:Object,default:function(){return{}}},data:{type:Object,default:function(){return{}}},item:{type:Object,default:function(){return{}}},pos:{type:Number,default:0},typePos:{type:Number,default:0}},data:function(){return{validateFnList:[],btnLoading:!1,confirmButton:{},refreshParams:{}}},computed:{itemInfo:function(){return o.Zr.isObjectEmpty(this.source)?this.item:this.source},formList:function(){return this.itemInfo.formList}},watch:{"itemInfo.button":{handler:function(t){this.confirmButton=t},immediate:!0},formList:{handler:function(t){var e=this;if(t){this.validateFnList=[],this.btnLoading=!1,this.refreshParams={};var i=[{key:"location",validate:this.validateLocation},{key:"pop",validate:this.validatePop},{key:"picker",validate:this.validatePicker},{key:"input",validate:this.validateInput}];t.forEach(function(t){var o=i.findIndex(function(e){return t[e.key]});o>-1&&(e.refreshParams[i[o].key]=t.selector.value,i[o].validate(t))})}},immediate:!0}},methods:{onCityClicked:function(t){var e=t.selector.value;this.M_clickReport({clickContent:e,actionType:r.At.CLICK_CITY_CODE},t)},onCityChanged:function(t,e){var i=this;if(t&&t[0]){var n=e.selector.value,a=t[0].value;this.refreshParams.location=a,e.selector.value=a,e.location.anchor.province=a,this.M_clickReport({clickContent:a,actionType:r.At.CONFIRM_CHANGE_CODE,cardExtInfo:{fromCode:n,toCode:a}},e);var l=e.location;if(l.cgi){var u=c().loading("加载中");o.hi.getServiceCommCgiData({cgiName:l.cgi.name,data:s({data:JSON.stringify({location:a})},l.cgi.params),target:this,refreshType:"doc"}).then(function(t){if(u.hide(),t.errCode&&0!==t.errCode){i.$store.commit("updateDialog",{title:"系统繁忙，请稍后再试",cancel:!1});return}console.log("大卡刷新：",t),i.$emit("refresh:itemInfo",i.M_getItemInfo(t.refreshItem))}).catch(function(t){console.error(t),u.hide(),i.$store.commit("updateDialog",{title:"请求超时，请稍后再试",cancel:!1})})}}},validateLocation:function(t){var e=this;this.validateFnList.push(function(){return!t.location||!!e.refreshParams.location||(e.$store.commit("updateDialog",{title:t.location.emptyMessage||"请选择地址",cancel:!1,confirm:t.confirmText||"我知道了"}),!1)})},validateInput:function(t){var e=this;this.validateFnList.push(function(){if(t.input&&t.input.validate&&1===t.input.validate.type){var i=+e.refreshParams.input;if(""==e.refreshParams.input||!/\d+/.test(i))return e.$store.commit("updateDialog",{title:t.input.emptyMessage,cancel:!1,confirm:t.confirmText||"我知道了"}),!1;if(void 0!==t.input.validate.numberMax&&i>t.input.validate.numberMax||void 0!==t.input.validate.numberMin&&i<t.input.validate.numberMin)return e.$store.commit("updateDialog",{title:t.input.rangesMessage,cancel:!1,confirm:t.confirmText||"我知道了"}),!1}return!0})},validatePicker:function(t){var e=this;this.validateFnList.push(function(){return!t.picker||!!e.refreshParams.picker||(e.$store.commit("updateDialog",{title:t.picker.emptyMessage||"请选择科目",cancel:!1,confirm:t.confirmText||"我知道了"}),!1)})},validatePop:function(t){var e=this;this.validateFnList.push(function(){return!t.pop||!!e.refreshParams.pop||(e.$store.commit("updateDialog",{title:t.pop.emptyMessage||"请选择科目",cancel:!1,confirm:t.confirmText||"我知道了"}),!1)})},onInput:function(t,e){e.selector.value=t.target.value.trim(),this.refreshParams.input=t.target.value.trim()},onFocus:function(t,e){var i=t.target.value.trim();this.M_clickReport({clickContent:i,actionType:r.At.CLICK_INPUT,cardExtInfo:{content:i}},e)},onClickSelector:function(t){var e=this,i=this;t.picker?(c().picker(t.picker.showList,{id:Date.now(),title:t.picker.popTitle||"",defaultValue:[t.selector.value],container:"body",onConfirm:function(e){var o=t.selector.value;t.selector=e[0],i.refreshParams.picker=t.selector.value;var n=t.picker.showList.findIndex(function(t){return t.value===t.selector.value}),a="";n>-1&&(a=i.M_parentItemPos("".concat(n+1,":").concat(i.M_getItemType(t.selector.reportId)))),i.M_clickReport({clickContent:t.selector.value,actionType:r.At.CONFIRM_CHANGE_CODE,itemPos:a,cardExtInfo:{fromCode:o,toCode:t.selector.value}},t)}}),this.M_clickReport({clickContent:t.selector.value,actionType:r.At.CLICK_CHOOSE,cardExtInfo:{content:t.selector.value}},t)):t.pop?(t.pop.itemInfo&&(t.pop.itemInfo._selectedList=t.selector.value&&t.selector.value.split("/")),this.M_serviceSearchGo(l(s({},t),{targetCallback:function(i){var o=i.selectedList;if(o){var n=[];o.forEach(function(t){t.forEach(function(t){n.push(t.title)})}),t.selector.value=n.join("/"),e.refreshParams.pop=n.join("/"),e.M_clickReport({clickContent:t.selector.value,actionType:r.At.CONFIRM_CHANGE_CODE,cardExtInfo:{content:t.selector.value}},t)}}})),this.M_clickReport({clickContent:t.selector.value,actionType:r.At.CLICK_CHOOSE,cardExtInfo:{content:t.selector.value}},t)):t.location},onTapTitleIcon:function(t){this.M_serviceSearchGo(t),this.M_clickReport({clickContent:t.title||""},t)},onTapConfirm:function(t){var e=this;if(console.log(this.refreshParams),t){for(var i=!0,r=0;r<this.validateFnList.length;r++)if(!(i=i&&this.validateFnList[r]()))return;t.cgi?(this.btnLoading=!0,o.hi.getServiceCommCgiData({cgiName:t.cgi.name,data:s({data:JSON.stringify(this.refreshParams)},t.cgi.params),target:this,refreshType:"doc"}).then(function(t){if(e.btnLoading=!1,0!==t.errCode){e.$store.commit("updateDialog",{confirm:"确定",cancel:!1,title:t.errMsg||"请求超时，请稍后再试"});return}try{t.refreshExtData=JSON.parse(t.refreshExtData)}catch(t){console.error("refreshExtData parse err: ",t);return}console.log("点击按钮 res: ",t);var i,o=(null===(i=t.refreshExtData)||void 0===i?void 0:i.button)||{};e.confirmButton=l(s({},e.confirmButton),{jumpInfo:o.jumpInfo,reportId:o.reportId,title:o.title}),e.M_serviceSearchGo(e.confirmButton),setTimeout(function(){e.M_clickReport({clickContent:e.confirmButton.title||""},e.confirmButton)})})):console.error("onTapConfirm err: button no cgi")}}}}}}]);