"use strict";(self.webpackChunkweixin_search=self.webpackChunkweixin_search||[]).push([["vendors-node_modules_echarts_lib_chart_bar_install_js"],{244112:function(e,t,r){var n=r(518299),a=r(117065),i=r(878004),o=r(115856),s=function(e){function t(){var r=null!==e&&e.apply(this,arguments)||this;return r.type=t.type,r}return(0,n.ZT)(t,e),t.prototype.getInitialData=function(){return(0,i.Z)(null,this,{useEncodeDefaulter:!0,createInvertedIndices:!!this.get("realtimeSort",!0)||null})},t.prototype.getProgressive=function(){return!!this.get("large")&&this.get("progressive")},t.prototype.getProgressiveThreshold=function(){var e=this.get("progressiveThreshold"),t=this.get("largeThreshold");return t>e&&(e=t),e},t.prototype.brushSelector=function(e,t,r){return r.rect(t.getItemLayout(e))},t.type="series.bar",t.dependencies=["grid","polar"],t.defaultOption=(0,o.ZL)(a.Z.defaultOption,{clip:!0,roundCap:!1,showBackground:!1,backgroundStyle:{color:"rgba(180, 180, 180, 0.2)",borderColor:null,borderWidth:0,borderType:"solid",borderRadius:0,shadowBlur:0,shadowColor:null,shadowOffsetX:0,shadowOffsetY:0,opacity:1},select:{itemStyle:{borderColor:"#212121"}},realtimeSort:!1}),t}(a.Z);t.Z=s},752919:function(e,t,r){var n=r(518299),a=r(894641),i=r(707498),o=r(807028),s=r(339738),l=r(202953),d=r(406822),u=r(171093),h=r(276868),c=r(623815),g=r(931918),p=r(71564),f=r(434337),y=r(159056),v=r(804575),m=r(567622),A=r(910904),b=r(10078),_=r(556798),x=Math.max,I=Math.min,w=function(e){function t(){var r=e.call(this)||this;return r.type=t.type,r._isFirstFrame=!0,r}return(0,n.ZT)(t,e),t.prototype.render=function(e,t,r,n){this._model=e,this._removeOnRenderedListener(r),this._updateDrawMode(e);var a=e.get("coordinateSystem");("cartesian2d"===a||"polar"===a)&&(this._progressiveEls=null,this._isLargeDraw?this._renderLarge(e,t,r):this._renderNormal(e,t,r,n))},t.prototype.incrementalPrepareRender=function(e){this._clear(),this._updateDrawMode(e),this._updateLargeClip(e)},t.prototype.incrementalRender=function(e,t){this._progressiveEls=[],this._incrementalRenderLarge(e,t)},t.prototype.eachRendered=function(e){(0,s.traverseElements)(this._progressiveEls||this.group,e)},t.prototype._updateDrawMode=function(e){var t=e.pipelineContext.large;(null==this._isLargeDraw||t!==this._isLargeDraw)&&(this._isLargeDraw=t,this._clear())},t.prototype._renderNormal=function(e,t,r,n){var a,o=this.group,s=e.getData(),h=this._data,c=e.coordinateSystem,p=c.getBaseAxis();"cartesian2d"===c.type?a=p.isHorizontal():"polar"===c.type&&(a="angle"===p.dim);var f=e.isAnimationEnabled()?e:null,y=function(e,t){var r=e.get("realtimeSort",!0),n=t.getBaseAxis();if(r&&"category"===n.type&&"cartesian2d"===t.type)return{baseAxis:n,otherAxis:t.getOtherAxis(n)}}(e,c);y&&this._enableRealtimeSort(y,s,r);var v=e.get("clip",!0)||y,A=function(e,t){var r=e.getArea&&e.getArea();if((0,m.H)(e,"cartesian2d")){var n=e.getBaseAxis();if("category"!==n.type||!n.onBand){var a=t.getLayout("bandWidth");n.isHorizontal()?(r.x-=a,r.width+=2*a):(r.y-=a,r.height+=2*a)}}return r}(c,s);o.removeClipPath();var b=e.get("roundCap",!0),_=e.get("showBackground",!0),x=e.getModel("backgroundStyle"),I=x.get("borderRadius")||0,w=[],D=this._backgroundEls,L=n&&n.isInitSort,R=n&&"changeAxisOrder"===n.type;function T(e){var t=O[c.type](s,e),r=function(e,t,r){return new("polar"===e.type?u.C:d.Z)({shape:W(t,r,e),silent:!0,z2:0})}(c,a,t);return r.useStyle(x.getItemStyle()),"cartesian2d"===c.type?r.setShape("r",I):r.setShape("cornerRadius",I),w[e]=r,r}s.diff(h).add(function(t){var r=s.getItemModel(t),n=O[c.type](s,t,r);if(_&&T(t),!!s.hasValue(t)&&!!C[c.type](n)){var i=!1;v&&(i=S[c.type](A,n));var d=k[c.type](e,s,t,n,a,f,p.model,!1,b);y&&(d.forceLabelAnimation=!0),P(d,s,t,r,n,e,a,"polar"===c.type),L?d.attr({shape:n}):y?M(y,f,d,n,t,a,!1,!1):(0,l.KZ)(d,{shape:n},e,t),s.setItemGraphicEl(t,d),o.add(d),d.ignore=i}}).update(function(t,r){var n=s.getItemModel(t),i=O[c.type](s,t,n);if(_){var d=void 0;0===D.length?d=T(r):((d=D[r]).useStyle(x.getItemStyle()),"cartesian2d"===c.type?d.setShape("r",I):d.setShape("cornerRadius",I),w[t]=d);var u=O[c.type](s,t),m=W(a,u,c);(0,l.D)(d,{shape:m},f,t)}var Z=h.getItemGraphicEl(r);if(!s.hasValue(t)||!C[c.type](i)){o.remove(Z);return}var E=!1;if(v&&(E=S[c.type](A,i))&&o.remove(Z),Z?(0,l.Zi)(Z):Z=k[c.type](e,s,t,i,a,f,p.model,!!Z,b),y&&(Z.forceLabelAnimation=!0),R){var N=Z.getTextContent();if(N){var V=(0,g.qA)(N);null!=V.prevValue&&(V.prevValue=V.value)}}else P(Z,s,t,n,i,e,a,"polar"===c.type);L?Z.attr({shape:i}):y?M(y,f,Z,i,t,a,!0,R):(0,l.D)(Z,{shape:i},e,t,null),s.setItemGraphicEl(t,Z),Z.ignore=E,o.add(Z)}).remove(function(t){var r=h.getItemGraphicEl(t);r&&(0,l.XD)(r,e,t)}).execute();var Z=this._backgroundGroup||(this._backgroundGroup=new i.Z);Z.removeAll();for(var E=0;E<w.length;++E)Z.add(w[E]);o.add(Z),this._backgroundEls=w,this._data=s},t.prototype._renderLarge=function(e,t,r){this._clear(),N(e,this.group),this._updateLargeClip(e)},t.prototype._incrementalRenderLarge=function(e,t){this._removeBackground(),N(t,this.group,this._progressiveEls,!0)},t.prototype._updateLargeClip=function(e){var t=e.get("clip",!0)&&(0,f.lQ)(e.coordinateSystem,!1,e),r=this.group;t?r.setClipPath(t):r.removeClipPath()},t.prototype._enableRealtimeSort=function(e,t,r){var n=this;if(!!t.count()){var a=e.baseAxis;if(this._isFirstFrame)this._dispatchInitSort(t,e,r),this._isFirstFrame=!1;else{var i=function(e){var r=t.getItemGraphicEl(e),n=r&&r.shape;return n&&Math.abs(a.isHorizontal()?n.height:n.width)||0};this._onRendered=function(){n._updateSortWithinSameData(t,i,a,r)},r.getZr().on("rendered",this._onRendered)}}},t.prototype._dataSort=function(e,t,r){var n=[];return e.each(e.mapDimension(t.dim),function(e,t){var a=r(t);a=null==a?NaN:a,n.push({dataIndex:t,mappedValue:a,ordinalNumber:e})}),n.sort(function(e,t){return t.mappedValue-e.mappedValue}),{ordinalNumbers:(0,o.UI)(n,function(e){return e.ordinalNumber})}},t.prototype._isOrderChangedWithinSameData=function(e,t,r){for(var n=r.scale,a=e.mapDimension(r.dim),i=Number.MAX_VALUE,o=0,s=n.getOrdinalMeta().categories.length;o<s;++o){var l=e.rawIndexOf(a,n.getRawOrdinalNumber(o)),d=l<0?Number.MIN_VALUE:t(e.indexOfRawIndex(l));if(d>i)return!0;i=d}return!1},t.prototype._isOrderDifferentInView=function(e,t){for(var r=t.scale,n=r.getExtent(),a=Math.max(0,n[0]),i=Math.min(n[1],r.getOrdinalMeta().categories.length-1);a<=i;++a)if(e.ordinalNumbers[a]!==r.getRawOrdinalNumber(a))return!0},t.prototype._updateSortWithinSameData=function(e,t,r,n){if(!!this._isOrderChangedWithinSameData(e,t,r)){var a=this._dataSort(e,r,t);this._isOrderDifferentInView(a,r)&&(this._removeOnRenderedListener(n),n.dispatchAction({type:"changeAxisOrder",componentType:r.dim+"Axis",axisId:r.index,sortInfo:a}))}},t.prototype._dispatchInitSort=function(e,t,r){var n=t.baseAxis,a=this._dataSort(e,n,function(r){return e.get(e.mapDimension(t.otherAxis.dim),r)});r.dispatchAction({type:"changeAxisOrder",componentType:n.dim+"Axis",isInitSort:!0,axisId:n.index,sortInfo:a})},t.prototype.remove=function(e,t){this._clear(this._model),this._removeOnRenderedListener(t)},t.prototype.dispose=function(e,t){this._removeOnRenderedListener(t)},t.prototype._removeOnRenderedListener=function(e){this._onRendered&&(e.getZr().off("rendered",this._onRendered),this._onRendered=null)},t.prototype._clear=function(e){var t=this.group,r=this._data;e&&e.isAnimationEnabled()&&r&&!this._isLargeDraw?(this._removeBackground(),this._backgroundEls=[],r.eachItemGraphicEl(function(t){(0,l.XD)(t,e,(0,h.A)(t).dataIndex)})):t.removeAll(),this._data=null,this._isFirstFrame=!0},t.prototype._removeBackground=function(){this.group.remove(this._backgroundGroup),this._backgroundGroup=null},t.type="bar",t}(v.Z),S={cartesian2d:function(e,t){var r=t.width<0?-1:1,n=t.height<0?-1:1;r<0&&(t.x+=t.width,t.width=-t.width),n<0&&(t.y+=t.height,t.height=-t.height);var a=e.x+e.width,i=e.y+e.height,o=x(t.x,e.x),s=I(t.x+t.width,a),l=x(t.y,e.y),d=I(t.y+t.height,i),u=s<o,h=d<l;return t.x=u&&o>a?s:o,t.y=h&&l>i?d:l,t.width=u?0:s-o,t.height=h?0:d-l,r<0&&(t.x+=t.width,t.width=-t.width),n<0&&(t.y+=t.height,t.height=-t.height),u||h},polar:function(e,t){var r=t.r0<=t.r?1:-1;if(r<0){var n=t.r;t.r=t.r0,t.r0=n}var a=I(t.r,e.r),i=x(t.r0,e.r0);t.r=a,t.r0=i;var o=a-i<0;if(r<0){var n=t.r;t.r=t.r0,t.r0=n}return o}},k={cartesian2d:function(e,t,r,n,a,i,s,l,u){var h=new d.Z({shape:(0,o.l7)({},n),z2:1});if(h.__dataIndex=r,h.name="item",i){var c=h.shape;c[a?"height":"width"]=0}return h},polar:function(e,t,r,n,a,i,o,s,d){var h=!a&&d?y.Z:u.C,c=new h({shape:n,z2:1});c.name="item";var g=T(a);if(c.calculateTextPosition=(0,b.R)(g,{isRoundCap:h===y.Z}),i){var p=c.shape,f=a?"r":"endAngle",v={};p[f]=a?n.r0:n.startAngle,v[f]=n[f],(s?l.D:l.KZ)(c,{shape:v},i)}return c}};function M(e,t,r,n,a,i,o,s){i?(u={x:n.x,width:n.width},d={y:n.y,height:n.height}):(u={y:n.y,height:n.height},d={x:n.x,width:n.width}),!s&&(o?l.D:l.KZ)(r,{shape:d},t,a,null);var d,u,h=t?e.baseAxis.model:null;(o?l.D:l.KZ)(r,{shape:u},h,a)}function D(e,t){for(var r=0;r<t.length;r++)if(!isFinite(e[t[r]]))return!0;return!1}var L=["x","y","width","height"],R=["cx","cy","r","startAngle","endAngle"],C={cartesian2d:function(e){return!D(e,L)},polar:function(e){return!D(e,R)}},O={cartesian2d:function(e,t,r){var n=e.getItemLayout(t),a=r?function(e,t){var r=e.get(["itemStyle","borderColor"]);return r&&"none"!==r?Math.min(e.get(["itemStyle","borderWidth"])||0,isNaN(t.width)?Number.MAX_VALUE:Math.abs(t.width),isNaN(t.height)?Number.MAX_VALUE:Math.abs(t.height)):0}(r,n):0,i=n.width>0?1:-1,o=n.height>0?1:-1;return{x:n.x+i*a/2,y:n.y+o*a/2,width:n.width-i*a,height:n.height-o*a}},polar:function(e,t,r){var n=e.getItemLayout(t);return{cx:n.cx,cy:n.cy,r0:n.r0,r:n.r,startAngle:n.startAngle,endAngle:n.endAngle,clockwise:n.clockwise}}};function T(e){var t;return t=e?"Arc":"Angle",function(e){switch(e){case"start":case"insideStart":case"end":case"insideEnd":return e+t;default:return e}}}function P(e,t,r,n,a,i,s,l){var d,u=t.getItemVisual(r,"style");if(l){if(!i.get("roundCap")){var h=e.shape,p=(0,_.T)(n.getModel("itemStyle"),h,!0);(0,o.l7)(h,p),e.setShape(h)}}else{var f=n.get(["itemStyle","borderRadius"])||0;e.setShape("r",f)}e.useStyle(u);var y=n.getShallow("cursor");y&&e.attr("cursor",y);var v=l?s?a.r>=a.r0?"endArc":"startArc":a.endAngle>=a.startAngle?"endAngle":"startAngle":s?a.height>=0?"bottom":"top":a.width>=0?"right":"left",m=(0,g.k3)(n);(0,g.ni)(e,m,{labelFetcher:i,labelDataIndex:r,defaultText:(0,A.H)(i.getData(),r),inheritColor:u.fill,defaultOpacity:u.opacity,defaultOutsidePosition:v});var x=e.getTextContent();if(l&&x){var I=n.get(["label","position"]);e.textConfig.inside="middle"===I||null,(0,b.W)(e,"outside"===I?v:I,T(s),n.get(["label","rotate"]))}(0,g.pe)(x,m,i.getRawValue(r),function(e){return(0,A.O)(t,e)});var w=n.getModel(["emphasis"]);if((0,c.k5)(e,w.get("focus"),w.get("blurScope"),w.get("disabled")),(0,c.WO)(e,n),null!=(d=a).startAngle&&null!=d.endAngle&&d.startAngle===d.endAngle)e.style.fill="none",e.style.stroke="none",(0,o.S6)(e.states,function(e){e.style&&(e.style.fill=e.style.stroke="none")})}var Z=function(){},E=function(e){function t(t){var r=e.call(this,t)||this;return r.type="largeBar",r}return(0,n.ZT)(t,e),t.prototype.getDefaultShape=function(){return new Z},t.prototype.buildPath=function(e,t){for(var r=t.points,n=this.baseDimIdx,a=1-this.baseDimIdx,i=[],o=[],s=this.barWidth,l=0;l<r.length;l+=3)o[n]=s,o[a]=r[l+2],i[n]=r[l+n],i[a]=r[l+a],e.rect(i[0],i[1],o[0],o[1])},t}(a.ZP);function N(e,t,r,n){var a=e.getData(),i=a.getLayout("valueAxisHorizontal")?1:0,o=a.getLayout("largeDataIndices"),s=a.getLayout("size"),l=e.getModel("backgroundStyle"),d=a.getLayout("largeBackgroundPoints");if(d){var u=new E({shape:{points:d},incremental:!!n,silent:!0,z2:0});u.baseDimIdx=i,u.largeDataIndices=o,u.barWidth=s,u.useStyle(l.getItemStyle()),t.add(u),r&&r.push(u)}var c=new E({shape:{points:a.getLayout("largePoints")},incremental:!!n,ignoreCoarsePointer:!0,z2:1});c.baseDimIdx=i,c.largeDataIndices=o,c.barWidth=s,t.add(c),c.useStyle(a.getVisual("style")),(0,h.A)(c).seriesIndex=e.seriesIndex,!e.get("silent")&&(c.on("mousedown",V),c.on("mousemove",V)),r&&r.push(c)}var V=(0,p.P2)(function(e){var t=function(e,t,r){for(var n=e.baseDimIdx,a=1-n,i=e.shape.points,o=e.largeDataIndices,s=[],l=[],d=e.barWidth,u=0,h=i.length/3;u<h;u++){var c=3*u;if(l[n]=d,l[a]=i[c+2],s[n]=i[c+n],s[a]=i[c+a],l[a]<0&&(s[a]+=l[a],l[a]=-l[a]),t>=s[0]&&t<=s[0]+l[0]&&r>=s[1]&&r<=s[1]+l[1])return o[u]}return -1}(this,e.offsetX,e.offsetY);(0,h.A)(this).dataIndex=t>=0?t:null},30,!1);function W(e,t,r){if((0,m.H)(r,"cartesian2d")){var n=r.getArea();return{x:e?t.x:n.x,y:e?n.y:t.y,width:e?t.width:n.width,height:e?n.height:t.height}}var n=r.getArea();return{cx:n.cx,cy:n.cy,r0:e?n.r0:t.r0,r:e?n.r:t.r,startAngle:e?t.startAngle:0,endAngle:e?t.endAngle:2*Math.PI}}t.Z=w},117065:function(e,t,r){var n=r(518299),a=r(970597),i=r(878004),o=r(807028),s=function(e){function t(){var r=null!==e&&e.apply(this,arguments)||this;return r.type=t.type,r}return(0,n.ZT)(t,e),t.prototype.getInitialData=function(e,t){return(0,i.Z)(null,this,{useEncodeDefaulter:!0})},t.prototype.getMarkerPosition=function(e,t,r){var n=this.coordinateSystem;if(n&&n.clampData){var a=n.clampData(e),i=n.dataToPoint(a);if(r)(0,o.S6)(n.getAxes(),function(e,r){if("category"===e.type&&null!=t){var n=e.getTicksCoords(),o=e.getTickModel().get("alignWithLabel"),s=a[r],l="x1"===t[r]||"y1"===t[r];if(l&&!o&&(s+=1),!(n.length<2)){if(2===n.length){i[r]=e.toGlobalCoord(e.getExtent()[l?1:0]);return}for(var d=void 0,u=void 0,h=1,c=0;c<n.length;c++){var g=n[c].coord,p=c===n.length-1?n[c-1].tickValue+h:n[c].tickValue;if(p===s){u=g;break}if(p<s)d=g;else if(null!=d&&p>s){u=(g+d)/2;break}1===c&&(h=p-n[0].tickValue)}null==u&&(d?d&&(u=n[n.length-1].coord):u=n[0].coord),i[r]=e.toGlobalCoord(u)}}});else{var s=this.getData(),l=s.getLayout("offset"),d=s.getLayout("size"),u=n.getBaseAxis().isHorizontal()?0:1;i[u]+=l+d/2}return i}return[NaN,NaN]},t.type="series.__base_bar__",t.defaultOption={z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,barMinHeight:0,barMinAngle:0,large:!1,largeThreshold:400,progressive:3e3,progressiveChunkMode:"mod"},t}(a.Z);a.Z.registerClass(s),t.Z=s},220298:function(e,t,r){r.d(t,{N:function(){return l}});var n=r(807028),a=r(30924),i=r(951092),o=r(244112),s=r(752919);function l(e){e.registerChartView(s.Z),e.registerSeriesModel(o.Z),e.registerLayout(e.PRIORITY.VISUAL.LAYOUT,n.WA(a.bK,"bar")),e.registerLayout(e.PRIORITY.VISUAL.PROGRESSIVE_LAYOUT,(0,a.Bk)("bar")),e.registerProcessor(e.PRIORITY.PROCESSOR.STATISTIC,(0,i.Z)("bar")),e.registerAction({type:"changeAxisOrder",event:"changeAxisOrder",update:"update"},function(e,t){var r=e.componentType||"series";t.eachComponent({mainType:r,query:e},function(t){e.sortInfo&&t.axis.setCategorySortInfo(e.sortInfo)})})}},556798:function(e,t,r){r.d(t,{T:function(){return i}});var n=r(807028),a=r(310123);function i(e,t,r){var i=e.get("borderRadius");if(null==i)return r?{cornerRadius:0}:null;!(0,n.kJ)(i)&&(i=[i,i,i,i]);var o=Math.abs(t.r||0-t.r0||0);return{cornerRadius:(0,n.UI)(i,function(e){return(0,a.GM)(e,o)})}}},10078:function(e,t,r){r.d(t,{R:function(){return i},W:function(){return o}});var n=r(310123),a=r(807028);function i(e,t){var r=(t=t||{}).isRoundCap;return function(t,a,i){var o=a.position;if(!o||o instanceof Array)return(0,n.wI)(t,a,i);var d=e(o),u=null!=a.distance?a.distance:5,h=this.shape,c=h.cx,g=h.cy,p=h.r,f=h.r0,y=(p+f)/2,v=h.startAngle,m=h.endAngle,A=(v+m)/2,b=r?Math.abs(p-f)/2:0,_=Math.cos,x=Math.sin,I=c+p*_(v),w=g+p*x(v),S="left",k="top";switch(d){case"startArc":I=c+(f-u)*_(A),w=g+(f-u)*x(A),S="center",k="top";break;case"insideStartArc":I=c+(f+u)*_(A),w=g+(f+u)*x(A),S="center",k="bottom";break;case"startAngle":I=c+y*_(v)+s(v,u+b,!1),w=g+y*x(v)+l(v,u+b,!1),S="right",k="middle";break;case"insideStartAngle":I=c+y*_(v)+s(v,-u+b,!1),w=g+y*x(v)+l(v,-u+b,!1),S="left",k="middle";break;case"middle":I=c+y*_(A),w=g+y*x(A),S="center",k="middle";break;case"endArc":I=c+(p+u)*_(A),w=g+(p+u)*x(A),S="center",k="bottom";break;case"insideEndArc":I=c+(p-u)*_(A),w=g+(p-u)*x(A),S="center",k="top";break;case"endAngle":I=c+y*_(m)+s(m,u+b,!0),w=g+y*x(m)+l(m,u+b,!0),S="left",k="middle";break;case"insideEndAngle":I=c+y*_(m)+s(m,-u+b,!0),w=g+y*x(m)+l(m,-u+b,!0),S="right",k="middle";break;default:return(0,n.wI)(t,a,i)}return(t=t||{}).x=I,t.y=w,t.align=S,t.verticalAlign=k,t}}function o(e,t,r,n){if((0,a.hj)(n)){e.setTextConfig({rotation:n});return}if((0,a.kJ)(t)){e.setTextConfig({rotation:0});return}var i,o=e.shape,s=o.clockwise?o.startAngle:o.endAngle,l=o.clockwise?o.endAngle:o.startAngle,d=r(t);switch(d){case"startArc":case"insideStartArc":case"middle":case"insideEndArc":case"endArc":i=(s+l)/2;break;case"startAngle":case"insideStartAngle":i=s;break;case"endAngle":case"insideEndAngle":i=l;break;default:e.setTextConfig({rotation:0});return}var u=1.5*Math.PI-i;"middle"===d&&u>Math.PI/2&&u<1.5*Math.PI&&(u-=Math.PI),e.setTextConfig({rotation:u})}function s(e,t,r){return t*Math.sin(e)*(r?-1:1)}function l(e,t,r){return t*Math.cos(e)*(r?1:-1)}},159056:function(e,t,r){var n=r(518299),a=r(894641),i=function(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0},o=function(e){function t(t){var r=e.call(this,t)||this;return r.type="sausage",r}return(0,n.ZT)(t,e),t.prototype.getDefaultShape=function(){return new i},t.prototype.buildPath=function(e,t){var r=t.cx,n=t.cy,a=Math.max(t.r0||0,0),i=Math.max(t.r,0),o=(i-a)*.5,s=a+o,l=t.startAngle,d=t.endAngle,u=t.clockwise,h=2*Math.PI,c=u?d-l<h:l-d<h;!c&&(l=d-(u?h:-h));var g=Math.cos(l),p=Math.sin(l),f=Math.cos(d),y=Math.sin(d);c?(e.moveTo(g*a+r,p*a+n),e.arc(g*s+r,p*s+n,o,-Math.PI+l,l,!u)):e.moveTo(g*i+r,p*i+n),e.arc(r,n,i,l,d,!u),e.arc(f*s+r,y*s+n,o,d-2*Math.PI,d-Math.PI,!u),0!==a&&e.arc(r,n,a,d,l,u)},t}(a.ZP);t.Z=o}}]);