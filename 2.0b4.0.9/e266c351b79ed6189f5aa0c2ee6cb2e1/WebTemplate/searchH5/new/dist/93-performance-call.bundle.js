"use strict";(self.webpackChunkweixin_search=self.webpackChunkweixin_search||[]).push([["93-performance-call"],{236363:function(t,n,e){e.r(n);var a=e(67286),i=e(557486),s=e(551900),r=e(857106),u=(0,s.Z)(i.Z,a.s,a.x,!1,null,"2f649c6b",null);"function"==typeof r.Z&&(0,r.Z)(u),n.default=u.exports},335374:function(t,n,e){e.d(n,{Z:function(){return a}});function a(t){t.options.__wxs_id="9714f097"}},857106:function(t,n,e){var a=e(335374);n.Z=a.Z},557486:function(t,n,e){var a=e(134469);n.Z=a.Z},67286:function(t,n,e){e.d(n,{s:function(){return a},x:function(){return i}});var a=function(){var t=this,n=t.$createElement,e=t._self._c||n;return e("div",{staticClass:"performance-call",attrs:{"data-fcn-performance-call":"","data-fr-1766df4b0":""}},[1==t.list.length&&t.list[0]&&t.list[0].status!==t.StatusEnum.Ended?t._l(t.list,function(n,a){return e("li",{directives:[{name:"active",rawName:"v-active.stop",modifiers:{stop:!0}}],key:a,staticClass:"call-info active__mask",attrs:{"data-report-id":t.M_itemReportId(n,a+1),"data-cli":""},on:{click:function(e){return e.stopPropagation(),t.onTap(n)}}},[n.title?e("span",{staticClass:"title"},[t._v(t._s(n.title))]):t._e(),n.date&&n.status==t.StatusEnum.WaitingStart?e("span",{staticClass:"date"},[t._v(t._s(n.date))]):t._e(),n.status?e("span",{staticClass:"status",staticStyle:{"flex-shrink":"0"}},[t._v(t._s(t.getStatusText(n.status)))]):t._e()])}):e("ui-column",{class:{"only-one":1==t.list.length},attrs:{col:t.list.length>1?2:1,list:t.list,fill:"","data-fc-15750a181":""},scopedSlots:t._u([{key:"default",fn:function(n){var a=n.item,i=n.index;return[e("ui-link",{attrs:{title:a.title,"data-report-id":t.M_itemReportId(a,i+1),"data-fc-15750a182":""},nativeOn:{click:function(n){return n.stopPropagation(),t.onTap(a)}}})]}}])})],2)},i=[]},134469:function(t,n,e){var a=e(798509),i=e(116746),s=e(462474),r=e(728223),u=e(984928);function c(t,n,e){return n in t?Object.defineProperty(t,n,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[n]=e,t}var o=new i.J({WaitingStart:1,OnLiving:2,Ended:3});n.Z={name:"PerformanceCall",mixins:[a.jB,a.uW],props:{info:{type:Object,default:function(){return{}}},data:{type:Object,default:function(){return{}}},item:{type:Object,default:function(){return{}}},pos:{type:Number,default:0},typePos:{type:Number,default:0}},data:function(){return{StatusEnum:o}},computed:{itemInfo:function(){return this.info},list:function(){var t=this.itemInfo,n=t.list,e=t.title,a=t.date,i=t.jumpInfo,s=t.status;return n||[{title:e,date:a,jumpInfo:i,status:s}]}},methods:{onTap:function(t){var n=t.status===o.WaitingStart;n?s.Z.$emit(s.U.showToast,{text:"暂未开始",action:r.g.pure}):this.M_serviceSearchGo(t),this.M_clickReport({clickContent:t.title,actionType:n?u.At.CLICK_SHOW_DIALOG:null},t)},getStatusText:function(t){var n;return(c(n={},o.WaitingStart,"未开始"),c(n,o.OnLiving,"直播中"),c(n,o.Ended,"回放"),n)[t]||""}}}}}]);