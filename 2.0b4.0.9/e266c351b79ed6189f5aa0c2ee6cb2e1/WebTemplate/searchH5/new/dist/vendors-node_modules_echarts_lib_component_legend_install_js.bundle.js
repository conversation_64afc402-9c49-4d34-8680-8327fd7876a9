"use strict";(self.webpackChunkweixin_search=self.webpackChunkweixin_search||[]).push([["vendors-node_modules_echarts_lib_component_legend_install_js"],{170024:function(e,t,n){n.d(t,{l:function(){return r}});var i=n(84164),o=n(406822);function r(e,t){var n=i.MY(t.get("padding")),r=t.getItemStyle(["color","opacity"]);return r.fill=t.get("backgroundColor"),e=new o.Z({shape:{x:e.x-n[3],y:e.y-n[0],width:e.width+n[1]+n[3],height:e.height+n[0]+n[2],r:t.get("borderRadius")},style:r,silent:!0,z2:-1})}},231405:function(e,t,n){var i=n(518299),o=n(807028),r=n(954069),l=n(133141),a=n(882425),s=function(e){function t(){var n=null!==e&&e.apply(this,arguments)||this;return n.type=t.type,n.layoutMode={type:"box",ignoreSize:!0},n}return(0,i.ZT)(t,e),t.prototype.init=function(e,t,n){this.mergeDefaultAndTheme(e,n),e.selected=e.selected||{},this._updateSelector(e)},t.prototype.mergeOption=function(t,n){e.prototype.mergeOption.call(this,t,n),this._updateSelector(t)},t.prototype._updateSelector=function(e){var t=e.selector,n=this.ecModel;!0===t&&(t=e.selector=["all","inverse"]),o.kJ(t)&&o.S6(t,function(e,i){var r,l;o.HD(e)&&(e={type:e}),t[i]=o.TS(e,(r=n,"all"===(l=e.type)?{type:"all",title:r.getLocaleModel().get(["legend","selector","all"])}:"inverse"===l?{type:"inverse",title:r.getLocaleModel().get(["legend","selector","inverse"])}:void 0))})},t.prototype.optionUpdated=function(){this._updateData(this.ecModel);var e=this._data;if(e[0]&&"single"===this.get("selectedMode")){for(var t=!1,n=0;n<e.length;n++){var i=e[n].get("name");if(this.isSelected(i)){this.select(i),t=!0;break}}t||this.select(e[0].get("name"))}},t.prototype._updateData=function(e){var t=[],n=[];e.eachRawSeries(function(i){var o,r=i.name;if(n.push(r),i.legendVisualProvider){var a=i.legendVisualProvider.getAllNames();!e.isSeriesFiltered(i)&&(n=n.concat(a)),a.length?t=t.concat(a):o=!0}else o=!0;o&&(0,l.yu)(i)&&t.push(i.name)}),this._availableNames=n;var i=this.get("data")||t,a=o.kW(),s=o.UI(i,function(e){return((o.HD(e)||o.hj(e))&&(e={name:e}),a.get(e.name))?null:(a.set(e.name,!0),new r.Z(e,this,this.ecModel))},this);this._data=o.hX(s,function(e){return!!e})},t.prototype.getData=function(){return this._data},t.prototype.select=function(e){var t=this.option.selected;if("single"===this.get("selectedMode")){var n=this._data;o.S6(n,function(e){t[e.get("name")]=!1})}t[e]=!0},t.prototype.unSelect=function(e){"single"!==this.get("selectedMode")&&(this.option.selected[e]=!1)},t.prototype.toggleSelected=function(e){var t=this.option.selected;!t.hasOwnProperty(e)&&(t[e]=!0),this[t[e]?"unSelect":"select"](e)},t.prototype.allSelect=function(){var e=this._data,t=this.option.selected;o.S6(e,function(e){t[e.get("name",!0)]=!0})},t.prototype.inverseSelect=function(){var e=this._data,t=this.option.selected;o.S6(e,function(e){var n=e.get("name",!0);!t.hasOwnProperty(n)&&(t[n]=!0),t[n]=!t[n]})},t.prototype.isSelected=function(e){var t=this.option.selected;return!(t.hasOwnProperty(e)&&!t[e])&&o.cq(this._availableNames,e)>=0},t.prototype.getOrient=function(){return"vertical"===this.get("orient")?{index:1,name:"vertical"}:{index:0,name:"horizontal"}},t.type="legend.plain",t.dependencies=["series"],t.defaultOption={z:4,show:!0,orient:"horizontal",left:"center",top:0,align:"auto",backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderRadius:0,borderWidth:0,padding:5,itemGap:10,itemWidth:25,itemHeight:14,symbolRotate:"inherit",symbolKeepAspect:!0,inactiveColor:"#ccc",inactiveBorderColor:"#ccc",inactiveBorderWidth:"auto",itemStyle:{color:"inherit",opacity:"inherit",borderColor:"inherit",borderWidth:"auto",borderCap:"inherit",borderJoin:"inherit",borderDashOffset:"inherit",borderMiterLimit:"inherit"},lineStyle:{width:"auto",color:"inherit",inactiveColor:"#ccc",inactiveWidth:2,opacity:"inherit",type:"inherit",cap:"inherit",join:"inherit",dashOffset:"inherit",miterLimit:"inherit"},textStyle:{color:"#333"},selectedMode:!0,selector:!1,selectorLabel:{show:!0,borderRadius:10,padding:[3,5,3,5],fontSize:12,fontFamily:"sans-serif",color:"#666",borderWidth:1,borderColor:"#666"},emphasis:{selectorLabel:{show:!0,color:"#eee",backgroundColor:"#666"}},selectorPosition:"auto",selectorItemGap:7,selectorButtonGap:10,tooltip:{show:!1}},t}(a.Z);t.Z=s},831543:function(e,t,n){var i=n(518299),o=n(807028),r=n(529134),l=n(707498),a=n(345262),s=n(406822),c=n(339738),d=n(623815),u=n(931918),g=n(170024),p=n(918712),h=n(860736),f=n(19750),y=n(973724),v=n(276868),m=o.WA,S=o.S6,x=l.Z,I=function(e){function t(){var n=null!==e&&e.apply(this,arguments)||this;return n.type=t.type,n.newlineDisabled=!1,n}return(0,i.ZT)(t,e),t.prototype.init=function(){this.group.add(this._contentGroup=new x),this.group.add(this._selectorGroup=new x),this._isFirstRender=!0},t.prototype.getContentGroup=function(){return this._contentGroup},t.prototype.getSelectorGroup=function(){return this._selectorGroup},t.prototype.render=function(e,t,n){var i=this._isFirstRender;if(this._isFirstRender=!1,this.resetInner(),!!e.get("show",!0)){var r=e.get("align"),l=e.get("orient");(!r||"auto"===r)&&(r="right"===e.get("left")&&"vertical"===l?"right":"left");var a=e.get("selector",!0),s=e.get("selectorPosition",!0);a&&(!s||"auto"===s)&&(s="horizontal"===l?"end":"start"),this.renderInner(r,e,t,n,a,l,s);var c=e.getBoxLayoutParams(),d={width:n.getWidth(),height:n.getHeight()},u=e.get("padding"),h=p.ME(c,d,u),f=this.layoutInner(e,r,h,i,a,s),y=p.ME(o.ce({width:f.width,height:f.height},c),d,u);this.group.x=y.x-f.x,this.group.y=y.y-f.y,this.group.markRedraw(),this.group.add(this._backgroundEl=(0,g.l)(f,e))}},t.prototype.resetInner=function(){this.getContentGroup().removeAll(),this._backgroundEl&&this.group.remove(this._backgroundEl),this.getSelectorGroup().removeAll()},t.prototype.renderInner=function(e,t,n,i,l,a,s){var c=this.getContentGroup(),d=o.kW(),u=t.get("selectedMode"),g=[];n.eachRawSeries(function(e){e.get("legendHoverLink")||g.push(e.id)}),S(t.getData(),function(l,a){var s=l.get("name");if(!this.newlineDisabled&&(""===s||"\n"===s)){var p=new x;p.newline=!0,c.add(p);return}var h=n.getSeriesByName(s)[0];if(!d.get(s))if(h){var f=h.getData(),y=f.getVisual("legendLineStyle")||{},S=f.getVisual("legendIcon"),I=f.getVisual("style"),w=this._createItem(h,s,a,l,t,e,y,I,S,u,i);w.on("click",m(_,s,null,i,g)).on("mouseover",m(C,h.name,null,i,g)).on("mouseout",m(b,h.name,null,i,g)),n.ssr&&w.eachChild(function(e){var t=(0,v.A)(e);t.seriesIndex=h.seriesIndex,t.dataIndex=a,t.ssrType="legend"}),d.set(s,!0)}else n.eachRawSeries(function(c){if(!d.get(s)){if(c.legendVisualProvider){var p=c.legendVisualProvider;if(!p.containName(s))return;var h=p.indexOfName(s),f=p.getItemVisual(h,"style"),y=p.getItemVisual(h,"legendIcon"),S=(0,r.Qc)(f.fill);S&&0===S[3]&&(S[3]=.2,f=o.l7(o.l7({},f),{fill:(0,r.Pz)(S,"rgba")}));var x=this._createItem(c,s,a,l,t,e,{},f,y,u,i);x.on("click",m(_,null,s,i,g)).on("mouseover",m(C,null,s,i,g)).on("mouseout",m(b,null,s,i,g)),n.ssr&&x.eachChild(function(e){var t=(0,v.A)(e);t.seriesIndex=c.seriesIndex,t.dataIndex=a,t.ssrType="legend"}),d.set(s,!0)}}},this)},this),l&&this._createSelector(l,t,i,a,s)},t.prototype._createSelector=function(e,t,n,i,o){var r=this.getSelectorGroup();S(e,function(e){var i=e.type,o=new a.ZP({style:{x:0,y:0,align:"center",verticalAlign:"middle"},onclick:function(){n.dispatchAction({type:"all"===i?"legendAllSelect":"legendInverseSelect"})}});r.add(o);var l=t.getModel("selectorLabel"),s=t.getModel(["emphasis","selectorLabel"]);(0,u.ni)(o,{normal:l,emphasis:s},{defaultText:e.title}),(0,d.vF)(o)})},t.prototype._createItem=function(e,t,n,i,r,l,g,p,h,v,m){var I=e.visualDrawType,_=r.get("itemWidth"),w=r.get("itemHeight"),C=r.isSelected(t),b=i.get("symbolRotate"),G=i.get("symbolKeepAspect"),D=i.get("icon"),P=function(e,t,n,i,o,r,l){function a(e,t){"auto"===e.lineWidth&&(e.lineWidth=t.lineWidth>0?2:0),S(e,function(n,i){"inherit"===e[i]&&(e[i]=t[i])})}var s=t.getModel("itemStyle"),c=s.getItemStyle(),d=0===e.lastIndexOf("empty",0)?"fill":"stroke",u=s.getShallow("decal");c.decal=u&&"inherit"!==u?(0,y.I)(u,l):i.decal,"inherit"===c.fill&&(c.fill=i[o]),"inherit"===c.stroke&&(c.stroke=i[d]),"inherit"===c.opacity&&(c.opacity=("fill"===o?i:n).opacity),a(c,i);var g=t.getModel("lineStyle"),p=g.getLineStyle();if(a(p,n),"auto"===c.fill&&(c.fill=i.fill),"auto"===c.stroke&&(c.stroke=i.fill),"auto"===p.stroke&&(p.stroke=i.fill),!r){var h=t.get("inactiveBorderWidth"),f=c[d];c.lineWidth="auto"===h?i.lineWidth>0&&f?2:0:c.lineWidth,c.fill=t.get("inactiveColor"),c.stroke=t.get("inactiveBorderColor"),p.stroke=g.get("inactiveColor"),p.lineWidth=g.get("inactiveWidth")}return{itemStyle:c,lineStyle:p}}(h=D||h||"roundRect",i,g,p,I,C,m),A=new x,M=i.getModel("textStyle");if(o.mf(e.getLegendIcon)&&(!D||"inherit"===D))A.add(e.getLegendIcon({itemWidth:_,itemHeight:w,icon:h,iconRotate:b,itemStyle:P.itemStyle,lineStyle:P.lineStyle,symbolKeepAspect:G}));else{var Z="inherit"===D&&e.getData().getVisual("symbol")?"inherit"===b?e.getData().getVisual("symbolRotate"):b:0;A.add(function(e){var t=e.icon||"roundRect",n=(0,f.th)(t,0,0,e.itemWidth,e.itemHeight,e.itemStyle.fill,e.symbolKeepAspect);return n.setStyle(e.itemStyle),n.rotation=(e.iconRotate||0)*Math.PI/180,n.setOrigin([e.itemWidth/2,e.itemHeight/2]),t.indexOf("empty")>-1&&(n.style.stroke=n.style.fill,n.style.fill="#fff",n.style.lineWidth=2),n}({itemWidth:_,itemHeight:w,icon:h,iconRotate:Z,itemStyle:P.itemStyle,lineStyle:P.lineStyle,symbolKeepAspect:G}))}var R=r.get("formatter"),k=t;o.HD(R)&&R?k=R.replace("{name}",null!=t?t:""):o.mf(R)&&(k=R(t));var W=C?M.getTextColor():i.get("inactiveColor");A.add(new a.ZP({style:(0,u.Lr)(M,{text:k,x:"left"===l?_+5:-5,y:w/2,fill:W,align:l,verticalAlign:"middle"},{inheritColor:W})}));var O=new s.Z({shape:A.getBoundingRect(),style:{fill:"transparent"}}),B=i.getModel("tooltip");return B.get("show")&&c.setTooltipConfig({el:O,componentModel:r,itemName:t,itemTooltipOption:B.option}),A.add(O),A.eachChild(function(e){e.silent=!0}),O.silent=!v,this.getContentGroup().add(A),(0,d.vF)(A),A.__legendDataIndex=n,A},t.prototype.layoutInner=function(e,t,n,i,o,r){var l=this.getContentGroup(),a=this.getSelectorGroup();p.BZ(e.get("orient"),l,e.get("itemGap"),n.width,n.height);var s=l.getBoundingRect(),c=[-s.x,-s.y];if(a.markRedraw(),l.markRedraw(),!o)return l.x=c[0],l.y=c[1],this.group.getBoundingRect();p.BZ("horizontal",a,e.get("selectorItemGap",!0));var d=a.getBoundingRect(),u=[-d.x,-d.y],g=e.get("selectorButtonGap",!0),h=e.getOrient().index,f=0===h?"width":"height",y=0===h?"height":"width",v=0===h?"y":"x";"end"===r?u[h]+=s[f]+g:c[h]+=d[f]+g,u[1-h]+=s[y]/2-d[y]/2,a.x=u[0],a.y=u[1],l.x=c[0],l.y=c[1];var m={x:0,y:0};return m[f]=s[f]+g+d[f],m[y]=Math.max(s[y],d[y]),m[v]=Math.min(0,d[v]+u[1-h]),m},t.prototype.remove=function(){this.getContentGroup().removeAll(),this._isFirstRender=!0},t.type="legend.plain",t}(h.Z);function _(e,t,n,i){b(e,t,n,i),n.dispatchAction({type:"legendToggleSelect",name:null!=e?e:t}),C(e,t,n,i)}function w(e){for(var t,n=e.getZr().storage.getDisplayList(),i=0,o=n.length;i<o&&!(t=n[i].states.emphasis);)i++;return t&&t.hoverLayer}function C(e,t,n,i){!w(n)&&n.dispatchAction({type:"highlight",seriesName:e,name:t,excludeSeriesId:i})}function b(e,t,n,i){!w(n)&&n.dispatchAction({type:"downplay",seriesName:e,name:t,excludeSeriesId:i})}t.Z=I},878940:function(e,t,n){var i=n(518299),o=n(231405),r=n(918712),l=n(115856),a=function(e){function t(){var n=null!==e&&e.apply(this,arguments)||this;return n.type=t.type,n}return(0,i.ZT)(t,e),t.prototype.setScrollDataIndex=function(e){this.option.scrollDataIndex=e},t.prototype.init=function(t,n,i){var o=(0,r.tE)(t);e.prototype.init.call(this,t,n,i),s(this,t,o)},t.prototype.mergeOption=function(t,n){e.prototype.mergeOption.call(this,t,n),s(this,this.option,t)},t.type="legend.scroll",t.defaultOption=(0,l.ZL)(o.Z.defaultOption,{scrollDataIndex:0,pageButtonItemGap:5,pageButtonGap:null,pageButtonPosition:"end",pageFormatter:"{current}/{total}",pageIcons:{horizontal:["M0,0L12,-10L12,10z","M0,0L-12,-10L-12,10z"],vertical:["M0,0L20,0L10,-20z","M0,0L20,0L10,20z"]},pageIconColor:"#2f4554",pageIconInactiveColor:"#aaa",pageIconSize:15,pageTextStyle:{color:"#333"},animationDurationUpdate:800}),t}(o.Z);function s(e,t,n){var i=e.getOrient(),o=[1,1];o[i.index]=0,(0,r.dt)(t,n,{type:"box",ignoreSize:!!o})}t.Z=a},105641:function(e,t,n){var i=n(518299),o=n(807028),r=n(707498),l=n(345262),a=n(339738),s=n(406822),c=n(202953),d=n(918712),u=n(831543),g=r.Z,p=["width","height"],h=["x","y"],f=function(e){function t(){var n=null!==e&&e.apply(this,arguments)||this;return n.type=t.type,n.newlineDisabled=!0,n._currentIndex=0,n}return(0,i.ZT)(t,e),t.prototype.init=function(){e.prototype.init.call(this),this.group.add(this._containerGroup=new g),this._containerGroup.add(this.getContentGroup()),this.group.add(this._controllerGroup=new g)},t.prototype.resetInner=function(){e.prototype.resetInner.call(this),this._controllerGroup.removeAll(),this._containerGroup.removeClipPath(),this._containerGroup.__rectSize=null},t.prototype.renderInner=function(t,n,i,r,s,c,d){var u=this;e.prototype.renderInner.call(this,t,n,i,r,s,c,d);var g=this._controllerGroup,p=n.get("pageIconSize",!0),h=o.kJ(p)?p:[p,p];y("pagePrev",0);var f=n.getModel("pageTextStyle");function y(e,t){var i=a.createIcon(n.get("pageIcons",!0)[n.getOrient().name][t],{onclick:o.ak(u._pageGo,u,e+"DataIndex",n,r)},{x:-h[0]/2,y:-h[1]/2,width:h[0],height:h[1]});i.name=e,g.add(i)}g.add(new l.ZP({name:"pageText",style:{text:"xx/xx",fill:f.getTextColor(),font:f.getFont(),verticalAlign:"middle",align:"center"},silent:!0})),y("pageNext",1)},t.prototype.layoutInner=function(e,t,n,i,r,l){var a=this.getSelectorGroup(),s=e.getOrient().index,c=p[s],u=h[s],g=p[1-s],f=h[1-s];r&&d.BZ("horizontal",a,e.get("selectorItemGap",!0));var y=e.get("selectorButtonGap",!0),v=a.getBoundingRect(),m=[-v.x,-v.y],S=o.d9(n);r&&(S[c]=n[c]-v[c]-y);var x=this._layoutContentAndController(e,i,S,s,c,g,f,u);if(r){if("end"===l)m[s]+=x[c]+y;else{var I=v[c]+y;m[s]-=I,x[u]-=I}x[c]+=v[c]+y,m[1-s]+=x[f]+x[g]/2-v[g]/2,x[g]=Math.max(x[g],v[g]),x[f]=Math.min(x[f],v[f]+m[1-s]),a.x=m[0],a.y=m[1],a.markRedraw()}return x},t.prototype._layoutContentAndController=function(e,t,n,i,r,l,a,u){var g=this.getContentGroup(),p=this._containerGroup,h=this._controllerGroup;d.BZ(e.get("orient"),g,e.get("itemGap"),i?n.width:null,i?null:n.height),d.BZ("horizontal",h,e.get("pageButtonItemGap",!0));var f=g.getBoundingRect(),y=h.getBoundingRect(),v=this._showController=f[r]>n[r],m=[-f.x,-f.y];!t&&(m[i]=g[u]);var S=[0,0],x=[-y.x,-y.y],I=o.pD(e.get("pageButtonGap",!0),e.get("itemGap",!0));v&&("end"===e.get("pageButtonPosition",!0)?x[i]+=n[r]-y[r]:S[i]+=y[r]+I),x[1-i]+=f[l]/2-y[l]/2,g.setPosition(m),p.setPosition(S),h.setPosition(x);var _={x:0,y:0};if(_[r]=v?n[r]:f[r],_[l]=Math.max(f[l],y[l]),_[a]=Math.min(0,y[a]+x[1-i]),p.__rectSize=n[r],v){var w={x:0,y:0};w[r]=Math.max(n[r]-y[r]-I,0),w[l]=_[l],p.setClipPath(new s.Z({shape:w})),p.__rectSize=w[r]}else h.eachChild(function(e){e.attr({invisible:!0,silent:!0})});var C=this._getPageInfo(e);return null!=C.pageIndex&&c.D(g,{x:C.contentPosition[0],y:C.contentPosition[1]},v?e:null),this._updatePageInfoView(e,C),_},t.prototype._pageGo=function(e,t,n){var i=this._getPageInfo(t)[e];null!=i&&n.dispatchAction({type:"legendScroll",scrollDataIndex:i,legendId:t.id})},t.prototype._updatePageInfoView=function(e,t){var n=this._controllerGroup;o.S6(["pagePrev","pageNext"],function(i){var o=null!=t[i+"DataIndex"],r=n.childOfName(i);r&&(r.setStyle("fill",o?e.get("pageIconColor",!0):e.get("pageIconInactiveColor",!0)),r.cursor=o?"pointer":"default")});var i=n.childOfName("pageText"),r=e.get("pageFormatter"),l=t.pageIndex,a=null!=l?l+1:0,s=t.pageCount;i&&r&&i.setStyle("text",o.HD(r)?r.replace("{current}",null==a?"":a+"").replace("{total}",null==s?"":s+""):r({current:a,total:s}))},t.prototype._getPageInfo=function(e){var t=e.get("scrollDataIndex",!0),n=this.getContentGroup(),i=this._containerGroup.__rectSize,o=e.getOrient().index,r=p[o],l=h[o],a=this._findTargetItemIndex(t),s=n.children(),c=s[a],d=s.length,u=d?1:0,g={contentPosition:[n.x,n.y],pageCount:u,pageIndex:u-1,pagePrevDataIndex:null,pageNextDataIndex:null};if(!c)return g;var f=x(c);g.contentPosition[o]=-f.s;for(var y=a+1,v=f,m=f,S=null;y<=d;++y)(!(S=x(s[y]))&&m.e>v.s+i||S&&!I(S,v.s))&&(v=m.i>v.i?m:S)&&(null==g.pageNextDataIndex&&(g.pageNextDataIndex=v.i),++g.pageCount),m=S;for(var y=a-1,v=f,m=f,S=null;y>=-1;--y)(!(S=x(s[y]))||!I(m,S.s))&&v.i<m.i&&(m=v,null==g.pagePrevDataIndex&&(g.pagePrevDataIndex=v.i),++g.pageCount,++g.pageIndex),v=S;return g;function x(e){if(e){var t=e.getBoundingRect(),n=t[l]+e[l];return{s:n,e:n+t[r],i:e.__legendDataIndex}}}function I(e,t){return e.e>=t&&e.s<=t+i}},t.prototype._findTargetItemIndex=function(e){var t,n;if(!this._showController)return 0;return this.getContentGroup().eachChild(function(i,o){var r=i.__legendDataIndex;null==n&&null!=r&&(n=o),r===e&&(t=o)}),null!=t?t:n},t.type="legend.scroll",t}(u.Z);t.Z=f},295978:function(e,t,n){n.d(t,{N:function(){return l}});var i=n(574138),o=n(76197),r=n(642206);function l(e){(0,i.D)(o.N),(0,i.D)(r.N)}},76197:function(e,t,n){n.d(t,{N:function(){return a}});var i=n(231405),o=n(831543),r=n(63740),l=n(164952);function a(e){e.registerComponentModel(i.Z),e.registerComponentView(o.Z),e.registerProcessor(e.PRIORITY.PROCESSOR.SERIES_FILTER,r.Z),e.registerSubTypeDefaulter("legend",function(){return"plain"}),(0,l.U)(e)}},642206:function(e,t,n){n.d(t,{N:function(){return s}});var i=n(574138),o=n(76197),r=n(878940),l=n(105641),a=n(755475);function s(e){(0,i.D)(o.N),e.registerComponentModel(r.Z),e.registerComponentView(l.Z),(0,a.Z)(e)}},164952:function(e,t,n){n.d(t,{U:function(){return r}});var i=n(807028);function o(e,t,n){var o,r={},l="toggleSelected"===e;return n.eachComponent("legend",function(n){l&&null!=o?n[o?"select":"unSelect"](t.name):"allSelect"===e||"inverseSelect"===e?n[e]():(n[e](t.name),o=n.isSelected(t.name));var a=n.getData();(0,i.S6)(a,function(e){var t=e.get("name");if("\n"!==t&&""!==t){var i=n.isSelected(t);r.hasOwnProperty(t)?r[t]=r[t]&&i:r[t]=i}})}),"allSelect"===e||"inverseSelect"===e?{selected:r}:{name:t.name,selected:r}}function r(e){e.registerAction("legendToggleSelect","legendselectchanged",(0,i.WA)(o,"toggleSelected")),e.registerAction("legendAllSelect","legendselectall",(0,i.WA)(o,"allSelect")),e.registerAction("legendInverseSelect","legendinverseselect",(0,i.WA)(o,"inverseSelect")),e.registerAction("legendSelect","legendselected",(0,i.WA)(o,"select")),e.registerAction("legendUnSelect","legendunselected",(0,i.WA)(o,"unSelect"))}},63740:function(e,t,n){n.d(t,{Z:function(){return i}});function i(e){var t=e.findComponents({mainType:"legend"});t&&t.length&&e.filterSeries(function(e){for(var n=0;n<t.length;n++)if(!t[n].isSelected(e.name))return!1;return!0})}},755475:function(e,t,n){n.d(t,{Z:function(){return i}});function i(e){e.registerAction("legendScroll","legendscroll",function(e,t){var n=e.scrollDataIndex;null!=n&&t.eachComponent({mainType:"legend",subType:"scroll",query:e},function(e){e.setScrollDataIndex(n)})})}}}]);