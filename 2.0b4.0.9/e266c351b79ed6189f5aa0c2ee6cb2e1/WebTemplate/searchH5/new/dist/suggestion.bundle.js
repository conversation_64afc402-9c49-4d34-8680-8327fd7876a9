"use strict";(self.webpackChunkweixin_search=self.webpackChunkweixin_search||[]).push([["suggestion"],{387647:function(e,t,r){var n=r(93167),s=r(65764),i=r(551900),a=r(271431),o=(0,i.Z)(s.Z,n.s,n.x,!1,null,null,null);"function"==typeof a.Z&&(0,a.Z)(o),t.Z=o.exports},399689:function(e,t,r){r.d(t,{Z:function(){return n}});function n(e){e.options.__wxs_id="2011c14a"}},71968:function(e,t,r){var n=r(139912),s=r(477426),i=r(551900),a=r(730823),o=(0,i.Z)(s.Z,n.s,n.x,!1,null,null,null);"function"==typeof a.Z&&(0,a.Z)(o),t.Z=o.exports},677670:function(e,t,r){r.d(t,{Z:function(){return n}});function n(e){e.options.__wxs_id="92cc415a"}},737753:function(e,t,r){var n=r(190829),s=r(900726),i=r(551900),a=r(301658),o=(0,i.Z)(s.Z,n.s,n.x,!1,null,null,null);"function"==typeof a.Z&&(0,a.Z)(o),t.Z=o.exports},991516:function(e,t,r){r.d(t,{Z:function(){return n}});function n(e){e.options.__wxs_id="2f02e546"}},517157:function(e,t,r){var n=r(410973),s=r(795923),i=r(551900),a=r(687449),o=(0,i.Z)(s.Z,n.s,n.x,!1,null,null,null);"function"==typeof a.Z&&(0,a.Z)(o),t.Z=o.exports},318276:function(e,t,r){r.d(t,{Z:function(){return n}});function n(e){e.options.__wxs_id="df2c9584"}},207929:function(e,t,r){var n=r(28652),s=r(75736),i=r(551900),a=r(584630),o=(0,i.Z)(s.Z,n.s,n.x,!1,null,null,null);"function"==typeof a.Z&&(0,a.Z)(o),t.Z=o.exports},265585:function(e,t,r){r.d(t,{Z:function(){return n}});function n(e){e.options.__wxs_id="7784b5b1"}},456378:function(e,t,r){r.r(t);var n=r(221884),s=r(387165),i=r(551900),a=r(674837),o=(0,i.Z)(s.Z,n.s,n.x,!1,null,null,null);"function"==typeof a.Z&&(0,a.Z)(o),t.default=o.exports},408088:function(e,t,r){r.d(t,{Z:function(){return n}});function n(e){e.options.__wxs_id="bca8e319"}},271431:function(e,t,r){var n=r(399689);t.Z=n.Z},730823:function(e,t,r){var n=r(677670);t.Z=n.Z},301658:function(e,t,r){var n=r(991516);t.Z=n.Z},687449:function(e,t,r){var n=r(318276);t.Z=n.Z},584630:function(e,t,r){var n=r(265585);t.Z=n.Z},674837:function(e,t,r){var n=r(408088);t.Z=n.Z},65764:function(e,t,r){var n=r(629585);t.Z=n.Z},477426:function(e,t,r){var n=r(657132);t.Z=n.Z},900726:function(e,t,r){var n=r(294241);t.Z=n.Z},795923:function(e,t,r){var n=r(929704);t.Z=n.Z},75736:function(e,t,r){var n=r(779410);t.Z=n.Z},387165:function(e,t,r){var n=r(774733);t.Z=n.Z},93167:function(e,t,r){r.d(t,{s:function(){return n},x:function(){return s}});var n=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"sug-bottom",attrs:{"data-fcn-bottom":"","data-fr-105590428":""}},e._l(e.data.items,function(t,n){return r(e.types[t.type],e._b({key:n,tag:"components",attrs:{item:t,"data-fc-1b470fdee":""}},"components",e.$props,!1))}),1)},s=[]},139912:function(e,t,r){r.d(t,{s:function(){return n},x:function(){return s}});var n=function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"sug-complaint",class:{fixed:this.fixed},attrs:{"data-fcn-complaint":"","data-fr-16aaf729a":""}},[t("span",{directives:[{name:"active",rawName:"v-active"}],staticClass:"active__opacity",attrs:{role:"link","data-cli":""},on:{click:this.tap}},[this._v("\n      "+this._s(this.item.word)+"\n    ")])])},s=[]},190829:function(e,t,r){r.d(t,{s:function(){return n},x:function(){return s}});var n=function(){var e=this.$createElement,t=this._self._c||e;return 3==this.type?t("div",{staticClass:"weui-cell sug_biz",class:{active:this.isActive},attrs:{"data-sug-id":this.reportId,"data-new-teach-report-id":""+this.M_getItemReportId(this.item,this.blockIndex),role:"button","data-fcn-contact":"","data-fr-1a3aefcec":""},on:{click:this.onTapContact}},[t("div",{staticClass:"weui-cell_hd"},[t("client-image",{key:this.item.userName,staticClass:"sug__moment-thumb",attrs:{item:this.item,type:8,"data-fc-116e7afe7":""}})],1),t("div",{staticClass:"weui-cell__bd biz_item"},[t("h4",{domProps:{innerHTML:this._s(this.xss(this.item.nickNameHighlight||this.item.nickName))}}),this.item.extraHighlight?t("p",{staticClass:"biz_account_tips",domProps:{innerHTML:this._s(this.xss(this.item.extraHighlight))}}):this._e()])]):t("div",{staticClass:"weui-cell sug_biz",class:{active:this.isActive},attrs:{"data-new-teach-report-id":""+this.M_getItemReportId(this.item,this.blockIndex),docid:this.item.docID,"data-sug-id":this.reportId,role:"button","data-fcn-contact":"","data-fr-1a3aefcec":""},on:{click:this.onTap}},[t("div",{staticClass:"weui-cell_hd"},[t("div",{directives:[{name:"image",rawName:"v-image",value:this.item.iconUrl,expression:"item.iconUrl"}],staticClass:"sug__biz-thumb"})]),t("div",{staticClass:"weui-cell__bd biz_item"},[t("h4",{domProps:{innerHTML:this._s(this.xss(this.item.nickNameHighlight||this.item.nickName))}}),this.item.extraHighlight?t("p",{staticClass:"biz_account_tips",domProps:{innerHTML:this._s(this.xss(this.item.extraHighlight))}}):this._e()])])},s=[]},410973:function(e,t,r){r.d(t,{s:function(){return n},x:function(){return s}});var n=function(){var e=this.$createElement,t=this._self._c||e;return 1===this.item.showType?t("div",{attrs:{"data-fcn-general-sug":"","data-fr-105ed678a":""}}):t("simple",this._b({ref:"GeneralSug",attrs:{"data-new-teach-report-id":""+this.M_getItemReportId(this.item,this.blockIndex),"data-fc-19080ec32":"","data-fcn-general-sug":"","data-fr-105ed678a":""},on:{tapitem:this.onTapItem,tapaddto:this.onTapAddTo}},"simple",this.$props,!1))},s=[]},28652:function(e,t,r){r.d(t,{s:function(){return n},x:function(){return s}});var n=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{directives:[{name:"active",rawName:"v-active.instant",modifiers:{instant:!0}}],staticClass:"sug__simple active__item",attrs:{"data-sug-id":e.reportId,"data-cli":"","data-fcn-simple":"","data-fr-1155f8a10":""},on:{click:e.onTapSuggestionSearch}},[r("div",{staticClass:"line"},[r("svg-icon",{attrs:{"aria-hidden":"true","class-name":"sug__simple-icon line__item no-shrink",name:"magnifier","data-fc-19691b78e":""}}),r("div",{staticClass:"line line__item primary",attrs:{role:"button"}},[r("p",{staticClass:"line__item",domProps:{innerHTML:e._s(e.xss(e.word))}}),e.weakTag?r("ui-tag",{staticClass:"line__item no-shrink",staticStyle:{"margin-left":"4px"},attrs:{title:e.weakTag.title,type:e.weakTag.type,"icon-url":e.weakTag.iconUrl,"no-bg":"true","data-fc-19691b78c":""}}):e._e()],1),e.os.mobile?r("div",{staticClass:"line__item no-shrink",staticStyle:{"margin-left":"12px"},attrs:{role:"button","aria-label":"添加到搜索栏"}},[r("div",{directives:[{name:"active",rawName:"v-active.stop",modifiers:{stop:!0}}],staticClass:"sug__simple-active active__opacity",attrs:{"data-cli":""},on:{click:function(t){return t.stopPropagation(),e.onTapSuggestionAddto(t)}}},[r("svg-icon",{staticClass:"sug__simple-end",attrs:{name:"arrow.up.left","data-fc-19691b78a":""}})],1)]):e._e()],1),e.strongTag?r("div",{staticClass:"line",staticStyle:{"margin-top":"8px"},attrs:{"aria-hidden":"true"}},[r("div",{staticClass:"line__item no-shrink sug__simple-occupy"}),r("ui-tag",{staticClass:"line__item",attrs:{title:e.strongTag.title,type:e.strongTag.type,"icon-url":e.strongTag.iconUrl,size:"big","no-bg":"true","data-fc-19691b788":""}})],1):e._e()])},s=[]},221884:function(e,t,r){r.d(t,{s:function(){return n},x:function(){return s}});var n=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"suggestion-list",attrs:{"data-fcn-suggestion":"","data-fr-17d8efd54":""},on:{touchmove:e.handleTouchmove}},[e._l(e.suggestoinObj,function(t,n){return r("div",{key:n,staticClass:"weui-cells suggestion-section",style:{marginTop:e.os.mobile&&n>0?"5px":0}},[r("exposure-block",{attrs:{attr:"data-new-teach-report-id","data-fc-165d7a99b":""},on:{expose:e.M_exposeReportFor26897}},[t.title?r("div",{staticClass:"weui-cells_title"},[e._v("\n          "+e._s(t.title)+"\n        ")]):e._e(),e._l(t.items,function(s,i){return[1==t.type||4==t.type?r("general",{key:i+""+n,ref:"SugItem",refInFor:!0,class:{active:e.selectIndex===e.getListIndex(n,i)},attrs:{index:e.getListIndex(n,i),data:t,item:s,type:t.type,pos:i+1,"type-pos":n+1,"block-index":i+1,ts:e.dataReadyTs,"data-fc-165d7a99c":""},nativeOn:{mouseover:function(t){return e.$emit("hover",i)}}}):e._e(),2==t.type||3==t.type?r("contactSug",{key:i+""+n,ref:"SugItem",refInFor:!0,class:{active:e.selectIndex===e.getListIndex(n,i)},attrs:{index:e.getListIndex(n,i),data:t,item:s,type:t.type,pos:i+1,"block-index":i+1,"type-pos":n+1,"data-fc-165d7a99d":""},nativeOn:{mouseover:function(t){return e.$emit("hover",i)}}}):e._e()]})],2)],1)}),e.os.mobile&&e.bottom?r("bottom",{attrs:{data:e.bottom,"data-fc-165d7a99e":""}}):e._e()],2)},s=[]},629585:function(e,t,r){var n=r(71968),s=new(r(116746)).J({complaint:1});t.Z={components:{complaint:n.Z},props:{data:{type:Object,default:function(){return{}}}},data:function(){return{types:s}}}},657132:function(e,t,r){var n=r(798509);t.Z={mixins:[n.jB],props:{data:{type:Object,default:function(){return{}}},item:{type:Object,default:function(){return{}}}},data:function(){return{fixed:!1}},watch:{item:function(){this._calculateHeight()}},mounted:function(){this._calculateHeight()},methods:{_calculateHeight:function(){this.$parent.$parent.$el.clientHeight+this.$el.clientHeight<n.Zr.getWindowHeight()?this.fixed=!0:this.fixed=!1},tap:function(){this.M_go(this.item)}}}},294241:function(e,t,r){var n=r(798509),s=r(275994),i=r(959018),a=r(185048),o=r(984928),c=r(136525),u=r(619269),p=r(378350);r(827798);var l=r(420629);function d(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){var n,s,i;n=e,s=t,i=r[t],s in n?Object.defineProperty(n,s,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[s]=i})}return e}function m(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):(function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r.push.apply(r,n)}return r})(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}),e}t.Z={components:{clientImage:p.Z},mixins:[n.jB,n.uW,u.Z],props:["data","item","type","tapCb","pos","typePos","index","blockIndex"],data:function(){return{isActive:!1,isVerify:!1,isTradmarkProtection:!1}},computed:m(d({},(0,l.Se)({resultExperiment:"result/self/getExperiment"}),(0,l.rn)({experiment:function(e){return e.teach.experiment}})),{reportId:function(){return"".concat(this.typePos,"|").concat(this.data.title||"","|").concat(this.type,"|").concat(this.pos,"|").concat(this.item.nickName,"|").concat(this.item.reportFlag||0)},isExptBoxUpdate:function(){return!0}}),methods:{onTap:function(){var e,t=this,r=t.item.nickName,u={scene:n.xB.scene,businessType:n.xB.type,docId:r,docPos:t.pos,typePos:t.typePos,clickType:1,suggestionId:t.$store.state.sug.sugId,searchId:n.xB.searchId};(0,a.lO)(u);var p={};if(n.xB.type==c.jJ.OFFICE_ACCOUNT||3==this.item.opType?p.actionId=o.At.BIZ:(null===(e=this.item.jumpInfo)||void 0===e?void 0:e.jumpType)===2&&(p.actionId=o.At.WE_APP),this.M_clickReportFor26897(t.item,t.blockIndex,m(d({},p),{searchId:n.xB.searchId})),t.item.jumpInfo)t.M_go(t.item.jumpInfo);else if(n.xB.type==c.jJ.OFFICE_ACCOUNT||3==this.item.opType)t.M_jump(Object.assign(this.item,{type:1,bizuin:this.item.bizuin||this.item.docID,opType:this.item.opType||o.xQ.bizSession,query:n.xB.query,searchId:n.xB.searchId,extraParams:"from_sug=1&search_type=1&docid=".concat(decodeURIComponent(this.item.docID),"&scene=").concat(n.xB.scene),reportDocId:r}));else{t.$store.commit({type:"result/updateMoment",tagInfo:{tagType:o.xo.BIZFEEDS,tagText:""},matchUser:{matchWord:r,userName:t.item.docID},nextObjID:"",tagList:[{tagName:r,tagType:o.xo.BIZFEEDS}]});var l=t.M_composeParentSid({t:o.X$.SUG,s:t.$store.state.sug.sugId,did:r});t.M_checkSpecialSearchBeforeLaunchNewSearch({query:r,callback:function(){n.xB.setValue({custom:""}),n.Zr.setSearchInputWord({query:r,custom:"",tagList:[{tagName:r,tagType:o.xo.BIZFEEDS,userName:t.item.userName}],isInputChange:!1}),n.Gc.$emit(n.U3.GOTO,{page:o.kO.RESULT,query:r,extReqParams:{key:"parentSearchID",textValue:l},action:o.Ss.SUG_BIZ_CLICK,searchId:""})},extParams:{crossExtReqParams:[{key:"parentSearchID",textValue:l}]}},u)}(0,i.m6)(s.Z.detail.suggestion.suggestionItemBiz),n.yG.suggestion.reportCommonClick(this.reportId),t.item.needEnterHistory&&n.su.storeHistory({type:n.xB.type,query:r,scene:n.xB.scene,docPullType:1})},onTapContact:function(){n.Gc.$emit(n.U3.RESET_MOMENT_FILTER);var e=this,t=e.item.nickName;e.$store.commit({type:"result/updateMoment",nextObjID:"",tagInfo:{tagType:o.xo.SNSALBUM,tagText:""},matchUser:{matchWord:t,userName:e.item.userName},tagList:[{tagName:t,tagType:o.xo.SNSALBUM}]});var r=e.M_composeParentSid({t:o.X$.SUG,s:e.$store.state.sug.sugId,did:t}),c={scene:n.xB.scene,businessType:n.xB.type,docId:t,docPos:e.pos,typePos:e.typePos,clickType:1,suggestionId:e.$store.state.sug.sugId,searchId:n.xB.searchId};(0,a.lO)(c),this.M_clickReportFor26897(e.item,e.blockIndex,{actionId:o.At.ALL_SEARCH,searchId:n.xB.searchId}),e.M_checkSpecialSearchBeforeLaunchNewSearch({query:t,callback:function(){n.Zr.setSearchInputWord({query:t,custom:"",tagList:[{tagName:t,tagType:o.xo.SNSALBUM,userName:e.item.userName}],isInputChange:!1}),n.xB.custom="",n.Gc.$emit(n.U3.GOTO,{page:o.kO.RESULT,query:t,extReqParams:{key:"parentSearchID",textValue:r},action:o.Ss.SUG_CONTACT_CLICK,searchId:""})},extParams:{crossExtReqParams:[{key:"parentSearchID",textValue:r}]}},c),(0,i.m6)(s.Z.detail.suggestion.suggestionItemContact),n.yG.suggestion.reportCommonClick(this.reportId)},tap:function(){3===this.type?this.onTapContact():this.onTap()}}}},929704:function(e,t,r){var n=r(207929),s=r(619269);function i(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){var n,s,i;n=e,s=t,i=r[t],s in n?Object.defineProperty(n,s,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[s]=i})}return e}function a(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):(function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r.push.apply(r,n)}return r})(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}),e}t.Z={components:{simple:n.Z},mixins:[s.Z],props:{data:{type:Object},item:{type:Object,default:function(){return{}}},type:{type:Number,default:0},pos:{type:Number,default:0},typePos:{type:Number,default:0},ts:{type:Number,default:0},index:{type:Number,default:0},blockIndex:{type:Number,default:0}},methods:{tap:function(){1===this.item.showType?this.$refs.GeneralSug.tap():this.$refs.GeneralSug.onTapSuggestionSearch()},onTapItem:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.M_clickReportFor26897(this.item,this.blockIndex,e)},onTapAddTo:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.M_clickReportFor26897(a(i({},this.item),{docType:"upbutton"}),this.blockIndex,a(i({},e),{actionId:3216}))}}}},779410:function(e,t,r){var n=r(798509),s=r(959018),i=r(185048),a=r(984928),o=r(462474);t.Z={mixins:[n.uW,n.jB],props:{data:{type:Object},item:{type:Object,default:function(){return{}}},type:{type:Number,default:0},pos:{type:Number,default:0},typePos:{type:Number,default:0},ts:{type:Number,default:0}},data:function(){return{}},computed:{reportId:function(){return"".concat(this.typePos,"|").concat(this.data.title||"","|").concat(this.type,"|").concat(this.pos,"|").concat(this.item.word,"|").concat(this.item.reportFlag||0)},word:function(){return this.item.wordHighlight||this.item.word},weakTag:function(){return this.item.tagInfo&&2===this.item.tagInfo.showType?this.item.tagInfo:null},strongTag:function(){return this.item.tagInfo&&1===this.item.tagInfo.showType?this.item.tagInfo:null}},methods:{onTapSuggestionSearch:function(){var e,t=this;this.$emit("tapitem");var r=t.M_composeParentSid({t:a.X$.SUG,s:t.$store.state.sug.sugId,did:t.item.word}),c={scene:n.xB.scene,businessType:n.xB.type,docId:t.item.word,docPos:t.pos,typePos:t.typePos,query:t.item.word,suggestionId:t.$store.state.sug.sugId,resultType:t.item.showType||0,expand:"suggestion",expand1:new Date-t.ts,searchId:"",requestId:null===(e=t.$store.state.sug.sugData)||void 0===e?void 0:e.requestID};(0,i.lO)(c),t.$store.commit({type:"result/updateSearchInputValue",value:t.item.word}),t.M_checkSpecialSearchBeforeLaunchNewSearch({query:t.item.word,callback:function(){n.Zr.setSearchInputWord({query:t.item.word,isInputChange:!1}),t.item.preRenderData&&n.xB.setValue({preRenderData:t.item.preRenderData});var e=[{key:"parentSearchID",textValue:r}];t.item.mixerCommonContext&&e.push({key:"mixerCommonContext",textValue:t.item.mixerCommonContext}),o.Z.$emit(o.U.GOTO,{page:a.kO.RESULT,query:t.item.word,action:a.Ss.SUG_CLICK,extReqParams:e,searchId:""})},extParams:{crossExtReqParams:[{key:"parentSearchID",textValue:r}]}},c),n.yG.suggestion.reportCommonClick(this.reportId),(0,s.B4)(n.AX.feature.sug),n.yG.reportEventTrack({actionType:18,query:t.item.word}),this.$store.commit({type:"result/updateSearchInputValue",value:t.item.word}),this.$store.commit({type:"result/updateSearchKeyboardShow",value:!1})},onTapSuggestionAddto:function(){n.Zr.setSearchInputWord({query:this.item.word,isInputChange:!0}),n.yG.suggestion.reportCommonClick(this.reportId),(0,i.lO)({scene:n.xB.scene,businessType:n.xB.type,docId:this.item.word,docPos:this.pos,typePos:this.typePos,expand:"suggestionAttach",suggestionId:this.$store.state.sug.sugId,resultType:this.item.showType||0,searchId:""}),(0,s.B4)(n.AX.feature.sugAdd),this.$emit("tapaddto"),this.$store.commit({type:"result/updateSearchKeyboardShow",value:!1})}}}},774733:function(e,t,r){var n=r(720144),s=r(517157),i=r(737753),a=r(387647),o=r(798509),c=r(783466),u=r(448188),p=r(707239),l=r(984928),d=r(327075),m=r(838605),h=r(619269),f=r(349854),g=r(338200),y=r(244732);y.Z.install(n.Z),y.Z.installPlugin(n.Z),n.Z.mixin(o.uW),t.Z={components:{general:s.Z,contactSug:i.Z,bottom:a.Z,exposureBlock:f.Z},mixins:[o.jB,h.Z,g.Z],props:{selectIndex:{type:Number,default:-1}},data:function(){return{sugRequestId:"",isShow:!1,dataReadyTs:0,deviceType:o.xB.deviceType,isKeybordShow:!0}},computed:{suggestoinObj:function(){return this.$store.state.sug.data},bottom:function(){var e=this,t=this,r=t.$store.state.sug.bottom;return r&&r.items&&r.items.length&&r.items.forEach(function(r){if(1==r.type&&r.jumpUrl&&!r.jumpUrl.includes("relatedWords")){var n=[];e.suggestoinObj.map(function(e){e.items&&e.items.length&&(n=n.concat(e.items.map(function(e){return e.word})))}),r.jumpUrl=t.M_composeUrl(r.jumpUrl,{relatedWords:n.join("|")})}}),r}},watch:{$route:function(e){e.name===l.kO.SUG?this.$store.dispatch({type:"sug/handleSug",data:{}}):(e.name===l.kO.INDEX||e.name===l.kO.TEACH)&&this.$store.commit({type:"sug/updateState",sugSession:null})}},created:function(){var e=this,t=this;t.query="",t.lastQuery="",t.inputFinishTimestamp=+new Date,t.preGetSearchDataTimeout=null,t.preGetSearchDataArgs="",t.preGetSearchDataResolve=null,t.$store.getters.getExtReqParams,o.xB.parentType==l.X$.LOCALRELEVANT_MORE&&o.xB.setValue({parentType:l.X$.MARK_LOCALRELEVANT_MORE});var r=t.$route.params.option,n=void 0===r?{}:r;n.query&&(n.cache&&"{}"!==n.cache?(t.handleSugData({json:n.cache}),t.lastQuery=t.query,t.query=n.query):t.getData({query:n.query})),c.N.onInputChange(function(r){e.isKeybordShow=!0,t.inputFinishTimestamp=+new Date,r.query&&(t.getData(r),d.Z.isSupportSugPreSearchOnInputChange&&t._preGetSearchData()),t.$store.commit("result/updateCacheArgs",null),t.$store.commit("result/updateCachePromise",null)}),c.N.onInputConfirm(function(r){clearTimeout(t.preGetSearchDataTimeout),o.yG.reportEventTrack({actionType:17,timeStamp:t.inputFinishTimestamp,query:r.query||o.xB.query}),e.$store.commit({type:"sug/updateState",sugSession:{type:2,sessionId:"sug_sid_".concat(Date.now())}})});o.Gc.$on(o.U3.VERT_CHANGE,t.clearData),o.Gc.$on(o.U3.TAB_SWITCH,t.clearData),c.N.onSugReady(function(e){var r=function(){try{return JSON.parse(e.json)}catch(e){return{}}}();if(r.requestID&&t.sugRequestId&&r.requestID!==t.sugRequestId){var n="receive rid[".concat(r.requestID,"] !== send rid[").concat(t.sugRequestId,"]");console.error("onSugReady",n),c.h.log({key:"onSugReady",msg:n});return}var s=r.searchId;!r.searchId&&(s=o.Zr.generateGuuId()),t.handleSugData(e,r.requestID,s)}),c.N.onResultReady(function(e){console.log("[preSearch] receive suggestion pre getSearchData",e.requestId,t.preGetSearchDataArgs.requestId),e.requestId&&e.requestId===t.preGetSearchDataArgs.requestId&&(t.$store.commit("result/cacheResultData",e),t.preGetSearchDataResolve(e))}),c.N.onCommonCgiReady(function(e){})},mounted:function(){var e=this,t=this;t.expose=null,this.$nextTick().then(function(){!e.expose&&(t.expose=new m.Expose(function(e){e.length&&o.yG.suggestion.reportCommonExpose(e)},{container:t.$el,attr:"data-sug-id"}))})},activated:function(){!this.$store.state.sug.sugSession&&this.$store.commit({type:"sug/updateState",sugSession:{type:1,sessionId:"sug_sid_".concat(Date.now())}})},methods:{handleTouchmove:function(){[l.RL.S1S,l.RL.GLOBAL_HISTORY,l.RL.GLOBAL,l.RL.KEYBORD_PRELOAD].includes(o.xB.scene)},reportSugSession:function(){var e=(this.$store.state.sug.sugSession||{}).sessionId,t=this.$store.state.sug.sugId,r=this.deviceType;t&&c.h.getCommonCgiData({cgiName:u.yh.mmDataReport,data:{clientversion:Number(o.xB.clientVersion),device:r,item_list:[{logid:24687,log_buffer:["",t,o.xB.scene,o.xB.isHomePage,o.xB.type,e].join(",")}]}})},getData:function(e){if(!(e.query===this.query&&o.xB.parentType==l.X$.MARK_LOCALRELEVANT_MORE||[l.RL.BIZ_SPECIFIC,l.RL.MP_INNER_TOP_RIGHT_SEARCH,l.RL.MP_TOP_RIGHT_SEARCH_ENTER,l.RL.EXTLINK_TOP_RIGHT_SEARCH_ENTER].includes(o.xB.scene)&&o.xB.isMpVertical)){console.log("sug option",e);var t=Object.assign({query:o.xB.query},e);o.xB.scene===l.RL.NEW_LIFE&&(t.type=2),(o.xB.scene===l.RL.EMOTICON_MALL||o.xB.scene===l.RL.CHATROOM_EMOTICON)&&384===o.xB.type&&(t.type=385),this.getSearchSuggestionData(t),this.lastQuery=this.query,this.query=t.query}},clearData:function(){this.$store.commit("sug/updateState",{data:[]})},_preGetSearchData:function(){var e=this;e.lastQuery!==e.query&&!/\w/.test(e.query)&&e.query.length>1&&o.xB.preSearchSwitch&&(clearTimeout(e.preGetSearchDataTimeout),e.preGetSearchDataTimeout=setTimeout(function(){e.preGetSearchData(e.query)},100))},addExtReqParamsForW1W:function(e){if(16777248===o.xB.type)try{var t,r,n,s=null===(n=this.$store.state.result.sift.slideFilter)||void 0===n?void 0:null===(r=n.filters)||void 0===r?void 0:null===(t=r.find(function(e){return e.display}))||void 0===t?void 0:t.options,i=null==s?void 0:s.find(function(e){return 1===e.selected});i&&i.paramKey&&i.paramValue&&e.push({key:i.paramKey,textValue:JSON.stringify([i.paramValue])})}catch(e){console.error("addExtReqParamsForW1W err: ",e)}},getSearchSuggestionData:function(e){var t=o.Zr.generateGuuId();this.sugRequestId=t;var r=[{key:"requestId",textValue:t}];o.xB.parentType==l.X$.MARK_LOCALRELEVANT_MORE&&r.push({key:"sugActionType",uintValue:1}),this.addExtReqParamsForW1W(r),o.yG.reportEventTrack({actionType:303,requestId:t}),c.h.getSug({requestId:t,scene:o.xB.scene,version:o.xB.version,query:e.query||"",type:e.type||o.xB.type,isHomePage:o.xB.isHomePage,requestType:e.requestType||8===o.xB.type&&p.Z.requestType.local,sugId:"",sessionId:o.xB.sessionId,extReqParams:r})},preGetSearchData:function(e){var t=this,r=t.$store.state.result;if(!r.self.base.isLoading){var n,s,i=o.Zr.generateGuuId(),a=[{key:"netType",textValue:o.xB.netType},{key:"subType",uintValue:+o.xB.subType,textValue:o.xB.subType?"".concat(o.xB.subType):""},{key:"currentPage",uintValue:1},{key:"requestId",textValue:i},{key:"cookies",textValue:r.state.cookies},{key:"widgetVersion",uintValue:1023022}].concat(o.xB.crossExtReqParams);var u=Object.assign((n=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){var n,s,i;n=e,s=t,i=r[t],s in n?Object.defineProperty(n,s,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[s]=i})}return e}({},o.xB.getBase()),s=(s={query:e||"",offset:0,tagInfo:t.$store.getters["result/tagInfo"],type:o.xB.type,isHomePage:o.xB.isHomePage,subType:o.xB.subType,sceneActionType:l.E9.PRE_SEARCH,matchUser:t.$store.getters["result/matchUser"],numConditions:r.moment.numConditions,extReqParams:a,requestId:i,fromTagSearch:o.xB.queryString.fromTagSearch,extClientParams:o.xB.queryString.extClientParams,isBackButtonClick:!0},s),Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(s)):(function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r.push.apply(r,n)}return r})(Object(s)).forEach(function(e){Object.defineProperty(n,e,Object.getOwnPropertyDescriptor(s,e))}),n),{parentSearchID:"",sugId:""});c.h.getResult(u),t.preGetSearchDataArgs=u,t.$store.commit("result/cacheResultData",{}),t.$store.commit("result/updateCacheArgs",t.preGetSearchDataArgs),t.$store.commit("result/updateCachePromise",new Promise(function(e){t.preGetSearchDataResolve=e})),o.yG.reportEventTrack(Object.assign({actionType:3,requestId:i,isPreload:100},u))}},getListIndex:function(e,t){return e?t+this.suggestoinObj.slice(0,e).reduce(function(e,t){var r=t.items;return e+(void 0===r?[]:r).length},0):t},tapIndex:function(e){this.$refs.SugItem.find(function(t){return t.index===e}).tap()},handleSugData:function(e,t,r){var n=this;this.$store.dispatch({type:"sug/handleSug",data:e});var s=this.$store.state.sug.sugId;o.xB.setValue({searchId:s||r}),o.yG.reportEventTrack({actionType:308,searchId:s||r,requestId:t}),this.reportSugSession(),!d.Z.isSupportSugPreSearchOnInputChange&&this._preGetSearchData(),this.dataReadyTs=+new Date,setTimeout(function(){o.yG.reportEventTrack({actionType:309,searchId:s||r,requestId:t}),n.expose&&(n.expose.resetExposeId(),n.expose.analysis())})}}}},707239:function(e,t,r){r.d(t,{Z:function(){return i}});var n=r(116746),s=new n.J({WORD:1,BIZ:2,CONTACT:3,HISTORY:4}),i={requestType:new n.J({net:0,local:1}),sugType:s};Object.freeze(i)},338200:function(e,t,r){var n=r(231097),s=r(462474),i=r(624312),a=r(185048),o=r(984928);function c(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function u(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function p(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r,n,s=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=s){var i=[],a=!0,o=!1;try{for(s=s.call(e);!(a=(r=s.next()).done)&&(i.push(r.value),!t||i.length!==t);a=!0);}catch(e){o=!0,n=e}finally{try{!a&&null!=s.return&&s.return()}finally{if(o)throw n}}return i}}(e,t)||function(e,t){if(e){if("string"==typeof e)return c(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(r);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return c(e,t)}}(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var l={};t.Z={data:function(){return{isOpenExposeRecord:n.Z.isFinderSearch,isOpenScrollRecord:!1,exposeInfo:null,scrollReportMap:{}}},watch:{$route:function(e,t){e.name!=o.kO.TEACH&&this._endRecordExpose()}},created:function(){this.isOpenExposeRecord&&s.Z.$on(s.U.TEACH_NEW_EXPOSE,this._onExpose),window.addEventListener("scroll",this._scroll,!1)},beforeDestroy:function(){this.isOpenExposeRecord&&s.Z.$off(s.U.TEACH_NEW_EXPOSE,this._onExpose),this._endRecordExpose(),window.removeEventListener("scroll",this._scroll,!1)},methods:{getPageType:function(){var e,t=this.$router.currentRoute.name;return(u(e={},o.kO.SUG,i.Z.pageType.SUGGESTION),u(e,o.kO.TEACH,i.Z.pageType.TEACH),e)[t]},getSearchId:function(){var e;return this.$router.currentRoute.name==o.kO.SUG?this.$store.state.sug.sugId:null===(e=this.$store.state.teach.teachData)||void 0===e?void 0:e.searchID},_onExpose:function(e){var t=this,r=e.reportMap,n=void 0===r?{}:r,s=e.isCache,a=this.getSearchId(),c=this.$router.currentRoute.name;!s&&Object.keys(n).forEach(function(e){var r=p(e.split("@"),2),u=r[0];r[1];var d=p(u.split("|"),3),m=(d[0],d[1]);d[2],((void 0===m?"":m)==i.Z.teachType.RECOMMEND_BOX||c==o.kO.SUG)&&!l[a]&&(l[a]=!0,t.exposeInfo={itemInfo:u,docInfo:Array.from(new Set(n[e])).join("#"),pageType:t.getPageType(),isCache:s,startExposeTs:Date.now()},t.isOpenScrollRecord=!0,t._startRecordExpose())})},_startRecordExpose:function(){var e=this;this._endRecordExpose(),this.heartbeatTimer=setInterval(function(){var t,r,n=e.$router.currentRoute.name;if(e.$store.state.isWebviewActivated&&(n==o.kO.TEACH||n==o.kO.SUG)&&e.exposeInfo){;a.ZP.teach.commonTeachReport((t=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){u(e,t,r[t])})}return e}({},e.exposeInfo),r=(r={actionId:o.At.EXPOSE_HEARTBEAT_REPORT,extInfo:encodeURIComponent(JSON.stringify({expstaytime:Date.now()-e.exposeInfo.startExposeTs}))},r),Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):(function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r.push.apply(r,n)}return r})(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}),t))}else e._endRecordExpose()},500)},_endRecordExpose:function(){clearInterval(this.heartbeatTimer)},_scroll:function(){this.isOpenScrollRecord&&this.$store.state.isWebviewActivated&&this.$router.currentRoute.name==o.kO.TEACH&&this.exposeInfo&&a.ZP.teach.commonTeachReport({actionId:o.At.RESULT_PAGE_SCROLL,itemInfo:"",docInfo:"",pageType:i.Z.pageType.TEACH}),this.isOpenScrollRecord=!1}}}}}]);