"use strict";(self.webpackChunkweixin_search=self.webpackChunkweixin_search||[]).push([["src_components_general_competition_schedule_vue"],{913036:function(t,e,n){n.r(e);var o=n(226888),i=n(422016),r=n(551900),s=n(776801),a=n(291345),l=(0,r.Z)(i.Z,o.s,o.x,!1,null,"5a8b6565",null);"function"==typeof s.Z&&(0,s.Z)(l),"function"==typeof a.Z&&(0,a.Z)(l),e.default=l.exports},109759:function(t,e,n){n.d(e,{Z:function(){return o}});function o(t){t.options.__ruleConfig={version:6,componentPath:"11b4fca62",componentType:1,rules:[{model:"cnProps.list[$index].extraInfo.jumpInfo",path:".schedule-extraInfo",isReportIdFromJson:!0,aliasValue:"定制数据",isInformal:!0,event:3,type:2},{model:"cnProps.list[$index]",path:".schedule-line",isReportIdFromJson:!0,aliasValue:"赛程",isInformal:!0,event:3,type:2},{model:"cnProps.list[$index].button",path:'[data-vr-id="YkOljrYN7"]',isReportIdFromJson:!0,aliasValue:"赛程按钮",isInformal:!0,event:3,type:2},{model:"cnProps.more",path:".schedule-more",isReportIdFromJson:!0,aliasValue:"全部场次",isInformal:!0,event:3,type:2}]}}},203997:function(t,e,n){n.d(e,{Z:function(){return o}});function o(t){t.options.__wxs_id="08f2caa7"}},776801:function(t,e,n){var o=n(109759);e.Z=o.Z},291345:function(t,e,n){var o=n(203997);e.Z=o.Z},422016:function(t,e,n){var o=n(771981);e.Z=o.Z},226888:function(t,e,n){n.d(e,{s:function(){return o},x:function(){return i}});var o=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{directives:[{name:"resize",rawName:"v-resize",value:t.adjustColumnsWidth,expression:"adjustColumnsWidth"}],attrs:{"data-fcn-schedule":"","data-fr-11b4fca62":""}},[t.source.list?n("div",{directives:[{name:"no-scale",rawName:"v-no-scale"}],staticClass:"competition-schedule",class:{"eliminating-mb":t.needEliminatingMb,"eliminating-mt":t.needEliminatingMt,isOpponent:t.isOpponent},attrs:{role:"listbox"}},t._l(t.source.list,function(e,o){return n("div",{directives:[{name:"active",rawName:"v-active.stop",value:t.canLineClick(e),expression:"canLineClick(line)",modifiers:{stop:!0}}],key:e.title+e.desc+o,ref:"line",refInFor:!0,staticClass:"schedule-line-wrapper",attrs:{"data-cli":""},on:{click:function(n){return n.stopPropagation(),t.onTapLine(e,o)}}},[n("div",{staticClass:"schedule-line",class:["schedule-line--"+t.getLineColumnsCount(e),{"schedule-line--pop":!!t.halfScreenContext}],attrs:{role:"option","data-report-id":t.M_itemReportId(e,o+1)}},[n("div",{staticClass:"line-left"},[n("span",{staticClass:"line-left__title",style:t.getColorStyle(e.title),domProps:{innerHTML:t._s(t.xss(e.title))}}),e.desc?n("span",{staticClass:"line-left__desc",style:t.getColorStyle(e.desc),domProps:{innerHTML:t._s(t.xss(e.desc))}}):t._e()]),e.opponent?n("div",{staticClass:"line-round-wrapper"},t._l(e.opponent,function(i,r){return n("div",{key:i.title+r,staticClass:"line-round"},[n("div",{staticClass:"line-round-spacer"}),n("div",{staticClass:"line-round-content"},[n("div",{staticClass:"line-round__left",class:"line-round__left-"+o},[i.iconUrl?n("ui-image",{staticClass:"country__icon",class:{"country__icon--isPlayer":i.isPlayer,"country__icon--team":i.isTeam},attrs:{url:i.iconUrl,"scale-rpx":"",mode:"plain","data-fc-158ab3f42":""}}):t._e(),n("span",{staticClass:"line-round__title"},t._l(i.title,function(e,o){return n("div",{key:e+o,staticClass:"item"},[n("span",{staticClass:"name"},[t._v(t._s(e))]),o<i.title.length-1?n("span",{staticClass:"and"},[t._v("/")]):t._e()])}),0),n("ui-tags",{staticClass:"line-round__tags",attrs:{gap:"",flex:"",tags:i.tags,"data-fc-158ab3f43":""}})],1),i.gameScore?n("div",{staticClass:"line-game-score"},[n("div",{staticClass:"game-score"},t._l(i.gameScore,function(o,i){return n("div",{key:i,staticClass:"game-score-item",class:t.gameScoreClasses(e,o,i)},[t._v("\n                      "+t._s(o.score)+"\n                      "),o.get7>=0?n("sup",[t._v(t._s(o.get7))]):t._e()])}),0),i.currentScore?n("div",{staticClass:"current-score-wrapper"},[n("div",{staticClass:"current-score"},[t._v("\n                      "+t._s(i.currentScore)+"\n                    ")])]):t._e()]):n("div",{staticClass:"line-game-empty"},[t._v("-")])]),n("div",{staticClass:"line-round-spacer"})])}),0):e.country?n("div",{ref:"lineMiddleCol",refInFor:!0,staticClass:"line-opponent-wrapper"},t._l(e.country,function(e,o){return n("div",{key:e.title+o,staticClass:"line-opponent"},[n("div",{staticClass:"line-opponent__left",class:"line-round__left-"+o},[e.iconUrl?n("ui-image",{staticClass:"country__icon",class:{"country__icon--isPlayer":e.isPlayer,"country__icon--team":e.isTeam},attrs:{url:e.iconUrl,"scale-rpx":"",mode:"plain","data-fc-158ab3f44":""}}):t._e(),n("span",{staticClass:"line-opponent__title"},[t._v(t._s(e.title))]),e.tags&&!t.isBigPoint(e.title)?n("ui-tags",{staticClass:"line-opponent__tags",attrs:{gap:"",flex:"",tags:e.tags,"data-fc-158ab3f45":""}}):t._e()],1),n("div",{staticClass:"line-score-wrapper",class:{"line-score-wrapper--none":t.isNone(e.score)}},[n("div",{staticClass:"line-score",class:t.isAnyBigPoint?"line-score-start":""},[n("span",[t._v(t._s(e.score))]),e.tags&&t.isBigPoint(e.title)?n("ui-tags",{staticClass:"line-opponent__tags",attrs:{gap:"",flex:"",tags:e.tags,"data-fc-158ab3f46":""}}):t._e()],1)])])}),0):e.event?n("div",{ref:"lineMiddleCol",refInFor:!0,staticClass:"line-event"},[n("ui-clamp",{attrs:{autoresize:"","max-lines":2,"vertical-middle":"","raw-html":t.xss(e.event.title),"data-fc-158ab3f47":""},scopedSlots:t._u([{key:"after",fn:function(){return[n("ui-tags",{attrs:{tags:e.event.tags,"vertical-middle":"","data-fc-158ab3f48":""}})]},proxy:!0}],null,!0)}),e.event.country?n("div",{staticClass:"line-event__country"},t._l(e.event.country,function(e,o){return n("div",{key:e.title+o,staticClass:"line-opponent"},[e.iconUrl?n("ui-image",{staticClass:"country__icon",class:{"country__icon--isPlayer":e.isPlayer,"country__icon--team":e.isTeam},attrs:{url:e.iconUrl,"scale-rpx":"",mode:"plain","data-fc-158ab3f49":""}}):t._e(),n("div",{staticClass:"line-opponent__text"},[n("div",{staticClass:"line-opponent__name"},[e.title?n("span",[t._v(t._s(e.title))]):t._e(),e.player?n("span",[t._v("（"+t._s(e.player)+"）")]):t._e()])]),e.score?n("span",{staticClass:"line-opponent__score"},[t._v(t._s(e.score))]):t._e()],1)}),0):t._e()],1):t._e(),t.isHideButton?t._e():n("div",{staticClass:"line-button-wrapper"},[t.getButton(e.button,o)?n("ui-button",{staticClass:"line-button",attrs:{"icon-url":t.getButton(e.button,o).iconUrl,"data-report-id":t.M_itemReportId(t.getButtonReportId(e.button,o),0,o+1+":"+t.M_getItemType(e)),"icon-size":16,type:10,"data-fc-158ab3f4a":""},nativeOn:{click:function(n){n.stopPropagation(),t.onTapButton(t.getButton(e.button,o),o)}}},[t._v("\n              "+t._s(t.getButtonTitle(e.button,o))+"\n            ")]):t._e()],1)]),e.extraInfo?n("div",{directives:[{name:"active",rawName:"v-active.stop",value:e.extraInfo.jumpInfo,expression:"line.extraInfo.jumpInfo",modifiers:{stop:!0}}],staticClass:"schedule-extraInfo active__mask",attrs:{"data-report-id":t.M_itemReportId(e.extraInfo,0,""+(o+1)+t.M_getItemType(e)),"data-cli":""},on:{click:function(n){return n.stopPropagation(),t.M_commonJump(e.extraInfo)}}},[n("span",{staticClass:"schedule-extraInfo-text"},[n("ui-image",{staticClass:"schedule-extraInfo__icon",attrs:{size:16,url:e.extraInfo.iconUrl,"scale-rpx":"",mode:"plain","data-fc-18686ada4":""}}),n("span",{staticClass:"schedule-extraInfo__title"},[t._v(t._s(e.extraInfo.title)+":")]),n("span",{staticClass:"schedule-extraInfo__desc"},[t._v(t._s(e.extraInfo.desc))])],1),e.extraInfo.jumpInfo?n("ui-arrow",{attrs:{align:"flex",direction:"right","no-scale":!0,"data-fc-18686ada2":""}}):t._e()],1):t._e()])}),0):t._e(),t.source.text?n("div",{staticClass:"schedule-empty"},[t._v("\n      "+t._s(t.source.text)+"\n    ")]):t._e(),t.source.more?n("div",{directives:[{name:"active",rawName:"v-active.stop",modifiers:{stop:!0}}],staticClass:"schedule-more active__item",attrs:{"data-report-id":t.M_itemReportId(t.source.more.reportId),"data-cli":""},on:{click:function(e){return e.stopPropagation(),t.onTapMore()}}},[t._v("\n      "+t._s(t.source.more.title)+"\n    ")]):t._e()])},i=[]},771981:function(t,e,n){var o=n(798509),i=n(859096);function r(t,e,n,o,i,r,s){try{var a=t[r](s),l=a.value}catch(t){n(t);return}a.done?e(l):Promise.resolve(l).then(o,i)}function s(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},o=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(o=o.concat(Object.getOwnPropertySymbols(n).filter(function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable}))),o.forEach(function(e){var o,i,r;o=t,i=e,r=n[e],i in o?Object.defineProperty(o,i,{value:r,enumerable:!0,configurable:!0,writable:!0}):o[i]=r})}return t}function a(t,e){return e=null!=e?e:{},Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):(function(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);n.push.apply(n,o)}return n})(Object(e)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(e,n))}),t}e.Z={name:"Schedule",mixins:[o.jB,o.uW,o.Sx],inject:{halfScreenContext:{default:function(){return null}}},props:{shouldShowTabs:{type:Boolean,default:!0}},data:function(){return{buttonMap:{},popDataMap:{},chooseIndexMap:{},isAnyBigPoint:!1,isHideButton:!1}},computed:{needEliminatingMb:function(){var t,e,n;return!(null===(n=this.item.subItems)||void 0===n?void 0:null===(e=n[0])||void 0===e?void 0:null===(t=e.header)||void 0===t?void 0:t.action)},needEliminatingMt:function(){var t,e,n,o=!1,i=null===(t=this.item.subItems)||void 0===t?void 0:t[0];return(null==i?void 0:null===(e=i.header)||void 0===e?void 0:e.type)===5&&(null==i?void 0:null===(n=i.header)||void 0===n?void 0:n.title)&&(o=!0),o=!this.shouldShowTabs},gameScoreClasses:function(){return function(t,e,n){return{"game-score-item-win":e.win,"game-score-get7-long":t.opponent.some(function(t){var e;return t.gameScore&&(null===(e=t.gameScore[n])||void 0===e?void 0:e.get7)>=10}),"game-score-last-long":t.opponent.some(function(t){var e;return t.gameScore&&(null===(e=t.gameScore[n])||void 0===e?void 0:e.score)>=10})}}},isOpponent:function(){return this.source.list[0].opponent}},watch:{source:{handler:function(){var t=this;this.$nextTick(function(){t.adjustColumnsWidth();var e=t.$refs.line,n=void 0;if(t.source.scrollIndex)n=e[t.source.scrollIndex];else{var o=new Date;e&&e.forEach(function(i,r){var s=i.querySelectorAll(".line-opponent__text"),a=i.querySelectorAll(".line-opponent__score");if([s,a].forEach(function(t){if(t){t.forEach(function(t){t.style.width="auto"});var e=0;t.forEach(function(t){var n=Math.ceil(t.getBoundingClientRect().width);n>=e&&(e=n)}),t.forEach(function(t){t.style.width="".concat(e,"px")})}}),t.halfScreenContext&&!1!==t.source.autoScroll&&!n){var l,c,u,p,d,f,v,h=null===(u=t.source)||void 0===u?void 0:null===(c=u.list)||void 0===c?void 0:null===(l=c[r])||void 0===l?void 0:l.title,m=null===(f=t.source)||void 0===f?void 0:null===(d=f.list)||void 0===d?void 0:null===(p=d[r])||void 0===p?void 0:p.desc,g=null===(v=t.halfScreenContext.header)||void 0===v?void 0:v.title;if((null==h?void 0:h.includes("进行中"))||(null==m?void 0:m.includes("进行中")))n=e[r-1]||i;else{var _=/\b(\d{1,2}):(\d{1,2})\b/.exec(h)||/\b(\d{1,2}):(\d{1,2})\b/.exec(m),y=/^(\d+)月(\d+)日$/.exec(h)||/^(\d+)月(\d+)日$/.exec(m)||/^(\d+)月(\d+)日/.exec(g)||/^(\d+)\/(\d+)( \d+:\d+)?$/.exec(h);if(_&&y){var b=new Date(o.getFullYear(),((null==y?void 0:y[1])?Number(null==y?void 0:y[1]):o.getMonth()+1)-1,(null==y?void 0:y[2])?Number(null==y?void 0:y[2]):o.getDate(),null==_?void 0:_[1],null==_?void 0:_[2],0).getTime();!Number.isNaN(b)&&b>o.getTime()&&(n=e[r-1]||i)}}}})}if(!!n)setTimeout(function(){var e,o,i,r=null==n?void 0:n.offsetTop;if(r){;console.warn("top",r,r,(null==n?void 0:null===(o=n.getBoundingClientRect)||void 0===o?void 0:null===(e=o.call(n))||void 0===e?void 0:e.height)||0),null===(i=t.halfScreenContext)||void 0===i||i.scrollTo({top:r,behavior:"smooth"})}},450)})},immediate:!0}},created:function(){},methods:{adjustColumnsWidth:function(){var t=this,e=["line-left","line-opponent-wrapper","line-event","line-button-wrapper","line-score","line-opponent__left","game-score-last-long","game-score-get7-long","current-score","line-round-content"];if(this.source.list&&this.source.list.length)for(var n=0;n<this.source.list.length;n++)this.source.list[n].opponent?e.push("line-round__left-".concat(n)):this.source.list[n].country&&e.push("line-opponent__left-".concat(n));var o=["line-left","line-round-content","line-button-wrapper"],i=[];if(e.forEach(function(e){var n,r=t.$el&&(null===(n=t.$el)||void 0===n?void 0:n.querySelectorAll(".".concat(e)));if(null==r?void 0:r.length){r.forEach(function(t){t.style.width="auto"});var s=0;r.forEach(function(t){var e=Math.ceil(t.getBoundingClientRect().width);e>=s&&(s=e)}),r.forEach(function(t){t.style.width="".concat(s+1,"px")}),o.includes(e)&&i.push(s+1)}}),this.source.list&&this.source.list.length&&this.source.list[0].opponent&&!this.isHideButton){var r=i.reduce(function(t,e){return t-e},this.$el.offsetWidth);(r=r-12-12)<-10&&(this.isHideButton=!0)}},onTapMore:function(){this.source.more&&this.M_commonJump(this.source.more)},getButtonTitle:function(t,e){if(t.isFromLine)return t.title;var n=this.getButton(t,e),o=n.title,i=this.getButtonPop(n);if(t.voteData&&(null==i?void 0:i.type)===31){var r=t.voteData,s=this.chooseIndexMap[e],a=void 0!==s?s:i.chooseIndex;o=void 0!==a?a>0?r.votedTitle:r.unVotedTitle:r.unVotedTitle||n.title}return o||n.title},onTapLine:function(t,e){var n=this.getLineClickOptions(t);this.onTapButton(n,e)},getButtonReportId:function(t,e){var n=this.getButton(t,e);if(n.voteData&&!t.isFromLine){var o=n.voteData,i=n.votePop,r=this.chooseIndexMap[e];return(void 0!==r?r:i.chooseIndex)>0?o.votedReportId:o.unVotedReportId}return n.reportId},getButton:function(t,e){return this.buttonMap[e]||t},getButtonPop:function(t){return(null==t?void 0:t.pop)||(null==t?void 0:t.votePop)},getVotePopMethods:function(t,e){var n=this;return t.votePop?{onSavePopData:function(t){n.$set(n.popDataMap,e,t)},onResumePopData:function(){return n.popDataMap[e]},onChooseIndexChange:function(t){n.$set(n.chooseIndexMap,e,t)},pkParentItemPos:t.popItemPosReportRefix||this.getPopItemPosReportRefix(t,e)}:null},onTapButton:function(t,e){var n;return(n=function(){var n,o,r,l;return function(t,e){var n,o,i,r,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function a(r){return function(a){return function(r){if(n)throw TypeError("Generator is already executing.");for(;s;)try{if(n=1,o&&(i=2&r[0]?o.return:r[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,r[1])).done)return i;switch(o=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return s.label++,{value:r[1],done:!1};case 5:s.label++,o=r[1],r=[0];continue;case 7:r=s.ops.pop(),s.trys.pop();continue;default:if(!(i=(i=s.trys).length>0&&i[i.length-1])&&(6===r[0]||2===r[0])){s=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){s.label=r[1];break}if(6===r[0]&&s.label<i[1]){s.label=i[1],i=r;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(r);break}i[2]&&s.ops.pop(),s.trys.pop();continue}r=e.call(t,s)}catch(t){r=[6,t],o=0}finally{n=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}([r,a])}}}(this,function(c){switch(c.label){case 0:if(!t.cgi)return[3,2];return[4,(0,i.W)({cgiName:t.cgi.cgiName,params:t.cgi.param})];case 1:n=c.sent(),this.$set(this.buttonMap,e,a(s({},null==n?void 0:n.button),{reportId:(null==n?void 0:n.buttonReportId)||t.reportId})),c.label=2;case 2:if(o=this.getButtonPop(t),r=t.jumpInfo||t.askJumpInfo,l={clickContent:this.getButtonTitle(t,e),reportId:this.getButtonReportId(t,e)},!r&&!o)return this.M_clickReport(l,t),[2];if(o){if(this.halfScreenContext&&o.isNested)return this.$emit("tap:target",{target:{pop:o}}),[2];this.M_commonJump(a(s({},t),{pop:s({},o,this.getVotePopMethods(t,e))}),l)}else this.M_commonJump(a(s({},t),{jumpInfo:r}),l);return[2]}})},function(){var t=this,e=arguments;return new Promise(function(o,i){var s=n.apply(t,e);function a(t){r(s,o,i,a,l,"next",t)}function l(t){r(s,o,i,a,l,"throw",t)}a(void 0)})}).apply(this)},isNone:function(t){return"-"===t},canLeftClick:function(t){return!!t.leftJumpInfo||!1},canLineClick:function(t){return!!this.getLineClickOptions(t)},getLineClickOptions:function(t){if(!t)return null;if(t.jumpInfo||t.askJumpInfo||this.getButtonPop(t))return t;var e=t.button;return e&&(e.jumpInfo||e.askJumpInfo||this.getButtonPop(e))?a(s({},e),{title:t.title,reportId:t.reportId,isFromLine:!0}):null},getPopItemPosReportRefix:function(t,e){return this.M_getItemPos(this.getButtonReportId(t,e))},getColorStyle:function(t){if(!t)return{};var e=t.match(/font-color-([A-Z]+)/),n=(null==e?void 0:e[1])||null;if(n)return{color:n,"font-weight":500}},getLineColumnsCount:function(t){var e=0;return(t.title||t.desc)&&e++,(t.country||t.event||t.opponent)&&e++,t.button&&e++,e},isBigPoint:function(t){var e=/\([^)]*\)/;return!this.isAnyBigPoint&&(this.isAnyBigPoint=e.test(t)),e.test(t)}}}},859096:function(t,e,n){n.d(e,{W:function(){return a}});var o=n(798509),i=n(462474),r=n(728223);function s(t,e,n,o,i,r,s){try{var a=t[r](s),l=a.value}catch(t){n(t);return}a.done?e(l):Promise.resolve(l).then(o,i)}function a(t){return l.apply(this,arguments)}function l(){var t;return t=function(t){var e,n,s,a,l;return function(t,e){var n,o,i,r,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function a(r){return function(a){return function(r){if(n)throw TypeError("Generator is already executing.");for(;s;)try{if(n=1,o&&(i=2&r[0]?o.return:r[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,r[1])).done)return i;switch(o=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return s.label++,{value:r[1],done:!1};case 5:s.label++,o=r[1],r=[0];continue;case 7:r=s.ops.pop(),s.trys.pop();continue;default:if(!(i=(i=s.trys).length>0&&i[i.length-1])&&(6===r[0]||2===r[0])){s=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){s.label=r[1];break}if(6===r[0]&&s.label<i[1]){s.label=i[1],i=r;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(r);break}i[2]&&s.ops.pop(),s.trys.pop();continue}r=e.call(t,s)}catch(t){r=[6,t],o=0}finally{n=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}([r,a])}}}(this,function(c){switch(c.label){case 0:if(e=t.cgiName,n=t.params,a=void 0===(s=t.toast)||s,!e)return console.error("cgiName is required"),[2];return!n&&console.warn("cgiParams is undefined"),[4,o.hi.getCommonCgiData({cgiName:e,data:n})];case 1:return l=c.sent(),a&&l.successMsg&&i.Z.$emit(o.U3.showToast,{action:r.g.pure,text:l.successMsg}),[2,l]}})},(l=function(){var e=this,n=arguments;return new Promise(function(o,i){var r=t.apply(e,n);function a(t){s(r,o,i,a,l,"next",t)}function l(t){s(r,o,i,a,l,"throw",t)}a(void 0)})}).apply(this,arguments)}}}]);