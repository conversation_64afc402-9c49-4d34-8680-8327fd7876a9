"use strict";(self.webpackChunkweixin_search=self.webpackChunkweixin_search||[]).push([["96-red-pocket-cover-list"],{521278:function(t,e,r){r.r(e);var i=r(549191),o=r(34482),n=r(551900),a=r(669192),c=(0,n.Z)(o.Z,i.s,i.x,!1,null,"717be80b",null);"function"==typeof a.Z&&(0,a.Z)(c),e.default=c.exports},601779:function(t,e,r){r.d(e,{Z:function(){return i}});function i(t){t.options.__wxs_id="83b2e7bb"}},110567:function(t,e,r){var i=r(758227),o=r(616261),n=r(551900),a=r(719253),c=(0,n.Z)(o.Z,i.s,i.x,!1,null,"47a48c21",null);"function"==typeof a.Z&&(0,a.Z)(c),e.Z=c.exports},603658:function(t,e,r){r.d(e,{Z:function(){return i}});function i(t){t.options.__wxs_id="374e3682"}},479358:function(t,e,r){var i=r(321173),o=r(278089),n=r(551900),a=r(914601),c=(0,n.Z)(o.Z,i.s,i.x,!1,null,"ff9a25b8",null);"function"==typeof a.Z&&(0,a.Z)(c),e.Z=c.exports},568746:function(t,e,r){r.d(e,{Z:function(){return i}});function i(t){t.options.__wxs_id="a87aa854"}},669192:function(t,e,r){var i=r(601779);e.Z=i.Z},719253:function(t,e,r){var i=r(603658);e.Z=i.Z},914601:function(t,e,r){var i=r(568746);e.Z=i.Z},34482:function(t,e,r){var i=r(336255);e.Z=i.Z},616261:function(t,e,r){var i=r(982408);e.Z=i.Z},278089:function(t,e,r){var i=r(200089);e.Z=i.Z},549191:function(t,e,r){r.d(e,{s:function(){return i},x:function(){return o}});var i=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("ui-scroll",{directives:[{name:"active",rawName:"v-active.stop",modifiers:{stop:!0}}],staticClass:"red-pocket-cover-list",class:t.listClass,attrs:{items:t.list,"data-fc-104e9583a":"","data-cli":"","data-fcn-red-pocket-cover-list":"","data-fr-139f702d7":""},on:{scroll:t.onScroll},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.item,o=e.index;return[i.moreObj?r("div",{staticClass:"red-pocket-cover-list__more"},[r("div",{directives:[{name:"active",rawName:"v-active.stop",modifiers:{stop:!0}}],staticClass:"red-pocket-cover-list__more-inner active__mask",attrs:{"data-report-id":t.M_itemReportId(i.moreObj,o+1),"data-cli":""},on:{click:function(e){return e.stopPropagation(),t.onTapMore(e)}}},[r("div",{staticClass:"arrow-circle"},[r("ui-arrow",{attrs:{"data-fc-104e9583b":""}})],1),r("div",{staticClass:"title"},[t._v(t._s(i.moreObj.title))])])]):r("div",{directives:[{name:"active",rawName:"v-active.stop",modifiers:{stop:!0}}],staticClass:"red-pocket-cover-list__item active__mask",attrs:{"data-report-id":t.M_itemReportId(i,o+1),"data-cli":""},on:{click:function(e){return e.stopPropagation(),t.onTapItem(i)}}},[r("div",{staticClass:"image"},[r("red-pocket-cover",{attrs:{"image-url":i.imageUrl,"widget-image-url":i.widgetImageUrl,"show-open":"","data-fc-104e9583c":""}})],1),r("div",{staticClass:"content"},[r("div",{staticClass:"content__title"},[t._v(t._s(i.title))]),r("red-pocket-cover-tags",{attrs:{tags:i.recommendTag,"start-time":i.startTime,template:"%s抢",block:!0,single:t.list.length>2,"data-fc-104e9583d":""}})],1)])]}}])})},o=[]},758227:function(t,e,r){r.d(e,{s:function(){return i},x:function(){return o}});var i=function(){var t=this,e=t.$createElement,r=t._self._c||e;return t.block?r("div",{staticClass:"red-pocket-cover-tags red-pocket-cover-tags--block",attrs:{"data-fcn-red-pocket-cover-tags":"","data-fr-1d5bd1bdc":""}},t._l(t.renderTags,function(e,i){return r("ui-tag",t._b({key:i,attrs:{"data-fc-10f0cd05f":""}},"ui-tag",e,!1))}),1):r("ui-tags",{staticClass:"red-pocket-cover-tags",attrs:{tags:t.renderTags,flex:"","data-fc-10f0cd060":"","data-fcn-red-pocket-cover-tags":"","data-fr-1d5bd1bdc":""}})},o=[]},321173:function(t,e,r){r.d(e,{s:function(){return i},x:function(){return o}});var i=function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"red-pocket-cover",attrs:{"data-fcn-red-pocket-cover":"","data-fr-1ef9c5538":""}},[e("div",{directives:[{name:"image",rawName:"v-image",value:this.imageUrl,expression:"imageUrl"}],staticClass:"red-pocket-cover__rec-image"}),this.widgetImageUrl?e("div",{directives:[{name:"image",rawName:"v-image:plain",value:this.widgetImageUrl,expression:"widgetImageUrl",arg:"plain"}],staticClass:"red-pocket-cover__rec-widget-image"}):this._e(),this.showOpen?e("div",{directives:[{name:"image",rawName:"v-image:plain",value:"https://bs.qpic.cn/mmux/wprMnqDUJH441RYW7CgMHUQ1W0tH1iaB8ibzY0oQZ95MxCuWxgzxXMFCia5XEVmBHPugroMleseHxQ/",expression:"'https://bs.qpic.cn/mmux/wprMnqDUJH441RYW7CgMHUQ1W0tH1iaB8ibzY0oQZ95MxCuWxgzxXMFCia5XEVmBHPugroMleseHxQ/'",arg:"plain"}],staticClass:"red-pocket-cover__open"}):this._e()])},o=[]},336255:function(t,e,r){var i=r(798509),o=r(479358),n=r(110567),a=r(737133),c=r(984928),s=r(136525);e.Z={name:"RedPocketCoverList",components:{RedPocketCoverTags:n.Z,RedPocketCover:o.Z,UiScroll:a.Z},mixins:[i.uW,i.jB],props:{data:{type:Object,default:function(){return{}}},item:{type:Object,default:function(){return{}}},source:{type:Object,default:function(){return{}}},pos:{type:Number,default:0},typePos:{type:Number,default:0}},computed:{list:function(){var t,e=this.source.list||[];return(null===(t=this.source.moreObj)||void 0===t?void 0:t.title)?e.concat([{moreObj:this.source.moreObj}]):e},listClass:function(){var t;return{"red-pocket-cover-list--horizontal":this.list.length<=2,"red-pocket-cover-list--single":1===this.list.length,"red-pocket-cover-list--double":2===this.list.length,"red-pocket-cover-list--tripple":3===this.list.length&&3===this.maxCount,"red-pocket-cover-list--scrollable":this.list.length>=3&&this.maxCount>4,"red-pocket-cover-list--has-more":!!(null===(t=this.source.moreObj)||void 0===t?void 0:t.title)}},maxCount:function(){var t=this,e=0;return this.data.items.forEach(function(r){var i=t.M_getItemInfo(r)||{};i.type===s.jJ.redPocketCoverList&&(e=Math.max(e,(i.list||[]).length))}),e}},methods:{onTapItem:function(t){var e,r,o=t.startTime>i.Zr.getSvrTimeSecs()&&t.previewPop?(e=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{},i=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(r).filter(function(t){return Object.getOwnPropertyDescriptor(r,t).enumerable}))),i.forEach(function(e){var i,o,n;i=t,o=e,n=r[e],o in i?Object.defineProperty(i,o,{value:n,enumerable:!0,configurable:!0,writable:!0}):i[o]=n})}return t}({},t),r=(r={pop:t.previewPop,jumpInfo:null},r),Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):(function(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);r.push.apply(r,i)}return r})(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}),e):t;this.M_serviceSearchGo(o),this.M_clickReport({clickContent:o.title||""},o)},onTapMore:function(){var t,e;this.M_serviceSearchGo(this.source.header),this.M_clickReport({clickContent:(null===(t=this.source.moreObj)||void 0===t?void 0:t.title)||"",reportId:null===(e=this.source.moreObj)||void 0===e?void 0:e.reportId},this.source.header)},onScroll:function(){var t=this;this.scrollTimer&&(clearTimeout(this.scrollTimer),this.scrollTimer=0),this.scrollTimer=setTimeout(function(){var e;i.Gc.$emit(i.U3.resultExposeAnalysis),t.M_clickReport({ban12721Report:!0,banAdReport:!0,actionType:c.At.HOR_TOUCHMOVE,clickContent:(null===(e=t.source.header)||void 0===e?void 0:e.title)||""})},300)}}}},982408:function(t,e,r){var i=r(798509);function o(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,i=Array(e);r<e;r++)i[r]=t[r];return i}var n=function(t){return 0===t?"00":t<10?"0".concat(t):t};e.Z={name:"RedPocketCoverTags",props:{tags:{type:Array},startTime:{type:Number},template:{type:String,default:"%s"},block:{type:Boolean,default:!1},single:{type:Boolean,default:!1}},data:function(){return{previewTags:[{type:3,title:"",noBg:!0}]}},computed:{renderTags:function(){var t,e,r,i=[];if((null===(e=this.previewTags[0])||void 0===e?void 0:e.title)&&i.push(this.previewTags[0]),null===(r=this.tags)||void 0===r?void 0:r.length){;i.push.apply(i,function(t){if(Array.isArray(t))return o(t)}(t=this.tags)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return o(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(r);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return o(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())}return this.single?i.slice(0,1):i}},created:function(){this.check()},mounted:function(){var t=this;this._timer=setInterval(function(){t.check()},1e3)},beforeDestroy:function(){this._timer&&(clearInterval(this._timer),this._timer=null)},methods:{check:function(){var t=1e3*this.startTime,e=i.Zr.getSvrTimeMs();if(!t||e>=t){this.previewTags[0].title="";return}var r=new Date(t),o=new Date(e),a=new Date(o).setHours(0,0,0,0)+864e5===new Date(r).setHours(0,0,0,0),c=new Date(o).setHours(0,0,0,0)===new Date(r).setHours(0,0,0,0),s="",l=r.getMonth()+1,u=r.getDate(),d=n(r.getHours()),p=n(r.getMinutes());s=a?"明天".concat(d,":").concat(p):c?"今天".concat(d,":").concat(p):"".concat(l,"月").concat(u,"日").concat(d,":").concat(p),this.previewTags[0].title=this.template.replace("%s",s)}}}},200089:function(t,e){e.Z={name:"RedPocketCover",props:{imageUrl:{type:String},widgetImageUrl:{type:String},showOpen:{type:Boolean,default:!1}}}}}]);