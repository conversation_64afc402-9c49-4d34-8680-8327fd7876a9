(self.webpackChunkweixin_search=self.webpackChunkweixin_search||[]).push([["vendors-node_modules_cropperjs_dist_cropper_js"],{433129:function(t){var e,i;e=0,i=function(){"use strict";function t(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),i.push.apply(i,a)}return i}function e(e){for(var a=1;a<arguments.length;a++){var n=null!=arguments[a]?arguments[a]:{};a%2?t(Object(n),!0).forEach(function(t){(function(t,e,a){(e=i(e))in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a})(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):t(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function i(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var a=i.call(t,e||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}function a(t){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function n(t,e){for(var a=0;a<e.length;a++){var n=e[a];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,i(n.key),n)}}function o(t){return function(t){if(Array.isArray(t))return r(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return r(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);if("Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i)return Array.from(t);if("Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return r(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function r(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,a=Array(e);i<e;i++)a[i]=t[i];return a}var h="undefined"!=typeof window&&void 0!==window.document,s=h?window:{},c=!!h&&!!s.document.documentElement&&"ontouchstart"in s.document.documentElement,l=!!h&&"PointerEvent"in s,d="cropper",p="crop",u="move",m="zoom",g="".concat(d,"-crop"),f="".concat(d,"-disabled"),v="".concat(d,"-hidden"),w="".concat(d,"-hide"),b="".concat(d,"-invisible"),y="".concat(d,"-modal"),x="".concat(d,"-move"),M="".concat(d,"Action"),C="".concat(d,"Preview"),D="crop",k="move",B="none",O="crop",T="cropend",E="cropmove",W="cropstart",H="dblclick",N=l?"pointerdown":c?"touchstart":"mousedown",L=l?"pointermove":c?"touchmove":"mousemove",z=l?"pointerup pointercancel":c?"touchend touchcancel":"mouseup",Y="ready",X="resize",R="wheel",S="zoom",j="image/jpeg",A=/^e|w|s|n|se|sw|ne|nw|all|crop|move|zoom$/,P=/^data:/,I=/^data:image\/jpeg;base64,/,U=/^img|canvas$/i,q={viewMode:0,dragMode:D,initialAspectRatio:NaN,aspectRatio:NaN,data:null,preview:"",responsive:!0,restore:!0,checkCrossOrigin:!0,checkOrientation:!0,modal:!0,guides:!0,center:!0,highlight:!0,background:!0,autoCrop:!0,autoCropArea:.8,movable:!0,rotatable:!0,scalable:!0,zoomable:!0,zoomOnTouch:!0,zoomOnWheel:!0,wheelZoomRatio:.1,cropBoxMovable:!0,cropBoxResizable:!0,toggleDragModeOnDblclick:!0,minCanvasWidth:0,minCanvasHeight:0,minCropBoxWidth:0,minCropBoxHeight:0,minContainerWidth:200,minContainerHeight:100,ready:null,cropstart:null,cropmove:null,cropend:null,crop:null,zoom:null},$=Number.isNaN||s.isNaN;function _(t){return"number"==typeof t&&!$(t)}var Q=function(t){return t>0&&t<1/0};function K(t){return void 0===t}function Z(t){return"object"===a(t)&&null!==t}var G=Object.prototype.hasOwnProperty;function V(t){if(!Z(t))return!1;try{var e=t.constructor,i=e.prototype;return e&&i&&G.call(i,"isPrototypeOf")}catch(t){return!1}}function F(t){return"function"==typeof t}var J=Array.prototype.slice;function tt(t){return Array.from?Array.from(t):J.call(t)}function te(t,e){return t&&F(e)&&(Array.isArray(t)||_(t.length)?tt(t).forEach(function(i,a){e.call(t,i,a,t)}):Z(t)&&Object.keys(t).forEach(function(i){e.call(t,t[i],i,t)})),t}var ti=Object.assign||function(t){for(var e=arguments.length,i=Array(e>1?e-1:0),a=1;a<e;a++)i[a-1]=arguments[a];return Z(t)&&i.length>0&&i.forEach(function(e){Z(e)&&Object.keys(e).forEach(function(i){t[i]=e[i]})}),t},ta=/\.\d*(?:0|9){12}\d*$/;function tn(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1e11;return ta.test(t)?Math.round(t*e)/e:t}var to=/^width|height|left|top|marginLeft|marginTop$/;function tr(t,e){var i=t.style;te(e,function(t,e){to.test(e)&&_(t)&&(t="".concat(t,"px")),i[e]=t})}function th(t,e){if(!!e){if(_(t.length)){te(t,function(t){th(t,e)});return}if(t.classList){t.classList.add(e);return}var i=t.className.trim();i?0>i.indexOf(e)&&(t.className="".concat(i," ").concat(e)):t.className=e}}function ts(t,e){if(!!e){if(_(t.length)){te(t,function(t){ts(t,e)});return}if(t.classList){t.classList.remove(e);return}t.className.indexOf(e)>=0&&(t.className=t.className.replace(e,""))}}function tc(t,e,i){if(!!e){if(_(t.length)){te(t,function(t){tc(t,e,i)});return}i?th(t,e):ts(t,e)}}var tl=/([a-z\d])([A-Z])/g;function td(t){return t.replace(tl,"$1-$2").toLowerCase()}function tp(t,e){return Z(t[e])?t[e]:t.dataset?t.dataset[e]:t.getAttribute("data-".concat(td(e)))}function tu(t,e,i){Z(i)?t[e]=i:t.dataset?t.dataset[e]=i:t.setAttribute("data-".concat(td(e)),i)}var tm=/\s\s*/,tg=function(){var t=!1;if(h){var e=!1,i=function(){},a=Object.defineProperty({},"once",{get:function(){return t=!0,e},set:function(t){e=t}});s.addEventListener("test",i,a),s.removeEventListener("test",i,a)}return t}();function tf(t,e,i){var a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},n=i;e.trim().split(tm).forEach(function(e){if(!tg){var o=t.listeners;o&&o[e]&&o[e][i]&&(n=o[e][i],delete o[e][i],0===Object.keys(o[e]).length&&delete o[e],0===Object.keys(o).length&&delete t.listeners)}t.removeEventListener(e,n,a)})}function tv(t,e,i){var a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},n=i;e.trim().split(tm).forEach(function(e){if(a.once&&!tg){var o=t.listeners,r=void 0===o?{}:o;n=function(){delete r[e][i],t.removeEventListener(e,n,a);for(var o=arguments.length,h=Array(o),s=0;s<o;s++)h[s]=arguments[s];i.apply(t,h)},!r[e]&&(r[e]={}),r[e][i]&&t.removeEventListener(e,r[e][i],a),r[e][i]=n,t.listeners=r}t.addEventListener(e,n,a)})}function tw(t,e,i){var a;return F(Event)&&F(CustomEvent)?a=new CustomEvent(e,{detail:i,bubbles:!0,cancelable:!0}):(a=document.createEvent("CustomEvent")).initCustomEvent(e,!0,!0,i),t.dispatchEvent(a)}function tb(t){var e=t.getBoundingClientRect();return{left:e.left+(window.pageXOffset-document.documentElement.clientLeft),top:e.top+(window.pageYOffset-document.documentElement.clientTop)}}var ty=s.location,tx=/^(\w+:)\/\/([^:/?#]*):?(\d*)/i;function tM(t){var e=t.match(tx);return null!==e&&(e[1]!==ty.protocol||e[2]!==ty.hostname||e[3]!==ty.port)}function tC(t){var e="timestamp=".concat(new Date().getTime());return t+(-1===t.indexOf("?")?"?":"&")+e}function tD(t){var e=t.rotate,i=t.scaleX,a=t.scaleY,n=t.translateX,o=t.translateY,r=[];_(n)&&0!==n&&r.push("translateX(".concat(n,"px)")),_(o)&&0!==o&&r.push("translateY(".concat(o,"px)")),_(e)&&0!==e&&r.push("rotate(".concat(e,"deg)")),_(i)&&1!==i&&r.push("scaleX(".concat(i,")")),_(a)&&1!==a&&r.push("scaleY(".concat(a,")"));var h=r.length?r.join(" "):"none";return{WebkitTransform:h,msTransform:h,transform:h}}function tk(t,i){var a=t.pageX,n=t.pageY,o={endX:a,endY:n};return i?o:e({startX:a,startY:n},o)}function tB(t){var e=t.aspectRatio,i=t.height,a=t.width,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"contain",o=Q(a),r=Q(i);if(o&&r){var h=i*e;"contain"===n&&h>a||"cover"===n&&h<a?i=a/e:a=i*e}else o?i=a/e:r&&(a=i*e);return{width:a,height:i}}var tO=String.fromCharCode,tT=/^data:.*,/,tE=s.Cropper,tW=function(){var t,e,i;function a(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,a),!t||!U.test(t.tagName))throw Error("The first argument is required and must be an <img> or <canvas> element.");this.element=t,this.options=ti({},q,V(e)&&e),this.cropped=!1,this.disabled=!1,this.pointers={},this.ready=!1,this.reloading=!1,this.replaced=!1,this.sized=!1,this.sizing=!1,this.init()}return t=a,e=[{key:"init",value:function(){var t,e=this.element,i=e.tagName.toLowerCase();if(!e[d]){if(e[d]=this,"img"===i){if(this.isImg=!0,t=e.getAttribute("src")||"",this.originalUrl=t,!t)return;t=e.src}else"canvas"===i&&window.HTMLCanvasElement&&(t=e.toDataURL());this.load(t)}}},{key:"load",value:function(t){var e,i,a,n=this;if(!!t){this.url=t,this.imageData={};var o=this.element,r=this.options;if(!r.rotatable&&!r.scalable&&(r.checkOrientation=!1),!r.checkOrientation||!window.ArrayBuffer){this.clone();return}if(P.test(t)){if(I.test(t)){;this.read((te(a=new Uint8Array(i=new ArrayBuffer((e=atob(t.replace(tT,""))).length)),function(t,i){a[i]=e.charCodeAt(i)}),i))}else this.clone();return}var h=new XMLHttpRequest,s=this.clone.bind(this);this.reloading=!0,this.xhr=h,h.onabort=s,h.onerror=s,h.ontimeout=s,h.onprogress=function(){h.getResponseHeader("content-type")!==j&&h.abort()},h.onload=function(){n.read(h.response)},h.onloadend=function(){n.reloading=!1,n.xhr=null},r.checkCrossOrigin&&tM(t)&&o.crossOrigin&&(t=tC(t)),h.open("GET",t,!0),h.responseType="arraybuffer",h.withCredentials="use-credentials"===o.crossOrigin,h.send()}}},{key:"read",value:function(t){var e=this.options,i=this.imageData,a=function(t){var e=new DataView(t);try{if(255===e.getUint8(0)&&216===e.getUint8(1)){for(var i=e.byteLength,a=2;a+1<i;){if(255===e.getUint8(a)&&225===e.getUint8(a+1)){l=a;break}a+=1}}if(l){var n=l+4,o=l+10;if("Exif"===function(t,e,i){var a="";i+=e;for(var n=e;n<i;n+=1)a+=tO(t.getUint8(n));return a}(e,n,4)){var r=e.getUint16(o);if(((c=18761===r)||19789===r)&&42===e.getUint16(o+2,c)){var h=e.getUint32(o+4,c);h>=8&&(d=o+h)}}}if(d){var s,c,l,d,p,u,m=e.getUint16(d,c);for(u=0;u<m;u+=1)if(p=d+12*u+2,274===e.getUint16(p,c)){p+=8,s=e.getUint16(p,c),e.setUint16(p,1,c);break}}}catch(t){s=1}return s}(t),n=0,o=1,r=1;if(a>1){this.url=function(t,e){for(var i=[],a=new Uint8Array(t);a.length>0;)i.push(tO.apply(null,tt(a.subarray(0,8192)))),a=a.subarray(8192);return"data:".concat(e,";base64,").concat(btoa(i.join("")))}(t,j);var h=function(t){var e=0,i=1,a=1;switch(t){case 2:i=-1;break;case 3:e=-180;break;case 4:a=-1;break;case 5:e=90,a=-1;break;case 6:e=90;break;case 7:e=90,i=-1;break;case 8:e=-90}return{rotate:e,scaleX:i,scaleY:a}}(a);n=h.rotate,o=h.scaleX,r=h.scaleY}e.rotatable&&(i.rotate=n),e.scalable&&(i.scaleX=o,i.scaleY=r),this.clone()}},{key:"clone",value:function(){var t=this.element,e=this.url,i=t.crossOrigin,a=e;this.options.checkCrossOrigin&&tM(e)&&(!i&&(i="anonymous"),a=tC(e)),this.crossOrigin=i,this.crossOriginUrl=a;var n=document.createElement("img");i&&(n.crossOrigin=i),n.src=a||e,n.alt=t.alt||"The image to crop",this.image=n,n.onload=this.start.bind(this),n.onerror=this.stop.bind(this),th(n,w),t.parentNode.insertBefore(n,t.nextSibling)}},{key:"start",value:function(){var t=this,e=this.image;e.onload=null,e.onerror=null,this.sizing=!0;var i=s.navigator&&/(?:iPad|iPhone|iPod).*?AppleWebKit/i.test(s.navigator.userAgent),a=function(e,i){ti(t.imageData,{naturalWidth:e,naturalHeight:i,aspectRatio:e/i}),t.initialImageData=ti({},t.imageData),t.sizing=!1,t.sized=!0,t.build()};if(e.naturalWidth&&!i){a(e.naturalWidth,e.naturalHeight);return}var n=document.createElement("img"),o=document.body||document.documentElement;this.sizingImage=n,n.onload=function(){a(n.width,n.height),!i&&o.removeChild(n)},n.src=e.src,!i&&(n.style.cssText="left:0;max-height:none!important;max-width:none!important;min-height:0!important;min-width:0!important;opacity:0;position:absolute;top:0;z-index:-1;",o.appendChild(n))}},{key:"stop",value:function(){var t=this.image;t.onload=null,t.onerror=null,t.parentNode.removeChild(t),this.image=null}},{key:"build",value:function(){if(!!this.sized&&!this.ready){var t=this.element,e=this.options,i=this.image,a=t.parentNode,n=document.createElement("div");n.innerHTML='<div class="cropper-container" touch-action="none"><div class="cropper-wrap-box"><div class="cropper-canvas"></div></div><div class="cropper-drag-box"></div><div class="cropper-crop-box"><span class="cropper-view-box"></span><span class="cropper-dashed dashed-h"></span><span class="cropper-dashed dashed-v"></span><span class="cropper-center"></span><span class="cropper-face"></span><span class="cropper-line line-e" data-cropper-action="e"></span><span class="cropper-line line-n" data-cropper-action="n"></span><span class="cropper-line line-w" data-cropper-action="w"></span><span class="cropper-line line-s" data-cropper-action="s"></span><span class="cropper-point point-e" data-cropper-action="e"></span><span class="cropper-point point-n" data-cropper-action="n"></span><span class="cropper-point point-w" data-cropper-action="w"></span><span class="cropper-point point-s" data-cropper-action="s"></span><span class="cropper-point point-ne" data-cropper-action="ne"></span><span class="cropper-point point-nw" data-cropper-action="nw"></span><span class="cropper-point point-sw" data-cropper-action="sw"></span><span class="cropper-point point-se" data-cropper-action="se"></span></div></div>';var o=n.querySelector(".".concat(d,"-container")),r=o.querySelector(".".concat(d,"-canvas")),h=o.querySelector(".".concat(d,"-drag-box")),s=o.querySelector(".".concat(d,"-crop-box")),c=s.querySelector(".".concat(d,"-face"));this.container=a,this.cropper=o,this.canvas=r,this.dragBox=h,this.cropBox=s,this.viewBox=o.querySelector(".".concat(d,"-view-box")),this.face=c,r.appendChild(i),th(t,v),a.insertBefore(o,t.nextSibling),ts(i,w),this.initPreview(),this.bind(),e.initialAspectRatio=Math.max(0,e.initialAspectRatio)||NaN,e.aspectRatio=Math.max(0,e.aspectRatio)||NaN,e.viewMode=Math.max(0,Math.min(3,Math.round(e.viewMode)))||0,th(s,v),!e.guides&&th(s.getElementsByClassName("".concat(d,"-dashed")),v),!e.center&&th(s.getElementsByClassName("".concat(d,"-center")),v),e.background&&th(o,"".concat(d,"-bg")),!e.highlight&&th(c,b),e.cropBoxMovable&&(th(c,x),tu(c,M,"all")),!e.cropBoxResizable&&(th(s.getElementsByClassName("".concat(d,"-line")),v),th(s.getElementsByClassName("".concat(d,"-point")),v)),this.render(),this.ready=!0,this.setDragMode(e.dragMode),e.autoCrop&&this.crop(),this.setData(e.data),F(e.ready)&&tv(t,Y,e.ready,{once:!0}),tw(t,Y)}}},{key:"unbuild",value:function(){if(!!this.ready){this.ready=!1,this.unbind(),this.resetPreview();var t=this.cropper.parentNode;t&&t.removeChild(this.cropper),ts(this.element,v)}}},{key:"uncreate",value:function(){this.ready?(this.unbuild(),this.ready=!1,this.cropped=!1):this.sizing?(this.sizingImage.onload=null,this.sizing=!1,this.sized=!1):this.reloading?(this.xhr.onabort=null,this.xhr.abort()):this.image&&this.stop()}}],i=[{key:"noConflict",value:function(){return window.Cropper=tE,a}},{key:"setDefaults",value:function(t){ti(q,V(t)&&t)}}],e&&n(t.prototype,e),i&&n(t,i),Object.defineProperty(t,"prototype",{writable:!1}),t}();return ti(tW.prototype,{render:function(){this.initContainer(),this.initCanvas(),this.initCropBox(),this.renderCanvas(),this.cropped&&this.renderCropBox()},initContainer:function(){var t=this.element,e=this.options,i=this.container,a=this.cropper,n=Number(e.minContainerWidth),o=Number(e.minContainerHeight);th(a,v),ts(t,v);var r={width:Math.max(i.offsetWidth,n>=0?n:200),height:Math.max(i.offsetHeight,o>=0?o:100)};this.containerData=r,tr(a,{width:r.width,height:r.height}),th(t,v),ts(a,v)},initCanvas:function(){var t=this.containerData,e=this.imageData,i=this.options.viewMode,a=Math.abs(e.rotate)%180==90,n=a?e.naturalHeight:e.naturalWidth,o=a?e.naturalWidth:e.naturalHeight,r=n/o,h=t.width,s=t.height;t.height*r>t.width?3===i?h=t.height*r:s=t.width/r:3===i?s=t.width/r:h=t.height*r;var c={aspectRatio:r,naturalWidth:n,naturalHeight:o,width:h,height:s};this.canvasData=c,this.limited=1===i||2===i,this.limitCanvas(!0,!0),c.width=Math.min(Math.max(c.width,c.minWidth),c.maxWidth),c.height=Math.min(Math.max(c.height,c.minHeight),c.maxHeight),c.left=(t.width-c.width)/2,c.top=(t.height-c.height)/2,c.oldLeft=c.left,c.oldTop=c.top,this.initialCanvasData=ti({},c)},limitCanvas:function(t,e){var i=this.options,a=this.containerData,n=this.canvasData,o=this.cropBoxData,r=i.viewMode,h=n.aspectRatio,s=this.cropped&&o;if(t){var c=Number(i.minCanvasWidth)||0,l=Number(i.minCanvasHeight)||0;r>1?(c=Math.max(c,a.width),l=Math.max(l,a.height),3===r&&(l*h>c?c=l*h:l=c/h)):r>0&&(c?c=Math.max(c,s?o.width:0):l?l=Math.max(l,s?o.height:0):s&&(c=o.width,(l=o.height)*h>c?c=l*h:l=c/h));var d=tB({aspectRatio:h,width:c,height:l});c=d.width,l=d.height,n.minWidth=c,n.minHeight=l,n.maxWidth=1/0,n.maxHeight=1/0}if(e){if(r>(s?0:1)){var p=a.width-n.width,u=a.height-n.height;n.minLeft=Math.min(0,p),n.minTop=Math.min(0,u),n.maxLeft=Math.max(0,p),n.maxTop=Math.max(0,u),s&&this.limited&&(n.minLeft=Math.min(o.left,o.left+(o.width-n.width)),n.minTop=Math.min(o.top,o.top+(o.height-n.height)),n.maxLeft=o.left,n.maxTop=o.top,2===r&&(n.width>=a.width&&(n.minLeft=Math.min(0,p),n.maxLeft=Math.max(0,p)),n.height>=a.height&&(n.minTop=Math.min(0,u),n.maxTop=Math.max(0,u))))}else n.minLeft=-n.width,n.minTop=-n.height,n.maxLeft=a.width,n.maxTop=a.height}},renderCanvas:function(t,e){var i=this.canvasData,a=this.imageData;if(e){var n=function(t){var e=t.width,i=t.height,a=t.degree;if(90==(a=Math.abs(a)%180))return{width:i,height:e};var n=a%90*Math.PI/180,o=Math.sin(n),r=Math.cos(n),h=e*r+i*o,s=e*o+i*r;return a>90?{width:s,height:h}:{width:h,height:s}}({width:a.naturalWidth*Math.abs(a.scaleX||1),height:a.naturalHeight*Math.abs(a.scaleY||1),degree:a.rotate||0}),o=n.width,r=n.height,h=i.width*(o/i.naturalWidth),s=i.height*(r/i.naturalHeight);i.left-=(h-i.width)/2,i.top-=(s-i.height)/2,i.width=h,i.height=s,i.aspectRatio=o/r,i.naturalWidth=o,i.naturalHeight=r,this.limitCanvas(!0,!1)}(i.width>i.maxWidth||i.width<i.minWidth)&&(i.left=i.oldLeft),(i.height>i.maxHeight||i.height<i.minHeight)&&(i.top=i.oldTop),i.width=Math.min(Math.max(i.width,i.minWidth),i.maxWidth),i.height=Math.min(Math.max(i.height,i.minHeight),i.maxHeight),this.limitCanvas(!1,!0),i.left=Math.min(Math.max(i.left,i.minLeft),i.maxLeft),i.top=Math.min(Math.max(i.top,i.minTop),i.maxTop),i.oldLeft=i.left,i.oldTop=i.top,tr(this.canvas,ti({width:i.width,height:i.height},tD({translateX:i.left,translateY:i.top}))),this.renderImage(t),this.cropped&&this.limited&&this.limitCropBox(!0,!0)},renderImage:function(t){var e=this.canvasData,i=this.imageData,a=i.naturalWidth*(e.width/e.naturalWidth),n=i.naturalHeight*(e.height/e.naturalHeight);ti(i,{width:a,height:n,left:(e.width-a)/2,top:(e.height-n)/2}),tr(this.image,ti({width:i.width,height:i.height},tD(ti({translateX:i.left,translateY:i.top},i)))),t&&this.output()},initCropBox:function(){var t=this.options,e=this.canvasData,i=t.aspectRatio||t.initialAspectRatio,a=Number(t.autoCropArea)||.8,n={width:e.width,height:e.height};i&&(e.height*i>e.width?n.height=n.width/i:n.width=n.height*i),this.cropBoxData=n,this.limitCropBox(!0,!0),n.width=Math.min(Math.max(n.width,n.minWidth),n.maxWidth),n.height=Math.min(Math.max(n.height,n.minHeight),n.maxHeight),n.width=Math.max(n.minWidth,n.width*a),n.height=Math.max(n.minHeight,n.height*a),n.left=e.left+(e.width-n.width)/2,n.top=e.top+(e.height-n.height)/2,n.oldLeft=n.left,n.oldTop=n.top,this.initialCropBoxData=ti({},n)},limitCropBox:function(t,e){var i=this.options,a=this.containerData,n=this.canvasData,o=this.cropBoxData,r=this.limited,h=i.aspectRatio;if(t){var s=Number(i.minCropBoxWidth)||0,c=Number(i.minCropBoxHeight)||0,l=r?Math.min(a.width,n.width,n.width+n.left,a.width-n.left):a.width,d=r?Math.min(a.height,n.height,n.height+n.top,a.height-n.top):a.height;s=Math.min(s,a.width),c=Math.min(c,a.height),h&&(s&&c?c*h>s?c=s/h:s=c*h:s?c=s/h:c&&(s=c*h),d*h>l?d=l/h:l=d*h),o.minWidth=Math.min(s,l),o.minHeight=Math.min(c,d),o.maxWidth=l,o.maxHeight=d}e&&(r?(o.minLeft=Math.max(0,n.left),o.minTop=Math.max(0,n.top),o.maxLeft=Math.min(a.width,n.left+n.width)-o.width,o.maxTop=Math.min(a.height,n.top+n.height)-o.height):(o.minLeft=0,o.minTop=0,o.maxLeft=a.width-o.width,o.maxTop=a.height-o.height))},renderCropBox:function(){var t=this.options,e=this.containerData,i=this.cropBoxData;(i.width>i.maxWidth||i.width<i.minWidth)&&(i.left=i.oldLeft),(i.height>i.maxHeight||i.height<i.minHeight)&&(i.top=i.oldTop),i.width=Math.min(Math.max(i.width,i.minWidth),i.maxWidth),i.height=Math.min(Math.max(i.height,i.minHeight),i.maxHeight),this.limitCropBox(!1,!0),i.left=Math.min(Math.max(i.left,i.minLeft),i.maxLeft),i.top=Math.min(Math.max(i.top,i.minTop),i.maxTop),i.oldLeft=i.left,i.oldTop=i.top,t.movable&&t.cropBoxMovable&&tu(this.face,M,i.width>=e.width&&i.height>=e.height?u:"all"),tr(this.cropBox,ti({width:i.width,height:i.height},tD({translateX:i.left,translateY:i.top}))),this.cropped&&this.limited&&this.limitCanvas(!0,!0),!this.disabled&&this.output()},output:function(){this.preview(),tw(this.element,O,this.getData())}},{initPreview:function(){var t=this.element,e=this.crossOrigin,i=this.options.preview,a=e?this.crossOriginUrl:this.url,n=t.alt||"The image to preview",o=document.createElement("img");if(e&&(o.crossOrigin=e),o.src=a,o.alt=n,this.viewBox.appendChild(o),this.viewBoxImage=o,!!i){var r=i;"string"==typeof i?r=t.ownerDocument.querySelectorAll(i):i.querySelector&&(r=[i]),this.previews=r,te(r,function(t){var i=document.createElement("img");tu(t,C,{width:t.offsetWidth,height:t.offsetHeight,html:t.innerHTML}),e&&(i.crossOrigin=e),i.src=a,i.alt=n,i.style.cssText='display:block;width:100%;height:auto;min-width:0!important;min-height:0!important;max-width:none!important;max-height:none!important;image-orientation:0deg!important;"',t.innerHTML="",t.appendChild(i)})}},resetPreview:function(){te(this.previews,function(t){var e=tp(t,C);tr(t,{width:e.width,height:e.height}),t.innerHTML=e.html,!function(t,e){if(Z(t[e]))try{delete t[e]}catch(i){t[e]=void 0}else if(t.dataset)try{delete t.dataset[e]}catch(i){t.dataset[e]=void 0}else t.removeAttribute("data-".concat(td(e)))}(t,C)})},preview:function(){var t=this.imageData,e=this.canvasData,i=this.cropBoxData,a=i.width,n=i.height,o=t.width,r=t.height,h=i.left-e.left-t.left,s=i.top-e.top-t.top;if(!!this.cropped&&!this.disabled)tr(this.viewBoxImage,ti({width:o,height:r},tD(ti({translateX:-h,translateY:-s},t)))),te(this.previews,function(e){var i=tp(e,C),c=i.width,l=i.height,d=c,p=l,u=1;a&&(p=n*(u=c/a)),n&&p>l&&(d=a*(u=l/n),p=l),tr(e,{width:d,height:p}),tr(e.getElementsByTagName("img")[0],ti({width:o*u,height:r*u},tD(ti({translateX:-h*u,translateY:-s*u},t))))})}},{bind:function(){var t=this.element,e=this.options,i=this.cropper;F(e.cropstart)&&tv(t,W,e.cropstart),F(e.cropmove)&&tv(t,E,e.cropmove),F(e.cropend)&&tv(t,T,e.cropend),F(e.crop)&&tv(t,O,e.crop),F(e.zoom)&&tv(t,S,e.zoom),tv(i,N,this.onCropStart=this.cropStart.bind(this)),e.zoomable&&e.zoomOnWheel&&tv(i,R,this.onWheel=this.wheel.bind(this),{passive:!1,capture:!0}),e.toggleDragModeOnDblclick&&tv(i,H,this.onDblclick=this.dblclick.bind(this)),tv(t.ownerDocument,L,this.onCropMove=this.cropMove.bind(this)),tv(t.ownerDocument,z,this.onCropEnd=this.cropEnd.bind(this)),e.responsive&&tv(window,X,this.onResize=this.resize.bind(this))},unbind:function(){var t=this.element,e=this.options,i=this.cropper;F(e.cropstart)&&tf(t,W,e.cropstart),F(e.cropmove)&&tf(t,E,e.cropmove),F(e.cropend)&&tf(t,T,e.cropend),F(e.crop)&&tf(t,O,e.crop),F(e.zoom)&&tf(t,S,e.zoom),tf(i,N,this.onCropStart),e.zoomable&&e.zoomOnWheel&&tf(i,R,this.onWheel,{passive:!1,capture:!0}),e.toggleDragModeOnDblclick&&tf(i,H,this.onDblclick),tf(t.ownerDocument,L,this.onCropMove),tf(t.ownerDocument,z,this.onCropEnd),e.responsive&&tf(window,X,this.onResize)}},{resize:function(){if(!this.disabled){var t,e,i=this.options,a=this.container,n=this.containerData,o=a.offsetWidth/n.width,r=a.offsetHeight/n.height,h=Math.abs(o-1)>Math.abs(r-1)?o:r;1!==h&&(i.restore&&(t=this.getCanvasData(),e=this.getCropBoxData()),this.render(),i.restore&&(this.setCanvasData(te(t,function(e,i){t[i]=e*h})),this.setCropBoxData(te(e,function(t,i){e[i]=t*h}))))}},dblclick:function(){var t,e;if(!this.disabled&&this.options.dragMode!==B)this.setDragMode((t=this.dragBox,e=g,t.classList?t.classList.contains(e):t.className.indexOf(e)>-1)?k:D)},wheel:function(t){var e=this,i=Number(this.options.wheelZoomRatio)||.1,a=1;if(this.disabled)return;if(t.preventDefault(),!this.wheeling)this.wheeling=!0,setTimeout(function(){e.wheeling=!1},50),t.deltaY?a=t.deltaY>0?1:-1:t.wheelDelta?a=-t.wheelDelta/120:t.detail&&(a=t.detail>0?1:-1),this.zoom(-a*i,t)},cropStart:function(t){var e,i=t.buttons,a=t.button;if(this.disabled||("mousedown"===t.type||"pointerdown"===t.type&&"mouse"===t.pointerType)&&(_(i)&&1!==i||_(a)&&0!==a||t.ctrlKey))return;var n=this.options,o=this.pointers;if(t.changedTouches?te(t.changedTouches,function(t){o[t.identifier]=tk(t)}):o[t.pointerId||0]=tk(t),e=Object.keys(o).length>1&&n.zoomable&&n.zoomOnTouch?m:tp(t.target,M),!!A.test(e)&&!1!==tw(this.element,W,{originalEvent:t,action:e}))t.preventDefault(),this.action=e,this.cropping=!1,e===p&&(this.cropping=!0,th(this.dragBox,y))},cropMove:function(t){var e=this.action;if(this.disabled||!e)return;var i=this.pointers;if(t.preventDefault(),!1!==tw(this.element,E,{originalEvent:t,action:e}))t.changedTouches?te(t.changedTouches,function(t){ti(i[t.identifier]||{},tk(t,!0))}):ti(i[t.pointerId||0]||{},tk(t,!0)),this.change(t)},cropEnd:function(t){if(this.disabled)return;var e=this.action,i=this.pointers;if(t.changedTouches?te(t.changedTouches,function(t){delete i[t.identifier]}):delete i[t.pointerId||0],!!e)t.preventDefault(),!Object.keys(i).length&&(this.action=""),this.cropping&&(this.cropping=!1,tc(this.dragBox,y,this.cropped&&this.options.modal)),tw(this.element,T,{originalEvent:t,action:e})}},{change:function(t){var i,a,n,o,r=this.options,h=this.canvasData,s=this.containerData,c=this.cropBoxData,l=this.pointers,d=this.action,g=r.aspectRatio,f=c.left,w=c.top,b=c.width,y=c.height,x=f+b,M=w+y,C=0,D=0,k=s.width,B=s.height,O=!0;!g&&t.shiftKey&&(g=b&&y?b/y:1),this.limited&&(C=c.minLeft,D=c.minTop,k=C+Math.min(s.width,h.width,h.left+h.width),B=D+Math.min(s.height,h.height,h.top+h.height));var T=l[Object.keys(l)[0]],E={x:T.endX-T.startX,y:T.endY-T.startY},W=function(t){switch(t){case"e":x+E.x>k&&(E.x=k-x);break;case"w":f+E.x<C&&(E.x=C-f);break;case"n":w+E.y<D&&(E.y=D-w);break;case"s":M+E.y>B&&(E.y=B-M)}};switch(d){case"all":f+=E.x,w+=E.y;break;case"e":if(E.x>=0&&(x>=k||g&&(w<=D||M>=B))){O=!1;break}W("e"),(b+=E.x)<0&&(d="w",f-=b=-b),g&&(y=b/g,w+=(c.height-y)/2);break;case"n":if(E.y<=0&&(w<=D||g&&(f<=C||x>=k))){O=!1;break}W("n"),y-=E.y,w+=E.y,y<0&&(d="s",w-=y=-y),g&&(b=y*g,f+=(c.width-b)/2);break;case"w":if(E.x<=0&&(f<=C||g&&(w<=D||M>=B))){O=!1;break}W("w"),b-=E.x,f+=E.x,b<0&&(d="e",f-=b=-b),g&&(y=b/g,w+=(c.height-y)/2);break;case"s":if(E.y>=0&&(M>=B||g&&(f<=C||x>=k))){O=!1;break}W("s"),(y+=E.y)<0&&(d="n",w-=y=-y),g&&(b=y*g,f+=(c.width-b)/2);break;case"ne":if(g){if(E.y<=0&&(w<=D||x>=k)){O=!1;break}W("n"),y-=E.y,w+=E.y,b=y*g}else W("n"),W("e"),E.x>=0?x<k?b+=E.x:E.y<=0&&w<=D&&(O=!1):b+=E.x,E.y<=0?w>D&&(y-=E.y,w+=E.y):(y-=E.y,w+=E.y);b<0&&y<0?(d="sw",b=-b,w-=y=-y,f-=b):b<0?(d="nw",f-=b=-b):y<0&&(d="se",w-=y=-y);break;case"nw":if(g){if(E.y<=0&&(w<=D||f<=C)){O=!1;break}W("n"),y-=E.y,w+=E.y,b=y*g,f+=c.width-b}else W("n"),W("w"),E.x<=0?f>C?(b-=E.x,f+=E.x):E.y<=0&&w<=D&&(O=!1):(b-=E.x,f+=E.x),E.y<=0?w>D&&(y-=E.y,w+=E.y):(y-=E.y,w+=E.y);b<0&&y<0?(d="se",b=-b,w-=y=-y,f-=b):b<0?(d="ne",f-=b=-b):y<0&&(d="sw",w-=y=-y);break;case"sw":if(g){if(E.x<=0&&(f<=C||M>=B)){O=!1;break}W("w"),b-=E.x,f+=E.x,y=b/g}else W("s"),W("w"),E.x<=0?f>C?(b-=E.x,f+=E.x):E.y>=0&&M>=B&&(O=!1):(b-=E.x,f+=E.x),E.y>=0?M<B&&(y+=E.y):y+=E.y;b<0&&y<0?(d="ne",b=-b,w-=y=-y,f-=b):b<0?(d="se",f-=b=-b):y<0&&(d="nw",w-=y=-y);break;case"se":if(g){if(E.x>=0&&(x>=k||M>=B)){O=!1;break}W("e"),b+=E.x,y=b/g}else W("s"),W("e"),E.x>=0?x<k?b+=E.x:E.y>=0&&M>=B&&(O=!1):b+=E.x,E.y>=0?M<B&&(y+=E.y):y+=E.y;b<0&&y<0?(d="nw",b=-b,w-=y=-y,f-=b):b<0?(d="sw",f-=b=-b):y<0&&(d="ne",w-=y=-y);break;case u:this.move(E.x,E.y),O=!1;break;case m:;this.zoom((a=e({},i=l),n=0,te(i,function(t,e){delete a[e],te(a,function(e){var i=Math.abs(t.startX-e.startX),a=Math.abs(t.startY-e.startY),o=Math.abs(t.endX-e.endX),r=Math.abs(t.endY-e.endY),h=Math.sqrt(i*i+a*a),s=(Math.sqrt(o*o+r*r)-h)/h;Math.abs(s)>Math.abs(n)&&(n=s)})}),n),t),O=!1;break;case p:if(!E.x||!E.y){O=!1;break}o=tb(this.cropper),f=T.startX-o.left,w=T.startY-o.top,b=c.minWidth,y=c.minHeight,E.x>0?d=E.y>0?"se":"ne":E.x<0&&(f-=b,d=E.y>0?"sw":"nw"),E.y<0&&(w-=y),!this.cropped&&(ts(this.cropBox,v),this.cropped=!0,this.limited&&this.limitCropBox(!0,!0))}O&&(c.width=b,c.height=y,c.left=f,c.top=w,this.action=d,this.renderCropBox()),te(l,function(t){t.startX=t.endX,t.startY=t.endY})}},{crop:function(){return this.ready&&!this.cropped&&!this.disabled&&(this.cropped=!0,this.limitCropBox(!0,!0),this.options.modal&&th(this.dragBox,y),ts(this.cropBox,v),this.setCropBoxData(this.initialCropBoxData)),this},reset:function(){return this.ready&&!this.disabled&&(this.imageData=ti({},this.initialImageData),this.canvasData=ti({},this.initialCanvasData),this.cropBoxData=ti({},this.initialCropBoxData),this.renderCanvas(),this.cropped&&this.renderCropBox()),this},clear:function(){return this.cropped&&!this.disabled&&(ti(this.cropBoxData,{left:0,top:0,width:0,height:0}),this.cropped=!1,this.renderCropBox(),this.limitCanvas(!0,!0),this.renderCanvas(),ts(this.dragBox,y),th(this.cropBox,v)),this},replace:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return!this.disabled&&t&&(this.isImg&&(this.element.src=t),e?(this.url=t,this.image.src=t,this.ready&&(this.viewBoxImage.src=t,te(this.previews,function(e){e.getElementsByTagName("img")[0].src=t}))):(this.isImg&&(this.replaced=!0),this.options.data=null,this.uncreate(),this.load(t))),this},enable:function(){return this.ready&&this.disabled&&(this.disabled=!1,ts(this.cropper,f)),this},disable:function(){return this.ready&&!this.disabled&&(this.disabled=!0,th(this.cropper,f)),this},destroy:function(){var t=this.element;return t[d]?(t[d]=void 0,this.isImg&&this.replaced&&(t.src=this.originalUrl),this.uncreate(),this):this},move:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,i=this.canvasData,a=i.left,n=i.top;return this.moveTo(K(t)?t:a+Number(t),K(e)?e:n+Number(e))},moveTo:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,i=this.canvasData,a=!1;return t=Number(t),e=Number(e),this.ready&&!this.disabled&&this.options.movable&&(_(t)&&(i.left=t,a=!0),_(e)&&(i.top=e,a=!0),a&&this.renderCanvas(!0)),this},zoom:function(t,e){var i=this.canvasData;return t=(t=Number(t))<0?1/(1-t):1+t,this.zoomTo(i.width*t/i.naturalWidth,null,e)},zoomTo:function(t,e,i){var a=this.options,n=this.canvasData,o=n.width,r=n.height,h=n.naturalWidth,s=n.naturalHeight;if((t=Number(t))>=0&&this.ready&&!this.disabled&&a.zoomable){var c=h*t,l=s*t;if(!1===tw(this.element,S,{ratio:t,oldRatio:o/h,originalEvent:i}))return this;if(i){var d,p,u,m=this.pointers,g=tb(this.cropper);var f=m&&Object.keys(m).length?(d=0,p=0,u=0,te(m,function(t){var e=t.startX,i=t.startY;d+=e,p+=i,u+=1}),d/=u,p/=u,{pageX:d,pageY:p}):{pageX:i.pageX,pageY:i.pageY};n.left-=(c-o)*((f.pageX-g.left-n.left)/o),n.top-=(l-r)*((f.pageY-g.top-n.top)/r)}else V(e)&&_(e.x)&&_(e.y)?(n.left-=(c-o)*((e.x-n.left)/o),n.top-=(l-r)*((e.y-n.top)/r)):(n.left-=(c-o)/2,n.top-=(l-r)/2);n.width=c,n.height=l,this.renderCanvas(!0)}return this},rotate:function(t){return this.rotateTo((this.imageData.rotate||0)+Number(t))},rotateTo:function(t){return _(t=Number(t))&&this.ready&&!this.disabled&&this.options.rotatable&&(this.imageData.rotate=t%360,this.renderCanvas(!0,!0)),this},scaleX:function(t){var e=this.imageData.scaleY;return this.scale(t,_(e)?e:1)},scaleY:function(t){var e=this.imageData.scaleX;return this.scale(_(e)?e:1,t)},scale:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,i=this.imageData,a=!1;return t=Number(t),e=Number(e),this.ready&&!this.disabled&&this.options.scalable&&(_(t)&&(i.scaleX=t,a=!0),_(e)&&(i.scaleY=e,a=!0),a&&this.renderCanvas(!0,!0)),this},getData:function(){var t,e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],i=this.options,a=this.imageData,n=this.canvasData,o=this.cropBoxData;if(this.ready&&this.cropped){t={x:o.left-n.left,y:o.top-n.top,width:o.width,height:o.height};var r=a.width/a.naturalWidth;if(te(t,function(e,i){t[i]=e/r}),e){var h=Math.round(t.y+t.height),s=Math.round(t.x+t.width);t.x=Math.round(t.x),t.y=Math.round(t.y),t.width=s-t.x,t.height=h-t.y}}else t={x:0,y:0,width:0,height:0};return i.rotatable&&(t.rotate=a.rotate||0),i.scalable&&(t.scaleX=a.scaleX||1,t.scaleY=a.scaleY||1),t},setData:function(t){var e=this.options,i=this.imageData,a=this.canvasData,n={};if(this.ready&&!this.disabled&&V(t)){var o=!1;e.rotatable&&_(t.rotate)&&t.rotate!==i.rotate&&(i.rotate=t.rotate,o=!0),e.scalable&&(_(t.scaleX)&&t.scaleX!==i.scaleX&&(i.scaleX=t.scaleX,o=!0),_(t.scaleY)&&t.scaleY!==i.scaleY&&(i.scaleY=t.scaleY,o=!0)),o&&this.renderCanvas(!0,!0);var r=i.width/i.naturalWidth;_(t.x)&&(n.left=t.x*r+a.left),_(t.y)&&(n.top=t.y*r+a.top),_(t.width)&&(n.width=t.width*r),_(t.height)&&(n.height=t.height*r),this.setCropBoxData(n)}return this},getContainerData:function(){return this.ready?ti({},this.containerData):{}},getImageData:function(){return this.sized?ti({},this.imageData):{}},getCanvasData:function(){var t=this.canvasData,e={};return this.ready&&te(["left","top","width","height","naturalWidth","naturalHeight"],function(i){e[i]=t[i]}),e},setCanvasData:function(t){var e=this.canvasData,i=e.aspectRatio;return this.ready&&!this.disabled&&V(t)&&(_(t.left)&&(e.left=t.left),_(t.top)&&(e.top=t.top),_(t.width)?(e.width=t.width,e.height=t.width/i):_(t.height)&&(e.height=t.height,e.width=t.height*i),this.renderCanvas(!0)),this},getCropBoxData:function(){var t,e=this.cropBoxData;return this.ready&&this.cropped&&(t={left:e.left,top:e.top,width:e.width,height:e.height}),t||{}},setCropBoxData:function(t){var e,i,a=this.cropBoxData,n=this.options.aspectRatio;return this.ready&&this.cropped&&!this.disabled&&V(t)&&(_(t.left)&&(a.left=t.left),_(t.top)&&(a.top=t.top),_(t.width)&&t.width!==a.width&&(e=!0,a.width=t.width),_(t.height)&&t.height!==a.height&&(i=!0,a.height=t.height),n&&(e?a.height=a.width/n:i&&(a.width=a.height*n)),this.renderCropBox()),this},getCroppedCanvas:function(){var t,e,i,a,n,r,h,s,c,l,d,p,u,m,g,f,v,w,b,y,x,M,C,D,k,B,O,T,E,W,H,N,L,z,Y,X,R,S,j,A,P=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!this.ready||!window.HTMLCanvasElement)return null;var I=this.canvasData;var U=(t=this.image,e=this.imageData,i=I,a=P,n=e.aspectRatio,r=e.naturalWidth,h=e.naturalHeight,s=e.rotate,c=e.scaleX,l=e.scaleY,d=i.aspectRatio,p=i.naturalWidth,u=i.naturalHeight,m=a.fillColor,g=a.imageSmoothingEnabled,f=a.imageSmoothingQuality,w=void 0===(v=a.maxWidth)?1/0:v,y=void 0===(b=a.maxHeight)?1/0:b,M=void 0===(x=a.minWidth)?0:x,D=void 0===(C=a.minHeight)?0:C,B=(k=document.createElement("canvas")).getContext("2d"),O=tB({aspectRatio:d,width:w,height:y}),T=tB({aspectRatio:d,width:M,height:D},"cover"),E=Math.min(O.width,Math.max(T.width,p)),W=Math.min(O.height,Math.max(T.height,u)),H=tB({aspectRatio:n,width:w,height:y}),N=tB({aspectRatio:n,width:M,height:D},"cover"),L=Math.min(H.width,Math.max(N.width,r)),z=Math.min(H.height,Math.max(N.height,h)),k.width=tn(E),k.height=tn(W),B.fillStyle=void 0===m?"transparent":m,B.fillRect(0,0,E,W),B.save(),B.translate(E/2,W/2),B.rotate((void 0===s?0:s)*Math.PI/180),B.scale(void 0===c?1:c,void 0===l?1:l),B.imageSmoothingEnabled=void 0===g||g,B.imageSmoothingQuality=void 0===f?"low":f,B.drawImage.apply(B,[t].concat(o([-L/2,-z/2,L,z].map(function(t){return Math.floor(tn(t))})))),B.restore(),k);if(!this.cropped)return U;var q=this.getData(P.rounded),$=q.x,_=q.y,Q=q.width,K=q.height,Z=U.width/Math.floor(I.naturalWidth);1!==Z&&($*=Z,_*=Z,Q*=Z,K*=Z);var G=Q/K,V=tB({aspectRatio:G,width:P.maxWidth||1/0,height:P.maxHeight||1/0}),F=tB({aspectRatio:G,width:P.minWidth||0,height:P.minHeight||0},"cover"),J=tB({aspectRatio:G,width:P.width||(1!==Z?U.width:Q),height:P.height||(1!==Z?U.height:K)}),tt=J.width,te=J.height;tt=Math.min(V.width,Math.max(F.width,tt)),te=Math.min(V.height,Math.max(F.height,te));var ti=document.createElement("canvas"),ta=ti.getContext("2d");ti.width=tn(tt),ti.height=tn(te),ta.fillStyle=P.fillColor||"transparent",ta.fillRect(0,0,tt,te);var to=P.imageSmoothingEnabled,tr=P.imageSmoothingQuality;ta.imageSmoothingEnabled=void 0===to||to,tr&&(ta.imageSmoothingQuality=tr);var th=U.width,ts=U.height,tc=$,tl=_;tc<=-Q||tc>th?(tc=0,Y=0,R=0,j=0):tc<=0?(R=-tc,j=Y=Math.min(th,Q+(tc=0))):tc<=th&&(R=0,j=Y=Math.min(Q,th-tc)),Y<=0||tl<=-K||tl>ts?(tl=0,X=0,S=0,A=0):tl<=0?(S=-tl,A=X=Math.min(ts,K+(tl=0))):tl<=ts&&(S=0,A=X=Math.min(K,ts-tl));var td=[tc,tl,Y,X];if(j>0&&A>0){var tp=tt/Q;td.push(R*tp,S*tp,j*tp,A*tp)}return ta.drawImage.apply(ta,[U].concat(o(td.map(function(t){return Math.floor(tn(t))})))),ti},setAspectRatio:function(t){var e=this.options;return!this.disabled&&!K(t)&&(e.aspectRatio=Math.max(0,t)||NaN,this.ready&&(this.initCropBox(),this.cropped&&this.renderCropBox())),this},setDragMode:function(t){var e=this.options,i=this.dragBox,a=this.face;if(this.ready&&!this.disabled){var n=t===D,o=e.movable&&t===k;t=n||o?t:B,e.dragMode=t,tu(i,M,t),tc(i,g,n),tc(i,x,o),!e.cropBoxMovable&&(tu(a,M,t),tc(a,g,n),tc(a,x,o))}return this}}),tW},t.exports=i()}}]);