"use strict";(self.webpackChunkweixin_search=self.webpackChunkweixin_search||[]).push([["src_libs_member-info-mixin_js-src_libs_status_vote-mixin_js"],{382907:function(e,t,r){var n=r(462474),o=r(798509),i=r(448188),a=r(679017),c=r(491262),s=a.Z._getAvatar;t.Z={data:function(){return{displayNameMap:{},avatarMap:{}}},mounted:function(){var e=this;n.Z.$on(n.U.CLIENT_NAME_DONE,function(t){console.log("CLIENT_NAME_DONE",t),e.displayNameMap=Object.assign({},e.displayNameMap,t)}),i.ZP.onImageReady(function(t){console.log("onImageReady",t),e.avatarMap=Object.assign({},e.avatarMap,t)})},methods:{M_getMemberInfo:function(e){var t="string"==typeof e?e:e.username;(0,c.Z)({userName:t}),s({id:t,userName:t})},M_getMemberInfoList:function(e){var t=this;if(!Array.isArray(e))return console.error("memberList is not array");e.forEach(function(e){t.M_getMemberInfo(e)})},M_getAvatar:function(e){return this.avatarMap[e.username]||e.avatar||(o.Zr.isDarkModeRef()?"https://res.wx.qq.com/t/fed_upload/98bf15ab-9c2d-446a-abd7-4b7b1b07a691/Placeholder_dm.svg":"https://res.wx.qq.com/t/fed_upload/98bf15ab-9c2d-446a-abd7-4b7b1b07a691/Placeholder_lm.svg")},M_getDisplayName:function(e){return this.displayNameMap[e.username]||e.name},M_getJumpInfos:function(e){var t=this;return e.map(function(e){return{iconUrl:t.M_getAvatar(e),title:t.M_getDisplayName(e),noJump:!0,thumbType:"small-user",reportId:e.reportId,jumpInfo:{jumpType:29,username:e.username}}}).filter(function(e){return!!e.title})}}}},677009:function(e,t,r){var n=r(798509),o=r(984928),i=r(728223);function a(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function c(e,t,r,n,o,i,a){try{var c=e[i](a),s=c.value}catch(e){r(e);return}c.done?t(s):Promise.resolve(s).then(n,o)}function s(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function u(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function l(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){u(e,t,r[t])})}return e}function p(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):(function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r.push.apply(r,n)}return r})(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}),e}function f(e){return function(e){if(Array.isArray(e))return a(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return a(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(r);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return a(e,t)}}(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var d=function(){var e,t,r;function n(e){!function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n),u(this,"code",0),this.code=e}return e=n,t=[{key:"isSuccess",value:function(){return!this.code||this.isIgnore()}},{key:"isIgnore",value:function(){return this.code>0}},{key:"needToHandleRsp",value:function(){return!this.code}},{key:"isFail",value:function(){return this.code<0}}],s(e.prototype,t),n}();t.Z={mixins:[n.jB,n.uW],methods:{M_vote:function(e){var t,r=e.itemInfo,a=e.cgi,s=e.disable,u=e.choose,l=e.info,p=e.pop,f=e.reportPrefix,m=e.onVoteStart,g=e.onVoteEnd,h=e.itemPos,I=e.isShowStatusDialog,y=void 0===I||I;return(t=function(){var e,t,c,I,v,b,_;return function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function c(i){return function(c){return function(i){if(r)throw TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(o=2&i[0]?n.return:i[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,n=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],n=0}finally{r=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,c])}}}(this,function(M){switch(M.label){case 0:if(e=l||(null==r?void 0:r.info),t=p||(null==r?void 0:r.pop),!(c=a||(null==r?void 0:r.cgi))||s)return console.log("vote cant click"),[2];return I=u,[4,null==m?void 0:m()];case 1:M.sent(),M.label=2;case 2:return M.trys.push([2,10,,12]),[4,n.hi.getCommonCgiData({cgiName:c.name,data:c.params})];case 3:if(v=M.sent(),console.log("vote cgi:",{cgiName:c.name,param:c.params,rsp:v}),b=!v.errCode,!(v.errCode<0))return[3,5];return[4,null==g?void 0:g({result:new d(v.errCode),chooseTarget:r})];case 4:return M.sent(),n.Gc.$emit(n.U3.showToast,{action:i.g.pure,text:v.errMsg||"投票失败"}),[3,9];case 5:if(!(0===v.errCode||void 0===v.errCode))return[3,7];return v.selfInfo&&!v.selfInfo.reportId&&(v.selfInfo.reportId="".concat(encodeURIComponent(v.selfInfo.username),":friend_list:").concat(n.Zr.getRandom(6))),[4,null==g?void 0:g({data:v,isCancel:I,result:new d(v.errCode),chooseTarget:r})];case 6:return M.sent(),[3,9];case 7:return[4,null==g?void 0:g({isCancel:I,result:new d(v.errCode),chooseTarget:r})];case 8:M.sent(),M.label=9;case 9:if(I)return this._getReportFn()({clickContent:null==e?void 0:e.title,actionType:o.At.CLICK_CANCEL_VOTE,reportId:null==e?void 0:e.reportId,itemPos:h}),_=r.successMsg||v.successMsg,b&&_&&n.Gc.$emit(n.U3.showToast,{action:i.g.pure,text:_}),[2];return y&&this._popStatusDialog({pop:t,info:e,reportPrefix:f}),this._getReportFn()({clickContent:null==e?void 0:e.title,actionType:o.At.CLICK_VOTE,reportId:null==e?void 0:e.reportId,itemPos:h}),[3,12];case 10:return console.error("pk cgi error: ",M.sent()),[4,null==g?void 0:g()];case 11:return M.sent(),[3,12];case 12:return[2]}})},function(){var e=this,r=arguments;return new Promise(function(n,o){var i=t.apply(e,r);function a(e){c(i,n,o,a,s,"next",e)}function s(e){c(i,n,o,a,s,"throw",e)}a(void 0)})}).apply(this)},_popStatusDialog:function(e){var t,r,n,i,a=this,c=e.pop,s=e.info,u=e.reportPrefix;if(!!c){var f=(null===(t=c.close)||void 0===t?void 0:t.reportId)||c.closeReportId,d=(null===(r=c.confirm)||void 0===r?void 0:r.reportId)||c.confirmReportId;this._getGoFn()({pop:p(l({},c),{confirm:p(l({},c.confirm||{}),{title:(null===(n=c.confirm)||void 0===n?void 0:n.title)||(null===(i=c.confirm)||void 0===i?void 0:i.text),reportId:d,itemPos:"".concat(u,":").concat(this.M_getItemType(s.reportId),"|1:").concat(this.M_getItemType(d)),actionType:o.At.GO_PUBLISH_TEXT_STATE}),close:{reportId:f,itemPos:"".concat(u,":").concat(this.M_getItemType(s.reportId),"|0:").concat(this.M_getItemType(f))}})},this.M_getClickZonePath(),function(e,t){if(!!e)if("function"==typeof t)t(o.At.STATUS_PUBLISH_SUCCESS,"".concat(u,":").concat(a.M_getItemType(s.reportId),"|1:").concat(a.M_getItemType(d),"|1:button"));else{var r;a._getReportFn()({clickContent:null===(r=a.itemInfo.info)||void 0===r?void 0:r.title,actionType:o.At.STATUS_PUBLISH_SUCCESS,reportId:d,itemPos:"".concat(u,":").concat(a.M_getItemType(s.reportId),"|1:").concat(a.M_getItemType(d),"|1:button")})}})}},M_popAvatarListDialog:function(e){var t=this,r=e.jumpInfos,n=e.pop,i=e.reportPrefix,a=e.reportId,c=e.itemPos;if(!!n&&!(r.length<1))this._getGoFn()({popTitle:"".concat(r.length,"个朋友").concat(null==n?void 0:n.title),closeBtnReportId:n.closeReportId,closeBtnItemPos:"".concat(i,":").concat(this.M_getItemType(a),"|0:").concat(this.M_getItemType(n.closeReportId)),jumpInfos:r.map(function(e){return p(l({},e),{itemPos:"".concat(i,":").concat(t.M_getItemType(a),"|1:").concat(t.M_getItemType(e.reportId))})})}),this._getReportFn()({clickContent:"头像",reportId:a,itemPos:c,actionType:r.length<=1?o.At.CLICK_FRIEND_PROFILE:o.At.CLICK_SHOW_DIALOG})},M_popNewAvatarListDialog:function(e){var t=this,r=e.jumpInfos,n=e.pop,i=e.reportPrefix,a=e.reportId,c=e.itemPos;if(r.length<1)return console.error("jumpInfos is empty");1===r.length?this._getGoFn()(r[0]):this._getGoFn()({pop:{type:32,items:r.map(function(e){var r=n.reportId||e.reportId;return p(l({},e),{reportId:r,itemPos:"".concat(i,":").concat(t.M_getItemType(a),"|1:").concat(t.M_getItemType(r))})}),closeBtnReportId:n.closeReportId,closeBtnItemPos:"".concat(i,":").concat(this.M_getItemType(a),"|0:").concat(this.M_getItemType(n.closeReportId)),isNested:!0,popTitle:"".concat(r.length,"个朋友").concat(null==n?void 0:n.title),popSubTitle:null==n?void 0:n.subTitle,close:{reportId:null==n?void 0:n.closeReportId}}}),this._getReportFn()({clickContent:"头像",reportId:a,itemPos:c,actionType:r.length<=1?o.At.CLICK_FRIEND_PROFILE:o.At.CLICK_SHOW_DIALOG})},_getGoFn:function(){return this.goPop||this.M_serviceSearchGo},_getReportFn:function(){return this.clickReport||this.M_clickReport},M_composeMemberList:function(e,t,r){var n=e||[];if(t){if(r){if(-1===n.findIndex(function(e){return e.username===t.username}))return[t].concat(f(n))}else{var o=n.findIndex(function(e){return e.username===t.username});if(-1!==o){var i=f(n);return i.splice(o,1),i}}}return n}}}},491262:function(e,t,r){var n,o,i,a=r(798509),c=r(462474);t.Z=(o=[],i={},function(e){if(!o.some(function(t){return t.userName===e.userName})&&o.push({id:e.vueId,userName:e.userName,nickName:e.nickName||"",alias:e.alias||""}),!n)n=setTimeout(function(e){a.Zr.WeixinJSBridgeInvoke("getSearchDisplayNameList",{data:o.splice(0)},function(e){if(e&&(0==+e.retCode||0==+e.ret)&&e.data){e.data="string"==typeof e.data?JSON.parse(e.data):e.data;var t={};e.data.forEach(function(e){t[e.userName]=e.displayName}),i=Object.assign(t),c.Z.$emit(c.U.CLIENT_NAME_DONE,i)}}),n=null},200)})}}]);