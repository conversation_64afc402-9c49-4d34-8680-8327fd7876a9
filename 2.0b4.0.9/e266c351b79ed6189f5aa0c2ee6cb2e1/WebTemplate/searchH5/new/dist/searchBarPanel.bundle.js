"use strict";(self.webpackChunkweixin_search=self.webpackChunkweixin_search||[]).push([["searchBarPanel"],{955588:function(e,r,t){t.r(r);var n=t(965618),o=t(730013),a=t(551900),i=t(509855),s=(0,a.Z)(o.Z,n.s,n.x,!1,null,"22acd7d0",null);"function"==typeof i.Z&&(0,i.Z)(s),r.default=s.exports},696642:function(e,r,t){t.d(r,{Z:function(){return n}});function n(e){e.options.__wxs_id="4a445c6e"}},509855:function(e,r,t){var n=t(696642);r.Z=n.Z},730013:function(e,r,t){var n=t(568150);r.Z=n.Z},965618:function(e,r,t){t.d(r,{s:function(){return n},x:function(){return o}});var n=function(){var e=this.$createElement,r=this._self._c||e;return r("div",{staticClass:"search-bar-panel",attrs:{"data-fcn-search-bar-panel":"","data-fr-14fc92317":""}},[this.searchBarForm?r("div",{class:{"search-bar-bottom-space":!!this.searchBarForm.itemInfo&&!this.searchBarForm.noSearchBarBottomSpace}},[r("search-bar",{ref:"search-bar",attrs:{form:this.searchBarForm,"parent-box-report-id":this.boxReportId,"parent-doc-report-id":this.docReportId,"parent-item-pos":this.parentItemPos,"data-fc-1ad49800c":""},on:{"value-change":this.onSearchValueChange,refresh:this.onSearchBarRefresh,report:this.onReport}})],1):this._e(),this.searchBarForm.itemInfo?[r(this.subItemTypes[this.searchBarForm.itemInfo.type]||this.globalType[this.searchBarForm.itemInfo.type],this._b({directives:[{name:"show",rawName:"v-show",value:!(this.hideItemInfo&&this.searchBarForm.hideOnChange),expression:"!(hideItemInfo && searchBarForm.hideOnChange)"}],tag:"component",attrs:{"data-fc-1ad49800a":""},on:{"replace-renderItem":this.onReplaceRenderItem,"tap:refresh":this.onTapRefresh}},"component",Object.assign({},this.$props,{source:this.innerItemInfo}),!1))]:this._e()],2)},o=[]},568150:function(e,r,t){var n=t(798509),o=t(603290),a=t(740115),i=t(136525),s=t(417655),c=t(689291);function f(e,r){(null==r||r>e.length)&&(r=e.length);for(var t=0,n=Array(r);t<r;t++)n[t]=e[t];return n}function h(e){return function(e){if(Array.isArray(e))return f(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,r){if(e){if("string"==typeof e)return f(e,r);var t=Object.prototype.toString.call(e).slice(8,-1);if("Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return f(e,r)}}(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}r.Z={name:"SearchBarPanel",components:{searchBar:o.Z,gridTable:a.default,sourcePanel:function(){var e=c.Z.startImport("sourcePanel");return Promise.all([t.e("src_views_result_block_service-search_service-search-item_sub-item_package-info_package-info_vue"),t.e("src_components_general_smart-volunteer_smart-volunteer_vue"),t.e("src_components_general_source-panel_source-panel_vue"),t.e("sourcePanel")]).then(t.bind(t,191311)).then(function(r){return c.Z.afterImport(e,"sourcePanel"),r})}},mixins:[n.jB,n.uW],props:{source:{type:Object,default:function(){return{}}},data:{type:Object,default:function(){return{}}},item:{type:Object,default:function(){return{}}},pos:{type:Number,default:0},typePos:{type:Number,default:0}},data:function(){return{globalType:i.jJ,subItemTypes:s._t,innerItemInfo:{},hideItemInfo:!1}},computed:{itemInfo:function(){return n.Zr.isObjectEmpty(this.source)?this.item:this.source},searchBarForm:function(){return this.itemInfo.form}},watch:{"itemInfo.form.itemInfo":{handler:function(e){this.innerItemInfo=e,this.hideItemInfo=!1},immediate:!0}},methods:{onSearchBarRefresh:function(e){var r,t,n,o=this;console.log("onSearchBarRefresh: ",e),this.mergeSearchQuery(e.refreshItem);var a=this.M_getItemInfo(e.refreshItem);if(null==a?void 0:null===(n=a.afterRefresh)||void 0===n?void 0:n.pop){;this.M_serviceSearchGo((r=function(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{},n=Object.keys(t);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(t).filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.forEach(function(r){var n,o,a;n=e,o=r,a=t[r],o in n?Object.defineProperty(n,o,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[o]=a})}return e}({},a.afterRefresh),t=(t={targetCallback:function(e){o.$emit("refresh:itemInfo",o.M_getItemInfo(e)),o.$emit("replace-renderItem",e)}},t),Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(t)):(function(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t})(Object(t)).forEach(function(e){Object.defineProperty(r,e,Object.getOwnPropertyDescriptor(t,e))}),r))}a&&this.$emit("refresh:itemInfo",a),e.refreshItem&&this.$emit("replace-renderItem",e.refreshItem)},mergeSearchQuery:function(e){var r,t,n=null===(r=this.$refs["search-bar"])||void 0===r?void 0:r.value;(null==e?void 0:null===(t=e.subItems)||void 0===t?void 0:t.length)&&n&&e.subItems.forEach(function(e){var r;(null===(r=e.list)||void 0===r?void 0:r.length)&&e.list.forEach(function(e){var r,t;(null===(t=e.itemInfo)||void 0===t?void 0:null===(r=t.form)||void 0===r?void 0:r.searchBar)&&(e.itemInfo.form.searchBar.query=e.itemInfo.form.searchBar.query||n)})})},onReport:function(e,r){this.M_clickReport(e,r)},onSearchValueChange:function(e,r){e!==r&&(this.hideItemInfo=!0)},onTapRefresh:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];this.$emit.apply(this,["tap:refresh"].concat(h(r)))},onReplaceRenderItem:function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];this.$emit.apply(this,["replace-renderItem"].concat(h(r)))}}}}}]);