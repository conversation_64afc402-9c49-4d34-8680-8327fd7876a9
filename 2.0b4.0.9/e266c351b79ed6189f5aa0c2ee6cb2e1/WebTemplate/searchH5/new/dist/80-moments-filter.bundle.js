"use strict";(self.webpackChunkweixin_search=self.webpackChunkweixin_search||[]).push([["80-moments-filter"],{434533:function(e,t,n){var i=n(705713),r=n(610431),o=n(551900),a=n(446150),s=(0,o.Z)(r.Z,i.s,i.x,!1,null,"519d844e",null);"function"==typeof a.Z&&(0,a.Z)(s),t.Z=s.exports},244374:function(e,t,n){n.d(t,{Z:function(){return i}});function i(e){e.options.__wxs_id="82be3551"}},336264:function(e,t,n){var i=n(768207),r=n(410169),o=n(551900),a=n(268213),s=(0,o.Z)(r.Z,i.s,i.x,!1,null,null,null);"function"==typeof a.Z&&(0,a.Z)(s),t.Z=s.exports},39275:function(e,t,n){n.d(t,{Z:function(){return i}});function i(e){e.options.__wxs_id="d133a7dd"}},802916:function(e,t,n){n.r(t);var i=n(94382),r=n(945257),o=n(551900),a=n(245716),s=(0,o.Z)(r.Z,i.s,i.x,!1,null,null,null);"function"==typeof a.Z&&(0,a.Z)(s),t.default=s.exports},693472:function(e,t,n){n.d(t,{Z:function(){return i}});function i(e){e.options.__wxs_id="f2e67690"}},446150:function(e,t,n){var i=n(244374);t.Z=i.Z},268213:function(e,t,n){var i=n(39275);t.Z=i.Z},245716:function(e,t,n){var i=n(693472);t.Z=i.Z},610431:function(e,t,n){var i=n(393621);t.Z=i.Z},945257:function(e,t,n){var i=n(799765);t.Z=i.Z},410169:function(e,t,n){var i=n(359094);t.Z=i.Z},705713:function(e,t,n){n.d(t,{s:function(){return i},x:function(){return r}});var i=function(){var e=this.$createElement,t=this._self._c||e;return t("div",{directives:[{name:"active",rawName:"v-active.stop",modifiers:{stop:!0}}],staticClass:"ui-dropdown",attrs:{role:this.role,tabindex:"-1","data-cli":"","data-fcn-component":"","data-fr-18dd28066":""}},[t("p",{staticClass:"wording",class:{placeholder:!this.value}},[this.$slots.default?[this._t("default")]:[this._v("\n        "+this._s(this.value?this.value:this.placeholder)+"\n      ")]],2),t("ui-arrow",{attrs:{direction:this.direction,align:"flex","data-fc-169c13a04":""}})],1)},r=[]},768207:function(e,t,n){n.d(t,{s:function(){return i},x:function(){return r}});var i=function(){var e=this,t=e.$createElement;return(e._self._c||t)("div",{staticClass:"scroll-node",attrs:{"data-fcn-scroll-modal":"","data-fr-169e03b84":""},on:{touchstart:e.touchStart,touchmove:function(t){return t.stopPropagation(),e.touchMove(t)}}},[e._t("default")],2)},r=[]},94382:function(e,t,n){n.d(t,{s:function(){return i},x:function(){return r}});var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{directives:[{name:"show",rawName:"v-show",value:e.isShow,expression:"isShow"}],attrs:{"data-fcn-moments-filter":"","data-fr-17e140092":""}},[n("div",{staticClass:"moments-filter",class:{isPickerShow:e.isTimeActived,"moments-filter-touch-action-none":e.isTypeFilterActived}},[n("div",[n("div",{ref:"Picker",staticClass:"sift__box",class:{sift_box_single:e.isOnlyTimeSelect}},[n("span",{staticClass:"ui-aria-hidden"},[e._v("筛选器：")]),n("div",{directives:[{name:"show",rawName:"v-show",value:e.isAdvSearch,expression:"isAdvSearch"}],staticClass:"sift__item",attrs:{"data-index":"0",role:"button"},on:{click:function(t){return t.stopPropagation(),t.preventDefault(),e.onContactTap(t)},touchmove:e.handleTouchmovePrevent}},[n("ui-dropdown",{staticClass:"sift__item-i",attrs:{"data-report-id":e.M_itemReportId(e.FILTER_REPORT_IDS.FRIEND,1),placeholder:e.publishDisplayName,value:e.isNameSelected?e.publishDisplayName:"","data-fc-1446c11df":""}})],1),n("div",{staticClass:"sift__item",class:{sift__item_single:e.isOnlyTimeSelect},attrs:{role:"button"},on:{click:function(t){return t.stopPropagation(),t.preventDefault(),e.onTimeTap(t)},touchmove:e.handleTouchmovePrevent}},[n("ui-dropdown",{staticClass:"sift__item-i",class:{selected:e.isTimeActived},attrs:{"data-report-id":e.M_itemReportId(e.FILTER_REPORT_IDS.TIME,2),placeholder:e.publishDisplayTime,value:e.isTimeSelected?e.publishDisplayTime:"","data-fc-1446c11e0":""}})],1),!e.isExptBoxUpdate&&e.snsContentType.length?n("div",{staticClass:"sift__item",attrs:{role:"button"},on:{click:function(t){return e.tapSift(e.snsContentType)},touchmove:e.handleTouchmovePrevent}},[n("ui-dropdown",{staticClass:"sift__item-i sift__item-i-type",class:{selected:e.isTypeFilterActived},attrs:{"data-report-id":e.M_itemReportId(e.FILTER_REPORT_IDS.CONTENT,3),placeholder:e.publishDisplayType,value:e.isSnsTypeSelected?e.publishDisplayType:"","data-fc-1446c11e1":""}})],1):e._e()])])]),[e.renderOption&&e.renderOption.length?n("heightAnimate",{ref:"dialog",staticClass:"sift__multi moment-filter-sift__multi",class:{"moment-filter-sift__touch-action-none":e.isTypeFilterActived},style:e.siftOptionStyle,attrs:{"aria-modal":"true",tabindex:"-1","data-fc-1446c11e2":""}},[n("scroll-modal",{staticClass:"sift__multi-w moment-filter-sift__touch-action-none",attrs:{"data-fc-1446c11e3":""},nativeOn:{mousewheel:function(e){e.preventDefault()}}},e._l(e.renderOption,function(t){return n("div",{key:t.tip,staticClass:"sift__multi-o moment-filter-sift__touch-action-none",attrs:{role:"button"},on:{click:function(n){return e.tapSingleSelection(t)}}},[n("div",{directives:[{name:"active",rawName:"v-active.stop",value:e.os.mobile,expression:"os.mobile",modifiers:{stop:!0}}],staticClass:"sift__multi-o-inner",class:{selected:t.value===e.snsContentTypeFilter,active__item:e.os.pc},attrs:{"aria-label":t.value===e.moment.snsContentTypeFilter?"已选定，"+t.tip:t.tip,"data-cli":""},on:{touchmove:function(e){e.preventDefault()}}},[e._v("\n              "+e._s(t.tip)+"\n            ")])])}),0)],1):e._e()],n("transition",{attrs:{name:"showMask","data-fc-1446c11e4":""}},[n("div",{directives:[{name:"show",rawName:"v-show",value:e.renderOption&&e.renderOption.length,expression:"renderOption && renderOption.length"}],staticClass:"weui-mask filter-mask",style:e.M_os({top:e.siftOptionTop+"px"},{background:"transparent"}),on:{touchmove:function(e){e.preventDefault()},click:function(t){return t.stopPropagation(),e.handleMaskHide(t)},mousewheel:function(e){e.preventDefault()}}})])],2)},r=[]},393621:function(e,t){t.Z={name:"UiDropdown",props:{value:{type:String,default:""},placeholder:{type:String,default:""},direction:{type:String,default:"down"},role:{type:String,default:"button"}}}},799765:function(e,t,n){var i,r,o=n(420629);n(787806);var a=n(382760),s=n(411795),l=n(336264),c=n(798509),u=n(959018),p=n(783466),f=n(984928),m=n(136525),h=n(462474),d=n(75464),y=n(142862),v=n(827798);function T(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=Array(t);n<t;n++)i[n]=e[n];return i}function b(e,t,n,i,r,o,a){try{var s=e[o](a),l=s.value}catch(e){n(e);return}s.done?t(l):Promise.resolve(l).then(i,r)}function _(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},i=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),i.forEach(function(t){var i,r,o;i=e,r=t,o=n[t],r in i?Object.defineProperty(i,r,{value:o,enumerable:!0,configurable:!0,writable:!0}):i[r]=o})}return e}function g(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):(function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);n.push.apply(n,i)}return n})(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}var S=[-1,-1,-1],C=c.Zr.L("选择朋友"),w=c.Zr.L("选择发布时间"),O=c.Zr.L("内容类型"),D={FRIEND:"friend:filter_option:".concat(c.Zr.getRandom(6)),TIME:"time:filter_option:".concat(c.Zr.getRandom(6)),CONTENT:"content:filter_option:".concat(c.Zr.getRandom(6))},I={FRIEND:1,TIME:2,CONTENT:3};t.Z={components:{uiDropdown:a.Z,heightAnimate:s.Z,"scroll-modal":l.Z},mixins:[c.jB],data:function(){return{PICKER_TYPE:I,FILTER_REPORT_IDS:D,isAdvSearch:0,publishDisplayName:C,publishDisplayTime:w,publishUsername:"",isShow:!1,isTimeActived:!1,isTypeFilterActived:!1,renderOption:[],siftOptionTop:0,siftOptionLeft:0,snsContentType:[]}},computed:g(_(g(_({},(0,o.rn)({experiment:function(e){return e.teach.experiment}}),(0,o.Se)({resultExperiment:"result/self/getExperiment"})),{isNoSelected:function(){return this.publishDisplayName==C&&this.publishDisplayTime==w},isNameSelected:function(){return this.publishDisplayName!==C},isTimeSelected:function(){return this.publishDisplayTime!==w},isSnsTypeSelected:function(){return this.moment.snsContentTypeFilter},publishDisplayType:function(){var e=this,t=this.snsContentType.find(function(t){return t.value===e.moment.snsContentTypeFilter});return t?t.tip:O}}),(0,o.rn)({moment:function(e){return e.result.moment}})),{snsContentTypeFilter:function(){return this.moment.snsContentTypeFilter||0},siftOptionStyle:function(){return{marginLeft:"".concat(this.siftOptionLeft-20,"px")}},isOnlyTimeSelect:function(){return(this.isShow||this.isExptBoxUpdate)&&!this.isAdvSearch},isExptBoxUpdate:function(){return!0}}),watch:{$route:function(e){e.name===f.ZP.view.INDEX&&(this._reset(),this.isShow=!1)},isTypeFilterActived:function(e){}},created:function(){var e=this,t=this;p.N.onSelectContact(function(e){if(0==e.ret){t.publishDisplayName=e.nickname||C,t.publishUsername=e.username||"",""==e.username?t.$store.commit({type:"result/updateMoment",tagInfoFromFilter:{},matchUserFromFilter:{}}):t.$store.commit({type:"result/updateMoment",tagInfoFromFilter:{tagText:c.xB.query,tagType:f.xo.SNSALBUM},matchUserFromFilter:{matchWord:e.nickname||C,userName:e.username||""}}),t.onSearchTap();var n=t.M_genClickId();t.M_clickReport({actionType:f.At.CLICK_FOLD,businessType:1e7,resultSubType:1,reportId:D.FRIEND,clickId:n,clickContent:e.nickname,currentTab:c.xB.type}),c.yG.result.momentFilter({reportType:2,clickType:3})}else t.$store.commit({type:"result/updateMoment",tagInfoFromFilter:{},matchUserFromFilter:{}})}),c.Gc.$on(h.U.RESET_MOMENT_FILTER,function(){t._reset(!0)}),p.N.onInputChange(function(){i&&i.hide(),e.typeFilterHide()}),p.N.onInputConfirm(function(){i&&i.hide(),e.typeFilterHide()}),c.Gc.$on(h.U.SEARCH_DATA_RENDERED,function(n){if(t.onFiltersExpose(),c.xB.type!=m.jJ.MOMENT||!e.isExptBoxUpdate&&e._checkEmtyQueryWithTag()){t.isShow=!1;return}t.isAdvSearch=!c.Zr.isArrayLength(t.$store.state.result.moment.tagList);var i=n.resultObj;if(i.data&&i.data.length?(t.setPlaceholder(),t.isShow=!0,c.yG.result.momentFilter()):t.publishDisplayName!=C||t.publishDisplayTime!=w||e.moment.snsContentTypeFilter?(t.setPlaceholder(),t.isShow=!0,c.yG.result.momentFilter()):!e.isExptBoxUpdate&&(t.isShow=!1),i.data&&i.data.length){var r=i.data.find(function(e){return e.type===m.jJ.MOMENT});r&&r.snsContentType&&r.snsContentType.length&&(e.snsContentType=r.snsContentType)}}),c.Gc.$on(h.U.REAL_SWITCH_TAB,this.typeFilterHide),c.Gc.$on(h.U.APP_VIEW_CHANGED,this.typeFilterHide),c.Gc.$on(h.U.BACK_BUTTON_CLICK,function(n){if(i&&i.hide(),e.typeFilterHide(),t.isAdvSearch=!c.Zr.isArrayLength(t.$store.state.result.moment.tagList),t.isAdvSearch){if(t.$store.commit({type:"result/updateMoment",tagInfoFromFilter:n.tagInfo,matchUserFromFilter:n.matchUser,numConditions:[],snsContentTypeFilter:n.snsContentTypeFilter}),c.Zr.isArrayLength(n.numConditions)){var r=n.numConditions[0],o=new Date(1e3*r.from).getFullYear();if(r.to-r.from>2678400)t.publishDisplayTime="".concat(o,"年");else if(r.to-r.from>86400){var a=new Date(1e3*r.from).getMonth();t.publishDisplayTime="".concat(o,"年").concat(a+1,"月")}else{var s=new Date(1e3*r.from).getMonth(),l=new Date(1e3*r.from).getDate();t.publishDisplayTime="".concat(o,"年").concat(s+1,"月").concat(l,"日")}}else t.publishDisplayTime=w;t.publishDisplayName=n.matchUser&&n.matchUser.matchWord?n.matchUser.matchWord:C,t.publishUsername=n.matchUser&&n.matchUser.userName?n.matchUser.userName:""}}),t._initial()},mounted:function(){this.setPlaceholder()},methods:{setPlaceholder:function(){v.Z.configNavBar({tagSearchPlaceholder:c.Zr.L("搜索朋友圈关键词")}),v.Z.configNavBar({recommendSearchQuery:c.Zr.L("搜索朋友圈关键词")})},onFiltersExpose:function(){var e=this;Object.entries(D).forEach(function(t){var n,i,r,o=(i=2,function(e){if(Array.isArray(e))return e}(n=t)||function(e,t){var n,i,r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var o=[],a=!0,s=!1;try{for(r=r.call(e);!(a=(n=r.next()).done)&&(o.push(n.value),!t||o.length!==t);a=!0);}catch(e){s=!0,i=e}finally{try{!a&&null!=r.return&&r.return()}finally{if(s)throw i}}return o}}(n,2)||function(e,t){if(e){if("string"==typeof e)return T(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return T(e,t)}}(n,i)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),a=o[0],s=o[1];if(r="FRIEND"===a?"1:filter_option":"TIME"===a?"2:filter_option":"3:filter_option","CONTENT"===a){if(!(!e.isExptBoxUpdate&&e.snsContentType.length))return;e.makeReport25032({searchId:c.xB.searchId,parentSearchID:c.xB.parentSearchID,actionType:f.At.EXPOSE_ITEM,itemInfos:[s],itemPoses:[r]})}e.makeReport25032({searchId:c.xB.searchId,parentSearchID:c.xB.parentSearchID,actionType:f.At.EXPOSE_ITEM,itemInfos:[s],itemPoses:[r]})})},makeReport25032:function(e,t){(0,y.Z)(_(g(_({},c.xB.getBase()),{reqBusinessType:c.xB.type,resultSubType:1,businessType:1e7,extInfo:JSON.stringify(_({currentTab:c.xB.type,clickId:this.clickId},t))}),e))},_initial:function(){r=+new Date},_reset:function(){r=+new Date,this.$store.commit({type:"result/updateMoment",tagInfoFromFilter:{},matchUserFromFilter:{},numConditions:[],snsContentTypeFilter:""}),this.isAdvSearch=!c.Zr.isArrayLength(this.$store.state.result.moment.tagList),this.publishDisplayName=C,this.publishDisplayTime=w,this.publishUsername=""},_showTimePicker:function(e){var t=this;t.isTimeActived=!0,i=(0,d.R)({id:r,defaultValue:S,className:"moment-picker",configColumns:t.isExptBoxUpdate?d.e.YEAR_MONTH:d.e.ALL,pickerRef:this.$refs.Picker,onClose:function(){t.isTimeActived=!1},onConfirm:function(n){var i=t.M_genClickId();if(n&&-1!=n[0].value){var r,o,a,s,l=n[0].value,u=n[1].value;o=t.isExptBoxUpdate?-1:n[2].value,-1==u?(a=Math.floor(new Date(l,0).getTime()/1e3),s=Math.floor((new Date(l+1,0).getTime()-1)/1e3)):-1==o?(a=Math.floor(new Date(l,u).getTime()/1e3),s=Math.floor((new Date(l,u+1).getTime()-1)/1e3)):(a=Math.floor(new Date(l,u,o).getTime()/1e3),s=Math.floor((new Date(l,u,o+1).getTime()-1)/1e3)),r={from:a,to:s,field:1},t.isExptBoxUpdate?t.publishDisplayTime=n[0].label+(-1==n[1].value?"":n[1].label):t.publishDisplayTime=n[0].label+(-1==n[1].value?"":n[1].label)+(-1==n[2].value?"":n[2].label)}else t.publishDisplayTime=w;t.M_clickReport({actionType:f.At.CLICK_FOLD,businessType:1e7,resultSubType:1,reportId:D.TIME,clickId:i,clickContent:t.publishDisplayTime,currentTab:c.xB.type}),r?t.$store.commit({type:"result/updateMoment",numConditions:[r]}):t.$store.commit({type:"result/updateMoment",numConditions:[]}),e&&e(n),t.isTimeActived=!1,c.yG.result.momentFilter({reportType:2,clickType:4})}})},_checkEmtyQueryWithTag:function(){try{var e=!c.Zr.isArrayLength(this.$store.state.result.moment.tagList),t=1===(this.$store.state.result.moment.tagList||[]).length&&this.$store.state.result.moment.tagList[0].tagName;if(!e&&c.xB.query.trim()===t)return!this.isExptBoxUpdate&&(this.isShow=!1),this._reset(),!0;return!1}catch(e){return!1}},onContactTap:function(){this.typeFilterHide(),c.hi.selectContact({selected_user_name:this.publishUsername});var e=this.M_genClickId();this.M_clickReport({actionType:f.At.CLICK_UNFOLD,businessType:1e7,resultSubType:1,reportId:D.FRIEND,clickId:e}),(0,u.B4)(c.AX.feature.advMoment.tapContactSort),c.yG.result.momentFilter({reportType:2,clickType:1}),(0,u.B4)(c.AX.feature.momentContact)},onTimeTap:function(){var e;return(e=function(){var e,t,n,i;return function(e,t){var n,i,r,o,a={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return o={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(o){return function(s){return function(o){if(n)throw TypeError("Generator is already executing.");for(;a;)try{if(n=1,i&&(r=2&o[0]?i.return:o[0]?i.throw||((r=i.return)&&r.call(i),0):i.next)&&!(r=r.call(i,o[1])).done)return r;switch(i=0,r&&(o=[2&o[0],r.value]),o[0]){case 0:case 1:r=o;break;case 4:return a.label++,{value:o[1],done:!1};case 5:a.label++,i=o[1],o=[0];continue;case 7:o=a.ops.pop(),a.trys.pop();continue;default:if(!(r=(r=a.trys).length>0&&r[r.length-1])&&(6===o[0]||2===o[0])){a=0;continue}if(3===o[0]&&(!r||o[1]>r[0]&&o[1]<r[3])){a.label=o[1];break}if(6===o[0]&&a.label<r[1]){a.label=r[1],r=o;break}if(r&&a.label<r[2]){a.label=r[2],a.ops.push(o);break}r[2]&&a.ops.pop(),a.trys.pop();continue}o=t.call(e,a)}catch(e){o=[6,e],i=0}finally{n=r=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,s])}}}(this,function(r){switch(r.label){case 0:if(e=this,t=this,n=this.isTypeFilterActived,t.typeFilterHide(),!n)return[3,2];return[4,new Promise(function(e){setTimeout(function(){e()},300)})];case 1:r.sent(),r.label=2;case 2:if(t.$store.state.result.self.base.isLoading)return[2];return this.isAdvSearch?t._showTimePicker(function(){t.onSearchTap()}):t._showTimePicker(function(){c.Gc.$emit(h.U.setQuery,{extReqParams:{key:"parentSearchID",textValue:e.M_composeParentSid({t:f.X$.FILTER,s:c.xB.searchId,did:"filter",rid:e.$store.state.result.previousRid})}})}),i=t.M_genClickId(),t.M_clickReport({actionType:f.At.CLICK_UNFOLD,businessType:1e7,resultSubType:1,reportId:D.TIME,clickId:i,currentTab:c.xB.type}),(0,u.B4)(c.AX.feature.advMoment.tapTimeSort),c.yG.result.momentFilter({reportType:2,clickType:2}),(0,u.B4)(c.AX.feature.momentTime),[2]}})},function(){var t=this,n=arguments;return new Promise(function(i,r){var o=e.apply(t,n);function a(e){b(o,i,r,a,s,"next",e)}function s(e){b(o,i,r,a,s,"throw",e)}a(void 0)})}).apply(this)},onSearchTap:function(e){this._checkEmtyQueryWithTag()&&this._reset(),c.xB.setValue({sceneActionType:f.E9.NORMAL}),c.xB.query&&c.Gc.$emit(h.U.setQuery,{extReqParams:{key:"parentSearchID",textValue:this.M_composeParentSid({t:f.X$.FILTER,s:c.xB.searchId,did:"filter",rid:this.$store.state.result.previousRid})}}),(0,u.B4)(c.AX.feature.advMoment.confirmSort)},tapSift:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.snsContentType,t=this,n=t.$refs.Picker,i=n.getElementsByClassName("sift__item-i-type")[0];if(n){var r=n.getBoundingClientRect(),o=window.pageYOffset+r.top,a=r.height;t.siftOptionTop=Math.ceil(o+a)}i&&(t.siftOptionLeft=i.offsetLeft),this.isTypeFilterActived=!this.isTypeFilterActived;var s=t.M_genClickId();t.M_clickReport({actionType:f.At.CLICK_UNFOLD,businessType:1e7,resultSubType:1,reportId:D.CONTENT,clickId:s,currentTab:c.xB.type}),this.isTypeFilterActived?(t.renderOption=e,setTimeout(function(){t.$refs.dialog&&t.$refs.dialog.$el.focus()}),c.yG.result.momentFilter({reportType:2,clickType:5,content:t.renderOption.map(function(e){return e.tip}).join(";")}),c.Gc.$emit(h.U.PC_SHOW_SCROLLBAR,!1)):(t.renderOption=[],c.Gc.$emit(h.U.PC_SHOW_SCROLLBAR))},tapSingleSelection:function(e){if(e.value===this.moment.snsContentTypeFilter){this.typeFilterHide();return}if(!this.$store.state.result.self.base.isLoading){this.$store.commit({type:"result/updateMoment",snsContentTypeFilter:e.value}),this.isAdvSearch?this.onSearchTap():c.Gc.$emit(h.U.setQuery,{});var t=this.M_genClickId();this.M_clickReport({actionType:f.At.CLICK_FOLD,businessType:1e7,resultSubType:1,reportId:D.CONTENT,clickId:t,clickContent:e.value,currentTab:c.xB.type}),c.yG.result.momentFilter({reportType:2,clickType:6,content:e.tip}),this.typeFilterHide()}},typeFilterHide:function(){this.isTypeFilterActived=!1,this.renderOption=[],c.Gc.$emit(h.U.PC_SHOW_SCROLLBAR)},handleMaskHide:function(){this.typeFilterHide()},handleTouchmovePrevent:function(e){this.isTypeFilterActived&&(e.cancelable&&e.preventDefault(),e.stopPropagation())}}}},382760:function(e,t,n){var i=n(434533);i.Z.install=function(e){e.component(i.Z.name,i.Z)},t.Z=i.Z},75464:function(e,t,n){n.d(t,{R:function(){return c},e:function(){return l}});var i=n(798509),r=n(787806),o=n.n(r);function a(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=Array(t);n<t;n++)i[n]=e[n];return i}function s(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},i=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),i.forEach(function(t){var i,r,o;i=e,r=t,o=n[t],r in i?Object.defineProperty(i,r,{value:o,enumerable:!0,configurable:!0,writable:!0}):i[r]=o})}return e}var l={ALL:3,YEAR_MONTH:2,YEAR:1};function c(){var e,t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=n.canNoLimit,c=n.startYear,u=void 0===c?2012:c,p=n.pickerRef,f=n.configColumns,m=void 0===f?l.ALL:f;var h=(t=3,function(e){if(Array.isArray(e))return e}(e=void 0===r?[1,1,1]:r)||function(e,t){var n,i,r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var o=[],a=!0,s=!1;try{for(r=r.call(e);!(a=(n=r.next()).done)&&(o.push(n.value),!t||o.length!==t);a=!0);}catch(e){s=!0,i=e}finally{try{!a&&null!=r.return&&r.return()}finally{if(s)throw i}}return o}}(e,3)||function(e,t){if(e){if("string"==typeof e)return a(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return a(e,t)}}(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),d=h[0],y=h[1],v=h[2],T=m===l.ALL,b=function(e){var t=e.year,n=e.month,r=void 0===n?12:n,o=[s({label:i.Zr.L("不限"),value:-1},T?{children:[{label:i.Zr.L("不限"),value:-1}]}:{})];!y&&(o=[]);for(var a=r;a>0;a--)o.push(s({label:i.Zr.L("".concat(a,"月")),value:a-1},T?{children:_({year:t,month:a})}:{}));return o},_=function(e){var t=e.year,n=e.month,r=new Date(t,n,0).getDate();t===new Date().getFullYear()&&n===new Date().getMonth()+1&&(r=new Date().getDate());var o=[{label:i.Zr.L("不限"),value:-1}];!v&&(o=[]);for(var a=r;a>0;a--)o.push({label:i.Zr.L("".concat(a,"日")),value:a});return o},g=function(){var e=[{label:i.Zr.L("不限"),value:-1,children:[s({label:i.Zr.L("不限"),value:-1},T?{children:[{label:i.Zr.L("不限"),value:-1}]}:{})]}];!d&&(e=[]);var t=new Date().getFullYear();e.push({label:t+i.Zr.L("年","","picker"),value:t,children:b({year:t,month:new Date().getMonth()+1})});for(var n=t-1;n>=u;n--)e.push({label:n+i.Zr.L("年","","picker"),value:n,children:b({year:n})});return e}();return o().picker(g,Object.assign({title:"请选择发布时间",closeText:"取消",container:p||"body"},{container:p||"body",animateIn:"weui-animate-fade-in",animateOut:"weui-animate-fade-out"},n))}},359094:function(e,t){var n;t.Z={data:function(){return{}},mixins:[],props:[],methods:{touchStart:function(e){n=e.changedTouches[0].pageY},touchMove:function(e){0===this.$el.scrollTop&&e.changedTouches[0].pageY>n&&e.preventDefault(),this.$el.scrollTop+this.$el.offsetHeight===this.$el.scrollHeight&&e.changedTouches[0].pageY<n&&e.preventDefault()}},created:function(){}}}}]);