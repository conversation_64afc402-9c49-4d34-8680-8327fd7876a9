(self.webpackChunkweixin_search=self.webpackChunkweixin_search||[]).push([["vendors-node_modules_tencent_stockfe-kline_dist_kline_js"],{796211:function(t){var i,e;i="undefined"!=typeof self&&self,e=function(){return function(t){function i(r){if(e[r])return e[r].exports;var o=e[r]={i:r,l:!1,exports:{}};return t[r].call(o.exports,o,o.exports,i),o.l=!0,o.exports}var e={};return i.m=t,i.c=e,i.d=function(t,e,r){i.o(t,e)||Object.defineProperty(t,e,{configurable:!1,enumerable:!0,get:r})},i.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return i.d(e,"a",e),e},i.o=function(t,i){return Object.prototype.hasOwnProperty.call(t,i)},i.p="",i(i.s=98)}([function(t,i,e){"use strict";function r(t,i,e){var r;return t.save(),e.font&&t.setFont(e.font),r=t.measureText(i).width,t.restore(),r}i.c=function(t,i,e,r,o){var n;t.save(),t.setTextStyle(o),n=t.measureText(i).width,t.fillText(i,e,r,n),t.restore()},i.b=function(t,i,e,o,n,s,a,h){t.save(),t.setTextStyle(n),(function(t,i,e,o){var n=i.split(""),s="",a=[],h=0;return n.forEach(function(i,c){var p=s+n[c];r(t,p,o)>e&&c>0?(a[h++]=s,s=n[c]):s=p}),a[h]=s,a})(t,i,s,n).forEach(function(i,r){0===r?o+=h+a/2:o+=h+a,t.fillText(i,e,o,s)}),t.restore()},i.d=r,e.d(i,"a",function(){return o});var o={baseLine:{mid:"middle",top:"top",btm:"bottom"},textAlign:{left:"left",right:"right",center:"center"}}},function(t,i,e){"use strict";function r(t,i,e,r,o,s,a,h){void 0===h&&(h=1),t.save(),t.setStrokeStyle(s),a&&t.setFillStyle(a),t.setLineWidth(h),a?t.fillRect(n(i),n(e),r,o):t.strokeRect(n(i),n(e),r,o),t.restore()}function o(t,i,e,r,o,s,a,h,c,p){void 0===h&&(h="round"),void 0===c&&(c=[4,7]),void 0===p&&(p=1),t.save(),t.setLineWidth(p),t.setStrokeStyle(s),"dash"==a?(t.setLineCap(h),t.beginPath(),t.dashedLine(i,e,r,o,c),t.closePath()):(t.beginPath(),1===p?(t.moveTo(n(i),n(e)),t.lineTo(n(r),n(o))):(t.moveTo(i,e),t.lineTo(r,o))),t.stroke(),t.restore()}function n(t){return parseInt(t)+.5}i.g=r,i.f=function(t,i,e,r,o,n,s,a,h){void 0===h&&(h=1);var c="number"==typeof n?{borderTopLeftRadius:n,borderTopRightRadius:n,borderBottomLeftRadius:n,borderBottomRightRadius:n}:n,p=c.borderTopLeftRadius,u=void 0===p?0:p,l=c.borderTopRightRadius,d=void 0===l?0:l,f=c.borderBottomLeftRadius,g=void 0===f?0:f,x=c.borderBottomRightRadius,y=void 0===x?0:x;t.save(),t.translate(i,e),t.beginPath(0),t.arc(r-y,o-y,y,0,Math.PI/2),t.lineTo(g,o),t.arc(g,o-g,g,Math.PI/2,Math.PI),t.lineTo(0,g),t.arc(u,u,u,Math.PI,3*Math.PI/2),t.lineTo(r-d,0),t.arc(r-d,d,d,3*Math.PI/2,2*Math.PI),t.lineTo(r,o-d),t.closePath(),t.setLineWidth(h),t.setStrokeStyle(s),a&&(t.setFillStyle(a),t.fill()),t.stroke(),t.restore()},i.c=o,i.a=function(t,i,e,n,s,a,h,c,p,u,l){void 0===l&&(l=2),t.save();var d=h===i.colorProp.rise,f=[t,n,s.highY,n,d?s.closeY:s.openY,a,c,p,u,l],g=[t,n,s.lowY,n,d?s.openY:s.closeY,a,c,p,u,l];o.apply(null,f),o.apply(null,g);var x=h;"hollow"===(i.setting.yangKStyle&&i.setting.yangKStyle.id||"solid")&&d&&(x=void 0),r(t,n-i.barWidth/2,e.close>=e.open?s.closeY:s.openY,i.barWidth,Math.max(Math.abs(s.closeY-s.openY),1),a,x,l),t.restore()},i.d=function(t,i,e,r,o,n,s){t.setFillStyle(r),t.setStrokeStyle(r),t.setLineWidth(o),0===n?(t.beginPath(),t.moveTo(i,e)):t.lineTo(i,e),n===s-1&&t.stroke()},i.b=function(t,i,e,r,o,n){void 0===o&&(o="#fff"),void 0===n&&(n="#fff"),t.setFillStyle(o),t.setStrokeStyle(n),t.beginPath(),t.arc(i,e,r,0,2*Math.PI,!0),t.stroke(),t.fill()},i.h=function(t,i,e,r,o,n,s,a,h){void 0===a&&(a="#fff"),void 0===h&&(h="#fff"),t.setStrokeStyle(a),t.setFillStyle(h),t.beginPath(),t.moveTo(i,e),t.lineTo(r,o),t.lineTo(n,s),t.closePath(),t.fill()},i.e=function(t,i,e,r,n,s){void 0===s&&(s=1),o(t,e,i.high,e,i.low,n,null,null,null,s),o(t,e,i.open,e-r/2,i.open,n,null,null,null,s),o(t,e,i.close,e+r/2,i.close,n,null,null,null,s)},window.CanvasRenderingContext2D&&CanvasRenderingContext2D.prototype.lineTo&&(CanvasRenderingContext2D.prototype.dashedLine=function(t,i,e,r,o){o||(o=[5,5]);var n=o.length;this.moveTo(t,i);for(var s=e-t,a=r-i,h=s?a/s:NaN,c=Math.sqrt(s*s+a*a),p=0,u=!0;c>=.1&&p<1e4;){var l=o[p++%n];if(0===l&&(l=.001),l>c&&(l=c),isNaN(h))i+=l;else{var d=Math.sqrt(l*l/(1+h*h));t+=d,i+=h*d}this[u?"lineTo":"moveTo"](t,i),c-=l,u=!u}this.moveTo(0,0)})},function(t,i,e){"use strict";var r,o=e(32),n=e(0),s=e(9),a=e(7),h=this&&this.__extends||(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var e in i)i.hasOwnProperty(e)&&(t[e]=i[e])},function(t,i){function e(){this.constructor=t}r(t,i),t.prototype=null===i?Object.create(i):(e.prototype=i.prototype,new e)}),c=this&&this.__assign||Object.assign||function(t){for(var i,e=1,r=arguments.length;e<r;e++)for(var o in i=arguments[e],i)Object.prototype.hasOwnProperty.call(i,o)&&(t[o]=i[o]);return t},p=function(t){function i(i,e,r,o,n,s,a,h,c){return t.call(this,i,e,r,o,n,s,a,h,c)||this}return h(i,t),i.prototype.drawScale=function(){var t=this.region.yAxis,i={font:this.props.textProp.font,textAlign:n.a.textAlign.right,baseLine:n.a.baseLine.top,color:this.props.colorProp.yAxis};new s.a(this.ctx,a.b.y,[{text:isNaN(this.max)||!isFinite(this.max)?"0.00":this.max.toFixed(2),x:t.x+t.width,y:t.y,props:i},{text:isNaN(this.min)||!isFinite(this.min)?"0.00":this.min.toFixed(2),x:t.x+t.width,y:t.y+t.height,props:c({},i,{baseLine:n.a.baseLine.btm})}],t).draw()},i}(o.a);i.a=p},function(t,i,e){"use strict";function r(t){var i=t;try{i=JSON.stringify(t)}catch(t){console.error(t)}return i}function o(t,i){var e=0;return function(){var r=Date.now();r-e>t&&(i.apply(this,arguments),e=r)}}e.d(i,"b",function(){return n}),e.d(i,"g",function(){return s}),e.d(i,"e",function(){return a}),i.f=o,e.d(i,"d",function(){return h}),e.d(i,"c",function(){return c});var n=function(t,i){void 0===i&&(i=!1);var e,r,o={},n=[1,1e4,1e8,Number.MAX_SAFE_INTEGER],s=i?["万","亿","亿万"]:["","万","亿"];if(isNaN(t=parseFloat(t)))return t;for(e=1;e<n.length;e++)if(r=s[e-1],t<n[e]){var a=t/n[e-1],h=a.toString().split("."),c=h[1]?Math.min(4-h[0].length,2):0;o={v:a.toFixed(Math.max(c,0)),u:r};break}return o},s=function(t,i,e){if(void 0===i&&(i=2),void 0===e&&(e=!0),!isNaN(t)){var r=100*parseFloat(t),o=r>0?"+":"";return""+(e?o:"")+r.toFixed(i)+"%"}},a=function(t){for(var i in t)if(Object.prototype.hasOwnProperty.call(t,i))return!1;return r(t)===r(t)},h=function(){return window.devicePixelRatio},c=function(t,i){var e=i.mainIndicator,r=i.setting,o=(void 0===e?"":e)||"ma",n=Math.max(t.maxMin.kline.max,t.maxMin[o].max),s=Math.min(t.maxMin.kline.min,t.maxMin[o].min);if((void 0===r?{}:r).supportPressureLine){var a=p(t.items),h=a.support,c=a.pressure;h&&c&&(n=Math.max(n,c),s=Math.min(s,h))}return{max:n,min:s}},p=function(t){var i=t.length;if(!i)return{};var e=t[i-1]&&t[i-1].defboll;if(!e)return{};var r=e||{},o=r.upper,n=void 0===o?0:o,s=r.mid,a=void 0===s?0:s,h=r.lower,c=void 0===h?0:h,p=t[i-1].close;return n&&a&&c?{support:p>a?a:c,pressure:p>a?n:a}:{}};i.a={formatNumber:n,isEmptyObject:a,getKlineMaxMin:c,throttle:o,getPixelRatio:h}},function(t,i){var e=t.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=e)},function(t,i,e){var r=e(42)("wks"),o=e(28),n=e(4).Symbol,s="function"==typeof n;(t.exports=function(t){return r[t]||(r[t]=s&&n[t]||(s?n:o)("Symbol."+t))}).store=r},function(t,i){var e=t.exports={version:"2.6.2"};"number"==typeof __e&&(__e=e)},function(t,i,e){"use strict";e.d(i,"b",function(){return r}),e.d(i,"a",function(){return o});var r={x:"x",y:"y"},o={h5:0,wx:1,mp:2}},function(t,i,e){"use strict";var r=function(){function t(t){this.ctx=t.ctx,this.region=t.region,this.drawCallback=t.drawCallback,this.data=t.data,this.count=t.count,t.count||(this.count=this.data.items.length),this.draw()}return t.prototype.start=function(){void 0===this.data.middle&&(this.data.middle=(this.data.max+this.data.min)/2),this.data.diff=Math.max(Math.abs(this.data.max-this.data.middle),Math.abs(this.data.min-this.data.middle)),this.ctx.save(),this.ctx.translate(this.region.x,this.region.y+this.region.height/2)},t.prototype.draw=function(){var t=this,i=this.data.items;this.start();for(var e=0;e<i.length;e++)this.drawCallback({ctx:this.ctx,index:e,currItem:i[e],getX:function(i){return t.region.width/t.count*i},getY:function(i){return 0===t.data.diff?NaN:-(i-t.data.middle)/t.data.diff*(t.region.height/2)},length:this.data.items.length});this.end()},t.prototype.end=function(){this.ctx.restore()},t}();i.a=r},function(t,i,e){"use strict";e.d(i,"a",function(){return n});var r=e(7),o=e(0),n=function(){function t(t,i,e,r){this.ctx=t,this.type=i,this.labels=e,this.region=r}return t.prototype.getX=function(t,i){var e=i.props;if(this.type==r.b.y)return e.textAlign==o.a.textAlign.left?this.region.x:e.textAlign==o.a.textAlign.right?this.region.x+this.region.width:this.region.x;var n=this.region.x+i.x,s=Object(o.d)(this.ctx,""+i.text,i.props);return n+s/2>this.region.x+this.region.width&&(n=this.region.x+this.region.width-s/2),n},t.prototype.getY=function(t,i){var e=i.props;return this.type===r.b.x?e.baseLine==o.a.baseLine.top?this.region.y:e.baseLine==o.a.baseLine.btm?this.region.y+this.region.height:this.region.y+this.region.height/2:i.y},t.prototype.draw=function(){var t=this;this.labels.forEach(function(i,e){var r=t.getX(e,i);Object(o.c)(t.ctx,i.text,r,t.getY(e,i),i.props)})},t}()},function(t,i,e){var r=e(14);t.exports=function(t){if(!r(t))throw TypeError(t+" is not an object!");return t}},function(t,i,e){var r=e(4),o=e(6),n=e(25),s=e(12),a=e(16),h=function(t,i,e){var c,p,u,l=t&h.F,d=t&h.G,f=t&h.S,g=t&h.P,x=t&h.B,y=t&h.W,w=d?o:o[i]||(o[i]={}),m=w.prototype,v=d?r:f?r[i]:(r[i]||{}).prototype;for(c in d&&(e=i),e)(p=!l&&v&&void 0!==v[c])&&a(w,c)||(u=p?v[c]:e[c],w[c]=d&&"function"!=typeof v[c]?e[c]:x&&p?n(u,r):y&&v[c]==u?function(t){var i=function(i,e,r){if(this instanceof t){switch(arguments.length){case 0:return new t;case 1:return new t(i);case 2:return new t(i,e)}return new t(i,e,r)}return t.apply(this,arguments)};return i.prototype=t.prototype,i}(u):g&&"function"==typeof u?n(Function.call,u):u,g&&((w.virtual||(w.virtual={}))[c]=u,t&h.R&&m&&!m[c]&&s(m,c,u)))};h.F=1,h.G=2,h.S=4,h.P=8,h.B=16,h.W=32,h.U=64,h.R=128,t.exports=h},function(t,i,e){var r=e(13),o=e(27);t.exports=e(15)?function(t,i,e){return r.f(t,i,o(1,e))}:function(t,i,e){return t[i]=e,t}},function(t,i,e){var r=e(10),o=e(60),n=e(40),s=Object.defineProperty;i.f=e(15)?Object.defineProperty:function(t,i,e){if(r(t),i=n(i,!0),r(e),o)try{return s(t,i,e)}catch(t){}if("get"in e||"set"in e)throw TypeError("Accessors not supported!");return"value"in e&&(t[i]=e.value),t}},function(t,i){t.exports=function(t){return"object"==typeof t?null!==t:"function"==typeof t}},function(t,i,e){t.exports=!e(17)(function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a})},function(t,i){var e={}.hasOwnProperty;t.exports=function(t,i){return e.call(t,i)}},function(t,i){t.exports=function(t){try{return!!t()}catch(t){return!0}}},function(t,i,e){var r=e(64),o=e(38);t.exports=function(t){return r(o(t))}},function(t,i,e){"use strict";function r(t,i,e){this.ctx=t,this.props=i,this.region=e,this.draw()}i.a=r;var o=e(1);r.prototype.draw=function(){var t,i,e,r,n,s,a,h,c=this.ctx,p=this.region,u=this.props,l=u.hline||{},d=u.vline||{},f=u.mline||{},g=l.posy,x=d.posx;if(u.border&&Object(o.g)(c,p.x,p.y,p.width,p.height,u.border.color,null,u.border.lineWidth),u.wxSearchStyle&&(Object(o.c)(c,p.x,p.y,p.x+p.width,p.y,l.color,"dash",null,l.dashGapArray,l.lineWidth),Object(o.c)(c,p.x,p.y+p.height,p.x+p.width,p.y+p.height,l.color,"dash",null,l.dashGapArray,l.lineWidth)),g&&g.length>0)g.forEach(function(t){Object(o.c)(c,p.x,t.y,p.x+p.width,t.y,l.color,null,null,l.dashGapArray,l.lineWidth)});else if(l.count)for(t=p.height/(l.count+1),e=0;e<l.count;)h=t*++e+p.y,r=l.color,n=l.linestyle,s=l.lineWidth,l.skipMiddle&&e===(l.count+1)/2||(f.count&&e==(l.count+1)/2&&(r=f.color,n=f.linestyle,s=f.lineWidth),Object(o.c)(c,p.x,h,p.x+p.width,h,r,n,null,l.dashGapArray,s));if(x&&x.length>0)x.forEach(function(t){Object(o.c)(c,p.x+t.x,p.y,p.x+t.x,p.y+p.height,d.color,d.linestyle,null,d.dashGapArray,d.lineWidth)});else if(d.count)for(i=p.width/(d.count+1),e=0;e<d.count;)a=i*++e+p.x,Object(o.c)(c,a,p.y,a,p.y+p.height,d.color,d.linestyle,null,d.dashGapArray||null,d.lineWidth)}},function(t,i){t.exports=!0},function(t,i){t.exports={}},function(t,i,e){var r=e(63),o=e(43);t.exports=Object.keys||function(t){return r(t,o)}},function(t,i){var e={}.toString;t.exports=function(t){return e.call(t).slice(8,-1)}},function(t,i,e){"use strict";i.a=function(t,i,e,r,o){t&&t.clearRect(i,e,r,o)},i.b=function(t,i,e,r,o,n){return i>t?r:i<t?o:i>e?r:i<e?o:n}},function(t,i,e){var r=e(26);t.exports=function(t,i,e){if(r(t),void 0===i)return t;switch(e){case 1:return function(e){return t.call(i,e)};case 2:return function(e,r){return t.call(i,e,r)};case 3:return function(e,r,o){return t.call(i,e,r,o)}}return function(){return t.apply(i,arguments)}}},function(t,i){t.exports=function(t){if("function"!=typeof t)throw TypeError(t+" is not a function!");return t}},function(t,i){t.exports=function(t,i){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:i}}},function(t,i){var e=0,r=Math.random();t.exports=function(t){return"Symbol(".concat(void 0===t?"":t,")_",(++e+r).toString(36))}},function(t,i,e){var r=e(13).f,o=e(16),n=e(5)("toStringTag");t.exports=function(t,i,e){t&&!o(t=e?t:t.prototype,n)&&r(t,n,{configurable:!0,value:i})}},function(t,i){i.f=({}).propertyIsEnumerable},function(t,i,e){"use strict";var r=this&&this.__assign||Object.assign||function(t){for(var i,e=1,r=arguments.length;e<r;e++)for(var o in i=arguments[e],i)Object.prototype.hasOwnProperty.call(i,o)&&(t[o]=i[o]);return t},o=function(){function t(t,i){this.ctx=t;var e=i.devicePixelRatio,o=t.getContextCanvas(),n=0;/kline/.test(i.layout)?n=i.wxSearchStyle?19*e:15*e:"mins"===i.type&&(i.isHistoryMins?n=0:i.showIOPV&&(n=15*e)),this.props=r({padding:{top:n,bottom:0,left:0,right:0},yAxis:{width:50*e},xAxis:{height:15*e}},i),i.wxSearchStyle&&(this.props.xAxis.height=19*e),this.height=o.height,this.width=o.width;var s="kline-portrait"===i.layout?i.setting.indicatorCount:1;this.indicatorHeight=i.hideIndicator?0:this.height*[.219,.31,.402,.473][s-1],this.chartHeight=this.height-this.props.padding.top-this.props.padding.bottom-this.indicatorHeight-this.props.xAxis.height,this.chartWidth=this.width-this.props.padding.left-this.props.padding.right-this.props.yAxis.width,this.xAxis=this.chart=this.indicator={},this.chipRegion={width:"kline-portrait"===i.layout?.32*this.width:Math.max(400,.184*this.width),height:this.height},this.calculate()}return t.prototype.getWidth=function(){return this.width},t.prototype.getHeight=function(){return this.height},t.prototype.getChart=function(){return this.chart},t.prototype.getChartYAxis=function(){return this.chart.yAxis},t.prototype.getXAxis=function(){return this.xAxis},t.prototype.getIndicator=function(){return this.indicator},t.prototype.getIndicatorYAxis=function(){return this.indicator.yAxis},t}();i.a=o},function(t,i,e){"use strict";var r,o=e(19),n=e(81),s=e(8),a=e(1),h=e(0),c=e(33),p=e(24),u=this&&this.__extends||(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var e in i)i.hasOwnProperty(e)&&(t[e]=i[e])},function(t,i){function e(){this.constructor=t}r(t,i),t.prototype=null===i?Object.create(i):(e.prototype=i.prototype,new e)}),l=function(t){function i(i,e,r,o,n,s,a,h,c){var p=t.call(this,i,e,r,o,n,s)||this;return h&&(p.max=h),c&&(p.min=c),a&&a.length>0&&(p.indexTypes=a),p.loadPos={show:!1,x:p.region.x,y:p.region.y+7*p.region.height/12},p}return u(i,t),i.prototype.drawGrid=function(){var t=this.props.colorProp;new o.a(this.ctx,{border:{color:t.border,lineWidth:t.border},vline:{color:t.vline,width:t.vline,posx:this.props.posx}},this.region)},i.prototype.drawLinearShapes=function(){var t=this;if(this.indexTypes&&this.indexTypes.length>0)this.indexTypes.forEach(function(i){t.drawLines(i,t.data.items.map(function(t){return t[i]}))});else{var i=this.data.items[0][this.name];if("number"==typeof i||void 0===i)this.drawLines(this.name,this.data.items.map(function(i){return i[t.name]}));else{var e=this;for(var r in i)!function(r){i.hasOwnProperty(r)&&e.drawLines(r,e.data.items.map(function(i){return i[t.name][r]}))}(r)}}},i.prototype.drawLines=function(t,i){var e=this,r=-1,o=-1;if("close"===t){var n=i.filter(function(t){return"number"==typeof t}),a=Math.max.apply(Math,n),h=Math.min.apply(Math,n);r=i.indexOf(a),o=i.indexOf(h)}for(var c=0,p=0;p<i.length;p++)if(void 0!==i[p]&&!isNaN(i[p])){c=p;break}new s.a({ctx:this.ctx,region:this.region,drawCallback:function(i){e.drawLineItem(i,t,c,r,o)},data:{items:i,max:this.max,min:this.min},count:this.props.count}),this.textArr&&this.textArr.length>0&&this.drawMaxMin(this.textArr),this.loadPos.show&&this.drawLoadPos()},i.prototype.drawLineItem=function(t,i,e,r,o){if(!(t.index<e)){var n=this.props,s=t.index,p=t.getX(s)+n.itemWidth/2,u=t.getY(t.currItem),l=this.data.items[s];if("close"===i&&l.forBounce&&(this.loadPos={show:!0,x:this.region.x+3*(s-c.a+7)*this.props.devicePixelRatio,y:this.region.y+7*this.region.height/12}),s===r||s===o){var d={text:t.currItem,x:p,y:u,rightRegion:s>this.data.items.length/2,baseline:s===o?h.a.baseLine.btm:h.a.baseLine.top},f=this.textArr?[].concat(this.textArr):[];f.push(d),this.textArr=[].concat(f)}Object(a.d)(this.ctx,p,u,this.getLineColor(n.colorProp,i),this.props.lineProp.indicator,t.index===e?0:s,t.length)}},i.prototype.getLineColor=function(t,i){if(/^ma\d/.test(i)){var e=+i.slice(2),r=(this.props.setting.maTypes||[]).indexOf(e);return t.ma[r]}if(/^ema\d/.test(i)){var e=+i.slice(3),r=(this.props.setting.emaTypes||[]).indexOf(e);return t.ema[r]}if(/^volume\d/.test(i)){var e=+i.slice(6),r=(this.props.setting.volumeTypes||[]).indexOf(e);return t.ma[r]}if(/^cje\d/.test(i)){var e=+i.slice(3),r=(this.props.setting.cjeTypes||[]).indexOf(e);return t.ma[r]}return/close/.test(i)?t.close:"string"==typeof t[this.name]?t[this.name]:t[this.name][i]},i.prototype.drawMaxMin=function(t){var i,e,r,o,n,s,c,p=this.ctx,u=this.props.maxminDist,l=this.region;for(p.save(),p.translate(l.x,l.y+l.height/2),i=0;i<t.length;i++)if(t[i]){(o=t[i].text).toFixed&&(o=o.toFixed(this.props.fixNum||2)),n=t[i].rightRegion,e=Object(h.d)(p,o,{font:this.props.textProp.font}),s=t[i].x,c=t[i].y,u=this.props.maxminDist,s<16*this.props.devicePixelRatio?u*=s<5*this.props.devicePixelRatio?4:2.5:u=this.props.maxminDist,r=n?s-u-e:s+u,Object(h.c)(p,o,Math.max(r,0),c,{baseLine:t[i].baseline,color:this.props.colorProp.maxMin,font:this.props.textProp.font});var d=n?r+e:r;Object(a.c)(p,s,c,d,c,this.props.colorProp.maxMinLine,null,null,null,this.props.lineProp.maxmin)}p.restore()},i.prototype.drawLoadPos=function(){Object(h.c)(this.ctx,c.c,this.loadPos.x,this.loadPos.y,{color:"#3077EC",font:this.props.textProp.font}),"kline-landscape"===this.props.layout&&Object(p.a)(this.ctx,0,this.region.y,this.region.x,this.region.y+this.region.height)},i}(n.a);i.a=l},function(t,i,e){"use strict";function r(t){var i=a({defaultCount:60,indicatorList:[],maTypes:t.setting.maTypes||[],volumeTypes:t.setting.volumeTypes||[],emaPeriods:t.setting.emaTypes||[],cjeTypes:t.setting.cjeTypes||[],remindPrice:t.setting.remindPrice||!1,list:[],trendline:[],mainViewData:{currTrendline:{},lastestPrice:void 0,remindPrice:{}},maxKlineCount:{landscape:320,portrait:195}},t);for(var e in i)this[e]=i[e];this.validIndex=Math.max.apply(null,this.maTypes),this.requestEnd=!1,this.count=this.defaultCount,this.queryCount=t.queryCount,this.index=0,this.fetching=!1,this.needBounce=!1,this.reachBoundary=!1,u===t.market&&l===t.scode||(u=t.market,l=t.scode,x=!1,g.clear()),this.switchIndicator()}e.d(i,"c",function(){return d}),e.d(i,"a",function(){return f}),e.d(i,"d",function(){return g}),e.d(i,"e",function(){return x}),i.b=r;var o,n=e(82),s=e(151),a=this&&this.__assign||Object.assign||function(t){for(var i,e=1,r=arguments.length;e<r;e++)for(var o in i=arguments[e],i)Object.prototype.hasOwnProperty.call(i,o)&&(t[o]=i[o]);return t},h=this&&this.__awaiter||function(t,i,e,r){return new(e||(e=Promise))(function(o,n){function s(t){try{h(r.next(t))}catch(t){n(t)}}function a(t){try{h(r.throw(t))}catch(t){n(t)}}function h(t){t.done?o(t.value):new e(function(i){i(t.value)}).then(s,a)}h((r=r.apply(t,i||[])).next())})},c=this&&this.__generator||function(t,i){function e(e){return function(s){return function(e){if(r)throw TypeError("Generator is already executing.");for(;a;)try{if(r=1,o&&(n=2&e[0]?o.return:e[0]?o.throw||((n=o.return)&&n.call(o),0):o.next)&&!(n=n.call(o,e[1])).done)return n;switch(o=0,n&&(e=[2&e[0],n.value]),e[0]){case 0:case 1:n=e;break;case 4:return a.label++,{value:e[1],done:!1};case 5:a.label++,o=e[1],e=[0];continue;case 7:e=a.ops.pop(),a.trys.pop();continue;default:if(!(n=(n=a.trys).length>0&&n[n.length-1])&&(6===e[0]||2===e[0])){a=0;continue}if(3===e[0]&&(!n||e[1]>n[0]&&e[1]<n[3])){a.label=e[1];break}if(6===e[0]&&a.label<n[1]){a.label=n[1],n=e;break}if(n&&a.label<n[2]){a.label=n[2],a.ops.push(e);break}n[2]&&a.ops.pop(),a.trys.pop();continue}e=i.call(t,a)}catch(t){e=[6,t],o=0}finally{r=n=0}if(5&e[0])throw e[1];return{value:e[0]?e[1]:void 0,done:!0}}([e,s])}}var r,o,n,s,a={label:0,sent:function(){if(1&n[0])throw n[1];return n[1]},trys:[],ops:[]};return s={next:e(0),throw:e(1),return:e(2)},"function"==typeof Symbol&&(s[Symbol.iterator]=function(){return this}),s};(p=o||(o={})).HAS_MORE="加载数据",p.LOADING="加载中...",p.NO_MORE="已到上市首日";var p,u,l,d=o.HAS_MORE,f=0,g=new Map,x=!1;r.prototype={switchIndicator:function(t,i){var e=["main","curr","second","third","fourth"];this[e[t]+"Indicator"]=i,this.indicatorList=[];for(var r="kline-portrait"===this.layout?this.setting.indicatorCount:1,o=0;o<=r;o++)this.indicatorList.push(this[e[o]+"Indicator"]);this.setting.supportPressureLine&&this.indicatorList.push("defboll"),this.indicatorList=Array.from(new Set(this.indicatorList)),this.list.length>0&&this.format(i)},format:function(t){var i,e,r,o,s,a,h=this,c={},p={};for(["open","close","low","high","volume","cje"].forEach(function(t){p[t]=h.list.map(function(i){return i[t]})}),(t?[t]:this.indicatorList).forEach(function(t){if("ma"===t)for(var i=0,e=h.maTypes;i<e.length;i++){var r=e[i];r>0&&(c["ma"+r]=n.a.MA(p.close,r))}else if("volume"===t)for(var o=0,s=h.volumeTypes;o<s.length;o++){var r=s[o];r>0&&(c["volume"+r]=n.a.MA(p.volume,r))}else if(/^ema$/.test(t))h.emaPeriods.forEach(function(i){var e;if(i>0){var r=n.a.EMA(p.close,i);c["ema"+i]=r,c[t]?c[t][i]=r:c[t]=((e={})[i]=r,e)}});else if("cje"===t)for(var a=0,u=h.cjeTypes;a<u.length;a++){var r=u[a];r>0&&(c["cje"+r]=n.a.MA(p.cje,r))}else/^defboll$/.test(t)?h.setting&&h.setting.supportPressureLine&&"day"===h.type&&h.list.length>=20&&(c[t]=n.a.boll(p)):"rally"===t||(c[t]=n.a[t](p,h.setting[t+"Params"]))}),e=this.list.length-1;e>=0;e--)for(r in i=this.list[e],c)if(Array.isArray(o=c[r]))i[r]=o[e];else{for(s in a={},o)a[s]=o[s][e];i[r]=a}this.setting.magicNine&&!t&&this.calcMagicNine()},calcMagicNine:function(){for(var t=this.list.length,i=4;i<t;i++){for(var e=!0,r=i;r<i+9;r++){if(!this.list[r]){e=r-i>=6;break}if(this.list[r].close<=this.list[r-4].close){e=!1,i+=r-i;break}}if(e){for(var r=i;r<i+9&&r<t;r++)this.list[r].magicNine=r-i+1;i+=8}}for(var i=4;i<t;i++){for(var e=!0,r=i;r<i+9;r++){if(!this.list[r]){e=r-i>=6;break}if(this.list[r]&&this.list[r].close>=this.list[r-4].close){e=!1,i+=r-i;break}}if(e){for(var r=i;r<i+9&&r<t;r++)this.list[r].magicNine=-(r-i+1);i+=8}}},fetchForInit:function(t){return h(this,void 0,void 0,function(){var i;return c(this,function(i){switch(i.label){case 0:return[4,this.fetchMore()];case 1:return i.sent()?(this.updateMainViewData(),t&&t(this.list.slice(this.index,this.index+this.count),this.mainViewData),this.remindPrice?[4,this.queryPriceRemind()]:[3,3]):[2];case 2:i.sent()&&t&&t(this.list.slice(this.index,this.index+this.count),this.mainViewData),i.label=3;case 3:return[2]}})})},fetchForSwipe:function(t,i){if(this.needBounce=!1,this.reachBoundary=!1,f=Math.round(this.count/5),t>0){if(this.list.length<this.count)return;this.index+t>this.list.length-this.count?(this.needBounce=!0,this.index=Math.min(this.index+t,this.list.length-this.count+f),this.reachBoundary=this.index===this.list.length-this.count+f):this.index=this.index+t}else if(t<0){if(this.index+t<0)return this.requestEnd&&(f=Math.round(this.count/4)),this.needBounce=!0,this.index=Math.max(this.index+t,-f),this.reachBoundary=this.index===-f,this.getMainViewData(),void(i&&i(this.getBounceList().concat(this.list.slice(0,this.index+this.count)),this.mainViewData));this.index=this.index+t,this.index<=100&&!this.fetching&&this.fetchMore()}this.getMainViewData(),i&&i(this.list.slice(this.index,this.index+this.count),this.mainViewData)},fetchBoundary:function(t){var i=this;this.needBounce=!1,this.reachBoundary=!1;var e=Math.ceil(this.count/30);if(this.index<0){this.requestEnd||(d=o.LOADING,t&&t());var r=this.index,n=Math.abs(r),s=this.getBounceList().concat(this.list.slice(0,this.count)),a=this.trendline.slice();this.fetchMore().then(function(){var o=i.trendline.slice(),h=setInterval(function(){r=Math.min(r+e,0),i.trendline=a,0===r?(clearInterval(h),i.index=i.requestEnd?0:i.index+n,i.trendline=o,i.getMainViewData()):i.getMainViewData(Math.abs(r)),t&&t(s.slice(r+n,r+n+i.count),i.mainViewData)},16)})}else var h=setInterval(function(){i.index=Math.max(i.index-e,i.list.length-i.count),i.getMainViewData(),t&&t(i.list.slice(i.index,i.index+i.count),i.mainViewData),i.index===i.list.length-i.count&&clearInterval(h)},16)},fetchForPinch:function(t,i,e){if(i<0)this.count>20&&(i=Math.abs(i),this.index+=this.list.length<this.count?0:Math.round(i*t),this.count-=i,this.getMainViewData(),e&&e({list:this.list.slice(this.index,this.index+this.count),extraData:this.mainViewData}));else{var r="kline-landscape"===this.layout?this.maxKlineCount.landscape:this.maxKlineCount.portrait;if(this.count<r){var o=Math.min(Math.round(i*(1-t)),this.list.length-(this.index+this.count)),n=i-o;this.index<n&&!this.requestEnd?this.fetchMore():(this.index=Math.max(this.index-n,0),this.count+=i,this.getMainViewData(),e&&e({list:this.list.slice(this.index,this.index+this.count),extraData:this.mainViewData}))}else console.log("kline count over MaxKlineCount:--\x3e",this.count),e&&e({type:"line"})}},fetchMore:function(){var t=this;if(this.fetching||this.requestEnd)return Promise.resolve();this.fetching=!0;var i=this.list&&this.list[0]&&this.list[0].time||"";return this.request(i).then(function(i){var e=i&&i.length;return t.requestEnd=0===e,d=t.requestEnd?o.NO_MORE:o.HAS_MORE,t.requestEnd||(t.resetIndex(e),t.list=i.concat(t.list),t.format(),t.trendline=t.getTrendline(),e<t.queryCount&&(t.requestEnd=!0,d=o.NO_MORE)),t.fetching=!1,t.list}).catch(function(){d=o.HAS_MORE,t.fetching=!1})},queryPriceRemind:function(){var t=this;return this.query().then(function(i){if(i&&i.qlist&&0!==i.qlist.length){var e=i.qlist[0];+e.upflag&&(t.mainViewData.remindPrice.upPrice=e.upprice),+e.downflag&&(t.mainViewData.remindPrice.downPrice=e.downprice)}return!0}).catch(function(t){console.error(t)})},resetIndex:function(t){var i=this.list.length;this.index=i>0?t+this.index:Math.max(t-this.count,0)},getBounceList:function(){for(var t=Object.assign({forBounce:!0},this.list[0]),i=0,e=this.indicatorList;i<e.length;i++){var r=e[i];if("object"==typeof t[r])for(var o in t[r]=Object.assign({},t[r]),t[r])t[r][o]=void 0;else t[r]=void 0}for(var n=[],s=-1;s>=this.index;s--)n.push(t);return n},repaint:function(t){t&&t(this.list.slice(this.index,this.index+this.count),this.mainViewData)},updateRepaint:function(t,i){Object.assign(this.list[this.list.length-1],t[t.length-1]),this.format(),this.updateMainViewData(),(this.setting.lastestPrice||this.index+this.count===this.list.length)&&i&&i(this.list.slice(this.index,this.index+this.count),this.mainViewData)},ZXRepaint:function(t,i){t?this.list[this.list.length-1].fh.since_add_zdf="0.00":this.list.forEach(function(t){delete t.fh.since_add_zdf}),i&&i(this.list.slice(this.index,this.index+this.count),this.mainViewData)},tradeRepaint:function(t,i){t[0].bst_exist?x=!0:t.forEach(function(t){var i=t.d.slice(0,4)+"-"+t.d.slice(4,6)+"-"+t.d.slice(6);g.set(i,t.t)}),i&&i(this.list.slice(this.index,this.index+this.count),this.mainViewData)},magicNineRepaint:function(t,i){t&&this.calcMagicNine(),i&&i(this.list.slice(this.index,this.index+this.count),this.mainViewData)},tradeSecretRepaint:function(t,i){var e=t.data,r=void 0===e?[]:e;if(void 0===this.list[0].rally)for(var o=0;r.length,o<this.list.length;o++)this.list[o].tradeSecret=r[o].key_point,this.list[o].rally=r[o].val/1e3;else this.list[this.list.length-1].rally=t.realtime_val/1e3;i&&i(this.list.slice(this.index,this.index+this.count),this.mainViewData)},getSiblingData:function(t){var i=0,e=this.list.length-1;if(this.list[0].time===t)return{index:0,pre:null,now:this.list[0],next:this.list[1]};if(this.list[e].time===t)return{index:e,pre:this.list[e-1],now:this.list[e],next:null};for(;i<e;){var r=Math.round((i+e)/2);if(this.list[r].time===t)return{index:r,pre:this.list[r-1],now:this.list[r],next:this.list[r+1]};this.list[r].time<t?i=r:this.list[r].time>t&&(e=r)}return{pre:null,now:null,next:null}},getRightData:function(){return this.list[this.index+this.count-1]},updateMainViewData:function(){this.getLastestPrice(),this.getCurrTrendline()},getMainViewData:function(){this.getCurrTrendline()},getTrendline:function(){var t=this,i=[];return this.setting&&this.setting.trendline&&"day"===this.type&&this.list.forEach(function(e,r){var o=i.length;if(0===o)return void i.push(0);var n=t.list[r].close,s=i[o-1],a=t.list[s].close,h=Math.abs((n-a)/a)>=.05;if(1===o)return void(h&&i.push(r));var c=i[o-2];a>t.list[c].close?n>=a?i[o-1]=r:h&&i.push(r):n<=a?i[o-1]=r:h&&i.push(r)}),i},getCurrTrendline:function(t){if(this.setting&&this.setting.trendline&&"day"===this.type){var i,e,r=this.trendline.length,o=-1,n=-1;this.mainViewData.currTrendline={left:{},right:{},point:[]},t?(i=0,e=this.count-t-1):(i=Math.max(this.index,0),e=this.index+this.count-1);for(var s=0;s<r;s++){var a=this.trendline[s],h=void 0;if(a<i)o=a;else{if(a===i&&(o=-1),a===e&&(n=-1),a>e){n=a;break}h=t?a+t:this.index<0?a+Math.abs(this.index):a-i,this.mainViewData.currTrendline.point.push(h)}}-1!==o&&(this.mainViewData.currTrendline.left.intervalNum=i-o,this.mainViewData.currTrendline.left.close=this.list[o].close),-1!==n&&(this.mainViewData.currTrendline.right.intervalNum=n-e+1,this.mainViewData.currTrendline.right.close=this.list[n].close)}},getLastestPrice:function(){this.mainViewData.lastestPrice=this.list[this.list.length-1]&&this.list[this.list.length-1].close},getChipList:function(t,i){if(t)try{var e=this.list.findIndex(function(i){return i.time===t.time});return this.chipModel=new s.a({list:this.list,targetIndex:e,type:this.type,crossPrice:i}),this.chipModel.getData(e)}catch(t){throw console.error("getChip error",t),Error(t)}}}},function(t,i,e){"use strict";var r=e(182),o=e(183),n=e(184),s=e(185);i.a={kline:{plain:r.a,dark:n.a},mins:{plain:o.a,dark:s.a}}},function(t,i,e){"use strict";i.a={white:"#ffffff",black:"#000000",red_e63:"#e63535",green_2db:"#2db955",gray_929:"#9299aa",yellow_ffa:"#ffa200",gray_f3:"#f3f3f3",gray_8:"#888888",blue_007:"#007aff",yellow_ff8:"#ff891e",purple_d90:"#d907ff",purple_9f2:"#9f2cc5",gray_b2b:"#b2b2b2",gray_a_15:"rgba(0, 0, 0, 0.15)",green_a_80:"rgba(45,185,85, .8)",red_a_80:"rgba(231,49,70, .8)",gray_e6:"#e6e6e6",black_384:"#38486a",blue_1d6:"#1d6ece",red_e73:"#e73146",blue_428:"#4280f2",trans:"rgba(66,128,242,0.1)",transparent:"transparent",blue_9ff:"rgba(0,121,255,.12)",gray_e9e:"#868E9E",green_8ac:"#8ac677",gray_e9eb:"#e9ebf0",gray_98a:"#98a0b3",blue_307:"#3077ec",gray_a7b:"#a7b0c4"}},function(t,i,e){"use strict";var r=e(3),o=function(){function t(t,i,e,o,n){this.ctx=t,this.data=i.items,this.mainViewData=e,this.props=o,this.region=n;var s=Object(r.c)(i,o),a=s.max,h=s.min;this.max=a,this.min=h,this.diff=-1,this.count=o.count,o.count||(this.count=this.data.items.length)}return t.prototype.traverseData=function(t){var i=this,e=this.props.itemWidth;this.data.forEach(function(r,o){t({index:o,x:i.dataToCoord().getX(o)+e/2,y1:i.dataToCoord().getY(r.open),y2:i.dataToCoord().getY(r.close),bottomY:i.dataToCoord().getY(r.low),topY:i.dataToCoord().getY(r.high)})})},t.prototype.dataToCoord=function(){var t=this,i=(this.max+this.min)/2,e=this.region.chart;return this.diff=Math.max(Math.abs(this.max-i),Math.abs(this.min-i)),{getX:function(i){return e.width/t.count*i},getY:function(r){return 0===t.diff?0:-(r-i)/t.diff*(e.height/2)}}},t.prototype.formatMoney=function(t,i){return void 0===i&&(i=2),t.toFixed(i)},t}();i.a=o},function(t,i){var e=Math.ceil,r=Math.floor;t.exports=function(t){return isNaN(t=+t)?0:(t>0?r:e)(t)}},function(t,i){t.exports=function(t){if(void 0==t)throw TypeError("Can't call method on  "+t);return t}},function(t,i,e){var r=e(14),o=e(4).document,n=r(o)&&r(o.createElement);t.exports=function(t){return n?o.createElement(t):{}}},function(t,i,e){var r=e(14);t.exports=function(t,i){var e,o;if(!r(t))return t;if(i&&"function"==typeof(e=t.toString)&&!r(o=e.call(t))||"function"==typeof(e=t.valueOf)&&!r(o=e.call(t))||!i&&"function"==typeof(e=t.toString)&&!r(o=e.call(t)))return o;throw TypeError("Can't convert object to primitive value")}},function(t,i,e){var r=e(42)("keys"),o=e(28);t.exports=function(t){return r[t]||(r[t]=o(t))}},function(t,i,e){var r=e(6),o=e(4),n=o["__core-js_shared__"]||(o["__core-js_shared__"]={});(t.exports=function(t,i){return n[t]||(n[t]=void 0!==i?i:{})})("versions",[]).push({version:r.version,mode:e(20)?"pure":"global",copyright:"\xa9 2019 Denis Pushkarev (zloirock.ru)"})},function(t,i){t.exports="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",")},function(t,i,e){var r=e(38);t.exports=function(t){return Object(r(t))}},function(t,i,e){"use strict";function r(t){var i,e;this.promise=new t(function(t,r){if(void 0!==i||void 0!==e)throw TypeError("Bad Promise constructor");i=t,e=r}),this.resolve=o(i),this.reject=o(e)}var o=e(26);t.exports.f=function(t){return new r(t)}},function(t,i,e){t.exports={default:e(126),__esModule:!0}},function(t,i){i.f=Object.getOwnPropertySymbols},function(t,i,e){i.f=e(5)},function(t,i,e){var r=e(4),o=e(6),n=e(20),s=e(48),a=e(13).f;t.exports=function(t){var i=o.Symbol||(o.Symbol=n?{}:r.Symbol||{});"_"==t.charAt(0)||t in i||a(i,t,{value:s.f(t)})}},function(t,i,e){"use strict";var r,o=e(32),n=this&&this.__extends||(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var e in i)i.hasOwnProperty(e)&&(t[e]=i[e])},function(t,i){function e(){this.constructor=t}r(t,i),t.prototype=null===i?Object.create(i):(e.prototype=i.prototype,new e)}),s=function(t){function i(i,e,r,o,n,s,a){return t.call(this,i,e,r,o,"ma",null,n,s,a)||this}return n(i,t),i.prototype.drawScale=function(){},i.prototype.drawGrid=function(){},i.prototype.drawBar=function(){},i}(o.a);i.a=s},function(t,i,e){"use strict";var r,o=e(52),n=this&&this.__extends||(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var e in i)i.hasOwnProperty(e)&&(t[e]=i[e])},function(t,i){function e(){this.constructor=t}r(t,i),t.prototype=null===i?Object.create(i):(e.prototype=i.prototype,new e)}),s=function(t){function i(i){return t.call(this,i)||this}return n(i,t),i.prototype.getMaxChangeSum=function(){return 3},i.prototype.getMaxKlineCount=function(){return 370},i.prototype.getMinKlineCount=function(){return 121},i.prototype.getPriceCount=function(){return 200},i}(o.a);i.a=s},function(t,i,e){"use strict";e.d(i,"a",function(){return a});var r,o=e(153),n=this&&this.__extends||(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var e in i)i.hasOwnProperty(e)&&(t[e]=i[e])},function(t,i){function e(){this.constructor=t}r(t,i),t.prototype=null===i?Object.create(i):(e.prototype=i.prototype,new e)}),s=this&&this.__assign||Object.assign||function(t){for(var i,e=1,r=arguments.length;e<r;e++)for(var o in i=arguments[e],i)Object.prototype.hasOwnProperty.call(i,o)&&(t[o]=i[o]);return t},a=function(t){function i(i){return t.call(this,i)||this}return n(i,t),i.prototype.getGraphDataCount=function(t,i){for(var e=0,r=0,o=i;o>=0;o--){var n=t[o];if(n&&(e+=this.tansformToRealChange(n.hsl),r++,e>this.getMaxChangeSum()&&r>=this.getMinKlineCount()&&r<this.getMaxKlineCount()))break}return e>=this.getMaxChangeSum()&&r>=this.getMinKlineCount()&&r<this.getMaxKlineCount()?r:r<this.getMaxKlineCount()?r:this.getMaxKlineCount()},i.prototype.getMaxMinPrice=function(t,i,e){for(var r=[],o=i;o>i-e&&o>=0;o--){var n=t[o];r.push(+n.high),r.push(+n.low)}return{max:Math.max.apply(Math,r),min:Math.min.apply(Math,r)}},i.prototype.genDayKlineData=function(t,i,e){for(var r=[],o=i,n=0;o>=0&&n<e;o--,n++)for(var a=t[o],h=0;h<a.tradeDays;h++)r.unshift(s({},a,{hsl:a.hsl/a.tradeDays,tradeDays:1}));return r},i.prototype.getATurn=function(t,i,e,r){var o=i-e;return 0==o?this.tansformToRealChange(t[e].hsl):(r[o]=r[o-1]*(1-this.tansformToRealChange(t[e+1].hsl)),this.tansformToRealChange(t[e].hsl)*r[o])},i.prototype.calc=function(t,i){try{var e=this.getGraphDataCount(t,i);if(0===e)return console.error("chip calc klineCount = 0"),null;for(var r=this.getMaxMinPrice(t,i,e),o=this.getPriceCount()-1,n=Math.abs(r.max-r.min)/o,s=[],a=[],h=[1],c=0;c<=o;c++)s.push(r.min+c*n);var p=t[i];p&&a.push(this.tansformToRealChange(p.hsl));for(var u=i-1;u>i-e&&u>=0;u--)a[i-u]=this.getATurn(t,i,u,h);for(var l=Array(o+1).fill(0),d=i;d>i-e&&d>=0;d--){var f=t[d];if(f){var g=f.high,x=f.low,y=(g+x)/2,w=this.findPriceSection(t,d,s,n);w.from>w.to&&(w.to=w.from);for(var m=Array(o+1).fill(0),v=0,b=w.from;b<=w.to;b++)s[b]>=y?m[b]=g-s[b]:m[b]=s[b]-x,v+=m[b];for(var M=w.from;M<=w.to;M++){var C=void 0;C=Math.abs(v)>1e-6?a[i-d]*m[M]/v:a[i-d],l[M]=(isNaN(l[M])?0:l[M])+C}}}return{chip:l,maxMin:r,price:s}}catch(t){return console.error("chip calc error",t),null}},i.prototype.findPriceSection=function(t,i,e,r){var o=t[i];if(null!=o){var n,s,a={from:0,to:e.length-1};for(n=0;n<e.length;n++)if(o.low<=e[n]){a.from=n;break}for(s=e.length-1;s>=0;s--)if(o.high>=e[s]){a.to=s;break}return a}},i.prototype.tansformToRealChange=function(t){return parseFloat(t)/100},i}(o.a)},function(t,i,e){"use strict";i.a=function(t,i){var e=t.getBoundingClientRect();return{x:i.clientX-e.left,y:i.clientY-e.top}}},function(t,i){t.exports="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDIxLjAuMCwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDYuMDAgQnVpbGQgMCkgIC0tPgo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IuWbvuWxgl8xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB4PSIwcHgiIHk9IjBweCIKCSB2aWV3Qm94PSIwIDAgMTAgMTgiIHN0eWxlPSJlbmFibGUtYmFja2dyb3VuZDpuZXcgMCAwIDEwIDE4OyIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+CjxzdHlsZSB0eXBlPSJ0ZXh0L2NzcyI+Cgkuc3Qwe2ZpbGw6IzMwNzdFQzt9Cjwvc3R5bGU+Cjx0aXRsZT7ot6/lvoQ8L3RpdGxlPgo8ZyBpZD0iS+e6v+WbvuWinuWKoOS5sOWNlueCueagh+iusCI+Cgk8ZyBpZD0i55S75p2/5aSH5Lu9LTEyIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgtNzI0LjAwMDAwMCwgLTY4NC4wMDAwMDApIj4KCQk8ZyBpZD0i57yW57uELTnlpIfku70iIHRyYW5zZm9ybT0idHJhbnNsYXRlKDYxNy4wMDAwMDAsIDY3MC4wMDAwMDApIj4KCQkJPGcgaWQ9Iue8lue7hCIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMTMuMDAwMDAwLCAxMi4wMDAwMDApIj4KCQkJCTxnIGlkPSLot6/lvoQtMyIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoODYuNTE0NzE5LCAyLjUxNDcxOSkiPgoJCQkJCTxwYXRoIGlkPSLot6/lvoQiIGNsYXNzPSJzdDAiIGQ9Ik0xMy40LDkuMmwtNS43LDUuN2wwLDBjLTAuNCwwLjQtMC40LDEsMCwxLjRjMC40LDAuNCwxLDAuNCwxLjQsMGw3LjEtNy4xCgkJCQkJCWMwLjQtMC40LDAuNC0xLDAtMS40TDkuMiwwLjdsMCwwYy0wLjQtMC40LTEtMC40LTEuNCwwYy0wLjQsMC40LTAuNCwxLDAsMS40bDUuNyw1LjdDMTMuOCw4LjIsMTMuOCw4LjgsMTMuNCw5LjJ6Ii8+CgkJCQk8L2c+CgkJCTwvZz4KCQk8L2c+Cgk8L2c+CjwvZz4KPC9zdmc+Cg=="},function(t,i,e){"use strict";e.d(i,"c",function(){return o}),i.b=function(t){o=t};var r=e(3),o=!1,n=function(){function t(t,i,e){this.tapCount=0,this.canvas=t,this.options=i,this.throttleSwipe=Object(r.f)(16,this.swipe),e&&(this.onTap=e.onTap,this.onCrossLineTap=e.onCrossLineTap,this.onDoubleTap=e.onDoubleTap,e.onBeforeTouchMove&&(this.onBeforeTouchMove=e.onBeforeTouchMove),this.onTouchMove=Object(r.f)(16,e.onTouchMove),this.onSwipeX=e.onSwipeX,this.onSwipeY=e.onSwipeY,this.onPinch=Object(r.f)(16,e.onPinch),this.onPinchEnd=e.onPinchEnd,this.onTouchEnd=e.onTouchEnd,this.onTouchCancle=e.onTouchCancle,this.onTouchStart=e.onTouchStart)}return t.prototype.handleEvent=function(t){if(window.TouchEvent&&t instanceof window.TouchEvent||window.$wujie)return this.isTouchMode=!0,void("touchstart"===t.type?this.start(t):"touchmove"===t.type?this.move(t):"touchend"===t.type&&this.end(t));if(t instanceof window.MouseEvent){if("mousewheel"===t.type)return t.preventDefault(),void(this.onPinch&&this.onPinch(t.pageX,t.wheelDelta>=0));if(this.isTouchMode)return;var i=void 0,e={identifier:0,target:t.target,clientX:t.clientX,clientY:t.clientY,pageX:t.pageX,pageY:t.pageY,screenX:t.screenX,screenY:t.screenY};if(window.Touch&&window.TouchEvent){var r=new window.Touch(e);i=new window.TouchEvent("simulateTouch",{touches:[r],changedTouches:[r]})}else i={touches:[e],changedTouches:[e]};"mousedown"===t.type?(this.isMouseDown=!0,this.start(i)):"mousemove"===t.type?this.isMouseDown&&this.move(i):"mouseup"===t.type&&(this.isMouseDown=!1,this.end(i))}},t.prototype.start=function(t){var i=this;if(this.isTap=!0,this.isPinch=!1,this.isInertia=!1,this.parentScrolling=!1,t.touches&&2===t.touches.length)return clearTimeout(this.tapTimeout),this.tapCount=0,this.isPinch=!0,void(this.pinchDistance=Math.sqrt(Math.pow(t.touches[0].pageX-t.touches[1].pageX,2)+Math.pow(t.touches[0].pageY-t.touches[1].pageY,2)));if(t.touches&&1===t.touches.length){if(2==++this.tapCount)return clearTimeout(this.tapTimeout),this.isTap=!1,this.tapCount=0,void(this.onDoubleTap&&this.onDoubleTap(t));this.isCrossLine||(this.longTapTimeout=setTimeout(function(){i.isTap=!1,i.tapCount=0,i.showCrossLine(t.touches[0])},500)),this.onTouchStart&&this.onTouchStart(t.touches[0])}this.lastTouch=this.firstTouch={x:t.touches[0].pageX,y:t.touches[0].pageY},this.lastMoveTime=Date.now(),this.lastMoveStart=t.touches[0].pageX},t.prototype.move=function(t){if(this.e=t,this.isTap=!1,this.tapCount=0,clearTimeout(this.longTapTimeout),this.isCrossLineTouchEnd){if(/mins/.test(this.options.layout))return;/kline/.test(this.options.layout)&&this.hideCrossLine()}if(this.isPinch){if(this.preventParentScroll(),t.touches&&2===t.touches.length){this.pinchCenter=(t.touches[0].pageX+t.touches[1].pageX)/2;var i=Math.sqrt(Math.pow(t.touches[0].pageX-t.touches[1].pageX,2)+Math.pow(t.touches[0].pageY-t.touches[1].pageY,2));this.onPinch&&this.onPinch(this.pinchCenter,i-this.pinchDistance>=0),this.pinchDistance=i}}else{if(t.touches&&1===t.touches.length&&this.onBeforeTouchMove&&this.onBeforeTouchMove(t.touches[0]))return void t.preventDefault();this.isCrossLine?(this.preventParentScroll(),this.showCrossLine(t.touches[0])):this.throttleSwipe(this.lastTouch,{x:t.touches[0].pageX,y:t.touches[0].pageY});var e=Date.now();e-this.lastMoveTime>300&&(this.lastMoveTime=e,this.lastMoveStart=t.touches[0].pageX)}},t.prototype.end=function(t){var i=this;if(clearTimeout(this.longTapTimeout),this.isPinch)return void(0===t.touches.length&&this.onPinchEnd&&this.onPinchEnd());if(this.isTap){if(this.isCrossLine){var e=this.onCrossLineTap&&this.onCrossLineTap(t);e||(this.isKlineWithHistoryMins?(this.showCrossLine(t.changedTouches[0]),this.isCrossLineTouchEnd=!0):this.hideCrossLine())}else{var e=this.onTap&&this.onTap(t);e||(this.showCrossLine(t.changedTouches[0]),this.isKlineWithHistoryMins||(this.cancelTimeout=setTimeout(function(){i.hideCrossLine()},4e3)))}this.tapTimeout=setTimeout(function(){i.tapCount=0},200)}else if(this.isCrossLine){if(this.isCrossLineTouchEnd)return;this.isKlineWithHistoryMins||(this.cancelTimeout=setTimeout(function(){i.hideCrossLine()},4e3))}else if(Math.abs(this.lastTouch.x-this.lastMoveStart)>30){if(this.parentScrolling)return;this.isInertia=!0;var r=Date.now(),o=(this.lastTouch.x-this.lastMoveStart)/(r-this.lastMoveTime),n=Math.abs(o),s=o>0?1:-1,a=function(){var t=Date.now()-r,e=n-.003*t;if(e<=0||!i.isInertia)return void i.swipe(null,null);i.throttleSwipe(i.lastTouch,{x:i.lastTouch.x+e*t*s,y:i.lastTouch.y}),requestAnimationFrame(a)};a()}else this.swipe(null,null);this.onTouchEnd&&this.onTouchEnd()},t.prototype.showCrossLine=function(t){this.isPinch||(clearTimeout(this.cancelTimeout),this.isCrossLine=!0,this.onTouchMove&&this.onTouchMove(t))},t.prototype.hideCrossLine=function(){clearTimeout(this.cancelTimeout),this.isCrossLine=!1,this.isCrossLineTouchEnd=!1,this.onTouchCancle&&this.onTouchCancle()},t.prototype.toggleKlineWithHistoryMins=function(t){this.isKlineWithHistoryMins=void 0===t?!this.isKlineWithHistoryMins:t,this.isKlineWithHistoryMins?clearTimeout(this.cancelTimeout):this.hideCrossLine()},t.prototype.cancleAll=function(){clearTimeout(this.tapTimeout),clearTimeout(this.longTapTimeout),clearTimeout(this.cancelTimeout)},t.prototype.swipe=function(t,i){if(t&&i){if(Math.abs(t.x-i.x)>Math.abs(t.y-i.y)){if(o=!0,!this.parentScrolling)this.preventParentScroll(),this.onSwipeX&&this.onSwipeX(t.x-i.x),this.lastTouch=i}else Math.abs(t.y-i.y)>0&&(this.parentScrolling=!0),this.onSwipeY&&this.onSwipeY(t.y-i.y)}else o=!1,this.onSwipeX&&this.onSwipeX(null),this.onSwipeY&&this.onSwipeY(null)},t.prototype.preventParentScroll=function(){this.e.preventDefault&&this.e.preventDefault(),this.e.stopPropagation&&this.e.stopPropagation()},t}();i.a=n},function(t,i){t.exports=function(t,i,e,r){var o,n=t=t||{},s=typeof t.default;"object"!==s&&"function"!==s||(o=t,n=t.default);var a="function"==typeof n?n.options:n;if(i&&(a.render=i.render,a.staticRenderFns=i.staticRenderFns),e&&(a._scopeId=e),r){var h=Object.create(a.computed||null);Object.keys(r).forEach(function(t){var i=r[t];h[t]=function(){return i}}),a.computed=h}return{esModule:o,exports:n,options:a}}},function(t,i){},function(t,i,e){"use strict";var r=e(103)(!0);e(59)(String,"String",function(t){this._t=String(t),this._i=0},function(){var t,i=this._t,e=this._i;return e>=i.length?{value:void 0,done:!0}:(t=r(i,e),this._i+=t.length,{value:t,done:!1})})},function(t,i,e){"use strict";var r=e(20),o=e(11),n=e(61),s=e(12),a=e(21),h=e(104),c=e(29),p=e(108),u=e(5)("iterator"),l=!([].keys&&"next"in[].keys()),d=function(){return this};t.exports=function(t,i,e,f,g,x,y){h(e,i,f);var w,m,v,b=function(t){if(!l&&t in L)return L[t];return function(){return new e(this,t)}},M=i+" Iterator",C="values"==g,A=!1,L=t.prototype,P=L[u]||L["@@iterator"]||g&&L[g],I=P||b(g),j=g?C?b("entries"):I:void 0,S="Array"==i&&L.entries||P;if(S&&(v=p(S.call(new t)))!==Object.prototype&&v.next&&(c(v,M,!0),r||"function"==typeof v[u]||s(v,u,d)),C&&P&&"values"!==P.name&&(A=!0,I=function(){return P.call(this)}),(!r||y)&&(l||A||!L[u])&&s(L,u,I),a[i]=I,a[M]=d,g){if(w={values:C?I:b("values"),keys:x?I:b("keys"),entries:j},y)for(m in w)m in L||n(L,m,w[m]);else o(o.P+o.F*(l||A),i,w)}return w}},function(t,i,e){t.exports=!e(15)&&!e(17)(function(){return 7!=Object.defineProperty(e(39)("div"),"a",{get:function(){return 7}}).a})},function(t,i,e){t.exports=e(12)},function(t,i,e){var r=e(10),o=e(105),n=e(43),s=e(41)("IE_PROTO"),a=function(){},h=function(){var t,i=e(39)("iframe"),r=n.length;for(i.style.display="none",e(66).appendChild(i),i.src="javascript:",(t=i.contentWindow.document).open(),t.write("<script>document.F=Object<\/script>"),t.close(),h=t.F;r--;)delete h.prototype[n[r]];return h()};t.exports=Object.create||function(t,i){var e;return null!==t?(a.prototype=r(t),e=new a,a.prototype=null,e[s]=t):e=h(),void 0===i?e:o(e,i)}},function(t,i,e){var r=e(16),o=e(18),n=e(106)(!1),s=e(41)("IE_PROTO");t.exports=function(t,i){var e,a=o(t),h=0,c=[];for(e in a)e!=s&&r(a,e)&&c.push(e);for(;i.length>h;)r(a,e=i[h++])&&(~n(c,e)||c.push(e));return c}},function(t,i,e){var r=e(23);t.exports=Object("z").propertyIsEnumerable(0)?Object:function(t){return"String"==r(t)?t.split(""):Object(t)}},function(t,i,e){var r=e(37),o=Math.min;t.exports=function(t){return t>0?o(r(t),9007199254740991):0}},function(t,i,e){var r=e(4).document;t.exports=r&&r.documentElement},function(t,i,e){e(109);for(var r=e(4),o=e(12),n=e(21),s=e(5)("toStringTag"),a="CSSRuleList,CSSStyleDeclaration,CSSValueList,ClientRectList,DOMRectList,DOMStringList,DOMTokenList,DataTransferItemList,FileList,HTMLAllCollection,HTMLCollection,HTMLFormElement,HTMLSelectElement,MediaList,MimeTypeArray,NamedNodeMap,NodeList,PaintRequestList,Plugin,PluginArray,SVGLengthList,SVGNumberList,SVGPathSegList,SVGPointList,SVGStringList,SVGTransformList,SourceBufferList,StyleSheetList,TextTrackCueList,TextTrackList,TouchList".split(","),h=0;h<a.length;h++){var c=a[h],p=r[c],u=p&&p.prototype;u&&!u[s]&&o(u,s,c),n[c]=n.Array}},function(t,i,e){var r=e(23),o=e(5)("toStringTag"),n="Arguments"==r(function(){return arguments}()),s=function(t,i){try{return t[i]}catch(t){}};t.exports=function(t){var i,e,a;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(e=s(i=Object(t),o))?e:n?r(i):"Object"==(a=r(i))&&"function"==typeof i.callee?"Arguments":a}},function(t,i,e){var r=e(10),o=e(26),n=e(5)("species");t.exports=function(t,i){var e,s=r(t).constructor;return void 0===s||void 0==(e=r(s)[n])?i:o(e)}},function(t,i,e){var r,o,n,s=e(25),a=e(118),h=e(66),c=e(39),p=e(4),u=p.process,l=p.setImmediate,d=p.clearImmediate,f=p.MessageChannel,g=p.Dispatch,x=0,y={},w=function(){var t=+this;if(y.hasOwnProperty(t)){var i=y[t];delete y[t],i()}},m=function(t){w.call(t.data)};l&&d||(l=function(t){for(var i=[],e=1;arguments.length>e;)i.push(arguments[e++]);return y[++x]=function(){a("function"==typeof t?t:Function(t),i)},r(x),x},d=function(t){delete y[t]},"process"==e(23)(u)?r=function(t){u.nextTick(s(w,t,1))}:g&&g.now?r=function(t){g.now(s(w,t,1))}:f?(n=(o=new f).port2,o.port1.onmessage=m,r=s(n.postMessage,n,1)):p.addEventListener&&"function"==typeof postMessage&&!p.importScripts?(r=function(t){p.postMessage(t+"","*")},p.addEventListener("message",m,!1)):r="onreadystatechange"in c("script")?function(t){h.appendChild(c("script")).onreadystatechange=function(){h.removeChild(this),w.call(t)}}:function(t){setTimeout(s(w,t,1),0)}),t.exports={set:l,clear:d}},function(t,i){t.exports=function(t){try{return{e:!1,v:t()}}catch(t){return{e:!0,v:t}}}},function(t,i,e){var r=e(10),o=e(14),n=e(45);t.exports=function(t,i){if(r(t),o(i)&&i.constructor===t)return i;var e=n.f(t);return(0,e.resolve)(i),e.promise}},function(t,i,e){"use strict";function r(t){return t&&t.__esModule?t:{default:t}}i.__esModule=!0;var o=r(e(129)),n=r(e(131)),s="function"==typeof n.default&&"symbol"==typeof o.default?function(t){return typeof t}:function(t){return t&&"function"==typeof n.default&&t.constructor===n.default&&t!==n.default.prototype?"symbol":typeof t};i.default="function"==typeof n.default&&"symbol"===s(o.default)?function(t){return void 0===t?"undefined":s(t)}:function(t){return t&&"function"==typeof n.default&&t.constructor===n.default&&t!==n.default.prototype?"symbol":void 0===t?"undefined":s(t)}},function(t,i,e){var r=e(63),o=e(43).concat("length","prototype");i.f=Object.getOwnPropertyNames||function(t){return r(t,o)}},function(t,i,e){t.exports={default:e(141),__esModule:!0}},function(t,i,e){"use strict";e.d(i,"b",function(){return j});var r=e(77),o=e(149),n=e(78),s=e(79),a=e(34),h=e(95),c=e(1),p=e(0),u=e(24),l=e(96),d=e(53),f=e(3),g=e(88),x=e(33),y=e(55),w=e(189),m=e(190),v=e(191),b=e(192),M=this&&this.__assign||Object.assign||function(t){for(var i,e=1,r=arguments.length;e<r;e++)for(var o in i=arguments[e],i)Object.prototype.hasOwnProperty.call(i,o)&&(t[o]=i[o]);return t},C=document.createElement("img"),A=document.createElement("img"),L=document.createElement("img"),P=document.createElement("img");C.src=e(54),A.src=e(97),L.src=e(193),P.src=e(194);var I=f.a.getPixelRatio(),j={foldState:!0,isTapFoldArrow:!1},S=function(){function t(t,i,e,o){this.ctx=new n.a(t,e,o),this.props=M({count:60,spacePercent:.2,minSpaceWidth:1,maxminDist:30,layout:"kline-portrait",maType:i.setting.maTypes||[],volumeTypes:i.setting.volumeTypes||[],cjeTypes:i.setting.cjeTypes||[],colorProp:a.a.kline[i.skin],lineProp:h.a.lineProp,textProp:h.a.textProp,posx:[],stockUnit:"股",sarCirclrRadius:1.25*I,showCrossLine:!0,showCrossLineTips:!0,showCrossLineValue:!0,translateX:0,devicePixelRatio:I},i),this.layout=Object(r.a)(this.ctx,this.props.layout,this.props);var s=this.layout.getChart().width,c=s/this.props.count;if(this.props.itemWidth=c,this.props.fixedWidth?(this.props.spacePercent=1-s/(720*c),this.props.spaceWidth=this.props.itemWidth*this.props.spacePercent):(this.props.spacePercent=.2,this.props.spaceWidth=Math.max(this.props.itemWidth*this.props.spacePercent,2)),this.props.barWidth=this.props.itemWidth-this.props.spaceWidth,this.chipData={},this.props.showCrossLine){var p=this.layout.chart;this.crossLine=new l.a(this.ctx,{x:p.x,y:p.y,width:p.width+(this.props.isSupportChip&&this.props.isShowChip?this.layout.chipRegion.width:0),height:p.height+this.layout.xAxis.height+this.layout.indicatorHeight,xAxis:this.layout.xAxis,chipWidth:this.layout.chipRegion.width},this.props)}j.foldState=!0}return t.prototype.format=function(t){var i=this,e={},r=this.props.maType.map(function(t){return"ma"+t}),o=this.props.volumeTypes.map(function(t){return"volume"+t}),n=this.props.cjeTypes.map(function(t){return"cje"+t});t.forEach(function(t,s){var a=r.map(function(i){return t[i]}).filter(function(t){return!!t}),h=o.map(function(i){return t[i]}).filter(function(t){return!!t}),c=n.map(function(i){return t[i]}).filter(function(t){return!!t});0==s?(e={kline:{min:Math.min(t.low,Math.min.apply(null,a)),max:Math.max(t.high,Math.max.apply(null,a))},ma:{min:Math.min.apply(null,a),max:Math.max.apply(null,a)},volumeMA:{min:Math.min.apply(null,h),max:Math.max.apply(null,h)},cjeMA:{min:Math.min.apply(null,c),max:Math.max.apply(null,c)}},i.props.useIndicators.forEach(function(i){var r=t[i];if("number"==typeof r)isNaN(r)?e[i]={max:Number.MIN_SAFE_INTEGER,min:Number.MAX_SAFE_INTEGER}:e[i]={max:r,min:r};else{var o=[];for(var n in r)if(r.hasOwnProperty(n)){var s=r[n];isNaN(s)||void 0===s||o.push(s)}e[i]={min:Math.min.apply(null,o),max:Math.max.apply(null,o)}}})):(e.kline={min:Math.min(t.low,e.kline.min),max:Math.max(t.high,e.kline.max)},e.ma={min:Math.min(Math.min.apply(null,a),e.ma.min),max:Math.max(Math.max.apply(null,a),e.ma.max)},e.volumeMA={min:Math.min(Math.min.apply(null,h),e.volumeMA.min),max:Math.max(Math.max.apply(null,h),e.volumeMA.max)},e.cjeMA={min:Math.min(Math.min.apply(null,c),e.cjeMA.min),max:Math.max(Math.max.apply(null,c),e.cjeMA.max)},i.props.useIndicators.forEach(function(i){var r=t[i];if("number"==typeof r){if(isNaN(r))return;e[i]={max:Math.max(r,e[i].max),min:Math.min(r,e[i].min)}}else{var o=[];for(var n in r)if(r.hasOwnProperty(n)){var s=r[n];isNaN(s)||void 0===s||o.push(s)}e[i]={min:Math.min(Math.min.apply(null,o),e[i].min),max:Math.max(Math.max.apply(null,o),e[i].max)}}}))});var s=e.kdj.max,a=e.kdj.min;return s<100&&(e.kdj.max=100),a>0&&(e.kdj.min=0),e.wr.max=100,e.wr.min=0,{items:t,maxMin:e}},t.prototype.getRatio=function(t){return Math.ceil((t*this.props.devicePixelRatio-this.layout.getChart().x)/this.props.itemWidth)/this.props.count},t.prototype.changeCount=function(t){this.props.count+=t,this.props.itemWidth=this.layout.getChart().width/this.props.count,this.props.spaceWidth=Math.max(this.props.itemWidth*this.props.spacePercent,2),this.props.barWidth=this.props.itemWidth-this.props.spaceWidth,this.props.showCrossLine&&this.crossLine.changeCount(this.props)},t.prototype.changeLineType=function(t){this.props.fixedWidth="line"===t,this.props.isPinchLine="line"===t;var i=this.layout.getChart().width,e=i/this.props.count;this.props.fixedWidth?(this.props.spacePercent=1-i/(720*e),this.props.spaceWidth=this.props.itemWidth*this.props.spacePercent):(this.props.spacePercent=.2,this.props.spaceWidth=Math.max(this.props.itemWidth*this.props.spacePercent,2))},t.prototype.initAreaSelect=function(t){void 0===t&&(t=[]),this.props.isShowAreaSelect&&(t=t.length?t:this.list,this.areaSelect=new o.a(this.ctx,this.props,this.layout.getChart(),t))},t.prototype.getAreaSelectData=function(){if(this.props.isShowAreaSelect)return this.areaSelect.getTipBarData()},t.prototype.touchMoveAreaSelect=function(t){var i=this,e=Object(d.a)(this.ctx.ctx.canvas,t);e.x=e.x*this.props.devicePixelRatio,e.y=e.y*this.props.devicePixelRatio,this.areaSelect.move(e,function(){i.draw(i.list)})},t.prototype.isTapAreaSelect=function(t){if(!this.props.isShowAreaSelect)return!1;var i=Object(d.a)(this.ctx.ctx.canvas,t);i.x=i.x*this.props.devicePixelRatio,i.y=i.y*this.props.devicePixelRatio;var e=this.areaSelect.findTarget(i);return!(!e||"close"!==e.action)},t.prototype.hasAreaSelectActive=function(){return!!this.props.isShowAreaSelect&&this.areaSelect.getActive()},t.prototype.cancelAreaActive=function(){this.props.isShowAreaSelect&&this.areaSelect.cancelActive()},t.prototype.updateChip=function(t){void 0===t&&(t={}),this.chipData=M({showType:"p90"},this.chipData,t)},t.prototype.getValueY=function(t){var i,e=this.layout.getChart(),r=this.crossLine.changeCoords(t);if(r.y<=e.height+e.y&&this.data.maxMin.kline){var o=this.data.maxMin.kline,n=o.max;i=n-(n-o.min)/e.height*(r.y-e.y)}return isNaN(i)?NaN:i},t.prototype.draw=function(t,i,e,r,o){var n=this;j.isTapFoldArrow=!!o;var a=this.props,h=this.ctx,c=this.layout;"switchIndicator"===t?this.data=this.format(this.list):t&&t.length>0&&(this.list=t,this.data=this.format(t),this.props.posx=[]),i&&(this.mainViewData=i),a.crossLineItem=e,Object(u.a)(h,0,0,c.getWidth(),c.getHeight()),new s.a.candle(h,this.data,a,{chart:c.getChart(),xAxis:c.getXAxis(),yAxis:c.getChartYAxis(),chartAndChipWidth:c.chartAndChipWidth},this.mainViewData).draw(),(a.isPinchLine?[w.a,m.a]:[w.a,m.a,v.a,b.a]).map(function(t){new t(h,n.data,n.mainViewData,a,{chart:c.getChart(),xAxis:c.getXAxis(),yAxis:c.getChartYAxis()}).draw()}),"day"!==a.type||!x.e||e||y.c||this.drawTradeEntranceBar(),a.hideIndicator||this.drawIndicator(e,r),a.isShowAreaSelect&&this.areaSelect.draw(),a.isSupportChip&&this.chipData&&(this.drawChipSwitch(),a.isShowChip&&new s.a.ChipBar(this.ctx,M({},this.data,{chipData:this.chipData,chipTime:isNaN(r)||null===r?this.data.items[this.data.items.length-1].time:this.data.items[r].time}),M({},this.props),this.layout).draw())},t.prototype.drawChipSwitch=function(){var t=this.props.devicePixelRatio,i={width:14*t,height:23*t},e=this.layout.getChart(),r=e.x+e.width-i.width,o=e.height+e.y;Object(c.f)(this.ctx,r,o,i.width,i.height,{borderTopLeftRadius:2*t,borderBottomLeftRadius:2*t},this.props.colorProp.chip.lightBlue,this.props.colorProp.chip.lightBlue,0),this.props.isShowChip?(Object(c.c)(this.ctx,r+5*t,o+7*t,r+i.width-4*t,o+i.height/2,"#fff","","",null,1*t),Object(c.c)(this.ctx,r+i.width-4*t,o+i.height/2,r+5*t,o+i.height-7*t,"#fff","","",null,1*t)):(Object(c.c)(this.ctx,r+i.width-5*t,o+7*t,r+4*t,o+i.height/2,"#fff","","",null,1*t),Object(c.c)(this.ctx,r+4*t,o+i.height/2,r+i.width-5*t,o+i.height-7*t,"#fff","","",null,1*t))},t.prototype.drawIndicator=function(t,i){for(var e,r,o="kline-portrait"===this.props.layout?this.props.setting.indicatorCount:1,n=1;n<=o;n++){var a=this.props[["curr","second","third","fourth"][n-1]+"Indicator"],h=t?M({},this.props,{index:i,indicator:((e={})[a]=t[a],e)}):M({},this.props,{indicator:((r={})[a]=this.data.items[this.data.items.length-1][a],r)}),c=this.layout[1===n?"indicator":["second","third","fourth"][n-2]+"Indicator"];new s.a[a](this.ctx,this.data,h,c).draw()}},t.prototype.getIndicatorData=function(t){return"kline"==(t=t||this.props.currIndicator)?{items:this.data.items,max:this.data.maxMin[t].max,min:this.data.maxMin[t].min}:{items:this.data.items.map(function(i){return i[t]}),max:this.data.maxMin[t].max,min:this.data.maxMin[t].min}},t.prototype.showCrossLine=function(t,i){var e,r=!1;window.Touch&&t instanceof window.Touch||"object"==typeof t||t instanceof Event||window.$wujie?e=this.crossLine.changeCoords(t):"number"==typeof t?e={x:this.lastScene.eventPoint.x+this.props.itemWidth*t,y:this.lastScene.eventPoint.y,index:this.lastScene.eventPoint.index+t}:"right"===t&&(e={x:this.layout.chartWidth-this.props.itemWidth/2,y:this.lastScene.eventPoint.y,index:this.props.count-1}),this.lastScene={eventPoint:e,count:this.props.count};var o,n=this.data.items[e.index],s=this.layout.getChart();if(n){var a=this.props.fixNum||2;if(n.leftval="",n.rightval="",n.showLeft=e.x-s.x>.5*s.width,e.y<=s.height+s.y&&this.data.maxMin.kline){var h=Object(f.c)(this.data,this.props),c=h.max,p=h.min;o=c-(c-p)/s.height*(e.y-s.y),n[n.showLeft?"leftval":"rightval"]=o.toFixed(a)}"kline-portrait"===this.props.layout&&this.props.isSupportChip&&this.props.isShowChip&&(n.showLeft=!0);for(var u="kline-portrait"===this.props.layout?this.props.setting.indicatorCount:1,l=1;l<=u;l++){var d=this.props[["curr","second","third","fourth"][l-1]+"Indicator"],g=this.layout[1===l?"indicator":["second","third","fourth"][l-2]+"Indicator"];if(e.y>=g.y&&e.y<=g.y+g.height){var c=void 0,p=void 0;if("volume"===d||"cje"===d)c=Math.max(this.data.maxMin[d].max,this.data.maxMin[d+"MA"].max),p=0;else if("ema"===d)c=Math.max(this.data.maxMin.kline.max,this.data.maxMin.ema.max),p=Math.min(this.data.maxMin.kline.min,this.data.maxMin.ema.min);else{var x=this.data.maxMin[d];c=x.max,p=x.min}o=c-(c-p)/g.height*(e.y-g.y),"volume"===d||"cje"===d||"obv"===d?("cje"===d&&(o*=1e4),o=Math.abs(o)>1e8?(o/1e8).toFixed(2)+"亿":Math.abs(o)>1e4?(o/1e4).toFixed(2)+"万":o.toFixed(2)):o=o.toFixed(3),n[n.showLeft?"leftval":"rightval"]=o}}n.toplval=/^m(1|5|15|30|60|120)$/.test(this.props.type)?n.time.substr(0,4)+"-"+n.time.substr(4,2)+"-"+n.time.substr(6,2)+" "+n.time.substr(8,2)+":"+n.time.substr(10,2):n.time,n.bottomval=n.toplval,this.draw(null,null,n,e.index),this.crossLine.draw(e,n,a),this.crossBarTop=s.y,this.props.hideTradeBar||this.drawTradeBar(n),this.drawZXBar(n),this.props.setting&&this.props.setting.ds&&(this.drawFHBar(n),this.drawHGBar(n)),n.fh&&(n.fh.FHcontent||n.fh.HGcontent)&&(r=!0),i&&i(n,r)}},t.prototype.judgeOverlap=function(t,i,e,r){for(var o=this.ctx.getImageData(t,i+r,e,1).data,n=0;n<o.length;n+=4)if(230===o[n]&&53===o[n+1]&&53===o[n+2]||45===o[n]&&185===o[n+1]&&85===o[n+2])return!0;return!1},t.prototype.drawTradeEntranceBar=function(){var t={color:"#3077EC",font:"500 "+11*this.props.devicePixelRatio+"px Arial",textAlign:p.a.textAlign.left,baseLine:p.a.baseLine.mid},i=this.layout.chart,e=Object(p.d)(this.ctx,"买卖点回顾",t),r=4.5*this.props.devicePixelRatio,o=9*this.props.devicePixelRatio,n=e+r+20*this.props.devicePixelRatio,s=23*this.props.devicePixelRatio,a=i.y,h=i.x;this.judgeOverlap(h,a,n,s)&&(h=i.x+i.width-n,this.judgeOverlap(h,a,n,s)&&(h=i.x)),Object(c.f)(this.ctx,h,a,n,s,2*this.props.devicePixelRatio,"rgba(48, 119, 236, 0.5)","#F1F7FF");var u=a+s/2,l=h+8*this.props.devicePixelRatio;Object(p.c)(this.ctx,"买卖点回顾",l,u,t);var d=a+(s-o)/2,f=l+e+4*this.props.devicePixelRatio;this.ctx.drawImage(C,f,d,r,o),this.tradeEntranceBarRegion={x:h,y:a,width:n,height:s}},t.prototype.drawTradeBar=function(t){if("day"===this.props.type&&t.tradeType){var i={B:"买入明细",S:"卖出明细",T:"买卖明细"}[t.tradeType],e={color:{B:"#3077EC",S:"#FAB06D",T:"#D58EF7"}[t.tradeType],font:"500 "+11*this.props.devicePixelRatio+"px Arial",textAlign:p.a.textAlign.left,baseLine:p.a.baseLine.mid},r=this.layout.getChart(),o=Object(p.d)(this.ctx,i,e),n=4.5*this.props.devicePixelRatio,s=9*this.props.devicePixelRatio,a=o+n+20*this.props.devicePixelRatio,h=23*this.props.devicePixelRatio,u=this.crossBarTop,l={B:"rgba(48, 119, 236, 0.3)",S:"rgba(255, 137, 30, 0.3)",T:"rgba(190, 51, 255, 0.3)"}[t.tradeType],d={B:"#F1F7FF",S:"#FFFAF5",T:"#FDF7FF"}[t.tradeType],f=t.showLeft?r.x:r.x+r.width-a;Object(c.f)(this.ctx,f,u,a,h,2*this.props.devicePixelRatio,l,d);var g=f+8*this.props.devicePixelRatio;Object(p.c)(this.ctx,i,g,u+h/2,e);var x={B:C,S:A,T:L}[t.tradeType],y=g+o+4*this.props.devicePixelRatio;this.ctx.drawImage(x,y,u+(h-s)/2,n,s),this.crossBarTop+=h,this.tradeBarRegion={x:f,y:u,width:a,height:h},this.tradeBarData=t}else this.tradeBarRegion=null,this.tradeBarData=null},t.prototype.drawZXBar=function(t){var i=this.props.setting,e=i.zx,r=i.zjzf;if("kline-portrait"===this.props.layout&&(r||e)){var o=this.data.items.findIndex(function(i){return i.time===t.time}),n=o>0?this.data.items[o-1].close:this.data.items[0].preClose,s=r?"至今涨幅":"",a=void 0;if("day"===this.props.type&&e&&t.fh&&t.fh.since_add_zdf?(a=t.fh.since_add_zdf,s="添加自选，至今涨幅"):a=((+this.mainViewData.lastestPrice-n)/n*100).toFixed(2),!s)return;var h=" "+(a>0?"+":"")+a+"% ",u={color:"#262E40",font:11*this.props.devicePixelRatio+"px Arial",textAlign:p.a.textAlign.left,baseLine:p.a.baseLine.mid},l=this.layout.getChart(),d=Object(p.d)(this.ctx,s,u),f=Object(p.d)(this.ctx,h,u),g=d+f+18*this.props.devicePixelRatio,x=23*this.props.devicePixelRatio,y=4.5*this.props.devicePixelRatio,w=9*this.props.devicePixelRatio,m=this.crossBarTop,v=t.showLeft?l.x:l.x+l.width-g;Object(c.f)(this.ctx,v,m,g,x,2*this.props.devicePixelRatio,"rgba(48, 119, 236, 0.5)","#F1F7FF");var b=m+x/2,M=v+8*this.props.devicePixelRatio;Object(p.c)(this.ctx,s,M,b,u),M+=d,Object(p.c)(this.ctx,h,M,b,Object.assign({},u,{color:0==+a?"#7a8499":a>0?"#e63535":"#1caa3c"})),this.ctx.drawImage(P,M+f,b-w/2,y,w),this.zxBarRegion={x:v,y:m,width:g,height:x},this.crossBarTop+=x}else this.zxBarRegion=null},t.prototype.drawFHBar=function(t){if(t.fh&&t.fh.FHcontent){var i=t.fh.FHcontent,e={color:"#262E40",font:11*this.props.devicePixelRatio+"px Arial",textAlign:p.a.textAlign.center,baseLine:p.a.baseLine.mid},r={color:"#262E40",font:11*this.props.devicePixelRatio+"px Arial",textAlign:p.a.textAlign.left,baseLine:p.a.baseLine.mid},o=this.layout.getChart(),n=Object(p.d)(this.ctx,i,e),s=o.width-16*this.props.devicePixelRatio,a=n+16*this.props.devicePixelRatio,h=23*this.props.devicePixelRatio,u=this.crossBarTop,l=t.showLeft?o.x:o.x+o.width-a,d=!1;if(n>s&&(d=!0),d){var f=Math.ceil(n/s),g=11*this.props.devicePixelRatio,x=h-g,y=1.5*this.props.devicePixelRatio;a=o.width-2*this.props.devicePixelRatio,h=x+f*g+(f+1)*y,l=t.showLeft?o.x:2*this.props.devicePixelRatio,Object(c.f)(this.ctx,l,u,a,h,2*this.props.devicePixelRatio,"rgba(255, 137, 30, 0.5)","#FFF8F2"),Object(p.b)(this.ctx,i,l+8*this.props.devicePixelRatio,u+x/2,r,s,g,y)}else Object(c.f)(this.ctx,l,u,a,h,2*this.props.devicePixelRatio,"rgba(255, 137, 30, 0.5)","#FFF8F2"),Object(p.c)(this.ctx,i,l+a/2,u+h/2,e);this.crossBarTop+=h}},t.prototype.drawHGBar=function(t){if(t.fh&&t.fh.HGcontent){var i=t.fh.HGcontent,e={color:"#262E40",font:11*this.props.devicePixelRatio+"px Arial",textAlign:p.a.textAlign.center,baseLine:p.a.baseLine.mid},r=this.layout.getChart(),o=Object(p.d)(this.ctx,i,e)+16*this.props.devicePixelRatio,n=23*this.props.devicePixelRatio,s=this.crossBarTop,a=t.showLeft?r.x:r.x+r.width-o;Object(c.f)(this.ctx,a,s,o,n,2*this.props.devicePixelRatio,"rgba(255, 137, 30, 0.5)","#FFF8F2"),Object(p.c)(this.ctx,i,a+o/2,s+n/2,e),this.crossBarTop+=n}},t.prototype.isPointInRegion=function(t,i){return i&&t.x>=i.x&&t.x<=i.x+i.width&&t.y>=i.y&&t.y<=i.y+i.height},t.prototype.isTapTradeEntranceBarRegion=function(t){var i=Object(d.a)(this.ctx.ctx.canvas,t);return i.x=i.x*this.props.devicePixelRatio,i.y=i.y*this.props.devicePixelRatio,this.isPointInRegion(i,this.tradeEntranceBarRegion)},t.prototype.isTapTradeBarRegion=function(t){var i=Object(d.a)(this.ctx.ctx.canvas,t);return i.x=i.x*this.props.devicePixelRatio,i.y=i.y*this.props.devicePixelRatio,this.isPointInRegion(i,this.tradeBarRegion)},t.prototype.updatePropByChip=function(t,i){if(void 0===i&&(i=[]),this.props.isShowChip=!!t,this.props.count+=this.props.isShowChip?-15:15,this.layout=Object(r.a)(this.ctx,this.props.layout,this.props),this.props.showCrossLine){var e=this.layout.getChart().width,o=e/this.props.count;this.props.itemWidth=o,this.props.fixedWidth?(this.props.spacePercent=1-e/(720*o),this.props.spaceWidth=this.props.itemWidth*this.props.spacePercent):(this.props.spacePercent=.2,this.props.spaceWidth=Math.max(this.props.itemWidth*this.props.spacePercent,2)),this.props.barWidth=this.props.itemWidth-this.props.spaceWidth,this.crossLine.changeCount(this.props)}this.props.isShowAreaSelect&&this.areaSelect.update("chip",{props:this.props,region:this.layout.getChart(),list:i}),this.draw(i)},t.prototype.isTapButtonRegion=function(t){function i(t){var i=10*e;return{x:t.x-i,y:t.y-i,width:t.buttonWidth+2*i,height:t.height+2*i}}var e=this.props.devicePixelRatio,o=Object(d.a)(this.ctx.ctx.canvas,t);o.x=o.x*e,o.y=o.y*e;var n={x:this.layout.chart.x,y:0,width:this.layout.chart.width,height:this.layout.chart.y,buttonWidth:45*e};if(this.isPointInRegion(o,i(n))&&!this.props.disableMainIndicator)return 0;for(var s="kline-portrait"===this.props.layout?this.props.setting.indicatorCount:1,a=1;a<=s;a++){var h=this.layout[1===a?"indicator":["second","third","fourth"][a-2]+"Indicator"].bar;if(this.isPointInRegion(o,i(h)))return a}this.layout=Object(r.a)(this.ctx,this.props.layout,this.props);var c=this.layout&&this.layout.foldArrow;return"kline-portrait"===this.props.layout&&c&&(c.x=g.b.x,this.isPointInRegion(o,i(c)))?(j.foldState=!j.foldState,"foldArrow"):void 0},t.prototype.isTapChipSwitch=function(t){var i,e,r=this.props.devicePixelRatio,o=Object(d.a)(this.ctx.ctx.canvas,t);o.x=o.x*r,o.y=o.y*r;var n=this.layout.getChart(),s={width:40,height:70,x:n.x+n.width-40,y:n.height+n.y};return!!this.isPointInRegion(o,(i=s,e=10*r,{x:i.x-e,y:i.y-e,width:i.width+2*e,height:i.height+2*e}))},t.prototype.isTapZxBarRegion=function(t){var i,e,r=this.props.devicePixelRatio,o=this.props.setting,n=(o.zx,o.zjzf);if("kline-portrait"!==this.props.layout||!this.props.crossLineItem||!n)return!1;var s=Object(d.a)(this.ctx.ctx.canvas,t);return s.x=s.x*r,s.y=s.y*r,!(!this.zxBarRegion||!this.isPointInRegion(s,(i=this.zxBarRegion,e=2*r,{x:i.x-e,y:i.y-e,width:i.width+2*e,height:i.height+2*e})))},t.prototype.isTapChipType=function(t){function i(t){var i=2*e;return{x:t.x-i,y:t.y-i,width:t.width+2*i,height:t.height+2*i}}if(!this.props.isShowChip)return!1;var e=this.props.devicePixelRatio,r=Object(d.a)(this.ctx.ctx.canvas,t);r.x=r.x*e,r.y=r.y*e;var o="kline-portrait"===this.props.layout,n=this.layout.getChart(),s=this.layout.height-this.layout.chart.height-this.layout.chart.y;if(o&&s<350)return!1;var a={portrait:{leftRegion:{x:n.x+n.width+3*e,y:n.y+n.height+s/2-5,width:(this.layout.chipRegion.width-3*e)/2,height:13*e},rightRegion:{x:n.x+n.width+3*e+this.layout.chipRegion.width/2,y:n.y+n.height+s/2-5,width:(this.layout.chipRegion.width-3*e)/2,height:13*e}},landscape:{leftRegion:{x:n.x+n.width+3*e,y:this.layout.chipRegion.height-100,width:90,height:45},rightRegion:{x:n.x+n.width+3*e,y:this.layout.chipRegion.height-50,width:90,height:45}}},h=o?a.portrait:a.landscape;return this.isPointInRegion(r,i(h.leftRegion))?(this.chipData.showType="p90",!0):!!this.isPointInRegion(r,i(h.rightRegion))&&(this.chipData.showType="p70",!0)},t.prototype.isTapIndicatorRegion=function(t){var i=Object(d.a)(this.ctx.ctx.canvas,t);i.x=i.x*this.props.devicePixelRatio,i.y=i.y*this.props.devicePixelRatio;for(var e="kline-portrait"===this.props.layout?this.props.setting.indicatorCount:1,r=1;r<=e;r++){var o=this.layout[1===r?"indicator":["second","third","fourth"][r-2]+"Indicator"];if(this.isPointInRegion(i,o))return r}},t.prototype.getNextIndicator=function(t){var i=this.props.useIndicators.length,e=this.props[["curr","second","third","fourth"][t-1]+"Indicator"],r=(this.props.useIndicators.indexOf(e)+1)%i,o=this.props.useIndicators[r];return"rally"===o?this.props.useIndicators[r+1]:o},t.prototype.switchIndicator=function(t,i){var e=["curr","second","third","fourth"][t-1]+"Indicator";this.props[e]=i,this.list&&this.draw("switchIndicator")},t.prototype.translateDraw=function(t){t&&(this.props.translateX=t,this.draw(null))},t}();i.a=S},function(t,i,e){"use strict";var r=e(145),o=e(146),n=e(147),s=e(148);i.a=function(t,i,e){switch(i){case"mins-landscape":return new r.a(t,e);case"mins-portrait":return new o.a(t,e);case"kline-landscape":return new n.a(t,e);case"kline-portrait":return new s.a(t,e)}}},function(t,i,e){"use strict";function r(t,i,e){var r,n;if(i===o.a.wx)r=window.wx.createCanvasContext(t,e);else r="object"==typeof t&&t.getContext?t.getContext("2d"):document.getElementById(t).getContext("2d");for(n in this.ctx=r,r)"function"==typeof r[n]&&(this[n]=function(t){return function(){var i=[].slice.call(arguments,0);return r[t].apply(r,i)}}(n))}i.a=r;var o=e(7);r.prototype={setFillStyle:function(t){this.ctx.setFillStyle?this.ctx.setFillStyle(t):this.ctx.fillStyle&&(this.ctx.fillStyle=t)},setStrokeStyle:function(t){this.ctx.setStrokeStyle?this.ctx.setFillStyle(t):this.ctx.strokeStyle&&(this.ctx.strokeStyle=t)},addColorStop:function(t,i){},setLineWidth:function(t){this.ctx.setLineWidth?this.ctx.setLineWidth(t):this.ctx.lineWidth&&(this.ctx.lineWidth=t)},setLineCap:function(t){this.ctx.setLineCap?this.ctx.setLineCap(t):this.ctx.lineCap&&(this.ctx.lineCap=t)},setLineJoin:function(t){this.ctx.setLineJoin?this.ctx.setLineJoin(t):this.ctx.lineJoin&&(this.ctx.lineJoin=t)},setTextAlign:function(t){this.ctx.setTextAlign?this.ctx.setTextAlign(t):this.ctx.textAlign&&(this.ctx.textAlign=t)},setTextBaseline:function(t){this.ctx.setTextBaseline?this.ctx.setTextBaseline(t):this.ctx.textBaseline&&(this.ctx.textBaseline=t)},setGlobalAlpha:function(t){this.ctx.setGlobalAlpha?this.ctx.setGlobalAlpha(t):this.ctx.globalAlpha&&(this.ctx.globalAlpha=t)},draw:function(t,i){this.ctx.draw&&this.ctx.draw(t,i)},setFont:function(t){this.ctx.font&&(this.ctx.font=t)},setTextStyle:function(t){t.color&&this.setFillStyle(t.color),t.font&&this.setFont(t.font),t.baseLine&&this.setTextBaseline(t.baseLine),t.textAlign&&this.setTextAlign(t.textAlign)},getContextCanvas:function(){return this.ctx.canvas}}},function(t,i,e){"use strict";var r=e(150),o=e(157),n=e(84),s=e(158),a=e(159),h=e(160),c=e(161),p=e(162),u=e(163),l=e(164),d=e(165),f=e(86),g=e(166),x=e(167),y=e(168),w=e(87),m=e(169),v=e(170),b=e(171),M=e(172),C=e(173),A=e(174),L=e(175),P=e(181);i.a={volume:r.a,cje:o.a,boll:n.a,cci:s.a,dmi:a.a,kdj:h.a,ema:c.a,macd:p.a,obv:u.a,rsi:l.a,sar:f.a,wr:d.a,bias:g.a,bbi:x.a,trix:y.a,ene:w.a,vr:m.a,arbr:v.a,psy:b.a,dma:M.a,dpo:C.a,rally:A.a,ChipBar:P.a,candle:L.a}},function(t,i,e){"use strict";var r,o=e(19),n=e(81),s=e(8),a=e(50),h=e(3),c=e(0),p=e(9),u=e(7),l=e(24),d=e(1),f=this&&this.__extends||(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var e in i)i.hasOwnProperty(e)&&(t[e]=i[e])},function(t,i){function e(){this.constructor=t}r(t,i),t.prototype=null===i?Object.create(i):(e.prototype=i.prototype,new e)}),g=this&&this.__assign||Object.assign||function(t){for(var i,e=1,r=arguments.length;e<r;e++)for(var o in i=arguments[e],i)Object.prototype.hasOwnProperty.call(i,o)&&(t[o]=i[o]);return t},x=function(t){function i(i,e,r,o,n,s,a){var h=t.call(this,i,e,r,o,n,s)||this;return/kline/.test(r.layout)&&(h.max=Math.max(e.maxMin[n].max,e.maxMin[n+"MA"].max)),h.unit=r.isHKIndex?"元":a,h}return f(i,t),i.prototype.draw=function(){this.drawGrid(),this.notSupport?this.drawEmptyData():(this.drawLinearShapes(),this.drawScale(),"kline-landscape"===this.props.layout&&this.addStatusData()&&(this.drawSpecialPoints(),this.drawSpecialTag())),this.drawBar()},i.prototype.drawEmptyData=function(){var t={color:"#7A8499",font:this.props.textProp.font,textAlign:c.a.textAlign.center,baseLine:c.a.baseLine.mid},i=this.region.x+this.region.width/2,e=this.region.y+this.region.height/2;Object(c.c)(this.ctx,"该类型暂不支持该指标",i,e,t)},i.prototype.drawGrid=function(){var t=this.props.colorProp,i=this.props.lineProp;new o.a(this.ctx,{border:{color:t.border,lineWidth:i.border},vline:{color:t.vline,width:i.vline,posx:this.props.posx}},this.region)},i.prototype.drawLinearShapes=function(){var t=this;if(0!==this.max&&(new s.a({ctx:this.ctx,region:this.region,drawCallback:function(i){return t.drawItem(t.ctx,i,t.props)},data:{items:this.data.items.map(function(i){return i[t.name]}),max:this.max,min:0},count:this.props.count}),/kline/.test(this.props.layout)&&this.props[this.name+"Types"].length>0)){var i=this.props[this.name+"Types"].map(function(i){return""+t.name+i});new a.a(this.ctx,this.data,this.props,this.region,i,this.max,0).draw()}},i.prototype.drawScale=function(){var t=Object(h.b)(this.max,"cje"===this.name||this.props.isHKIndex),i={font:this.props.textProp.font,textAlign:c.a.textAlign.right,color:this.props.colorProp.yAxis};/portrait/.test(this.props.layout)?new p.a(this.ctx,u.b.y,[{text:t.v+t.u+this.unit,x:this.region.yAxis.x,y:this.region.yAxis.y,props:g({},i,{baseLine:c.a.baseLine.top})}],this.region.yAxis).draw():new p.a(this.ctx,u.b.y,[{text:t.v,x:this.region.yAxis.x,y:this.region.yAxis.y,props:g({},i,{baseLine:c.a.baseLine.top})},{text:t.u+this.unit,x:this.region.yAxis.x,y:this.region.yAxis.y+this.region.yAxis.height,props:g({},i,{baseLine:c.a.baseLine.btm})}],this.region.yAxis).draw()},i.prototype.drawItem=function(t,i,e){var r=i.index,o=e.colorProp,n=i.currItem,s=this.data.items[r],a=e.itemWidth,h=i.getX(r)+a/2,c=i.getY(n),p=/mins/.test(e.layout)?0===r||this.data.items[r].price>=this.data.items[r-1].price?o.rise:o.drop:Object(l.b)(s.open,s.close,s.preClose,o.rise,o.drop,o.flat);Object(d.g)(t,h-e.barWidth/2,c,e.barWidth,this.region.height/2-c,p,p)},i}(n.a);i.a=x},function(t,i,e){"use strict";var r=e(1),o=e(0),n=e(3),s=this&&this.__assign||Object.assign||function(t){for(var i,e=1,r=arguments.length;e<r;e++)for(var o in i=arguments[e],i)Object.prototype.hasOwnProperty.call(i,o)&&(t[o]=i[o]);return t},a=function(){function t(t,i,e,r,o,n){this.ctx=t,this.data=i,this.props=e,this.region=r,this.name=o,this.max=i.maxMin[o].max,this.min=i.maxMin[o].min,this.tip=n,this.notSupport=!1}return t.prototype.draw=function(){this.drawGrid(),this.drawLinearShapes(),this.drawScale(),"kline-landscape"===this.props.layout&&this.addStatusData()&&(this.drawSpecialPoints(),this.drawSpecialTag()),this.drawBar()},t.prototype.addStatusData=function(){return!1},t.prototype.drawSpecialPoints=function(){for(var t=0;t<this.data.items.length;t++){var i=this.data.items[t][this.props.currIndicator+"Status"];if(i){var e="";"high"===i?e="wr"===this.props.currIndicator?this.props.colorProp.rise:this.props.colorProp.drop:"low"===i&&(e="wr"===this.props.currIndicator?this.props.colorProp.drop:this.props.colorProp.rise),Object(r.b)(this.ctx,this.region.x+this.region.width/this.props.count*t+this.props.itemWidth/2,this.region.y+2,4,e,e)}}},t.prototype.drawSpecialTag=function(){if(this.props.index){var t=this.data.items[this.props.index][this.props.currIndicator+"Status"];if(t){var i="",e=this.props.currIndicator.toUpperCase();"high"===t?(i="wr"===this.props.currIndicator?this.props.colorProp.rise:this.props.colorProp.drop,e+="macd"===this.props.currIndicator?"死叉":"指标高位"):"low"===t&&(i="wr"===this.props.currIndicator?this.props.colorProp.drop:this.props.colorProp.rise,e+="macd"===this.props.currIndicator?"金叉":"指标低位");var n={color:"white",font:this.props.textProp.font,baseLine:o.a.baseLine.mid,textAlign:o.a.textAlign.center},s=this.region.x+this.region.width/this.props.count*this.props.index+this.props.itemWidth/2,a=this.region.y+30,h=Object(o.d)(this.ctx,e,n)+20;s+h/2>this.region.x+this.region.width&&(s=this.region.x+this.region.width-h/2),Object(r.g)(this.ctx,s-h/2,a,h,40,i,i),Object(o.c)(this.ctx,e,s,a+20,n)}}},t.prototype.drawBar=function(){var t=this.region.bar.x,i=this.region.bar.y+this.region.bar.height/2,e=o.a.baseLine.mid;Object(r.c)(this.ctx,t,this.region.bar.y,t+this.region.bar.width,this.region.bar.y,this.props.colorProp.hline,null,null,null,2);var n="";n="volume"===this.name?"成交量":"cje"===this.name?"成交额":"rally"===this.name?"反弹指数":this.name.toUpperCase();var s={color:this.props.colorProp.tip,font:this.props.textProp.font,baseLine:e};if(void 0!==this.props.index||!/portrait/.test(this.props.layout)||"fmins"===this.props.type||this.props.isHistoryMins||this.props.disableInteract)Object(o.c)(this.ctx,n,t,i,s),t+=Object(o.d)(this.ctx,n,s),this.notSupport||this.drawCurrent(t,i,e);else{var a=this.region.bar,h=a.x,c=a.y,p=a.height,u="rally"===this.name?55*this.props.devicePixelRatio:this.region.bar.buttonWidth;Object(r.g)(this.ctx,h,c,u,p-1,this.props.colorProp.button.bg,this.props.colorProp.button.bg),Object(o.c)(this.ctx,n,h+.4*u,c+p/2,{color:this.props.colorProp.button.text,font:this.props.textProp.font,textAlign:o.a.textAlign.center,baseLine:o.a.baseLine.mid});var l=h+.85*u,d=c+p/2,f=2*this.props.devicePixelRatio,g=3*this.props.devicePixelRatio;Object(r.h)(this.ctx,l-g,d-f,l+g,d-f,l,d+f,this.props.colorProp.button.tri,this.props.colorProp.button.tri),this.notSupport||this.drawCurrent(h+u,i,e)}},t.prototype.drawCurrent=function(t,i,e){var r=5*this.props.devicePixelRatio;t+=r;var a=this.props.indicator[this.name],h=this.props.colorProp[this.name],c=10,p={color:this.props.colorProp.defaultGray,font:c*this.props.devicePixelRatio+"px Arial",baseLine:e};if("volume"===this.name||"cje"===this.name){for(var u=this.props.crossLineItem||this.data.items[this.data.items.length-1],l=Object(n.b)(u[this.name],"cje"===this.name||this.props.isHKIndex),d=l.v,f=l.u,g="volume"!==this.name||this.props.isHKIndex?"元":this.props.stockUnit,x=d+" "+f+g,y=x,w=1,m=this.props[this.name+"Types"]||[],v=0;v<m.length;v++)if(m[v]>0){var b=m[v]+": ",M=Object(n.b)(u[""+this.name+m[v]],"cje"===this.name),C=M.v,A=M.u;y+=b+(isNaN(C)?"--":C+" "+A+g),w+=1}for(var L=w*r;c>0;){var P=this.region.width-Object(o.d)(this.ctx,y,p)-L;if(!(P<(this.region.bar?this.region.bar.buttonWidth:0)))break;p.font=--c*this.props.devicePixelRatio+"px Arial"}Object(o.c)(this.ctx,x,t,i,p),t+=Object(o.d)(this.ctx,x,p)+r;for(var v=0;v<m.length;v++)if(m[v]>0){var b=m[v]+": ";Object(o.c)(this.ctx,b,t,i,s({},p,{color:this.props.colorProp.ma[v]})),t+=Object(o.d)(this.ctx,b,p);var I=Object(n.b)(u[""+this.name+m[v]],"cje"===this.name),j=I.v,S=I.u,T=isNaN(j)?"--":j+" "+S+g;Object(o.c)(this.ctx,T,t,i,p),t+=Object(o.d)(this.ctx,T,p)+r}}else if("ema"===this.name)this.drawEMABar(p,t,i);else if("number"==typeof a||void 0===a){Object(o.c)(this.ctx,this.tip,t,i,p),t+=Object(o.d)(this.ctx,this.tip,p)+r;var x=""+(isNaN(a)?"--":"rally"===this.name?a:a.toFixed(3));Object(o.c)(this.ctx,x,t,i,p)}else if("object"==typeof a){var y=this.tip,w=1;for(var D in a){var b=D.toUpperCase()+": ",x=""+(isNaN(a[D])?"--":a[D].toFixed(3));y+=b+x,w+=1}for(var L=w*r;c>7;){var P=this.region.width-t-Object(o.d)(this.ctx,y,p)-L;if(!(P/this.props.devicePixelRatio<0))break;p.font=--c*this.props.devicePixelRatio+"px Arial"}for(var D in Object(o.c)(this.ctx,this.tip,t,i,p),t+=Object(o.d)(this.ctx,this.tip,p)+r,a){var b=D.toUpperCase()+": ",x=""+(isNaN(a[D])?"--":a[D].toFixed(3));Object(o.c)(this.ctx,b,t,i,s({},p,{color:h[D]})),t+=Object(o.d)(this.ctx,b,p),Object(o.c)(this.ctx,x,t,i,p),t+=Object(o.d)(this.ctx,x,p)+r}}},t.prototype.drawEMABar=function(t,i,e){for(var r=this.props.indicator[this.name],n=this.props.colorProp[this.name],a=5*this.props.devicePixelRatio,h=10,c=this.props.setting.emaTypes,p="",u=0,l=[],d=0;d<c.length;d++)if(c[d]>0){var f=c[d]+": ",g=+r[c[d]],x=isNaN(g)?"--":g.toFixed(this.props.fixNum||2);if(u+=1,l.push({i:d,title:f,num:x}),"kline-portrait"===this.props.layout&&u>5){p+="...";break}p+=f+x}for(var y=(u-1)*a;h>0&&this.region.width-Object(o.d)(this.ctx,p,t)-y-i<0;)t.font=--h*this.props.devicePixelRatio+"px Arial";var w=this.ctx;w.save();for(var m=0;m<l.length;m++){var v=l[m],d=v.i,f=v.title,x=v.num;if("kline-portrait"===this.props.layout&&m>4){i-=a,Object(o.c)(w,"...",i,e,t);break}Object(o.c)(w,f,i,e,s({},t,{color:n[d]})),i+=Object(o.d)(this.ctx,f,t),Object(o.c)(w,x,i,e,t),i+=Object(o.d)(this.ctx,x,t)+a}w.restore()},t}();i.a=a},function(t,i,e){"use strict";var r={volume:function(t){return t.volume},cje:function(t){return t.cje},macd:function(t,i){void 0===i&&(i={});var e=t.close,r=i.short||12,o=i.long||26,n=i.m||9,s=this.SUB(this.EMA(e,r),this.EMA(e,o)),a=this.EMA(s,n);return{dif:s,dea:a,macd:this.MUL(2,this.SUB(s,a))}},rsi:function(t,i){void 0===i&&(i={});var e=t.close,r=[i.n1||6,i.n2||12,i.n3||24],o=this.REF(e,1),n=this.SUB(e,o);n[0]=0;for(var s=this.MAX(n,0),a=this.ABS(n),h={},c=0;c<3;c++){var p=r[c];h["rsi"+(c+1)]=this.MUL(100,this.DIV(this.SMA(s,p,1),this.SMA(a,p,1)))}return h},kdj:function(t,i){function e(t){return t<0?0:t>100?100:t}void 0===i&&(i={});var r=t.close,o=t.high,n=t.low,s=i.n1||9,a=i.n2||3,h=i.n3||3,c=this.LLV(n,s),p=this.DIV(this.MUL(this.SUB(r,c),100),this.SUB(this.HHV(o,s),c)),u=this.SMA(p,a,1),l=this.SMA(u,h,1),d=this.SUB(this.MUL(3,u),this.MUL(2,l));return{k:this.forEach([u],e),d:this.forEach([l],e),j:d}},dmi:function(t,i){void 0===i&&(i={});for(var e=t.close,r=t.high,o=t.low,n=i.n||14,s=i.m||6,a=this.REF(e,1),h=this.MAX(this.SUB(r,o),this.ABS(this.SUB(r,a))),c=this.ABS(this.SUB(o,a)),p=this.SUM(this.MAX(h,c),n),u=this.SUB(r,this.REF(r,1)),l=this.SUB(this.REF(o,1),o),d=[],f=[],g=0;g<e.length;g++)d[g]=u[g]>0&&u[g]>l[g]?u[g]:0,f[g]=l[g]>0&&l[g]>u[g]?l[g]:0;var x=this.SUM(d,n),y=this.SUM(f,n),w=this.DIV(this.MUL(x,100),p),m=this.DIV(this.MUL(y,100),p),v=this.MA(this.MUL(this.DIV(this.ABS(this.SUB(m,w)),this.ADD(m,w)),100),s);return{pdi:w,mdi:m,adx:v,adxr:this.DIV(this.ADD(v,this.REF(v,s)),2)}},wr:function(t,i){void 0===i&&(i={});for(var e=t.close,r=t.high,o=t.low,n=i.n1||10,s=i.n2||6,a=[n||10,s||6],h={},c=0;c<2;c++){var p=a[c],u=this.HHV(r,p);h["wr"+(c+1)]=this.DIV(this.MUL(this.SUB(u,e),100),this.SUB(u,this.LLV(o,p)))}return h},obv:function(t){for(var i=[0],e=t.close,r=t.volume,o=1,n=e.length;o<n;o++)e[o]>e[o-1]?i[o]=i[o-1]+r[o]:e[o]<e[o-1]?i[o]=i[o-1]-r[o]:i[o]=i[o-1];return i},boll:function(t,i){void 0===i&&(i={});var e=t.close,r=i.deviation||20,o=i.width||2,n=this.MA(e,r),s=this.MUL(this.STD(e,r),o);return{upper:this.ADD(n,s),mid:n,lower:this.SUB(n,s)}},sar:function(t){var i=t.high,e=t.low,r=[],o=[],n=[],s=i.length,a=[],h=function(t){if(!(t>=s)){if(r[t]=Math.min.apply(null,e.slice(t-4,t)),a[t]=1,r[t]>e[t])c(t+1);else for(n[t]=Math.max.apply(null,i.slice(t-4+1,t+1)),o[t]=2;t<s-1;){if(r[t+1]=r[t]+o[t]*(n[t]-r[t])/100,a[t+1]=1,r[t+1]>e[t+1])return void c(t+2);n[t+1]=Math.max.apply(null,i.slice(t-4+2,t+2)),i[t+1]>n[t]?(o[t+1]=o[t]+2,o[t+1]>20&&(o[t+1]=20)):o[t+1]=o[t],t++}}},c=function(t){if(!(t>=s)){if(r[t]=Math.max.apply(null,i.slice(t-4,t)),a[t]=-1,r[t]<i[t])return void h(t+1);for(n[t]=Math.min.apply(null,e.slice(t-4+1,t+1)),o[t]=2;t<s-1;){if(r[t+1]=r[t]+o[t]*(n[t]-r[t])/100,a[t+1]=-1,r[t+1]<i[t+1])return void h(t+2);n[t+1]=Math.min.apply(null,e.slice(t-4+2,t+2)),e[t+1]<n[t]?(o[t+1]=o[t]+2,o[t+1]>20&&(o[t+1]=20)):o[t+1]=o[t],t++}}};return i[4]>i[0]||e[4]>e[0]?h(4):c(4),{sar:r,low:e,high:i}},cci:function(t,i){void 0===i&&(i={});for(var e,r,o,n=i.n||14,s=[],a=t.close,h=t.low,c=t.high,p=0;p<a.length;p++)e=c[p],r=h[p],o=a[p],0==e&&(e=o),0==r&&(r=o),s[p]=(e+r+o)/3;return this.DIV(this.SUB(s,this.MA(s,n)),this.MUL(.015,this.AVEDEV(s,n)))},bias:function(t){var i=t.close,e=this.MA(i,6),r=this.MA(i,12),o=this.MA(i,24);return{bias1:this.MUL(this.DIV(this.SUB(i,e),e),100),bias2:this.MUL(this.DIV(this.SUB(i,r),r),100),bias3:this.MUL(this.DIV(this.SUB(i,o),o),100)}},bbi:function(t){var i=t.close;return this.DIV(this.ADD(this.MA(i,3),this.MA(i,6),this.MA(i,12),this.MA(i,24)),4)},trix:function(t){var i=t.close,e=this.EMA(this.EMA(this.EMA(i,12),12),12),r=this.REF(e,1),o=this.MUL(this.DIV(this.SUB(e,r),r),100);return{trix:o,trma:this.MA(o,20)}},ene:function(t){var i=t.close,e=this.MA(i,10),r=this.MUL(1.11,e),o=this.MUL(.91,e);return{ene:this.DIV(this.ADD(r,o),2),upper:r,lower:o}},vr:function(t){for(var i=t.close,e=t.volume,r=[],o=[],n=[],s=this.REF(i,1),a=0;a<i.length;a++)r[a]=i[a]>s[a]?e[a]:0,o[a]=i[a]<s[a]?e[a]:0,n[a]=i[a]===s[a]?e[a]:0;var h=this.SUM(r,26),c=this.SUM(o,26),p=this.SUM(n,26),u=this.ADD(this.MUL(h,2),p),l=this.ADD(this.MUL(c,2),p),d=this.MUL(this.DIV(u,l),100);return{vr:d,vrma:this.MA(d,6)}},arbr:function(t){var i=t.open,e=t.close,r=t.high,o=t.low,n=this.SUM(this.SUB(r,i),26),s=this.SUM(this.SUB(i,o),26),a=this.MUL(this.DIV(n,s),100),h=this.REF(e,1);h[0]=0;var c=this.SUM(this.MAX(this.SUB(r,h),0),26),p=this.SUM(this.MAX(this.SUB(h,o),0),26);return{ar:a,br:this.MUL(this.DIV(c,p),100)}},psy:function(t){for(var i=t.close,e=[],r=this.REF(i,1),o=0;o<i.length;o++)e[o]=i[o]>r[o]?1:0;var n=this.MUL(this.DIV(this.SUM(e,12),12),100);return{psy:n,psyma:this.MA(n,6)}},dma:function(t){var i=t.close,e=this.SUB(this.MA(i,10),this.MA(i,50));return{ddd:e,ama:this.MA(e,10)}},dpo:function(t){var i=t.close,e=this.SUB(i,this.REF(this.MA(i,20),11));return{dpo:e,dpoma:this.MA(e,6)}},AVEDEV:function(t,i){if(null===t||0==t.length)return null;var e=t.length,r=0,o=0,n=0,s=0,a=Array(e);if(i<=1||e<i)return a;for(;r<e&&n<i-1;)s+=t[r],n++,r++;for(;r<e;r++){s+=t[r],r-i>=0&&r-i<e&&(s-=t[r-i]);var h=s/i,c=0;for(o=r-i+1;o<=r;o++)c+=Math.abs(t[o]-h);a[r]=c/i}return a},ADD:function(){for(var t=this,i=[],e=0;e<arguments.length;e++)i[e]=arguments[e];return this.forEach(i,function(){for(var i=[],e=0;e<arguments.length;e++)i[e]=arguments[e];return t.reduce(i,function(t,i){return t+i},!0)})},SUB:function(){for(var t=this,i=[],e=0;e<arguments.length;e++)i[e]=arguments[e];return this.forEach(i,function(){for(var i=[],e=0;e<arguments.length;e++)i[e]=arguments[e];return t.reduce(i,function(t,i){return t-i},!0)})},MUL:function(){for(var t=this,i=[],e=0;e<arguments.length;e++)i[e]=arguments[e];return this.forEach(i,function(){for(var i=[],e=0;e<arguments.length;e++)i[e]=arguments[e];return t.reduce(i,function(t,i){return t*i},!0)})},DIV:function(){for(var t=this,i=[],e=0;e<arguments.length;e++)i[e]=arguments[e];return this.forEach(i,function(){for(var i=[],e=0;e<arguments.length;e++)i[e]=arguments[e];return t.reduce(i,function(t,i){if(i)return t/i},!0)})},POW:function(){for(var t=[],i=0;i<arguments.length;i++)t[i]=arguments[i];return this.forEach(t,function(t,i){return Math.pow(t,i)})},MAX:function(){for(var t=this,i=[],e=0;e<arguments.length;e++)i[e]=arguments[e];return this.forEach(i,function(){for(var i=[],e=0;e<arguments.length;e++)i[e]=arguments[e];return t.reduce(i,function(t,i){return t<i?i:t})})},MIN:function(){for(var t=this,i=[],e=0;e<arguments.length;e++)i[e]=arguments[e];return this.forEach(i,function(){for(var i=[],e=0;e<arguments.length;e++)i[e]=arguments[e];return t.reduce(i,function(t,i){return t<i?t:i})})},maxmin:function(){for(var t,i,e,r=0;r<arguments.length;r++)e=Math.max.apply(null,arguments[r]),t=void 0!==t?Math.max(t,e):e,e=Math.min.apply(null,arguments[r]),i=void 0!==i?Math.min(i,e):e;return{max:t,min:i}},ABS:function(t){return this.forEach([t],function(t){return Math.abs(t)})},REF:function(t,i){return Array(i).concat(t).slice(0,t.length)},HHV:function(t,i){return this.hhvllv(t,i,function(t,i){return t<i?i:t})},LLV:function(t,i){return this.hhvllv(t,i,function(t,i){return t>i?i:t})},SUM:function(t,i){var e=t.length,r=0,o=0,n=[];if(i<=0)return this.reduce(t,function(t,i){return t+i});if(i<=1)return t.slice();for(;r<e;r++)this.isNumber(t[r])&&(this.isNumber(o)?o+=t[r]:o=t[r]),n[r]=o,this.isNumber(t[r-i+1])&&(o-=t[r-i+1]);return n},MA:function(t,i){var e=t.length,o=0,n=-1,s=0,a=0,h=[];if(i<=0)return(a=this.reduce(t,function(t,i){return t+i}))/e;if(i<=1)return t.slice();if(e<i)return Array(e);for(;o<e&&s<i-1;)r.isNumber(t[o])&&(a+=t[o],s++),o++;for(;o<e;o++,n++)r.isNumber(t[o])&&(a+=t[o]),r.isNumber(t[o-i])&&(a-=t[o-i]),h[o]=a/i;return h},DMA:function(t,i,e){var o=t.length,n=0,s=1-i,a=0,h=[];if(i>1)return Array(o);if(1==i)return t.slice();if(void 0===e){for(;n<o;n++)if(r.isNumber(t[n])){h[n]=1==e?0:t[n],a=t[n],n++;break}}for(;n<o;n++)r.isNumber(t[n])?a=h[n]=i*t[n]+s*a:h[n]=h[n-1];return h},SMA:function(t,i,e){return this.DMA(t,e/i,1)},EMA:function(t,i){return this.DMA(t,2/(i+1))},WMA:function(t,i){var e=t.length,o=0,n=-1,s=[];if(i<=1)return t.slice();if(e<i)return Array(e);for(var a=i*(i+1)/2,h=0,c=0;o<i-1;o++)r.isNumber(t[o])&&(h+=t[o],c+=(o+1)*t[o]);for(;o<e;o++,n++)r.isNumber(t[o])&&(h+=t[o],c+=t[o]*i),n>=0&&r.isNumber(t[n])&&(h-=t[n]),s[o]=c/a,c-=h;return s},STD:function(t,i){for(var e=[],o=r.MA(t,i),n=i-1,s=t.length;n<s;n++){for(var a=0,h=n-i+1;h<=n;h++)a+=Math.pow(t[h]-o[n],2);e[n]=Math.sqrt(a/i)}return e},forEach:function(t,i){var e,r,o,n=t.length,s=[],a=-1,h=[];for(r=0;r<n;r++)s[r]=this.isArray(t[r])?t[r].length:-1,s[r]>a&&(a=s[r]);for(r=0;r<a;r++){for(e=[],o=0;o<n;o++)e[o]=s[o]<0?t[o]:t[o][r];h[r]=i.apply({index:r},e)}return h},reduce:function(t,i,e,r,o){var n,s=0,a=t.length;for("number"==typeof i&&(s=i,a=e,i=r,e=o);s<a;s++)if(e||this.isNumber(t[s])){n=+t[s];break}for(s++;s<a;s++)(e||this.isNumber(t[s]))&&(n=i(n,+t[s],s));return n},isArray:function(t){return"[object Array]"==({}).toString.call(t)},isNumber:function(t){return null!==t&&""!==t&&isFinite(t)},hhvllv:function(t,i,e){var r=[],o=i-1,n=t.length;if(i>n)return Array(n);if(i<=0)return this.reduce(t,e);if(i<=1)return t.slice();for(;o<n;o++)r[o]=this.reduce(t,o-i+1,o+1,e);return r}};i.a=r},function(t,i,e){"use strict";var r,o=e(52),n=this&&this.__extends||(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var e in i)i.hasOwnProperty(e)&&(t[e]=i[e])},function(t,i){function e(){this.constructor=t}r(t,i),t.prototype=null===i?Object.create(i):(e.prototype=i.prototype,new e)}),s=function(t){function i(i){return t.call(this,i)||this}return n(i,t),i.prototype.getMaxChangeSum=function(){return 6},i.prototype.getMaxKlineCount=function(){return 40},i.prototype.getMinKlineCount=function(){return 25},i.prototype.getPriceCount=function(){return 100},i.prototype.calc=function(t,i){try{var e=this.getGraphDataCount(t,i);if(0==e)return console.error("calc klinecount 0"),null;var r=this.genDayKlineData(t,i,e);i=r.length-1,e=r.length;for(var o=this.getPriceCount()-1,n=this.getMaxMinPrice(r,i,e),s=Math.abs(n.max-n.min)/o,a=[],h=0;h<=o;h++)a[h]=n.min+h*s;var c=[],p=[1],u=r[i];c[0]=this.tansformToRealChange(u.hsl);for(var h=i-1;h>i-e&&h>=0;h--)c[i-h]=this.getATurn(r,i,h,p);for(var l=Array(o+1).fill(0),d=Array(o+1).fill(0),f=i;f>i-e&&f>=0;f--){var g=r[f];if(null!=g){var x=g.high,y=g.low,w=(x+y)/2,m=this.findPriceSection(r,f,a,s);if(!(m.from>m.to)){for(var v=0,b=m.from;b<=m.to;b++)a[b]>=w?d[b]=x-a[b]:d[b]=a[b]-y,v+=d[b];for(var M=m.from;M<=m.to;M++)if(Math.abs(v)>1e-6){var C=c[i-f]*d[M]/v;l[M]+=C}}}}return{chip:l,price:a,maxMin:n}}catch(t){return console.error("calc",t),null}},i}(o.a);i.a=s},function(t,i,e){"use strict";var r,o=e(2),n=e(8),s=e(1),a=this&&this.__extends||(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var e in i)i.hasOwnProperty(e)&&(t[e]=i[e])},function(t,i){function e(){this.constructor=t}r(t,i),t.prototype=null===i?Object.create(i):(e.prototype=i.prototype,new e)}),h="boll",c=function(t){function i(i,e,r,o){var n=Math.max(e.maxMin.kline.max,e.maxMin.boll.max),s=Math.min(e.maxMin.kline.min,e.maxMin.boll.min),a=r.setting[h+"Params"],c=a.deviation,p=a.width;return t.call(this,i,e,r,o,h,"("+c+","+p+")",null,n,s)||this}return a(i,t),i.prototype.drawLinearShapes=function(t){var i=this;void 0===t&&(t=!1);var e=this.data.items;["lower","mid","upper"].forEach(function(t){var e=i.data.items.map(function(i){return i[h][t]});new n.a({ctx:i.ctx,region:i.region,drawCallback:function(e){return i.drawLineItem(e,t)},data:{items:e,max:i.max,min:i.min},count:i.props.count})}),t||new n.a({ctx:this.ctx,region:this.region,drawCallback:function(t){return i.drawKlineItem(t)},data:{items:e,max:this.max,min:this.min},count:this.props.count})},i.prototype.drawLineItem=function(t,i){var e=this.props,r=t.index,o=t.getX(r)+e.itemWidth/2,n=t.getY(t.currItem);Object(s.d)(this.ctx,o,n,this.props.colorProp[h][i],this.props.lineProp.indicator,r,t.length)},i.prototype.drawKlineItem=function(t){if(!t.currItem.forBounce){var i=t.index,e=this.data.items[i],r=t.getX(i)+this.props.itemWidth/2;Object(s.e)(this.ctx,{open:t.getY(e.open),close:t.getY(e.close),low:t.getY(e.low),high:t.getY(e.high)},r,this.props.itemWidth,this.props.colorProp[h].ochl,this.props.lineProp.ochl)}},i}(o.a);i.a=c},function(t,i,e){"use strict";var r,o=e(19),n=e(32),s=e(0),a=e(9),h=e(7),c=this&&this.__extends||(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var e in i)i.hasOwnProperty(e)&&(t[e]=i[e])},function(t,i){function e(){this.constructor=t}r(t,i),t.prototype=null===i?Object.create(i):(e.prototype=i.prototype,new e)}),p=this&&this.__assign||Object.assign||function(t){for(var i,e=1,r=arguments.length;e<r;e++)for(var o in i=arguments[e],i)Object.prototype.hasOwnProperty.call(i,o)&&(t[o]=i[o]);return t},u=function(t){function i(i,e,r,o,n,s){return t.call(this,i,e,r,o,n,s)||this}return c(i,t),i.prototype.drawScale=function(){var t=this.region.yAxis.x+this.region.yAxis.width,i=this.region.height/100,e={font:this.props.textProp.font,textAlign:s.a.textAlign.right,baseLine:s.a.baseLine.btm,color:this.props.colorProp.yAxis};new a.a(this.ctx,h.b.y,[{text:20,x:t,y:this.region.yAxis.y+80*i,props:p({},e,{baseLine:s.a.baseLine.top})},{text:50,x:t,y:this.region.yAxis.y+50*i,props:p({},e,{baseLine:s.a.baseLine.mid})},{text:80,x:t,y:this.region.yAxis.y+20*i,props:e}],this.region.yAxis).draw()},i.prototype.drawGrid=function(){var t=this.props.colorProp,i=this.region.height/100;new o.a(this.ctx,{border:{color:t.border,lineWidth:t.border},vline:{color:t.vline,lineWidth:t.vline,posx:this.props.posx},hline:{color:t.hline,lineWidth:t.vline,posy:[{y:this.region.y+80*i},{y:this.region.y+50*i},{y:this.region.y+20*i}]}},this.region)},i}(n.a);i.a=u},function(t,i,e){"use strict";var r,o=e(2),n=e(8),s=e(1),a=this&&this.__extends||(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var e in i)i.hasOwnProperty(e)&&(t[e]=i[e])},function(t,i){function e(){this.constructor=t}r(t,i),t.prototype=null===i?Object.create(i):(e.prototype=i.prototype,new e)}),h=function(t){function i(i,e,r,o){var n=Math.max(e.maxMin.kline.max,e.maxMin.sar.max),s=Math.min(e.maxMin.kline.min,e.maxMin.sar.min);return t.call(this,i,e,r,{x:o.x,y:o.y+r.sarCirclrRadius,width:o.width,height:o.height-r.sarCirclrRadius,yAxis:o.yAxis,bar:o.bar},"sar","(4,2,20)",null,n,s)||this}return a(i,t),i.prototype.drawLinearShapes=function(t){var i=this;void 0===t&&(t=!1);var e=this.data.items;new n.a({ctx:this.ctx,region:this.region,drawCallback:function(t){return i.drawCircle(t)},data:{items:e.map(function(t){return t.sar.sar}),max:this.max,min:this.min},count:this.props.count}),t||new n.a({ctx:this.ctx,region:this.region,drawCallback:function(t){return i.drawOCHL(t)},data:{items:e,max:this.max,min:this.min},count:this.props.count})},i.prototype.drawCircle=function(t){var i=this.props,e=t.index,r=t.getX(e)+i.itemWidth/2,o=t.getY(t.currItem),n=this.getSarColor(this.data.items[e].close,t.currItem),s=this.ctx;s.setStrokeStyle(n),s.beginPath(),s.arc(r,o,this.props.sarCirclrRadius,0,2*Math.PI,!0),s.stroke(),s.closePath()},i.prototype.getSarColor=function(t,i){return i<=t?this.props.colorProp.rise:this.props.colorProp.drop},i.prototype.drawOCHL=function(t){if(!t.currItem.forBounce){var i=t.index,e=this.data.items[i],r=t.getX(i)+this.props.itemWidth/2;Object(s.e)(this.ctx,{open:t.getY(e.open),close:t.getY(e.close),low:t.getY(e.low),high:t.getY(e.high)},r,this.props.itemWidth,"#4280f2",this.props.lineProp.ochl)}},i}(o.a);i.a=h},function(t,i,e){"use strict";var r,o=e(2),n=e(8),s=e(1),a=this&&this.__extends||(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var e in i)i.hasOwnProperty(e)&&(t[e]=i[e])},function(t,i){function e(){this.constructor=t}r(t,i),t.prototype=null===i?Object.create(i):(e.prototype=i.prototype,new e)}),h=function(t){function i(i,e,r,o){var n=Math.max(e.maxMin.kline.max,e.maxMin.ene.max),s=Math.min(e.maxMin.kline.min,e.maxMin.ene.min);return t.call(this,i,e,r,o,"ene","(11,9,10)",null,n,s)||this}return a(i,t),i.prototype.drawLinearShapes=function(t){var i=this;void 0===t&&(t=!1),t||new n.a({ctx:this.ctx,region:this.region,drawCallback:function(t){if(!t.currItem.forBounce){var e=i.data.items[t.index],r=t.getX(t.index)+i.props.itemWidth/2;Object(s.e)(i.ctx,{open:t.getY(e.open),close:t.getY(e.close),low:t.getY(e.low),high:t.getY(e.high)},r,i.props.itemWidth,"#4280f2",i.props.lineProp.ochl)}},data:{items:this.data.items,max:this.max,min:this.min},count:this.props.count});for(var e=this,r=0,o=["ene","upper","lower"];r<o.length;r++)!function(t){new n.a({ctx:e.ctx,region:e.region,drawCallback:function(e){var r=e.getX(e.index)+i.props.itemWidth/2,o=e.getY(e.currItem);Object(s.d)(i.ctx,r,o,i.props.colorProp.ene[t],i.props.lineProp.indicator,e.index,e.length)},data:{items:e.data.items.map(function(i){return i.ene[t]}),max:e.max,min:e.min},count:e.props.count})}(o[r])},i}(o.a);i.a=h},function(t,i,e){"use strict";e.d(i,"b",function(){return f});var r=e(50),o=e(84),n=e(86),s=e(87),a=e(0),h=e(3),c=e(1),p=e(76),u=this&&this.__assign||Object.assign||function(t){for(var i,e=1,r=arguments.length;e<r;e++)for(var o in i=arguments[e],i)Object.prototype.hasOwnProperty.call(i,o)&&(t[o]=i[o]);return t},l=document.createElement("img"),d=document.createElement("img");l.src=e(176),d.src=e(177);var f={x:0},g=function(){function t(t,i,e,r){this.ctx=t,this.data=i,this.props=e,this.region=r,this.indicator=this.props.mainIndicator||"ma";var o=Object(h.c)(i,e),n=o.max,s=o.min;this.max=n,this.min=s}return t.prototype.draw=function(){var t=this.region.chart.x,i=this.region.chart.y/2,e=a.a.baseLine.mid,r=this.indicator.toUpperCase(),o={color:this.props.colorProp.tip,font:this.props.textProp.font,baseLine:e};if(this.props.wxSearchStyle&&(i=0,o.baseLine=a.a.baseLine.top),!/portrait/.test(this.props.layout)||this.props.crossLineItem||this.props.disableInteract||this.props.disableMainIndicator)Object(a.c)(this.ctx,r,t,i,o),t+=Object(a.d)(this.ctx,r,o),this.drawCurrent(t);else{var n=this.region.chart,s=n.x,h=n.y,p=45*this.props.devicePixelRatio;Object(c.g)(this.ctx,s,0,p,h,this.props.colorProp.button.bg,this.props.colorProp.button.bg),Object(a.c)(this.ctx,r,s+.4*p,h/2,{color:this.props.colorProp.button.text,font:this.props.textProp.font,textAlign:a.a.textAlign.center,baseLine:a.a.baseLine.mid});var u=s+.85*p,l=h/2,d=2*this.props.devicePixelRatio,f=3*this.props.devicePixelRatio;Object(c.h)(this.ctx,u-f,l-d,u+f,l-d,u,l+d,this.props.colorProp.button.tri,this.props.colorProp.button.tri),this.drawCurrent(s+p)}},t.prototype.drawCurrent=function(t){t+=5*this.props.devicePixelRatio,"ma"===this.indicator||"ema"===this.indicator?this.drawMA(t,this.props.setting[this.indicator+"Types"]):this.drawOthers(t)},t.prototype.drawOthers=function(t){var i="";if("boll"===this.indicator){var e=this.props.setting.bollParams;i="("+e.deviation+","+e.width+")",this.props.fixedWidth||new o.a(this.ctx,this.data,this.props,this.region.chart).drawLinearShapes(!0)}else"sar"===this.indicator?(i="(4,2,20)",this.props.fixedWidth||new n.a(this.ctx,this.data,this.props,this.region.chart).drawLinearShapes(!0)):"ene"===this.indicator&&(i="(11,9,10)",this.props.fixedWidth||new s.a(this.ctx,this.data,this.props,this.region.chart).drawLinearShapes(!0));var r=(this.props.crossLineItem||this.data.items[this.data.items.length-1])[this.indicator],h=5*this.props.devicePixelRatio,c=this.region.chart.y/2,p=10,l={color:this.props.colorProp.defaultGray,font:p*this.props.devicePixelRatio+"px Arial",baseLine:a.a.baseLine.mid},d=i,f=1;for(var g in r){var x=g.toUpperCase()+": ",y=""+(isNaN(r[g])?"--":r[g].toFixed(3));d+=x+y,f+=1}for(var w=f*h+(this.props.crossLineItem?this.props.market<=2?90:50:30)*this.props.devicePixelRatio;p>7&&(this.region.chartAndChipWidth-t-Object(a.d)(this.ctx,d,l)-w)/this.props.devicePixelRatio<0;)l.font=--p*this.props.devicePixelRatio+"px Arial";for(var g in Object(a.c)(this.ctx,i,t,c,l),t+=Object(a.d)(this.ctx,i,l)+h,r){var x=g.toUpperCase()+": ",y=""+(isNaN(r[g])?"--":r[g].toFixed(3));Object(a.c)(this.ctx,x,t,c,u({},l,{color:this.props.colorProp[this.indicator][g]})),t+=Object(a.d)(this.ctx,x,l),Object(a.c)(this.ctx,y,t,c,l),t+=Object(a.d)(this.ctx,y,l)+h}},t.prototype.drawMA=function(t,i){var e=this,o=this.region.chart;if(i.length>0){var n=i.map(function(t){return""+e.indicator+t});this.props.fixedWidth||new r.a(this.ctx,this.data,this.props,o,n,this.max,this.min).draw();var s=o.y/2,h=10,c=this.props.devicePixelRatio,u={color:this.props.colorProp.defaultGray,font:h*c+"px Arial",baseLine:a.a.baseLine.mid};this.props.wxSearchStyle&&(s=0,u.baseLine=a.a.baseLine.top);var l=this.props.crossLineItem||this.data.items[this.data.items.length-1],d=[],f=[];if("kline-portrait"===this.props.layout){for(var g="",x=0,y=0;y<i.length;y++)if(i[y]>0){var w=i[y]+": ",m=+l[""+this.indicator+i[y]],v=isNaN(m)?"--":m.toFixed(this.props.fixNum||2);(x+=1)<=5?(d.push({i:y,title:w,num:v}),g+=w+v):f.push({i:y,title:w,num:v})}var b=5*(d.length-1)*c,M=void 0;for(M="day"===this.props.type?90:60;h>0&&d.length&&(this.region.chartAndChipWidth-Object(a.d)(this.ctx,g,u)-b-t)/c<M;)u.font=--h*c+"px Arial";this.drawMAText(d,t,s,u);var C=p.b.foldState;f.length&&(this.drawArrow(C),C||(s=1.5*o.y,this.drawMAText(f,t,s,u)))}else this.drawLandscapeBar(l,i,t,u)}},t.prototype.drawMAText=function(t,i,e,r){var o=this,n=this.ctx;n.save(),t.forEach(function(t){var s=t.i,h=t.title,c=t.num;Object(a.c)(n,h,i,e,u({},r,{color:o.props.colorProp.ma[s]})),i+=Object(a.d)(o.ctx,h,r),Object(a.c)(n,c,i,e,r),i+=Object(a.d)(o.ctx,c,r)+5*o.props.devicePixelRatio}),n.restore()},t.prototype.drawArrow=function(t){var i=this.ctx;i.save();var e=t?l:d,r=this.region.chart,o=this.props.devicePixelRatio,n=9*o,s=6*o,h=Object(a.d)(i,"前复权",{color:this.props.colorProp.tip,font:this.props.textProp.font,baseLine:a.a.baseLine.mid}),c=r.x+this.region.chartAndChipWidth-h-9*o-n,p=-r.height/2-r.y/2-s/2;f.x=c,i.translate(r.x,r.y+r.height/2),i.drawImage(e,c,p,n,s),i.restore()},t.prototype.drawLandscapeBar=function(t,i,e,r){var o=this,n="",s=10,h=0,c=[],p=this.region.chart,u=p.y/2,l=this.props.devicePixelRatio;i.forEach(function(i,e){if(i>0){var r=i+": ",s=+t[""+o.indicator+i],a=isNaN(s)?"--":s.toFixed(o.props.fixNum||2);h+=1,c.push({i:e,title:r,num:a}),n+=r+a}});for(var d=5*(h-1)*l;s>0&&c.length&&(this.region.chartAndChipWidth-Object(a.d)(this.ctx,n,r)-d-e+p.x)/l<60;)r.font=--s*l+"px Arial";this.drawMAText(c,e,u,r)},t}();i.a=g},function(t,i,e){"use strict";i.a=function(t){var i=t.layout,e=t.isHistoryMins,r=t.setting.chartRatio<100;switch(i){case"mins-portrait":return{yAixsCount:r||e?5:7,hlineCount:r||e?3:5,vlineCount:3};case"mins-landscape":return{yAixsCount:7,hlineCount:5,vlineCount:3};case"kline-portrait":return{yAixsCount:r?5:7,hlineCount:r?3:5};case"kline-landscape":return{yAixsCount:7,hlineCount:5}}}},function(t,i){t.exports="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDIxLjAuMCwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDYuMDAgQnVpbGQgMCkgIC0tPgo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IuWbvuWxgl8xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB4PSIwcHgiIHk9IjBweCIKCSB2aWV3Qm94PSIwIDAgMTMgMjAiIHN0eWxlPSJlbmFibGUtYmFja2dyb3VuZDpuZXcgMCAwIDEzIDIwOyIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+CjxzdHlsZSB0eXBlPSJ0ZXh0L2NzcyI+Cgkuc3Qwe2ZpbGw6IzMwNzdFQzt9Cgkuc3Qxe2ZpbGw6I0ZGRkZGRjt9Cjwvc3R5bGU+Cjx0aXRsZT7nvJbnu4QgMTXlpIfku708L3RpdGxlPgo8ZyBpZD0iS+e6v+WbvuWinuWKoOS5sOWNlueCueagh+iusCI+Cgk8ZyBpZD0i57yW57uELTE15aSH5Lu9IiB0cmFuc2Zvcm09InRyYW5zbGF0ZSg2LjUwMDAwMCwgMTAuMDAwMDAwKSBzY2FsZSgxLCAtMSkgdHJhbnNsYXRlKC02LjUwMDAwMCwgLTEwLjAwMDAwMCkgIj4KCQk8cGF0aCBpZD0i55+p5b2iIiBjbGFzcz0ic3QwIiBkPSJNMCwxNC41TDAsMS4zYzAtMC42LDAuNC0xLDEtMWgxMWMwLjYsMCwxLDAuNCwxLDF2MTMuMmMwLDAuMy0wLjEsMC42LTAuNCwwLjhsLTUuNSw0LjUKCQkJYy0wLjQsMC4zLTAuOSwwLjMtMS4zLDBsLTUuNS00LjVDMC4xLDE1LjEsMCwxNC44LDAsMTQuNXoiLz4KCQk8cGF0aCBpZD0iQiIgY2xhc3M9InN0MSIgZD0iTTIuNywzLjNWMTRoNC45YzEuMSwwLDEuOS0wLjIsMi41LTAuOGMwLjYtMC41LDAuOS0xLjEsMC45LTEuOWMwLTAuNi0wLjEtMS4xLTAuNC0xLjUKCQkJYy0wLjMtMC40LTEuMS0wLjctMS42LTAuOWMwLjctMC4xLDEuMi0wLjQsMS42LTAuOWMwLjQtMC40LDAuNS0xLDAuNS0xLjdjMC0xLjEtMC40LTEuOS0xLjEtMi40QzkuMywzLjUsOC44LDMuMyw3LjYsMy4zSDIuN3oKCQkJIE04LjgsOS44YzAuMywwLjMsMC41LDAuNywwLjUsMS4zYzAsMC41LTAuMiwwLjktMC41LDEuMmMtMC4zLDAuMy0wLjksMC40LTEuNiwwLjRINC40VjkuNWgyLjhDNy45LDkuNSw4LjUsOS42LDguOCw5Ljh6IE04LjcsNQoJCQljMC40LDAuMywwLjcsMC43LDAuNywxLjNjMCwwLjYtMC4yLDEuMS0wLjUsMS40UzgsOC4xLDcuMiw4LjFINC40VjQuNmgyLjhDNy44LDQuNiw4LjMsNC44LDguNyw1eiIvPgoJPC9nPgo8L2c+Cjwvc3ZnPgo="},function(t,i){t.exports="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDIxLjAuMCwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDYuMDAgQnVpbGQgMCkgIC0tPgo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IuWbvuWxgl8xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB4PSIwcHgiIHk9IjBweCIKCSB2aWV3Qm94PSIwIDAgMTMgMjAiIHN0eWxlPSJlbmFibGUtYmFja2dyb3VuZDpuZXcgMCAwIDEzIDIwOyIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+CjxzdHlsZSB0eXBlPSJ0ZXh0L2NzcyI+Cgkuc3Qwe2ZpbGw6IzMwNzdFQzt9Cgkuc3Qxe2ZpbGw6I0ZGRkZGRjt9Cjwvc3R5bGU+Cjx0aXRsZT7nvJbnu4QgMTXlpIfku708L3RpdGxlPgo8ZyBpZD0iS+e6v+WbvuWinuWKoOS5sOWNlueCueagh+iusCI+Cgk8ZyBpZD0i57yW57uELTE15aSH5Lu9IiB0cmFuc2Zvcm09InRyYW5zbGF0ZSg2LjUwMDAwMCwgMTAuMDAwMDAwKSBzY2FsZSgxLCAtMSkgdHJhbnNsYXRlKC02LjUwMDAwMCwgLTEwLjAwMDAwMCkgIj4KCQk8cGF0aCBpZD0i55+p5b2iIiBjbGFzcz0ic3QwIiBkPSJNMC40LDVsNS41LTQuNWMwLjQtMC4zLDAuOS0wLjMsMS4zLDBMMTIuNiw1QzEyLjksNS4yLDEzLDUuNSwxMyw1LjhWMTljMCwwLjYtMC40LDEtMSwxSDEKCQkJYy0wLjYsMC0xLTAuNC0xLTFWNS44QzAsNS41LDAuMSw1LjIsMC40LDV6Ii8+CgkJPHBhdGggaWQ9IkIiIGNsYXNzPSJzdDEiIGQ9Ik03LjYsMTdjMS4xLDAsMS43LTAuMiwyLjMtMC42YzAuNy0wLjUsMS4xLTEuMywxLjEtMi40YzAtMC43LTAuMi0xLjMtMC41LTEuNwoJCQljLTAuNC0wLjQtMC45LTAuNy0xLjYtMC45YzAuNS0wLjIsMS4zLTAuNSwxLjYtMC45YzAuMy0wLjQsMC40LTAuOSwwLjQtMS41YzAtMC44LTAuMy0xLjUtMC45LTEuOUM5LjUsNi41LDguNyw2LjMsNy42LDYuM0gyLjcKCQkJVjE3SDcuNnogTTcuMiwxMC44SDQuNFY3LjZoMi44YzAuNywwLDEuMywwLjEsMS42LDAuNGMwLjMsMC4yLDAuNSwwLjYsMC41LDEuMmMwLDAuNi0wLjIsMS0wLjUsMS4zQzguNSwxMC43LDcuOSwxMC44LDcuMiwxMC44egoJCQkgTTcuMiwxNS42SDQuNHYtMy41aDIuOWMwLjcsMCwxLjMsMC4xLDEuNiwwLjRzMC41LDAuOCwwLjUsMS40YzAsMC42LTAuMiwxLjEtMC43LDEuM0M4LjMsMTUuNSw3LjgsMTUuNiw3LjIsMTUuNnoiLz4KCTwvZz4KPC9nPgo8L3N2Zz4K"},function(t,i){t.exports="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDIxLjAuMCwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDYuMDAgQnVpbGQgMCkgIC0tPgo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IuWbvuWxgl8xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB4PSIwcHgiIHk9IjBweCIKCSB2aWV3Qm94PSIwIDAgMTMgMjAiIHN0eWxlPSJlbmFibGUtYmFja2dyb3VuZDpuZXcgMCAwIDEzIDIwOyIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+CjxzdHlsZSB0eXBlPSJ0ZXh0L2NzcyI+Cgkuc3Qwe2ZpbGw6I0ZGODkxRTt9Cgkuc3Qxe2ZpbGw6I0ZGRkZGRjt9Cjwvc3R5bGU+Cjx0aXRsZT7nvJbnu4QgMTU8L3RpdGxlPgo8ZyBpZD0iS+e6v+WbvuWinuWKoOS5sOWNlueCueagh+iusCI+Cgk8ZyBpZD0i57yW57uELTE1Ij4KCQk8cGF0aCBpZD0i55+p5b2iIiBjbGFzcz0ic3QwIiBkPSJNMCw1LjVsMCwxMy4yYzAsMC42LDAuNCwxLDEsMWgxMWMwLjYsMCwxLTAuNCwxLTFWNS41YzAtMC4zLTAuMS0wLjYtMC40LTAuOEw3LjEsMC4yCgkJCWMtMC40LTAuMy0wLjktMC4zLTEuMywwTDAuNCw0LjdDMC4xLDQuOSwwLDUuMiwwLDUuNXoiLz4KCQk8cGF0aCBpZD0iUyIgY2xhc3M9InN0MSIgZD0iTTYuNiwxNi45YzEuMywwLDIuMi0wLjMsMi45LTAuOGMwLjctMC41LDEuMS0xLjIsMS4xLTIuMWMwLTAuOS0wLjQtMS42LTEuMy0yLjIKCQkJYy0wLjQtMC4yLTEuMy0wLjYtMi42LTFjLTAuOS0wLjMtMS41LTAuNS0xLjctMC42QzQuNCwxMCw0LjIsOS43LDQuMiw5LjJjMC0wLjUsMC4yLTAuOSwwLjYtMS4xYzAuMy0wLjIsMC45LTAuMywxLjUtMC4zCgkJCUM3LjEsNy44LDcuNyw4LDgsOC4zQzguNCw4LjUsOC43LDksOC44LDkuN2gxLjVjLTAuMS0xLjEtMC41LTEuOS0xLjItMi40QzguNSw2LjcsNy42LDYuNSw2LjQsNi41Yy0xLjEsMC0xLjksMC4yLTIuNiwwLjcKCQkJQzMsNy43LDIuNyw4LjQsMi43LDkuM2MwLDAuOSwwLjQsMS41LDEuMSwyYzAuMywwLjIsMS4xLDAuNCwyLjMsMC44YzEuMSwwLjMsMS44LDAuNiwyLDAuN2MwLjYsMC4zLDEsMC44LDEsMS4zCgkJCWMwLDAuNC0wLjIsMC44LTAuNywxLjFzLTEuMSwwLjQtMS44LDAuNGMtMC44LDAtMS41LTAuMi0xLjktMC41Yy0wLjQtMC4zLTAuNy0wLjktMC44LTEuN0gyLjRjMC4xLDEuMywwLjUsMi4yLDEuNCwyLjgKCQkJQzQuNCwxNi42LDUuNCwxNi45LDYuNiwxNi45eiIvPgoJPC9nPgo8L2c+Cjwvc3ZnPgo="},function(t,i){t.exports="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDIxLjAuMCwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDYuMDAgQnVpbGQgMCkgIC0tPgo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IuWbvuWxgl8xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB4PSIwcHgiIHk9IjBweCIKCSB2aWV3Qm94PSIwIDAgMTMgMjAiIHN0eWxlPSJlbmFibGUtYmFja2dyb3VuZDpuZXcgMCAwIDEzIDIwOyIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+CjxzdHlsZSB0eXBlPSJ0ZXh0L2NzcyI+Cgkuc3Qwe2ZpbGw6I0ZGODkxRTt9Cgkuc3Qxe2ZpbGw6I0ZGRkZGRjt9Cjwvc3R5bGU+Cjx0aXRsZT7nvJbnu4QgMTU8L3RpdGxlPgo8ZyBpZD0iS+e6v+WbvuWinuWKoOS5sOWNlueCueagh+iusCI+Cgk8ZyBpZD0i57yW57uELTE1Ij4KCQk8cGF0aCBpZD0i55+p5b2iIiBjbGFzcz0ic3QwIiBkPSJNMC40LDE1bDUuNSw0LjVjMC40LDAuMywwLjksMC4zLDEuMywwbDUuNS00LjVjMC4yLTAuMiwwLjQtMC41LDAuNC0wLjhWMWMwLTAuNi0wLjQtMS0xLTFIMQoJCQlDMC40LDAsMCwwLjQsMCwxdjEzLjJDMCwxNC41LDAuMSwxNC44LDAuNCwxNXoiLz4KCQk8cGF0aCBpZD0iUyIgY2xhc3M9InN0MSIgZD0iTTYuNiwxMy4yYzEuMywwLDIuMi0wLjMsMi45LTAuOGMwLjctMC41LDEuMS0xLjIsMS4xLTIuMWMwLTAuOS0wLjQtMS42LTEuMy0yLjIKCQkJQzguOSw3LjksOCw3LjYsNi43LDcuMkM1LjcsNi45LDUuMSw2LjcsNC45LDYuNkM0LjQsNi4zLDQuMiw2LDQuMiw1LjVjMC0wLjUsMC4yLTAuOSwwLjYtMS4xYzAuMy0wLjIsMC45LTAuMywxLjUtMC4zCgkJCWMwLjgsMCwxLjMsMC4xLDEuNywwLjRDOC40LDQuOCw4LjcsNS4zLDguOCw2aDEuNWMtMC4xLTEuMS0wLjUtMS45LTEuMi0yLjRDOC41LDMsNy42LDIuOCw2LjQsMi44QzUuNCwyLjgsNC41LDMsMy44LDMuNQoJCQlDMyw0LDIuNyw0LjcsMi43LDUuNmMwLDAuOSwwLjQsMS41LDEuMSwyQzQuMSw3LjgsNC45LDgsNi4xLDguNGMxLjEsMC4zLDEuOCwwLjYsMiwwLjdjMC42LDAuMywxLDAuOCwxLDEuMwoJCQljMCwwLjQtMC4yLDAuOC0wLjcsMS4xcy0xLjEsMC40LTEuOCwwLjRjLTAuOCwwLTEuNS0wLjItMS45LTAuNUM0LjMsMTEuMSw0LDEwLjUsMy45LDkuN0gyLjRjMC4xLDEuMywwLjUsMi4yLDEuNCwyLjgKCQkJQzQuNCwxMi45LDUuNCwxMy4yLDYuNiwxMy4yeiIvPgoJPC9nPgo8L2c+Cjwvc3ZnPgo="},function(t,i){t.exports="data:image/svg+xml;base64,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"},function(t,i,e){"use strict";var r=e(186),o=e(187);i.a={lineProp:r.a,textProp:o.a}},function(t,i,e){"use strict";var r=e(1),o=e(53),n=e(0),s=document.createElement("img"),a=document.createElement("img");s.src=e(54),a.src=e(188);var h=function(){function t(t,i,e){this.ctx=t,this.region=i,this.prop=e,this.moveUnit=e.spaceWidth+e.barWidth,this.showCrossLineValue=e.showCrossLineValue,this.count=e.count,this.font=e.textProp.font,this.tipsColor=e.colorProp.tip,this.timeBarCanClick="day"===this.prop.type&&"kline-portrait"===this.prop.layout&&this.prop.market<=2}return t.prototype.changeCoordsForMins=function(t){var i=Object(o.a)(this.ctx.ctx.canvas,t),e=i.x,r=i.y;if(e*=this.prop.devicePixelRatio,r*=this.prop.devicePixelRatio,this.prop.showAuction&&e-this.region.x<this.region.auctionWidth){var n=Math.floor((e-this.region.x)/this.prop.auctionItemWidth);return n=Math.min(Math.max(n,0),this.prop.auctionCount-1),e=this.region.x+this.prop.auctionItemWidth*n,{x:e,y:r=Math.min(Math.max(r,this.region.y),this.region.y+this.region.height),index:n,auction:!0}}var s=Math.floor((e-this.region.x-this.region.auctionWidth)/this.moveUnit);return s=Math.min(Math.max(s,0),this.count-1),e=this.region.x+this.moveUnit*s+this.region.auctionWidth,{x:e,y:r=Math.min(Math.max(r,this.region.y),this.region.y+this.region.height),index:s,auction:!1}},t.prototype.changeCoords=function(t){var i,e=Object(o.a)(this.ctx.ctx.canvas,t),r=e.x,n=e.y,s=this.prop.devicePixelRatio;return r*=s,n*=s,(i=Math.ceil((r-this.region.x)/this.moveUnit))>=this.count&&(i=this.count-1),i<0&&(i=0),10*(r=this.region.x+this.moveUnit*i+.5*this.moveUnit)%10==0&&(r+=.5),n>this.region.y+this.region.height&&(n=this.region.y+this.region.height),n<this.region.y&&(n=this.region.y),{x:r,y:n,index:i}},t.prototype.getIndex=function(t){return this.changeCoords(t).index},t.prototype.changeCount=function(t){this.count=t.count,this.moveUnit=t.spaceWidth+t.barWidth},t.prototype.draw=function(t,i,e){void 0===e&&(e=2);var o=this.region,h=o.x,c=o.y,p=o.width,u=o.height,l=this.ctx,d="dark"===this.prop.skin,f=this.prop.devicePixelRatio;if(Object(r.c)(this.ctx,t.x,c,t.x,c+u,this.prop.colorProp.crossLine,"","",null,2),Object(r.c)(this.ctx,h,t.y,h+p,t.y,this.prop.colorProp.crossLine,"","",null,2),this.showCrossLineValue&&i){var g=i.leftval,x=i.rightval,y=i.toplval,w=i.bottomval,m="600 "+10*f+"px Arial",v=4*f,b=3.5*f,M=7*f,C=4*f,A=2*f,L=this.timeBarCanClick&&+i.time.replace(/-/g,"")>=this.prop.boundaryMinsDate;if(g){var P=Object(n.d)(l,g,this),I=P+2*C,j=15*f,S=this.region.x,T=t.y-j/2;T<this.region.y?T=this.region.y:T+j>this.region.y+this.region.height&&(T=this.region.y+this.region.height-j),Object(r.f)(l,S,T,I,j,A,d?"#475166":"#98A0B3",d?"#12161F":"white"),Object(n.c)(l,g,S+I/2,Math.ceil(T+j/2),{color:d?"#F0F1F5":"#262E40",font:m,textAlign:n.a.textAlign.center,baseLine:n.a.baseLine.mid})}if(x){var P=Object(n.d)(l,x,this),I=P+2*C,j=15*f,S=this.region.x+this.region.width-I,T=t.y-j/2;T<this.region.y?T=this.region.y:T+j>this.region.y+this.region.height&&(T=this.region.y+this.region.height-j),Object(r.f)(l,S,T,I,j,A,d?"#475166":"#98A0B3",d?"#12161F":"white"),Object(n.c)(l,x,S+I/2,Math.ceil(T+j/2),{color:d?"#F0F1F5":"#262E40",font:m,textAlign:n.a.textAlign.center,baseLine:n.a.baseLine.mid})}if(y){y=L?y+" 分时":y;var P=Object(n.d)(l,y,this),I=(L?P+v+b:P)+2*C,j=this.region.y,S=this.region.x+this.region.width-I,T=0;Object(r.f)(l,S,T,I,j,A,"rgba(48, 119, 236, 0.3)","#F1F7FF");var D=S+C;if(Object(n.c)(l,y,D,Math.ceil(T+j/2),{color:"#3077EC",font:m,textAlign:n.a.textAlign.left,baseLine:n.a.baseLine.mid}),L){var O=D+P+v,N=T+(j-M)/2;this.ctx.drawImage(s,O,N,b,M),this.timeBarTopRegion={x:S,y:T,width:I,height:j},this.timeBarData=i}else this.timeBarTopRegion=null,this.timeBarData=null}if(w){w=L?"查看 "+w+" 分时":w;var P=Object(n.d)(l,w,this),I=(L?P+v+b:P)+2*C,j=this.region.xAxis.height,S=t.x-I/2;S<0?S=0:S+I>this.region.x+this.region.width&&(S=this.region.x+this.region.width-I),"kline-portrait"===this.prop.layout&&this.prop.isSupportChip&&this.prop.isShowChip&&(S=Math.min(S,this.region.width-this.region.chipWidth-I));var T=this.region.xAxis.y;Object(r.f)(l,S,T,I,j,A,d?"#475166":L?"#3077EC":"#98A0B3",d?"#12161F":L?"#3077EC":"white");var D=S+C;if(Object(n.c)(l,w,D,Math.ceil(T+j/2),{color:d?"#F0F1F5":L?"white":"#262E40",font:m,textAlign:n.a.textAlign.left,baseLine:n.a.baseLine.mid}),L){var O=D+P+v,N=T+(j-M)/2;this.ctx.drawImage(a,O,N,b,M),this.timeBarBottomRegion={x:S,y:T,width:I,height:j},this.timeBarData=i}else this.timeBarBottomRegion=null,this.timeBarData=null}}},t.prototype.isTapTimeBarRegion=function(t){if(this.timeBarCanClick){var i=Object(o.a)(this.ctx.ctx.canvas,t);i.x=i.x*this.prop.devicePixelRatio,i.y=i.y*this.prop.devicePixelRatio;var e=this.timeBarTopRegion,r=this.timeBarBottomRegion,n=e&&i.x>=e.x&&i.x<=e.x+e.width&&i.y>=e.y&&i.y<=e.y+e.height,s=10*this.prop.devicePixelRatio,a=r&&{x:r.x-s,y:r.y-s,width:r.width+2*s,height:r.height+2*s},h=a&&i.x>=a.x&&i.x<=a.x+a.width&&i.y>=a.y&&i.y<=a.y+a.height;return n?(this.timeBarData.tapRegion="top",!0):!!h&&(this.timeBarData.tapRegion="bottom",!0)}},t}();i.a=h},function(t,i){t.exports="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDIxLjAuMCwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDYuMDAgQnVpbGQgMCkgIC0tPgo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IuWbvuWxgl8xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB4PSIwcHgiIHk9IjBweCIKCSB2aWV3Qm94PSIwIDAgMTAgMTgiIHN0eWxlPSJlbmFibGUtYmFja2dyb3VuZDpuZXcgMCAwIDEwIDE4OyIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+CjxzdHlsZSB0eXBlPSJ0ZXh0L2NzcyI+Cgkuc3Qwe2ZpbGw6I0ZBQjA2RDt9Cjwvc3R5bGU+Cjx0aXRsZT7ot6/lvoQ8L3RpdGxlPgo8ZyBpZD0iS+e6v+WbvuWinuWKoOS5sOWNlueCueagh+iusCI+Cgk8ZyBpZD0i55S75p2/5aSH5Lu9LTEyIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgtNzI0LjAwMDAwMCwgLTY4NC4wMDAwMDApIj4KCQk8ZyBpZD0i57yW57uELTnlpIfku70iIHRyYW5zZm9ybT0idHJhbnNsYXRlKDYxNy4wMDAwMDAsIDY3MC4wMDAwMDApIj4KCQkJPGcgaWQ9Iue8lue7hCIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMTMuMDAwMDAwLCAxMi4wMDAwMDApIj4KCQkJCTxnIGlkPSLot6/lvoQtMyIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoODYuNTE0NzE5LCAyLjUxNDcxOSkiPgoJCQkJCTxwYXRoIGlkPSLot6/lvoQiIGNsYXNzPSJzdDAiIGQ9Ik0xMy40LDkuMmwtNS43LDUuN2wwLDBjLTAuNCwwLjQtMC40LDEsMCwxLjRjMC40LDAuNCwxLDAuNCwxLjQsMGw3LjEtNy4xCgkJCQkJCWMwLjQtMC40LDAuNC0xLDAtMS40TDkuMiwwLjdsMCwwYy0wLjQtMC40LTEtMC40LTEuNCwwYy0wLjQsMC40LTAuNCwxLDAsMS40bDUuNyw1LjdDMTMuOCw4LjIsMTMuOCw4LjgsMTMuNCw5LjJ6Ii8+CgkJCQk8L2c+CgkJCTwvZz4KCQk8L2c+Cgk8L2c+CjwvZz4KPC9zdmc+Cg=="},function(t,i,e){"use strict";Object.defineProperty(i,"__esModule",{value:!0});var r=e(99),o=e.n(r),n=e(196),s=e.n(n);i.default={kline:o.a,mins:s.a}},function(t,i,e){var r=e(56)(e(100),e(195),null,null);i.__esModule=!0,i.default=r.exports},function(t,i,e){"use strict";Object.defineProperty(i,"__esModule",{value:!0});var r=e(101),o=e.n(r),n=e(46),s=e.n(n),a=e(73),h=e.n(a),c=e(75),p=e.n(c),u=e(144),l=e.n(u),d=e(76),f=e(34),g=e(55),x=e(33),y=window.navigator.appVersion.match(/android/gi)?8:4,w=function(t){return["oneMonth","threeMonth","halfYear","oneYear","threeYear","fiveYear","allYear"].includes(t)},m=function(t,i,e,r,o,n,s){var a=i.substr(0,7),h=e.substr(0,7),c=i.substr(0,8),p=e.substr(0,8),u=o.length&&o[o.length-1].x,l=o.length&&o[o.length-1].text,d=!u||r>u+150,f=parseInt(i.substr(5,2),10),g=parseInt(i.substr(0,4),10),x=(parseInt(e.substr(5,2),10),parseInt(e.substr(0,4),10));if("day"===t){if(e&&a!==h&&d)return!0}else if("week"===t){if(e&&(f+1)%4==0&&a!==h&&d)return!0}else if("month"===t){if(e&&g%2==0&&i.substr(0,4)!==e.substr(0,4))return!0}else if("m1"===t){if(e&&d&&i.substr(8,3)!==e.substr(8,3))return!0}else if(/^m(5|15|30|60|120)$/.test(t)){if(e&&d){if("2"!==s&&n<({m5:100,m15:50,m30:30,m60:10,m120:10})[t]&&"13"===i.substr(8,2)&&"13"!==e.substr(8,2))return!l||"11:30/13:00"!==l;if(c!==p)return!0}}else if("season"===t){if(e&&(f+1)%4==0&&a!==h&&d)return!0}else if("year"===t){if(e&&d&&i.substr(0,4)!==e.substr(0,4))return!0}else if(w(t)&&("oneMonth"===t&&e&&d||("threeMonth"===t||"halfYear"===t||"oneYear"===t)&&e&&a!==h&&d||("threeYear"===t||"fiveYear"===t||"allYear"===t)&&e&&x!==g&&d))return!0;return!1},v=function(t,i){var e={};return["m5","m15","m30","m60","m120"].forEach(function(i){var r=("0"===t[4]?t.substr(5,1):t.substr(4,2))+"-"+t.substr(6,2);e[i]="13"===t.substr(8,2)?"11:30/13:00":r}),l()({day:t.substr(0,7),week:t.substr(0,7),month:t.substr(0,4),m1:"m1"===i?t.substr(8,2)+":"+t.substr(10,2):""},e,{season:t.substr(0,7),year:t.substr(0,4),oneMonth:t.substr(0,10),threeMonth:t.substr(0,7),halfYear:t.substr(0,7),oneYear:t.substr(0,7),threeYear:t.substr(0,7),fiveYear:t.substr(0,7),allYear:t.substr(0,7)})[i]};i.default={props:{options:{type:Object,default:{}},width:{type:Number},height:{type:Number}},computed:{elem:function(){return this.$refs["kline-canvas"]}},watch:{options:function(t,i){var e=this,r=this.event&&this.event.isCrossLine;if(t.options.type!==i.options.type)r&&this.event.hideCrossLine(),this.init();else if(t.options.fq!==i.options.fq)r&&this.event.hideCrossLine(),this.init();else if(t.options.isShowAreaSelect!==i.options.isShowAreaSelect)r&&this.event.hideCrossLine(),this.view.props.isShowAreaSelect=t.options.isShowAreaSelect,this.view.initAreaSelect(),this.view.draw(),this.view.props.isShowAreaSelect&&this.$emit("areaSelectChange",this.view.getAreaSelectData());else if(t.options.isShowChip!==i.options.isShowChip)this.chipSwitchChange(t.options.isShowChip);else{var o=!1;t.options.mainIndicator!==i.options.mainIndicator&&(o=!0,r&&this.event.hideCrossLine(),this.model.switchIndicator(0,t.options.mainIndicator),this.model.repaint(function(i,r){e.view.props.mainIndicator=t.options.mainIndicator,e.view.draw(i,r)}));for(var n=t.options.setting.indicatorCount,s=1;s<=n;s++){var a=["curr","second","third","fourth"][s-1]+"Indicator";t.options[a]!==i.options[a]&&(o=!0,r&&this.event.hideCrossLine(),this.model.switchIndicator(s,t.options[a]),this.view.switchIndicator(s,t.options[a]))}if(r||o)return;this.model.updateRepaint(t.data,function(t,i){e.view.draw(t,i)})}}},data:function(){return{view:null,model:null,event:null,ratio:window.devicePixelRatio,type:""}},mounted:function(){this.init()},methods:{onAddedChange:function(t){var i=this;this.model.ZXRepaint(t,function(t,e){i.view.draw(t,e)})},updateTradeData:function(t){var i=this;this.model.tradeRepaint(t,function(t,e){i.view.draw(t,e)})},toggleMagicNine:function(t){var i=this;this.model.magicNineRepaint(t,function(t,e){i.view.draw(t,e)})},updateTradeSecret:function(t){var i=this;this.model.tradeSecretRepaint(t,function(t,e){i.view.draw(t,e)})},getSiblingData:function(t){return this.model.getSiblingData(t)},getRightData:function(){return this.model.getRightData()},switchCrossLine:function(t){var i=this;if(this.event&&this.event.isCrossLine){var e=this.view.lastScene,r=e.eventPoint,o=e.count;0===r.index&&-1===t||r.index===o-1&&1===t?this.model.fetchForSwipe(t,function(t,e){i.view.draw(t,e),i._switchCrossLine(0)}):this._switchCrossLine(t)}else this._switchCrossLine("right")},_switchCrossLine:function(t){var i=this;this.view.showCrossLine(t,function(t){i.event.isCrossLine=!0,i.event.isCrossLineTouchEnd=!0,i.$emit("onTouchMove",t)})},toggleKlineWithHistoryMins:function(t){this.event&&this.event.toggleKlineWithHistoryMins(t)},dispatchEvent:function(t){var i=(this.options||{}).options||{},e=i.disableInteract,r=i.type;e||(this.event&&this.type===r||(this.type=r,this.initEvent()),this.event.handleEvent(t))},cancelEvent:function(){this.event&&this.event.cancleAll()},init:function(){var t=this.options.options;t.isSupportChip&&t.isShowChip&&(t.count="kline-landscape"===t.layout?71:45);var i=t.type,e=w(i),r=this;try{var n=getComputedStyle(document.documentElement),a=n.getPropertyValue("--color-rise"),c=n.getPropertyValue("--color-drop");a&&c&&(f.a.kline.plain.rise=a,f.a.kline.plain.drop=c);for(var u=["plain","dark"],g=0;g<u.length;g++){var y=u[g];!function(i){var e={plain:"customColor",dark:"customDarkColor"}[i];t[e]&&p()(t[e]).forEach(function(r){"object"===h()(t[e][r])?s()(f.a.kline[i][r],t[e][r]):f.a.kline[i][r]=t[e][r]})}(y)}}catch(t){}this.view=new d.a(this.elem,l()({},t,{fixedWidth:e,getXAxis:function(e,r,o,n,s,a){var h=o.time||"";if(m(i,h,n[e-1]?n[e-1].time:"",r,s,a,t.market))return{text:v(h,i),x:r}}}));try{var b=this.view.layout,M=b.height,C=b.indicatorHeight,A=t.setting.indicatorCount,L=1-C/M,P=C/M/A;this.indicatorPercent=[];for(var I=0;I<A;I++)this.indicatorPercent.push(L+P*I)}catch(t){}var j=new x.b(l()({request:function(t){return new o.a(function(i,e){if(t){var o=setTimeout(function(){e()},5e3);if(t.indexOf("-")>=0){var n=new Date(t);n.setDate(n.getDate()-1);var s=n.getFullYear(),a=n.getMonth()+1<10?"0"+(n.getMonth()+1):n.getMonth()+1,h=10>n.getDate()?"0"+n.getDate():n.getDate();r.$emit("getMore",""+s+a+h,function(t){i(t),clearTimeout(o)})}else r.$emit("getMore",t,function(t){i(t),clearTimeout(o)})}else i(r.options.data)})},query:function(){return new o.a(function(t){r.$emit("queryPriceRemind",function(i){t(i)})})}},t,{defaultCount:t.count||(r.view.props.isShowChip?45:60),queryCount:t.queryCount||320}));j.fetchForInit(function(i,e){t.isSupportChip&&r.updateChip(i[i.length-1]),r.view.initAreaSelect(i),r.view.draw(i,e),t.enableDrawBoard&&r.initBoard(),r.view.props.isShowAreaSelect&&r.$emit("areaSelectChange",r.view.getAreaSelectData())}),this.model=j},initEvent:function(){var t=this,i=(this.options||{}).options,e=w((i||{}).type);this.event=new g.a(this.elem,this.options.options,{onTouchStart:function(i){t.view.props.isShowAreaSelect&&t.view.isTapAreaSelect(i)},onTap:function(e){var r=e.changedTouches[0];if(t.view.props.isShowAreaSelect&&t.view.isTapAreaSelect(r))return t.$emit("onAreaSwitch",{isShowAreaSelect:!t.view.props.isShowAreaSelect,source:"kline"}),!0;if(i.isSupportChip&&t.view.isTapChipSwitch(r))return t.$emit("onChipSwitch",{isShowChip:!t.view.props.isShowChip}),!0;if(i.isSupportChip&&t.view.isTapChipType(r))return t.view.draw(),!0;if("kline-portrait"===i.layout){var o=t.view.isTapButtonRegion(r);if(!isNaN(o))return e.preventDefault&&e.preventDefault(),t.$emit("onPopup",o),!0;if("foldArrow"===o)return e.preventDefault&&e.preventDefault(),t.view.draw(null,null,null,null,!0),!0;var n=t.view.isTapIndicatorRegion(r);if(n>0){var s=t.view.getNextIndicator(n);return t.model.switchIndicator(n,s),t.view.switchIndicator(n,s),t.$emit("onChange",n,s),!0}if(t.view.isTapTradeEntranceBarRegion(r))return t.$emit("onBarTap","tradeEntrance"),!0}},onCrossLineTap:function(i){var e=i.changedTouches[0];return t.view.isTapZxBarRegion(e)?(t.$emit("onAreaSwitch",{isShowAreaSelect:!0,source:"cross"}),!0):t.view.isTapTradeBarRegion(e)?(t.$emit("onBarTap","trade",t.view.tradeBarData),!0):t.view.crossLine.isTapTimeBarRegion(e)?(t.event.isKlineWithHistoryMins||(t.$emit("onBarTap","time",t.view.crossLine.timeBarData),t.toggleKlineWithHistoryMins(!0)),!0):void 0},onDoubleTap:function(i){t.$emit("onDoubleTap",i)},onSwipeX:function(e){if(t.model.needBounce){if(!e)return void t.model.fetchBoundary(function(e,r){i.isSupportChip&&e&&t.updateChip(e[e.length-1]),t.view.props.isShowAreaSelect&&(t.view.areaSelect.update("swipe",{list:e}),t.$emit("areaSelectChange",t.view.getAreaSelectData())),t.view.draw(e,r),t.changeBoardOptions()});if(t.model.reachBoundary)return t.model.reachBoundary=!1,void(t.event.isInertia=!1)}if(e){var r=Math.ceil(t.view.props.count/60)*(t.event.isInertia?3:1);t.model&&t.model.fetchForSwipe(e<0?-r:r,function(e,r){i.isSupportChip&&t.updateChip(e[e.length-1]),t.view.props.isShowAreaSelect&&(t.view.areaSelect.update("swipe",{list:e}),t.$emit("areaSelectChange",t.view.getAreaSelectData())),t.view.draw(e,r),t.changeBoardOptions()})}else t.view.draw();t.$emit("onSwipeX",e)},onSwipeY:function(){x.e&&g.c&&(Object(g.b)(!1),t.view.draw())},onBeforeTouchMove:function(i){return!(!t.view.props.isShowAreaSelect||!t.view.hasAreaSelectActive())&&(t.view.touchMoveAreaSelect(i),t.$emit("areaSelectChange",t.view.getAreaSelectData()),!0)},onTouchMove:function(e){if(!t.view.props.isShowAreaSelect){if(i.isSupportChip){var r=t.view.crossLine.changeCoords(e).index,o=t.view.getValueY(e);t.updateChip(t.view.data.items[r],o)}t.view.showCrossLine(e,function(i,e){t.$emit("onTouchMove",i),e&&t.$emit("extraInfoSelected")})}},onTouchCancle:function(){i.isSupportChip&&t.view.data.items.length&&t.updateChip(t.view.data.items[t.view.data.items.length-1]),t.view.draw(),t.$emit("onTouchCancle")},onTouchEnd:function(){t.view.props.isShowAreaSelect&&(t.view.cancelAreaActive(),t.view.draw()),t.$emit("onTouchEnd")},onPinch:function(r,o){if(!e){var n=t.view.getRatio(r),s=o?-y:y;t.model.fetchForPinch(n,s,function(e){var r=e.list,o=e.extraData,n=e.type,a=void 0===n?"kline":n;if(t.view.changeLineType(a),"line"===a)return void t.view.draw();t.view.changeCount(s),i.isSupportChip&&t.updateChip(r[r.length-1]),t.view.props.isShowAreaSelect&&(t.view.areaSelect.update("scale",{list:r,props:t.view.props}),t.$emit("areaSelectChange",t.view.getAreaSelectData())),t.view.draw(r,o),t.changeBoardOptions()}),t.$emit("onPinch",o)}},onPinchEnd:function(){t.$emit("onPinchEnd",{type:t.view.props.fixedWidth?"line":"candle"})}})},updateChip:function(t,i){if(t){var e=this.view.layout.getChart(),r={width:(this.view.layout.chipRegion.width-10)/window.devicePixelRatio+"px",height:(this.view.layout.chipRegion.height-e.height-e.y)/window.devicePixelRatio+"px"},o=this.model.getChipList(t,i);this.view.updateChip(o),this.$emit("ChipInfoChange",{layout:r,chipData:l()({},o,{chipTime:t.time})})}},chipSwitchChange:function(t){this.model.count+=t?-15:15,t?this.model.index+15<this.model.list.length&&(this.model.index+=15):this.model.index=Math.max(this.model.index-15,0),this.model.getCurrTrendline(),this.view.updatePropByChip(t,this.model.list.slice(this.model.index,this.model.index+this.model.count)),this.initBoard(!0)},initBoard:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(this.options.options.enableDrawBoard&&this.view.layout&&this.view.layout.chart){var i=this.view.layout.chart,e=i.width,r=i.height,o=i.x,n=i.y;this.boardStyle={width:this.width,height:this.height,chart:{width:e/this.ratio,height:r/this.ratio,left:o/this.ratio,top:n/this.ratio}},this.changeBoardOptions(!0),this.$emit("drawInit",{layout:this.boardStyle,options:this.boardOptions,reload:!t})}},changeBoardOptions:function(t){if((this.options.options.enableDrawBoard||this.view.data)&&this.view.props){var i=this.view.data,e=i.maxMin,r=i.items,o=this.view.props,n=o.count,s=o.itemWidth,a=o.barWidth,h=o.spaceWidth;this.boardOptions={chartType:"kline",type:this.options.options.type,xAxis:{count:n,itemWidth:s/this.ratio,barWidth:a/this.ratio,spaceWidth:h/this.ratio,viewItems:r.map(function(t){return t.time}),tradeDate:this.model.list.map(function(t){return t.time})},yAxis:{maxMin:e.kline}},t||this.$emit("optionChange",{layout:this.boardStyle,options:this.boardOptions})}}}}},function(t,i,e){t.exports={default:e(102),__esModule:!0}},function(t,i,e){e(57),e(58),e(67),e(112),e(124),e(125),t.exports=e(6).Promise},function(t,i,e){var r=e(37),o=e(38);t.exports=function(t){return function(i,e){var n,s,a=String(o(i)),h=r(e),c=a.length;return h<0||h>=c?t?"":void 0:(n=a.charCodeAt(h))<55296||n>56319||h+1===c||(s=a.charCodeAt(h+1))<56320||s>57343?t?a.charAt(h):n:t?a.slice(h,h+2):s-56320+(n-55296<<10)+65536}}},function(t,i,e){"use strict";var r=e(62),o=e(27),n=e(29),s={};e(12)(s,e(5)("iterator"),function(){return this}),t.exports=function(t,i,e){t.prototype=r(s,{next:o(1,e)}),n(t,i+" Iterator")}},function(t,i,e){var r=e(13),o=e(10),n=e(22);t.exports=e(15)?Object.defineProperties:function(t,i){o(t);for(var e,s=n(i),a=s.length,h=0;a>h;)r.f(t,e=s[h++],i[e]);return t}},function(t,i,e){var r=e(18),o=e(65),n=e(107);t.exports=function(t){return function(i,e,s){var a,h=r(i),c=o(h.length),p=n(s,c);if(t&&e!=e){for(;c>p;)if((a=h[p++])!=a)return!0}else for(;c>p;p++)if((t||p in h)&&h[p]===e)return t||p||0;return!t&&-1}}},function(t,i,e){var r=e(37),o=Math.max,n=Math.min;t.exports=function(t,i){return(t=r(t))<0?o(t+i,0):n(t,i)}},function(t,i,e){var r=e(16),o=e(44),n=e(41)("IE_PROTO"),s=Object.prototype;t.exports=Object.getPrototypeOf||function(t){return r(t=o(t),n)?t[n]:"function"==typeof t.constructor&&t instanceof t.constructor?t.constructor.prototype:t instanceof Object?s:null}},function(t,i,e){"use strict";var r=e(110),o=e(111),n=e(21),s=e(18);t.exports=e(59)(Array,"Array",function(t,i){this._t=s(t),this._i=0,this._k=i},function(){var t=this._t,i=this._k,e=this._i++;return!t||e>=t.length?(this._t=void 0,o(1)):"keys"==i?o(0,e):"values"==i?o(0,t[e]):o(0,[e,t[e]])},"values"),n.Arguments=n.Array,r("keys"),r("values"),r("entries")},function(t,i){t.exports=function(){}},function(t,i){t.exports=function(t,i){return{value:i,done:!!t}}},function(t,i,e){"use strict";var r,o,n,s,a=e(20),h=e(4),c=e(25),p=e(68),u=e(11),l=e(14),d=e(26),f=e(113),g=e(114),x=e(69),y=e(70).set,w=e(119)(),m=e(45),v=e(71),b=e(120),M=e(72),C=h.TypeError,A=h.process,L=A&&A.versions,P=L&&L.v8||"",I=h.Promise,j="process"==p(A),S=function(){},T=o=m.f,D=!!function(){try{var t=I.resolve(1),i=(t.constructor={})[e(5)("species")]=function(t){t(S,S)};return(j||"function"==typeof PromiseRejectionEvent)&&t.then(S) instanceof i&&0!==P.indexOf("6.6")&&-1===b.indexOf("Chrome/66")}catch(t){}}(),O=function(t){var i;return!(!l(t)||"function"!=typeof(i=t.then))&&i},N=function(t,i){if(!t._n){t._n=!0;var e=t._c;w(function(){for(var r=t._v,o=1==t._s,n=0;e.length>n;)!function(i){var e,n,s,a=o?i.ok:i.fail,h=i.resolve,c=i.reject,p=i.domain;try{a?(o||(2==t._h&&k(t),t._h=1),!0===a?e=r:(p&&p.enter(),e=a(r),p&&(p.exit(),s=!0)),e===i.promise?c(C("Promise-chain cycle")):(n=O(e))?n.call(e,h,c):h(e)):c(r)}catch(t){p&&!s&&p.exit(),c(t)}}(e[n++]);t._c=[],t._n=!1,i&&!t._h&&E(t)})}},E=function(t){y.call(h,function(){var i,e,r,o=t._v,n=R(t);if(n&&(i=v(function(){j?A.emit("unhandledRejection",o,t):(e=h.onunhandledrejection)?e({promise:t,reason:o}):(r=h.console)&&r.error&&r.error("Unhandled promise rejection",o)}),t._h=j||R(t)?2:1),t._a=void 0,n&&i.e)throw i.v})},R=function(t){return 1!==t._h&&0===(t._a||t._c).length},k=function(t){y.call(h,function(){var i;j?A.emit("rejectionHandled",t):(i=h.onrejectionhandled)&&i({promise:t,reason:t._v})})},_=function(t){var i=this;i._d||(i._d=!0,(i=i._w||i)._v=t,i._s=2,i._a||(i._a=i._c.slice()),N(i,!0))},W=function(t){var i,e=this;if(!e._d){e._d=!0,e=e._w||e;try{if(e===t)throw C("Promise can't be resolved itself");(i=O(t))?w(function(){var r={_w:e,_d:!1};try{i.call(t,c(W,r,1),c(_,r,1))}catch(t){_.call(r,t)}}):(e._v=t,e._s=1,N(e,!1))}catch(t){_.call({_w:e,_d:!1},t)}}};D||(I=function(t){f(this,I,"Promise","_h"),d(t),r.call(this);try{t(c(W,this,1),c(_,this,1))}catch(t){_.call(this,t)}},(r=function(t){this._c=[],this._a=void 0,this._s=0,this._d=!1,this._v=void 0,this._h=0,this._n=!1}).prototype=e(121)(I.prototype,{then:function(t,i){var e=T(x(this,I));return e.ok="function"!=typeof t||t,e.fail="function"==typeof i&&i,e.domain=j?A.domain:void 0,this._c.push(e),this._a&&this._a.push(e),this._s&&N(this,!1),e.promise},catch:function(t){return this.then(void 0,t)}}),n=function(){var t=new r;this.promise=t,this.resolve=c(W,t,1),this.reject=c(_,t,1)},m.f=T=function(t){return t===I||t===s?new n(t):o(t)}),u(u.G+u.W+!D*u.F,{Promise:I}),e(29)(I,"Promise"),e(122)("Promise"),s=e(6).Promise,u(u.S+!D*u.F,"Promise",{reject:function(t){var i=T(this);return(0,i.reject)(t),i.promise}}),u(u.S+u.F*(a||!D),"Promise",{resolve:function(t){return M(a&&this===s?I:this,t)}}),u(u.S+!(D&&e(123)(function(t){I.all(t).catch(S)}))*u.F,"Promise",{all:function(t){var i=this,e=T(i),r=e.resolve,o=e.reject,n=v(function(){var e=[],n=0,s=1;g(t,!1,function(t){var a=n++,h=!1;e.push(void 0),s++,i.resolve(t).then(function(t){h||(h=!0,e[a]=t,--s||r(e))},o)}),--s||r(e)});return n.e&&o(n.v),e.promise},race:function(t){var i=this,e=T(i),r=e.reject,o=v(function(){g(t,!1,function(t){i.resolve(t).then(e.resolve,r)})});return o.e&&r(o.v),e.promise}})},function(t,i){t.exports=function(t,i,e,r){if(!(t instanceof i)||void 0!==r&&r in t)throw TypeError(e+": incorrect invocation!");return t}},function(t,i,e){var r=e(25),o=e(115),n=e(116),s=e(10),a=e(65),h=e(117),c={},p={},i=t.exports=function(t,i,e,u,l){var d,f,g,x,y=l?function(){return t}:h(t),w=r(e,u,i?2:1),m=0;if("function"!=typeof y)throw TypeError(t+" is not iterable!");if(n(y)){for(d=a(t.length);d>m;m++)if((x=i?w(s(f=t[m])[0],f[1]):w(t[m]))===c||x===p)return x}else for(g=y.call(t);!(f=g.next()).done;)if((x=o(g,w,f.value,i))===c||x===p)return x};i.BREAK=c,i.RETURN=p},function(t,i,e){var r=e(10);t.exports=function(t,i,e,o){try{return o?i(r(e)[0],e[1]):i(e)}catch(i){var n=t.return;throw void 0!==n&&r(n.call(t)),i}}},function(t,i,e){var r=e(21),o=e(5)("iterator"),n=Array.prototype;t.exports=function(t){return void 0!==t&&(r.Array===t||n[o]===t)}},function(t,i,e){var r=e(68),o=e(5)("iterator"),n=e(21);t.exports=e(6).getIteratorMethod=function(t){if(void 0!=t)return t[o]||t["@@iterator"]||n[r(t)]}},function(t,i){t.exports=function(t,i,e){var r=void 0===e;switch(i.length){case 0:return r?t():t.call(e);case 1:return r?t(i[0]):t.call(e,i[0]);case 2:return r?t(i[0],i[1]):t.call(e,i[0],i[1]);case 3:return r?t(i[0],i[1],i[2]):t.call(e,i[0],i[1],i[2]);case 4:return r?t(i[0],i[1],i[2],i[3]):t.call(e,i[0],i[1],i[2],i[3])}return t.apply(e,i)}},function(t,i,e){var r=e(4),o=e(70).set,n=r.MutationObserver||r.WebKitMutationObserver,s=r.process,a=r.Promise,h="process"==e(23)(s);t.exports=function(){var t,i,e,c=function(){var r,o;for(h&&(r=s.domain)&&r.exit();t;){o=t.fn,t=t.next;try{o()}catch(r){throw t?e():i=void 0,r}}i=void 0,r&&r.enter()};if(h)e=function(){s.nextTick(c)};else if(!n||r.navigator&&r.navigator.standalone){if(a&&a.resolve){var p=a.resolve(void 0);e=function(){p.then(c)}}else e=function(){o.call(r,c)}}else{var u=!0,l=document.createTextNode("");new n(c).observe(l,{characterData:!0}),e=function(){l.data=u=!u}}return function(r){var o={fn:r,next:void 0};i&&(i.next=o),t||(t=o,e()),i=o}}},function(t,i,e){var r=e(4).navigator;t.exports=r&&r.userAgent||""},function(t,i,e){var r=e(12);t.exports=function(t,i,e){for(var o in i)e&&t[o]?t[o]=i[o]:r(t,o,i[o]);return t}},function(t,i,e){"use strict";var r=e(4),o=e(6),n=e(13),s=e(15),a=e(5)("species");t.exports=function(t){var i="function"==typeof o[t]?o[t]:r[t];s&&i&&!i[a]&&n.f(i,a,{configurable:!0,get:function(){return this}})}},function(t,i,e){var r=e(5)("iterator"),o=!1;try{var n=[7][r]();n.return=function(){o=!0},Array.from(n,function(){throw 2})}catch(t){}t.exports=function(t,i){if(!i&&!o)return!1;var e=!1;try{var n=[7],s=n[r]();s.next=function(){return{done:e=!0}},n[r]=function(){return s},t(n)}catch(t){}return e}},function(t,i,e){"use strict";var r=e(11),o=e(6),n=e(4),s=e(69),a=e(72);r(r.P+r.R,"Promise",{finally:function(t){var i=s(this,o.Promise||n.Promise),e="function"==typeof t;return this.then(e?function(e){return a(i,t()).then(function(){return e})}:t,e?function(e){return a(i,t()).then(function(){throw e})}:t)}})},function(t,i,e){"use strict";var r=e(11),o=e(45),n=e(71);r(r.S,"Promise",{try:function(t){var i=o.f(this),e=n(t);return(e.e?i.reject:i.resolve)(e.v),i.promise}})},function(t,i,e){e(127),t.exports=e(6).Object.assign},function(t,i,e){var r=e(11);r(r.S+r.F,"Object",{assign:e(128)})},function(t,i,e){"use strict";var r=e(22),o=e(47),n=e(30),s=e(44),a=e(64),h=Object.assign;t.exports=!h||e(17)(function(){var t={},i={},e=Symbol(),r="abcdefghijklmnopqrst";return t[e]=7,r.split("").forEach(function(t){i[t]=t}),7!=h({},t)[e]||Object.keys(h({},i)).join("")!=r})?function(t,i){for(var e=s(t),h=arguments.length,c=1,p=o.f,u=n.f;h>c;)for(var l,d=a(arguments[c++]),f=p?r(d).concat(p(d)):r(d),g=f.length,x=0;g>x;)u.call(d,l=f[x++])&&(e[l]=d[l]);return e}:h},function(t,i,e){t.exports={default:e(130),__esModule:!0}},function(t,i,e){e(58),e(67),t.exports=e(48).f("iterator")},function(t,i,e){t.exports={default:e(132),__esModule:!0}},function(t,i,e){e(133),e(57),e(139),e(140),t.exports=e(6).Symbol},function(t,i,e){"use strict";var r=e(4),o=e(16),n=e(15),s=e(11),a=e(61),h=e(134).KEY,c=e(17),p=e(42),u=e(29),l=e(28),d=e(5),f=e(48),g=e(49),x=e(135),y=e(136),w=e(10),m=e(14),v=e(18),b=e(40),M=e(27),C=e(62),A=e(137),L=e(138),P=e(13),I=e(22),j=L.f,S=P.f,T=A.f,D=r.Symbol,O=r.JSON,N=O&&O.stringify,E=d("_hidden"),R=d("toPrimitive"),k={}.propertyIsEnumerable,_=p("symbol-registry"),W=p("symbols"),B=p("op-symbols"),z=Object.prototype,F="function"==typeof D,Z=r.QObject,Y=!Z||!Z.prototype||!Z.prototype.findChild,Q=n&&c(function(){return 7!=C(S({},"a",{get:function(){return S(this,"a",{value:7}).a}})).a})?function(t,i,e){var r=j(z,i);r&&delete z[i],S(t,i,e),r&&t!==z&&S(z,i,r)}:S,H=function(t){var i=W[t]=C(D.prototype);return i._k=t,i},G=F&&"symbol"==typeof D.iterator?function(t){return"symbol"==typeof t}:function(t){return t instanceof D},U=function(t,i,e){return t===z&&U(B,i,e),w(t),i=b(i,!0),w(e),o(W,i)?(e.enumerable?(o(t,E)&&t[E][i]&&(t[E][i]=!1),e=C(e,{enumerable:M(0,!1)})):(o(t,E)||S(t,E,M(1,{})),t[E][i]=!0),Q(t,i,e)):S(t,i,e)},J=function(t,i){w(t);for(var e,r=x(i=v(i)),o=0,n=r.length;n>o;)U(t,e=r[o++],i[e]);return t},V=function(t){var i=k.call(this,t=b(t,!0));return!(this===z&&o(W,t)&&!o(B,t))&&(!(i||!o(this,t)||!o(W,t)||o(this,E)&&this[E][t])||i)},X=function(t,i){if(t=v(t),i=b(i,!0),t!==z||!o(W,i)||o(B,i)){var e=j(t,i);return!e||!o(W,i)||o(t,E)&&t[E][i]||(e.enumerable=!0),e}},K=function(t){for(var i,e=T(v(t)),r=[],n=0;e.length>n;)o(W,i=e[n++])||i==E||i==h||r.push(i);return r},q=function(t){for(var i,e=t===z,r=T(e?B:v(t)),n=[],s=0;r.length>s;)o(W,i=r[s++])&&(!e||o(z,i))&&n.push(W[i]);return n};F||(a((D=function(){if(this instanceof D)throw TypeError("Symbol is not a constructor!");var t=l(arguments.length>0?arguments[0]:void 0),i=function(e){this===z&&i.call(B,e),o(this,E)&&o(this[E],t)&&(this[E][t]=!1),Q(this,t,M(1,e))};return n&&Y&&Q(z,t,{configurable:!0,set:i}),H(t)}).prototype,"toString",function(){return this._k}),L.f=X,P.f=U,e(74).f=A.f=K,e(30).f=V,e(47).f=q,n&&!e(20)&&a(z,"propertyIsEnumerable",V,!0),f.f=function(t){return H(d(t))}),s(s.G+s.W+!F*s.F,{Symbol:D});for(var $="hasInstance,isConcatSpreadable,iterator,match,replace,search,species,split,toPrimitive,toStringTag,unscopables".split(","),tt=0;$.length>tt;)d($[tt++]);for(var ti=I(d.store),te=0;ti.length>te;)g(ti[te++]);s(s.S+!F*s.F,"Symbol",{for:function(t){return o(_,t+="")?_[t]:_[t]=D(t)},keyFor:function(t){if(!G(t))throw TypeError(t+" is not a symbol!");for(var i in _)if(_[i]===t)return i},useSetter:function(){Y=!0},useSimple:function(){Y=!1}}),s(s.S+!F*s.F,"Object",{create:function(t,i){return void 0===i?C(t):J(C(t),i)},defineProperty:U,defineProperties:J,getOwnPropertyDescriptor:X,getOwnPropertyNames:K,getOwnPropertySymbols:q}),O&&s(s.S+s.F*(!F||c(function(){var t=D();return"[null]"!=N([t])||"{}"!=N({a:t})||"{}"!=N(Object(t))})),"JSON",{stringify:function(t){for(var i,e,r=[t],o=1;arguments.length>o;)r.push(arguments[o++]);if(e=i=r[1],(m(i)||void 0!==t)&&!G(t))return y(i)||(i=function(t,i){if("function"==typeof e&&(i=e.call(this,t,i)),!G(i))return i}),r[1]=i,N.apply(O,r)}}),D.prototype[R]||e(12)(D.prototype,R,D.prototype.valueOf),u(D,"Symbol"),u(Math,"Math",!0),u(r.JSON,"JSON",!0)},function(t,i,e){var r=e(28)("meta"),o=e(14),n=e(16),s=e(13).f,a=0,h=Object.isExtensible||function(){return!0},c=!e(17)(function(){return h(Object.preventExtensions({}))}),p=function(t){s(t,r,{value:{i:"O"+ ++a,w:{}}})},u=t.exports={KEY:r,NEED:!1,fastKey:function(t,i){if(!o(t))return"symbol"==typeof t?t:("string"==typeof t?"S":"P")+t;if(!n(t,r)){if(!h(t))return"F";if(!i)return"E";p(t)}return t[r].i},getWeak:function(t,i){if(!n(t,r)){if(!h(t))return!0;if(!i)return!1;p(t)}return t[r].w},onFreeze:function(t){return c&&u.NEED&&h(t)&&!n(t,r)&&p(t),t}}},function(t,i,e){var r=e(22),o=e(47),n=e(30);t.exports=function(t){var i=r(t),e=o.f;if(e)for(var s,a=e(t),h=n.f,c=0;a.length>c;)h.call(t,s=a[c++])&&i.push(s);return i}},function(t,i,e){var r=e(23);t.exports=Array.isArray||function(t){return"Array"==r(t)}},function(t,i,e){var r=e(18),o=e(74).f,n={}.toString,s="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],a=function(t){try{return o(t)}catch(t){return s.slice()}};t.exports.f=function(t){return s&&"[object Window]"==n.call(t)?a(t):o(r(t))}},function(t,i,e){var r=e(30),o=e(27),n=e(18),s=e(40),a=e(16),h=e(60),c=Object.getOwnPropertyDescriptor;i.f=e(15)?c:function(t,i){if(t=n(t),i=s(i,!0),h)try{return c(t,i)}catch(t){}if(a(t,i))return o(!r.f.call(t,i),t[i])}},function(t,i,e){e(49)("asyncIterator")},function(t,i,e){e(49)("observable")},function(t,i,e){e(142),t.exports=e(6).Object.keys},function(t,i,e){var r=e(44),o=e(22);e(143)("keys",function(){return function(t){return o(r(t))}})},function(t,i,e){var r=e(11),o=e(6),n=e(17);t.exports=function(t,i){var e=(o.Object||{})[t]||Object[t],s={};s[t]=i(e),r(r.S+r.F*n(function(){e(1)}),"Object",s)}},function(t,i,e){"use strict";i.__esModule=!0;var r,o=(r=e(46))&&r.__esModule?r:{default:r};i.default=o.default||function(t){for(var i=1;i<arguments.length;i++){var e=arguments[i];for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])}return t}},function(t,i,e){"use strict";var r,o=e(31),n=e(0),s=this&&this.__extends||(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var e in i)i.hasOwnProperty(e)&&(t[e]=i[e])},function(t,i){function e(){this.constructor=t}r(t,i),t.prototype=null===i?Object.create(i):(e.prototype=i.prototype,new e)}),a=function(t){function i(i,e){var r=t.call(this,i,e)||this;return r.calculate(),r}return s(i,t),i.prototype.calculate=function(){var t=this.props.padding.top,i=this.props.padding.left,e=this.props.yAxis.width,r=this.props.xAxis.height;this.chartWidth=this.width-this.props.padding.left-this.props.padding.right-2*e,this.chart={x:i+e,y:t,width:this.chartWidth,height:this.chartHeight,yAxisLeft:{x:i,y:t,width:e-10,height:this.chartHeight,props:{textAlign:n.a.textAlign.right}},yAxisRight:{x:i+e+10+this.chartWidth,y:t,width:e,height:this.chartHeight,props:{textAlign:n.a.textAlign.left}}},this.auctionWidth=this.props.showAuction?Math.min(75*this.props.devicePixelRatio,.25*this.chartWidth):0,this.mainChart={x:i+e+this.auctionWidth,y:t,width:this.chartWidth-this.auctionWidth,height:this.chartHeight},this.auctionChart={x:i+e,y:t,width:this.auctionWidth,height:this.chartHeight},this.xAxis={x:i+e+this.auctionWidth,y:t+this.chartHeight,width:this.chartWidth-this.auctionWidth,height:r};var o=14*this.props.devicePixelRatio;this.mainIndicator={x:i+e+this.auctionWidth,y:this.height-this.indicatorHeight+o,width:this.chartWidth-this.auctionWidth,height:this.indicatorHeight-o,yAxis:{x:i,y:this.height-this.indicatorHeight+o,width:e-10,height:this.indicatorHeight-o,props:{textAlign:n.a.textAlign.right,baseLine:[n.a.baseLine.top,n.a.baseLine.btm]}},bar:{x:i+e+this.auctionWidth,y:this.height-this.indicatorHeight,width:this.chartWidth-this.auctionWidth,height:o}},this.auctionIndicator={x:i+e,y:this.height-this.indicatorHeight+o,width:this.auctionWidth,height:this.indicatorHeight-o}},i}(o.a);i.a=a},function(t,i,e){"use strict";var r,o=e(31),n=e(0),s=this&&this.__extends||(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var e in i)i.hasOwnProperty(e)&&(t[e]=i[e])},function(t,i){function e(){this.constructor=t}r(t,i),t.prototype=null===i?Object.create(i):(e.prototype=i.prototype,new e)}),a=function(t){function i(i,e){var r=t.call(this,i,e)||this;return r.calculate(),r}return s(i,t),i.prototype.calculate=function(){var t=this.props.padding.top,i=this.props.padding.left,e=this.props.padding.bottom,r=this.props.xAxis.height,o=this.props.yAxis.width;this.chartWidth=this.width-this.props.padding.left-this.props.padding.right,this.chart={x:i,y:t,width:this.chartWidth,height:this.chartHeight,yAxisLeft:{x:i,y:t,width:o,height:this.chartHeight,props:{textAlign:n.a.textAlign.left}},yAxisRight:{x:i+this.chartWidth-o,y:t,width:o,height:this.chartHeight,props:{textAlign:n.a.textAlign.right}}},this.auctionWidth=this.props.showAuction?Math.min(75*this.props.devicePixelRatio,.25*this.chartWidth):0,this.mainChart={x:i+this.auctionWidth,y:t,width:this.chartWidth-this.auctionWidth,height:this.chartHeight},this.auctionChart={x:i,y:t,width:this.auctionWidth,height:this.chartHeight},this.xAxis={x:i+this.auctionWidth,y:t+this.chartHeight+e,width:this.chartWidth-this.auctionWidth,height:r};var s=14*this.props.devicePixelRatio,a=45*this.props.devicePixelRatio;this.mainIndicator={x:i+this.auctionWidth,y:this.height-this.indicatorHeight+s,width:this.chartWidth-this.auctionWidth,height:this.indicatorHeight-s,yAxis:{x:this.width-45,y:this.height-this.indicatorHeight+s,width:45,height:this.indicatorHeight-s},bar:{x:i+this.auctionWidth,y:this.height-this.indicatorHeight,width:this.chartWidth-this.auctionWidth,buttonWidth:a,height:s}},this.auctionIndicator={x:i,y:this.height-this.indicatorHeight+s,width:this.auctionWidth,height:this.indicatorHeight-s}},i}(o.a);i.a=a},function(t,i,e){"use strict";var r,o=e(31),n=this&&this.__extends||(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var e in i)i.hasOwnProperty(e)&&(t[e]=i[e])},function(t,i){function e(){this.constructor=t}r(t,i),t.prototype=null===i?Object.create(i):(e.prototype=i.prototype,new e)}),s=function(t){function i(i,e){return t.call(this,i,e)||this}return n(i,t),i.prototype.calculate=function(){var t=this.props.padding.top,i=this.props.padding.left,e=this.props.yAxis.width,r=this.props.xAxis.height;this.chartAndChipWidth=this.chartWidth,this.props.isSupportChip&&this.props.isShowChip&&(this.chartWidth-=this.chipRegion.width),this.chart={x:i+e,y:t,width:this.chartWidth,height:this.chartHeight,yAxis:{x:i,y:t,width:e-10,height:this.chartHeight}},this.xAxis={x:i+e,y:t+this.chartHeight,width:this.chartWidth,height:r};var o=14*this.props.devicePixelRatio;this.indicator={x:i+e,y:this.height-this.indicatorHeight+o,width:this.chartWidth,height:this.indicatorHeight-o,yAxis:{x:i,y:this.height-this.indicatorHeight+o,width:e-10,height:this.indicatorHeight-o},bar:{x:i+e,y:this.height-this.indicatorHeight,width:this.chartWidth,height:o}}},i}(o.a);i.a=s},function(t,i,e){"use strict";var r,o=e(31),n=this&&this.__extends||(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var e in i)i.hasOwnProperty(e)&&(t[e]=i[e])},function(t,i){function e(){this.constructor=t}r(t,i),t.prototype=null===i?Object.create(i):(e.prototype=i.prototype,new e)}),s=function(t){function i(i,e){var r=t.call(this,i,e)||this;return r.calculate(),r}return n(i,t),i.prototype.calculate=function(){var t=this.props.padding.top,i=this.props.padding.left,e=this.props.padding.bottom,r=this.props.xAxis.height;this.chartWidth=this.width-this.props.padding.left-this.props.padding.right,this.chartAndChipWidth=this.chartWidth,this.props.isSupportChip&&this.props.isShowChip&&(this.chartWidth-=this.chipRegion.width),this.chart={x:i,y:t,width:this.chartWidth,height:this.chartHeight},this.xAxis={x:i,y:t+this.chartHeight+e,width:this.chartWidth,height:r};for(var o=14*this.props.devicePixelRatio,n=45*this.props.devicePixelRatio,s=this.props.setting.indicatorCount,a=this.indicatorHeight/s,h=1;h<=s;h++){var c=this.height-this.indicatorHeight+a*(h-1);this[1===h?"indicator":["second","third","fourth"][h-2]+"Indicator"]={x:i,y:c+o,width:this.chartWidth,height:a-o,yAxis:{x:this.chartWidth-45,y:c+o,width:45,height:a-o},bar:{x:i,y:c,width:this.chartWidth,buttonWidth:n,height:o}}}var p=9*this.props.devicePixelRatio,u=6*this.props.devicePixelRatio;this.props.maType.filter(function(t){return!!t}).length>5&&(this.foldArrow={y:t/2-u/2,buttonWidth:p,height:u})},i}(o.a);i.a=s},function(t,i,e){"use strict";var r=e(3),o=e(0),n=e(1),s=this&&this.__assign||Object.assign||function(t){for(var i,e=1,r=arguments.length;e<r;e++)for(var o in i=arguments[e],i)Object.prototype.hasOwnProperty.call(i,o)&&(t[o]=i[o]);return t},a=function(){function t(t,i,e,o){this.ctx=t,this.props=i,this.region=e,this.activeObj=null,this.data=o,this.config={minX:this.region.x,maxX:this.region.x+this.region.width,minCount:22,bgColor:this.props.colorProp.chip.blue,area:{height:this.region.height,bgColor:"rgba(48, 119, 236, 0.1)"},closeBtn:{width:18*this.props.devicePixelRatio,height:18*this.props.devicePixelRatio,bgColor:"rgba(48, 119, 236, 0.2)",color:this.props.colorProp.chip.blue}},this.move=Object(r.f)(1e3/60,this.moveShape),this.init()}return t.prototype.init=function(){this.points={count:this.config.minCount,start:{y:this.region.y+this.region.height/2},end:{x:this.region.x+this.data.length*this.props.itemWidth-this.props.itemWidth/2,y:this.region.y+this.region.height/2,data:this.data[this.data.length-1]}},this.config.maxX=this.region.x+this.data.length*this.props.itemWidth,this.data.length<this.config.minCount?(this.points.start.x=this.region.x+this.props.itemWidth/2,this.points.start.data=this.data[0]):(this.points.start.x=this.region.x+(this.data.length-this.config.minCount)*this.props.itemWidth+this.props.itemWidth/2,this.points.start.data=this.data[this.data.length-this.config.minCount])},t.prototype.findTarget=function(t){var i=this.isTapOperat(t);if(i){if("close"===i.target)return{action:"close"};this.activeObj=i}else this.cancelActive()},t.prototype.setData=function(t){this.activeObj||(this.data=Array.isArray(t)?t:this.data,this.init())},t.prototype.moveShape=function(t,i){if(this.activeObj){var e=this.config,r=e.minX,o=e.maxX;switch(this.activeObj.target){case"start":this.points.start.x=this.calcPointX(t)>r?Math.min(this.calcPointX(t),this.points.end.x-this.props.itemWidth):r+this.props.itemWidth/2;break;case"end":this.points.end.x=this.calcPointX(t)<o-this.props.itemWidth/2?Math.max(this.calcPointX(t),this.points.start.x+this.props.itemWidth):o-this.props.itemWidth/2;break;case"area":var n=this.calcPointX({x:t.x-this.activeObj.distance.left});n>this.region.x+this.region.width-this.points.count*this.props.itemWidth&&(n=this.region.x+this.region.width-this.points.count*this.props.itemWidth+this.props.itemWidth/2),this.points.start.x=n,this.points.end.x=this.points.start.x+this.props.itemWidth*(this.points.count-1)}this.updateShapeData(),i&&i()}},t.prototype.updateShapeData=function(){if(this.data.length){var t=Math.floor((this.points.start.x-this.region.x)/this.props.itemWidth),i=Math.floor((this.points.end.x-this.region.x)/this.props.itemWidth);this.data[t]&&(this.points.start.data=this.points.start.preData=this.data[t]),this.data[i]&&(this.points.end.data=this.points.end.preData=this.data[i]),this.points.count=Math.abs(i-t)+1}},t.prototype.update=function(t,i){var e=this,r=i.props,o=i.region,n=i.list,s=this.points,a=s.start,h=s.end,c=this.region.x+this.region.width/2;switch(t){case"chip":this.props=r,this.region=o,this.data=n,this.config.maxX=this.region.x+this.region.width;break;case"scale":this.props=r,this.data=n,this.points.start.preData||(this.points.start.preData=this.points.start.data,this.points.end.preData=this.points.end.data);break;case"swipe":this.data=n,this.points.start.preData&&(this.points.start.preData=null,this.points.end.preData=null)}var p=-1,u=-1;if("swipe"===t)return p=Math.floor((this.points.start.x-this.region.x)/this.props.itemWidth),u=Math.min(Math.floor((this.points.end.x-this.region.x)/this.props.itemWidth),this.data.length-1),p>=this.data.length-1&&(p=this.data.length-this.points.count),a.data=this.data[p],h.data=this.data[u],void(this.points.count=Math.abs(u-p)+1);this.data.map(function(t,i){t.time===(a.preData?a.preData.time:a.data.time)&&i!==e.data.length-1&&(p=i,a.data=t,a.x=e.region.x+(i+.5)*e.props.itemWidth),t.time===(h.preData?h.preData.time:h.data.time)&&(u=i,h.data=t,h.x=e.region.x+(i+.5)*e.props.itemWidth)}),-1===p&&(a.x<c?(p=0,a.x=this.region.x+this.props.itemWidth/2):(p=this.data.length-2,a.x=this.region.x+p*this.props.itemWidth+this.props.itemWidth/2),a.data=this.data[p]),-1===u&&(h.x<c?(u=1,h.x=this.region.x+1.5*this.props.itemWidth):(u=this.data.length-1,h.x=this.region.x+u*this.props.itemWidth+this.props.itemWidth/2),h.data=this.data[u]),this.points.count=Math.abs(u-p)+1},t.prototype.calcPointX=function(t){var i=this.region.x+this.props.itemWidth/2,e=this.region.x+this.region.width-this.props.itemWidth/2,r=this.region.x+(Math.floor((t.x-this.region.x)/this.props.itemWidth)+.5)*this.props.itemWidth;return r<i?i:r>e?e:r},t.prototype.isTapOperat=function(t){var i=this.points,e=i.start,r=i.end,o={x:Math.min(e.x,r.x),y:this.region.y,width:Math.abs(r.x-e.x),height:this.region.height},n={x:Math.min(Math.max(0,o.x-5*this.props.devicePixelRatio),this.region.x+this.region.width-10*this.props.devicePixelRatio),y:this.region.y,width:10*this.props.devicePixelRatio,height:this.region.height},s={x:Math.min(Math.max(0,o.x+o.width-5*this.props.devicePixelRatio),this.region.x+this.region.width-10*this.props.devicePixelRatio),y:this.region.y,width:10*this.props.devicePixelRatio,height:this.region.height},a={x:Math.min(e.x,r.x),y:this.region.y,width:this.config.closeBtn.width,height:this.config.closeBtn.height};return this.isPointInRegion(t,a)?{target:"close"}:this.isPointInRegion(t,s)?{target:"end"}:this.isPointInRegion(t,n)?{target:"start"}:this.isPointInRegion(t,o)?{target:"area",distance:{left:Math.abs(t.x-e.x),right:Math.abs(t.x-r.x)}}:void 0},t.prototype.isPointInRegion=function(t,i){return i&&t.x>=i.x&&t.x<=i.x+i.width&&t.y>=i.y&&t.y<=i.y+i.height},t.prototype.cancelActive=function(){this.activeObj=null},t.prototype.getActive=function(){return this.activeObj},t.prototype.drawClose=function(){var t=this.config.closeBtn,i=t.bgColor,e=t.color,r=t.width,o=t.height,s=this.points,a=s.start,h=s.end,c=this.props.devicePixelRatio,p={x:Math.min(a.x,h.x),y:this.region.y};Object(n.f)(this.ctx,p.x,p.y,r,o,4,i,i,0),Object(n.c)(this.ctx,p.x+5*c,p.y+5*c,p.x+13*c,p.y+13*c,e,"",null,null,2*c),Object(n.c)(this.ctx,p.x+13*c,p.y+5*c,p.x+5*c,p.y+13*c,e,"",null,null,2*c)},t.prototype.drawArea=function(){var t=this.config.area.bgColor,i=this.points,e=i.start,r=i.end,o=Math.min(e.x,r.x),s=Math.max(e.x,r.x),a=this.props.devicePixelRatio;Object(n.g)(this.ctx,o,this.region.y,Math.abs(s-o),this.region.height,t,t,0),Object(n.c)(this.ctx,o,this.region.y,o,this.region.y+this.region.height,this.config.bgColor,"dash",null,[10,5],1*a),Object(n.c)(this.ctx,s,this.region.y,s,this.region.y+this.region.height,this.config.bgColor,"dash",null,[10,5],1*a),Object(n.b)(this.ctx,o,this.region.y+this.region.height/2,36,t,t),Object(n.b)(this.ctx,o,this.region.y+this.region.height/2,12,this.config.bgColor,this.config.bgColor),Object(n.b)(this.ctx,s,this.region.y+this.region.height/2,36,t,t),Object(n.b)(this.ctx,s,this.region.y+this.region.height/2,12,this.config.bgColor,this.config.bgColor)},t.prototype.drawDateTips=function(){var t=this,i={color:this.props.colorProp.chip.text,font:"600 "+10*this.props.devicePixelRatio+"px Arial",textAlign:o.a.textAlign.center,baseLine:o.a.baseLine.mid},e=this.points,r=e.start,a=e.end,h=4*this.props.devicePixelRatio,c=function(t){return isNaN(t)?t:t.substr(0,4)+"-"+t.substr(4,2)+"-"+t.substr(6,2)+" "+t.substr(8,2)+":"+t.substr(-2)},p=c(r.data.time),u=c(a.data.time),l={width:Object(o.d)(this.ctx,p,i)+2*h,height:15*this.props.devicePixelRatio,top:this.region.y+this.region.height},d=function(i,e){var o=i-l.width/2,n=Math.abs(a.x-r.x);switch(e){case"start":i<t.region.x+l.width/2?o=t.region.x:n<l.width&&(i<t.region.x+l.width?o=Math.max(t.region.x,i-l.width/2-n/2):o-=Math.abs(l.width/2-n/2)),i>t.region.x+t.region.width-1.5*l.width&&(o=t.region.x+t.region.width-2*l.width);break;case"end":i<t.region.x+1.5*l.width?o=t.region.x+l.width:n<l.width&&(o+=Math.abs(l.width/2-n/2)),i>t.region.x+t.region.width-l.width/2&&(o=t.region.x+t.region.width-l.width)}return o};Object(n.f)(this.ctx,d(r.x,"start"),l.top,l.width,l.height,2*this.props.devicePixelRatio,this.activeObj&&"start"===this.activeObj.target?this.props.colorProp.chip.blue:this.props.colorProp.chip.subText,this.activeObj&&"start"===this.activeObj.target?this.props.colorProp.chip.blue:this.props.colorProp.chip.white,1),Object(o.c)(this.ctx,p,d(r.x,"start")+l.width/2,Math.ceil(l.top+l.height/2),s({},i,{color:this.activeObj&&"start"===this.activeObj.target?this.props.colorProp.chip.white:this.props.colorProp.chip.text})),Object(n.f)(this.ctx,d(a.x,"end"),l.top,l.width,l.height,2*this.props.devicePixelRatio,this.activeObj&&"end"===this.activeObj.target?this.props.colorProp.chip.blue:this.props.colorProp.chip.subText,this.activeObj&&"end"===this.activeObj.target?this.props.colorProp.chip.blue:this.props.colorProp.chip.white,1),Object(o.c)(this.ctx,u,d(a.x,"end")+l.width/2,Math.ceil(l.top+l.height/2),s({},i,{color:this.activeObj&&"end"===this.activeObj.target?this.props.colorProp.chip.white:this.props.colorProp.chip.text}))},t.prototype.drawTopTips=function(){var t=4*this.props.devicePixelRatio,i=this.getTipBarData();if(!i)return void console.error("calc tipbar data error!!!");var e=i.rangeIncrease,s=i.rangeBarText+" "+Object(r.g)(e),a=+e>0,h=0==+e,c={color:a?this.props.colorProp.rise:h?this.props.colorProp.flat:this.props.colorProp.drop,font:"600 "+10*this.props.devicePixelRatio+"px Arial",textAlign:o.a.textAlign.center,baseLine:o.a.baseLine.mid},p=Object(o.d)(this.ctx,s,c)+2*t,u={x:Math.max(this.region.x,Math.max(this.points.start.x,this.points.end.x)-p),y:this.region.y-15*this.props.devicePixelRatio,width:p,height:15*this.props.devicePixelRatio,borderColor:a?"rgba(230, 53, 53, 0.4)":h?"rgba(146, 153, 170, 0.4)":"rgba(45, 185, 85, 0.4)",bgColor:a?"#FCEDED":h?"#F5F6FA":"#F0F9F2"};Object(n.f)(this.ctx,u.x,u.y,u.width,u.height,2*this.props.devicePixelRatio,u.borderColor,u.bgColor,3),Object(o.c)(this.ctx,s,u.x+u.width/2,Math.ceil(u.y+u.height/2),c)},t.prototype.draw=function(){this.drawTopTips(),this.drawArea(),this.drawClose(),this.drawDateTips()},t.prototype.getTipBarData=function(){var t=this.points,i=t.start,e=t.end,r=this.data.findIndex(function(t){return t.time===i.data.time}),o=this.data.findIndex(function(t){return t.time===e.data.time}),n=this.data.filter(function(t,i){if(i>=Math.min(r,o)&&i<=Math.max(r,o))return t});if(!(n.length<=1)){var s={count:n.length,startTime:null,endTime:null,startPrice:null,endPrice:null,maxPrice:0,minPrice:0,cjl:0,cje:0,hsl:0,zf:0,rangeIncrease:0,rangeText:"",rangeBarText:""};switch(n.map(function(t,i){0==i&&(s.startTime=t.time,s.startPrice=t.preClose,s.minPrice=t.low),i===n.length-1&&(s.endTime=t.time,s.endPrice=t.close),s.maxPrice=Math.max(s.maxPrice,t.high),s.minPrice=Math.min(s.minPrice,t.low),s.cje+=t.cje||0,s.hsl+=(t.hasOwnProperty("exchangeRaw")?+t.exchangeRaw:+t.hsl)||0,s.cjl+=t.volume||0}),s.zf=(s.maxPrice-s.minPrice)/Math.abs(s.startPrice),s.rangeIncrease=(s.endPrice-s.startPrice)/Math.abs(s.startPrice),this.props.type){case"day":case"oneMonth":case"threeMonth":case"halfYear":case"oneYear":case"threeYear":case"fiveYear":s.rangeText=s.count+"个交易日",s.rangeBarText=s.count+"天";break;case"week":case"allYear":s.rangeText=s.rangeBarText=s.count+"周";break;case"month":s.rangeText=s.rangeBarText=s.count+"个月";break;case"season":s.rangeText=s.count+"个季度",s.rangeBarText=s.count+"季度";break;case"year":s.rangeText=s.rangeBarText=s.count+"年";break;case"m1":s.rangeText=s.rangeBarText=s.count+"分钟";break;case"m5":s.rangeText=s.rangeBarText=5*s.count+"分钟";break;case"m15":s.rangeText=s.rangeBarText=15*s.count+"分钟";break;case"m30":s.rangeText=s.rangeBarText=30*s.count+"分钟";break;case"m60":s.rangeText=s.rangeBarText=s.count+"小时";break;case"m120":s.rangeText=s.rangeBarText=2*s.count+"小时"}return s}},t}();i.a=a},function(t,i,e){"use strict";var r,o=e(80),n=this&&this.__extends||(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var e in i)i.hasOwnProperty(e)&&(t[e]=i[e])},function(t,i){function e(){this.constructor=t}r(t,i),t.prototype=null===i?Object.create(i):(e.prototype=i.prototype,new e)}),s=function(t){function i(i,e,r,o){var n=t.call(this,i,e,r,o,"volume","",r.stockUnit)||this;return n.notSupport="uk"===r.market||"ft"===r.market,n}return n(i,t),i}(o.a);i.a=s},function(t,i,e){"use strict";var r,o,n=e(152),s=e(51),a=e(154),h=e(83),c=e(155),p=e(156);(r=o||(o={})).DAY="day",r.WEEK="week",r.MONTH="month",r.M1="m1",r.M5="m5",r.M15="m15",r.M30="m30",r.M60="m60",r.M120="m120",r.SEASON="season",r.YEAR="year";var u=function(){function t(t){this.list=t.list,this.type=t.type,this.targetIndex=t.targetIndex,isNaN(t.crossPrice)||(this.crossPrice=t.crossPrice),this.init()}return t.prototype.init=function(){switch(this.type){case o.M1:case o.M5:case o.M15:case o.M30:case o.M60:case o.M120:this.klineModel=new n.a({});break;case o.DAY:this.klineModel=new s.a({});break;case o.WEEK:this.klineModel=new a.a({});break;case o.MONTH:this.klineModel=new h.a({});break;case o.SEASON:this.klineModel=new c.a({});break;case o.YEAR:this.klineModel=new p.a({})}},t.prototype.getData=function(t){var i=this.klineModel.calc(this.list,t||this.targetIndex),e=i.chip,r=i.price;this.chip=void 0===e?[]:e,this.price=void 0===r?[]:r;var o=this.calConcentration(5,95),n=this.calConcentration(15,85),s=this.getCurrentPrice(),a=this.getProfitPercent(s),h=this.cost(50),c=isNaN(this.crossPrice)?"":this.getProfitPercent(this.crossPrice);return{chip:{list:this.chip,maxValue:Math.max.apply(Math,this.chip),prices:this.price},p90:o,p70:n,profitPercent:isNaN(h)?"--":(100*a).toFixed(2)+"%",avgPrice:isNaN(h)?"--":h,currentPrice:s,crossProfitPercent:c,crossPrice:isNaN(this.crossPrice)?"":this.crossPrice}},t.prototype.getProfitPercent=function(t){var i=this,e=0,r=this.getTotalChip();return this.chip.map(function(r,o){i.price[o]<=t&&(e+=isNaN(r)?0:r)}),r<=1e-7?0:e/r},t.prototype.getTotalChip=function(){var t=0;return this.chip.map(function(i){isNaN(i)||(t+=i)}),t},t.prototype.getCurrentPrice=function(){return this.list[this.targetIndex]&&this.list[this.targetIndex].close},t.prototype.calConcentration=function(t,i){var e=this.cost(t),r=this.cost(i);if(isNaN(e)||isNaN(r))return{price:"--",percent:"--"};var o=1e-6>Math.abs(r+e)?0:100*(r-e)/(r+e);return{price:(e>1e4?e.toFixed(1):e.toFixed(2))+"~"+(r>1e4?r.toFixed(1):r.toFixed(2)),percent:o.toFixed(2)+"%"}},t.prototype.cost=function(t){var i=0,e=-1,r=this.getTotalChip();if(!r)return NaN;for(var o=0;o<this.chip.length;o++)if(100*(i+=this.chip[o])/r>=t){e=o;break}return this.price[e]},t}();i.a=u},function(t,i,e){"use strict";var r,o=e(51),n=this&&this.__extends||(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var e in i)i.hasOwnProperty(e)&&(t[e]=i[e])},function(t,i){function e(){this.constructor=t}r(t,i),t.prototype=null===i?Object.create(i):(e.prototype=i.prototype,new e)}),s=function(t){function i(i){return t.call(this,i)||this}return n(i,t),i.prototype.getGraphDataCount=function(t,i){return 121},i.prototype.getATurn=function(t,i,e,r){return this.tansformToRealChange(t[e].hsl)},i.prototype.getMaxKlineCount=function(){return 121},i.prototype.getMinKlineCount=function(){return 21},i.prototype.getPriceCount=function(){return 100},i}(o.a);i.a=s},function(t,i,e){"use strict";e.d(i,"a",function(){return r});var r=function(t){this.DAY_KLINE_MAX_COUNT=t.DAY_KLINE_MAX_COUNT||370,this.WEEK_KLINE_MAX_COUNT=5*this.DAY_KLINE_MAX_COUNT,this.MONTH_KLINE_MAX_COUNT=24*this.DAY_KLINE_MAX_COUNT}},function(t,i,e){"use strict";var r,o=e(51),n=this&&this.__extends||(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var e in i)i.hasOwnProperty(e)&&(t[e]=i[e])},function(t,i){function e(){this.constructor=t}r(t,i),t.prototype=null===i?Object.create(i):(e.prototype=i.prototype,new e)}),s=function(t){function i(i){return t.call(this,i)||this}return n(i,t),i.prototype.calc=function(t,i){try{var e=this.getGraphDataCount(t,i);if(0==e)return console.error("[cal] klinecount = 0"),null;var r=this.genDayKlineData(t,i,e);i=r.length-1,e=r.length;for(var o=this.getMaxMinPrice(r,i,e),n=[],s=this.getPriceCount()-1,a=(o.max-o.min)/s,h=0;h<=s;h++)n[h]=o.min+h*a;for(var c=r[i],p=c?[this.tansformToRealChange(c.hsl)]:[],u=[1],l=i-1;l>i-e&&l>=0;l--)p[i-l]=this.getATurn(r,i,l,u);for(var d=Array(s+1).fill(0),f=Array(s+1).fill(0),g=i;g>i-e&&g>=0;g--){var x=r[g];if(null!=x){var y=x.high,w=x.low,m=(y+w)/2,v=this.findPriceSection(r,g,n,a);if(!(v.from>v.to)){for(var b=0,M=v.from;M<=v.to;M++)n[M]>=m?d[M]=y-n[M]:d[M]=n[M]-w,b+=d[M];for(var C=v.from;C<=v.to;C++)if(Math.abs(b)>1e-6){var A=p[i-g]*d[C]/b;f[C]+=A}}}}return{chip:f,price:n,maxMin:o}}catch(t){return console.error("calc",t),null}},i.prototype.getPriceCount=function(){return 100},i.prototype.getMaxKlineCount=function(){return 121},i.prototype.getMinKlineCount=function(){return 21},i}(o.a);i.a=s},function(t,i,e){"use strict";var r,o=e(83),n=this&&this.__extends||(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var e in i)i.hasOwnProperty(e)&&(t[e]=i[e])},function(t,i){function e(){this.constructor=t}r(t,i),t.prototype=null===i?Object.create(i):(e.prototype=i.prototype,new e)}),s=function(t){function i(i){return t.call(this,i)||this}return n(i,t),i.prototype.getMaxChangeSum=function(){return 10},i.prototype.getMinKlineCount=function(){return 8},i.prototype.getMaxKlineCount=function(){return 15},i.prototype.getPriceCount=function(){return 100},i}(o.a);i.a=s},function(t,i,e){"use strict";var r,o=e(52),n=this&&this.__extends||(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var e in i)i.hasOwnProperty(e)&&(t[e]=i[e])},function(t,i){function e(){this.constructor=t}r(t,i),t.prototype=null===i?Object.create(i):(e.prototype=i.prototype,new e)}),s=function(t){function i(i){return t.call(this,i)||this}return n(i,t),i.prototype.getMaxChangeSum=function(){return 20},i.prototype.getMinKlineCount=function(){return 5},i.prototype.getMaxKlineCount=function(){return 10},i.prototype.getPriceCount=function(){return 100},i.prototype.calc=function(t,i){try{var e=this.getGraphDataCount(t,i);if(0==e)return null;for(var r=this.getPriceCount()-1,o=this.getMaxMinPrice(t,i,e),n=Math.abs(o.max-o.min)/r,s=[],a=0;a<=r;a++)s[a]=o.min+a*n;for(var h=t[i],c=h?[this.tansformToRealChange(h.hsl)]:[],p=[1],u=i-1;u>i-e&&u>=0;u--)c[i-u]=this.getATurn(t,i,u,p);for(var l=Array(r+1).fill(0),d=i;d>i-e&&d>=0;d--){var f=t[d];if(null!=f){var g=f.high,x=f.low,y=(g+x)/2,w=this.findPriceSection(t,d,s,n);if(!(w.from>w.to)){for(var m=Array(r+1).fill(0),v=0,b=w.from;b<=w.to;b++)s[b]>=y?m[b]=g-s[b]:m[b]=s[b]-x,v+=m[b];for(var M=w.from;M<=w.to;M++)if(Math.abs(v)>1e-6){var C=c[i-d]*m[M]/v;l[M]+=C}}}}for(var A=0;A<l.length;A++)l[A]=Math.abs(l[A]);return{chip:l,maxMin:o,price:s}}catch(t){return console.error("calc",t),null}},i}(o.a);i.a=s},function(t,i,e){"use strict";var r,o=e(80),n=this&&this.__extends||(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var e in i)i.hasOwnProperty(e)&&(t[e]=i[e])},function(t,i){function e(){this.constructor=t}r(t,i),t.prototype=null===i?Object.create(i):(e.prototype=i.prototype,new e)}),s=function(t){function i(i,e,r,o){var n=t.call(this,i,e,r,o,"cje","","元")||this;return n.notSupport=[3,"3","nq","uk","ft","fu"].indexOf(r.market)>=0||/^m(1|5|15|30|60|120)$/.test(r.type),n}return n(i,t),i}(o.a);i.a=s},function(t,i,e){"use strict";var r,o=e(2),n=this&&this.__extends||(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var e in i)i.hasOwnProperty(e)&&(t[e]=i[e])},function(t,i){function e(){this.constructor=t}r(t,i),t.prototype=null===i?Object.create(i):(e.prototype=i.prototype,new e)}),s=function(t){function i(i,e,r,o){var n=r.setting.cciParams.n;return t.call(this,i,e,r,o,"cci","("+n+")")||this}return n(i,t),i.prototype.addStatusData=function(){if(!this.data.items[2])return!1;if(void 0===this.data.items[2].cciStatus)for(var t=2;t<this.data.items.length;t++){var i=this.data.items[t],e=this.data.items[t-1],r=this.data.items[t-2],o=null;r.cci>100&&e.cci>100&&r.cci>e.cci&&e.cci>i.cci&&(o="high"),r.cci<-100&&e.cci<-100&&r.cci<e.cci&&e.cci<i.cci&&(o="low"),i.cciStatus=o}return!0},i}(o.a);i.a=s},function(t,i,e){"use strict";var r,o=e(2),n=this&&this.__extends||(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var e in i)i.hasOwnProperty(e)&&(t[e]=i[e])},function(t,i){function e(){this.constructor=t}r(t,i),t.prototype=null===i?Object.create(i):(e.prototype=i.prototype,new e)}),s=function(t){function i(i,e,r,o){var n=r.setting.dmiParams,s=n.n,a=n.m;return t.call(this,i,e,r,o,"dmi","("+s+","+a+")")||this}return n(i,t),i}(o.a);i.a=s},function(t,i,e){"use strict";var r,o=e(85),n=this&&this.__extends||(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var e in i)i.hasOwnProperty(e)&&(t[e]=i[e])},function(t,i){function e(){this.constructor=t}r(t,i),t.prototype=null===i?Object.create(i):(e.prototype=i.prototype,new e)}),s=function(t){function i(i,e,r,o){var n=r.setting.kdjParams,s=n.n1,a=n.n2,h=n.n3;return t.call(this,i,e,r,o,"kdj","("+s+","+a+","+h+")")||this}return n(i,t),i.prototype.addStatusData=function(){if(!this.data.items[1])return!1;if(void 0===this.data.items[1].kdjStatus)for(var t=1;t<this.data.items.length;t++){var i=this.data.items[t],e=this.data.items[t-1],r=null;e.kdj.k>=80&&e.kdj.j>=80&&e.kdj.j>e.kdj.k&&i.kdj.k>=i.kdj.j&&(r="high"),e.kdj.k<=20&&e.kdj.j<=20&&e.kdj.j<e.kdj.k&&i.kdj.k<=i.kdj.j&&(r="low"),i.kdjStatus=r}return!0},i}(o.a);i.a=s},function(t,i,e){"use strict";var r,o=e(2),n=e(8),s=e(1),a=this&&this.__extends||(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var e in i)i.hasOwnProperty(e)&&(t[e]=i[e])},function(t,i){function e(){this.constructor=t}r(t,i),t.prototype=null===i?Object.create(i):(e.prototype=i.prototype,new e)}),h=function(t){function i(i,e,r,o){var n=Math.max(e.maxMin.kline.max,e.maxMin.ema.max),s=Math.min(e.maxMin.kline.min,e.maxMin.ema.min);return t.call(this,i,e,r,o,"ema","",null,n,s)||this}return a(i,t),i.prototype.drawLinearShapes=function(){var t=this,i=this.data.items,e=this.props.setting.emaTypes||[],r=[];e.filter(function(i,e){if(i)return r.push(t.props.colorProp.ema[e]),i}).forEach(function(i,e){var o=t.data.items.map(function(t){return t.ema[i]});new n.a({ctx:t.ctx,region:t.region,drawCallback:function(i){return t.drawLineItem(i,r[e])},data:{items:o,max:t.max,min:t.min},count:t.props.count})}),new n.a({ctx:this.ctx,region:this.region,drawCallback:function(i){return t.drawKlineItem(i)},data:{items:i,max:this.max,min:this.min},count:this.props.count})},i.prototype.drawLineItem=function(t,i){var e=this.props,r=t.index,o=t.getX(r)+e.itemWidth/2,n=t.getY(t.currItem);Object(s.d)(this.ctx,o,n,i,this.props.lineProp.indicator,r,t.length)},i.prototype.drawKlineItem=function(t){if(!t.currItem.forBounce){var i=t.index,e=this.data.items[i],r=t.getX(i)+this.props.itemWidth/2;Object(s.e)(this.ctx,{open:t.getY(e.open),close:t.getY(e.close),low:t.getY(e.low),high:t.getY(e.high)},r,this.props.itemWidth,this.props.colorProp.emaOchl,this.props.lineProp.ochl)}},i}(o.a);i.a=h},function(t,i,e){"use strict";var r,o=e(7),n=e(2),s=e(8),a=e(1),h=e(9),c=e(0),p=this&&this.__extends||(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var e in i)i.hasOwnProperty(e)&&(t[e]=i[e])},function(t,i){function e(){this.constructor=t}r(t,i),t.prototype=null===i?Object.create(i):(e.prototype=i.prototype,new e)}),u=this&&this.__assign||Object.assign||function(t){for(var i,e=1,r=arguments.length;e<r;e++)for(var o in i=arguments[e],i)Object.prototype.hasOwnProperty.call(i,o)&&(t[o]=i[o]);return t},l="macd",d=function(t){function i(i,e,r,o){var n=r.setting[l+"Params"],s=n.short,a=n.long,h=n.m;return t.call(this,i,e,r,o,l,"("+s+","+a+","+h+")",null,null,null)||this}return p(i,t),i.prototype.drawLinearShapes=function(){var t=this;new s.a({ctx:this.ctx,region:this.region,drawCallback:function(i){return t.drawRect(i)},data:{items:this.data.items.map(function(t){return t[l].macd}),max:this.max,min:this.min,middle:0},count:this.props.count}),["dea","dif"].forEach(function(i){var e=t.data.items.map(function(t){return t[l][i]});new s.a({ctx:t.ctx,region:t.region,drawCallback:function(e){return t.drawLineItem(e,i)},data:{items:e,max:t.max,min:t.min,middle:0},count:t.props.count})})},i.prototype.drawLineItem=function(t,i){var e=this.props,r=t.index,o=t.getX(r)+e.itemWidth/2,n=t.getY(t.currItem);Object(a.d)(this.ctx,o,n,this.props.colorProp[l][i],this.props.lineProp.indicator,r,t.length)},i.prototype.drawRect=function(t){var i=this.props,e=t.index,r=t.getX(e),o=t.getY(t.currItem),n=t.currItem>=0?this.props.colorProp.rise:this.props.colorProp.drop;Object(a.g)(this.ctx,r,0,i.barWidth,o,n,n)},i.prototype.drawScale=function(){var t=this.region.yAxis.x+this.region.yAxis.width,i={font:this.props.textProp.font,textAlign:c.a.textAlign.right,baseLine:c.a.baseLine.btm,color:this.props.colorProp.yAxis};new h.a(this.ctx,o.b.y,[{text:this.max.toFixed(2),x:t,y:this.region.yAxis.y,props:u({},i,{baseLine:c.a.baseLine.top})},{text:0,x:t,y:this.region.yAxis.y+this.region.height/2,props:u({},i,{baseLine:c.a.baseLine.mid})},{text:this.min.toFixed(2),x:t,y:this.region.yAxis.y+this.region.height,props:i}],this.region.yAxis).draw()},i.prototype.addStatusData=function(){if(!this.data.items[1])return!1;if(void 0===this.data.items[1].macdStatus)for(var t=1;t<this.data.items.length;t++){var i=this.data.items[t],e=this.data.items[t-1],r=null;i.macd.dif<i.macd.dea&&e.macd.dif>e.macd.dea&&i.macd.dif<e.macd.dif&&(r="high"),i.macd.dif>i.macd.dea&&e.macd.dif<e.macd.dea&&i.macd.dif>e.macd.dif&&(r="low"),i.macdStatus=r}return!0},i}(n.a);i.a=d},function(t,i,e){"use strict";var r,o=e(32),n=e(9),s=e(7),a=e(3),h=e(0),c=this&&this.__extends||(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var e in i)i.hasOwnProperty(e)&&(t[e]=i[e])},function(t,i){function e(){this.constructor=t}r(t,i),t.prototype=null===i?Object.create(i):(e.prototype=i.prototype,new e)}),p=this&&this.__assign||Object.assign||function(t){for(var i,e=1,r=arguments.length;e<r;e++)for(var o in i=arguments[e],i)Object.prototype.hasOwnProperty.call(i,o)&&(t[o]=i[o]);return t},u=function(t){function i(i,e,r,o){return t.call(this,i,e,r,o,"obv","")||this}return c(i,t),i.prototype.drawScale=function(){var t=this.region.yAxis,i=Object(a.b)(this.max),e={font:this.props.textProp.font,textAlign:h.a.textAlign.right,baseLine:h.a.baseLine.top,color:this.props.colorProp.yAxis};new n.a(this.ctx,s.b.y,[{text:i.v.replace(/\.0+$/,""),x:t.x+t.width,y:t.y,props:e},{text:i.u,x:t.x+t.width,y:t.y+t.height,props:p({},e,{baseLine:h.a.baseLine.btm})}],t).draw()},i}(o.a);i.a=u},function(t,i,e){"use strict";var r,o=e(2),n=this&&this.__extends||(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var e in i)i.hasOwnProperty(e)&&(t[e]=i[e])},function(t,i){function e(){this.constructor=t}r(t,i),t.prototype=null===i?Object.create(i):(e.prototype=i.prototype,new e)}),s=function(t){function i(i,e,r,o){var n=r.setting.rsiParams,s=n.n1,a=n.n2,h=n.n3;return t.call(this,i,e,r,o,"rsi","("+s+","+a+","+h+")")||this}return n(i,t),i.prototype.addStatusData=function(){if(!this.data.items[2])return!1;if(void 0===this.data.items[2].rsiStatus)for(var t=2;t<this.data.items.length;t++){var i=this.data.items[t],e=this.data.items[t-1],r=this.data.items[t-2],o=null;r.rsi.rsi6>80&&e.rsi.rsi6>80&&r.rsi.rsi6>e.rsi.rsi6&&e.rsi.rsi6>i.rsi.rsi6&&(o="high"),r.rsi.rsi6<20&&e.rsi.rsi6<20&&r.rsi.rsi6<e.rsi.rsi6&&e.rsi.rsi6<i.rsi.rsi6&&(o="low"),i.rsiStatus=o}return!0},i}(o.a);i.a=s},function(t,i,e){"use strict";var r,o=e(85),n=this&&this.__extends||(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var e in i)i.hasOwnProperty(e)&&(t[e]=i[e])},function(t,i){function e(){this.constructor=t}r(t,i),t.prototype=null===i?Object.create(i):(e.prototype=i.prototype,new e)}),s=function(t){function i(i,e,r,o){var n=r.setting.wrParams,s=n.n1,a=n.n2;return t.call(this,i,e,r,o,"wr","("+s+","+a+")")||this}return n(i,t),i.prototype.addStatusData=function(){if(!this.data.items[2])return!1;if(void 0===this.data.items[2].wrStatus)for(var t=2;t<this.data.items.length;t++){var i=this.data.items[t],e=this.data.items[t-1],r=this.data.items[t-2],o=null;r.wr.wr6>80&&e.wr.wr6>80&&r.wr.wr6>e.wr.wr6&&e.wr.wr6>i.wr.wr6&&(o="high"),r.wr.wr6<20&&e.wr.wr6<20&&r.wr.wr6<e.wr.wr6&&e.wr.wr6<i.wr.wr6&&(o="low"),i.wrStatus=o}return!0},i}(o.a);i.a=s},function(t,i,e){"use strict";var r,o=e(2),n=this&&this.__extends||(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var e in i)i.hasOwnProperty(e)&&(t[e]=i[e])},function(t,i){function e(){this.constructor=t}r(t,i),t.prototype=null===i?Object.create(i):(e.prototype=i.prototype,new e)}),s=function(t){function i(i,e,r,o){return t.call(this,i,e,r,o,"bias","(6,12,24)")||this}return n(i,t),i}(o.a);i.a=s},function(t,i,e){"use strict";var r,o=e(2),n=e(8),s=e(1),a=this&&this.__extends||(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var e in i)i.hasOwnProperty(e)&&(t[e]=i[e])},function(t,i){function e(){this.constructor=t}r(t,i),t.prototype=null===i?Object.create(i):(e.prototype=i.prototype,new e)}),h=function(t){function i(i,e,r,o){var n=Math.max(e.maxMin.kline.max,e.maxMin.bbi.max),s=Math.min(e.maxMin.kline.min,e.maxMin.bbi.min);return t.call(this,i,e,r,o,"bbi","(3,6,12,24)",null,n,s)||this}return a(i,t),i.prototype.drawLinearShapes=function(){var t=this;new n.a({ctx:this.ctx,region:this.region,drawCallback:function(i){if(!i.currItem.forBounce){var e=t.data.items[i.index],r=i.getX(i.index)+t.props.itemWidth/2;Object(s.e)(t.ctx,{open:i.getY(e.open),close:i.getY(e.close),low:i.getY(e.low),high:i.getY(e.high)},r,t.props.itemWidth,"#4280f2",t.props.lineProp.ochl)}},data:{items:this.data.items,max:this.max,min:this.min},count:this.props.count}),new n.a({ctx:this.ctx,region:this.region,drawCallback:function(i){var e=i.getX(i.index)+t.props.itemWidth/2,r=i.getY(i.currItem);Object(s.d)(t.ctx,e,r,t.props.colorProp.bbi,t.props.lineProp.indicator,i.index,i.length)},data:{items:this.data.items.map(function(t){return t.bbi}),max:this.max,min:this.min},count:this.props.count})},i}(o.a);i.a=h},function(t,i,e){"use strict";var r,o=e(2),n=this&&this.__extends||(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var e in i)i.hasOwnProperty(e)&&(t[e]=i[e])},function(t,i){function e(){this.constructor=t}r(t,i),t.prototype=null===i?Object.create(i):(e.prototype=i.prototype,new e)}),s=function(t){function i(i,e,r,o){return t.call(this,i,e,r,o,"trix","(12,20)")||this}return n(i,t),i}(o.a);i.a=s},function(t,i,e){"use strict";var r,o=e(2),n=this&&this.__extends||(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var e in i)i.hasOwnProperty(e)&&(t[e]=i[e])},function(t,i){function e(){this.constructor=t}r(t,i),t.prototype=null===i?Object.create(i):(e.prototype=i.prototype,new e)}),s=function(t){function i(i,e,r,o){return t.call(this,i,e,r,o,"vr","(26,6)")||this}return n(i,t),i}(o.a);i.a=s},function(t,i,e){"use strict";var r,o=e(2),n=this&&this.__extends||(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var e in i)i.hasOwnProperty(e)&&(t[e]=i[e])},function(t,i){function e(){this.constructor=t}r(t,i),t.prototype=null===i?Object.create(i):(e.prototype=i.prototype,new e)}),s=function(t){function i(i,e,r,o){return t.call(this,i,e,r,o,"arbr","(26)")||this}return n(i,t),i}(o.a);i.a=s},function(t,i,e){"use strict";var r,o=e(2),n=this&&this.__extends||(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var e in i)i.hasOwnProperty(e)&&(t[e]=i[e])},function(t,i){function e(){this.constructor=t}r(t,i),t.prototype=null===i?Object.create(i):(e.prototype=i.prototype,new e)}),s=function(t){function i(i,e,r,o){return t.call(this,i,e,r,o,"psy","(12,6)")||this}return n(i,t),i}(o.a);i.a=s},function(t,i,e){"use strict";var r,o=e(2),n=this&&this.__extends||(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var e in i)i.hasOwnProperty(e)&&(t[e]=i[e])},function(t,i){function e(){this.constructor=t}r(t,i),t.prototype=null===i?Object.create(i):(e.prototype=i.prototype,new e)}),s=function(t){function i(i,e,r,o){return t.call(this,i,e,r,o,"dma","(10,50,10)")||this}return n(i,t),i}(o.a);i.a=s},function(t,i,e){"use strict";var r,o=e(2),n=this&&this.__extends||(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var e in i)i.hasOwnProperty(e)&&(t[e]=i[e])},function(t,i){function e(){this.constructor=t}r(t,i),t.prototype=null===i?Object.create(i):(e.prototype=i.prototype,new e)}),s=function(t){function i(i,e,r,o){return t.call(this,i,e,r,o,"dpo","(20,6)")||this}return n(i,t),i}(o.a);i.a=s},function(t,i,e){"use strict";var r,o=e(2),n=e(0),s=this&&this.__extends||(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var e in i)i.hasOwnProperty(e)&&(t[e]=i[e])},function(t,i){function e(){this.constructor=t}r(t,i),t.prototype=null===i?Object.create(i):(e.prototype=i.prototype,new e)}),a=function(t){function i(i,e,r,o){var n=t.call(this,i,e,r,o,"rally","实时反弹值")||this;return n.notSupport=!["day","week"].includes(r.type)||!r.isSupportTradeSecret,n}return s(i,t),i.prototype.draw=function(){this.drawGrid(),this.notSupport?this.drawEmptyData():(this.drawLinearShapes(),this.drawScale()),this.drawBar()},i.prototype.drawEmptyData=function(){var t={color:"#7A8499",font:this.props.textProp.font,textAlign:n.a.textAlign.center,baseLine:n.a.baseLine.mid},i=this.region.x+this.region.width/2,e=this.region.y+this.region.height/2;Object(n.c)(this.ctx,"该类型暂不支持该指标",i,e,t)},i}(o.a);i.a=a},function(t,i,e){"use strict";function r(t,i,e,r,o){if(this.ctx=t,this.region=r,this.props=e,this.data=i,this.kmax=i.maxMin.kline.max,this.kmin=i.maxMin.kline.min,e.fixedWidth)this.max=i.maxMin.kline.max,this.min=i.maxMin.kline.min;else{var n=Object(g.c)(i,e),s=n.max,a=n.min;this.max=s,this.min=a}if(e.wxSearchStyle){var h=.025*(this.max-this.min);this.max+=h,this.min-=h}this.loadPos={show:!1,x:this.region.chart.x,y:this.region.chart.y+7*this.region.chart.height/12},this.drawAddZXData=null,this.drawTradePointData=[],this.extraInfoHeight=0*this.props.devicePixelRatio,this.gapInfo=[],this.lastestItem=o.lastestItem,this.lastestPriceY=1/0,this.remindPrice=o.remindPrice,this.upPriceY=1/0,this.downPriceY=1/0,this.fqWidth=0,this.trendline=o.currTrendline,this.trendlineCoord={},this.supportLine={},this.pressureLine={}}i.a=r;var o=e(19),n=e(8),s=e(50),a=e(88),h=e(9),c=e(1),p=e(7),u=e(0),l=e(24),d=e(33),f=e(89),g=e(3),x=this&&this.__assign||Object.assign||function(t){for(var i,e=1,r=arguments.length;e<r;e++)for(var o in i=arguments[e],i)Object.prototype.hasOwnProperty.call(i,o)&&(t[o]=i[o]);return t},y=document.createElement("img"),w=document.createElement("img"),m=document.createElement("img"),v=document.createElement("img"),b=document.createElement("img"),M=document.createElement("img"),C=document.createElement("img"),A=document.createElement("img");y.src=e(90),w.src=e(91),m.src=e(92),v.src=e(93),b.src=e(178),M.src=e(179),C.src=e(94),A.src=e(180);r.prototype={draw:function(){var t,i,e=this,r=this.ctx,c=this.props,g=this.region,y=g.chart,w=g.xAxis,m=[],v=[],b=c.colorProp,M=c.lineProp,C=c.textProp,A=Object(f.a)(c);c.yAixsCount=A.yAixsCount,c.hlineCount=A.hlineCount,c.posx=c.posx||[];var L={font:C.font,textAlign:u.a.textAlign.center,baseLine:u.a.baseLine.mid,color:b.xAxis};if(c.wxSearchStyle&&(c.yAixsCount=3,L.baseLine=u.a.baseLine.btm),this.trendlineCoord={left:{},point:[],right:{}},new n.a({ctx:r,region:y,drawCallback:function(t){var i=t.getX(t.index)+c.itemWidth/2,r=c.getXAxis(t.index,i,t.currItem,e.data.items,c.posx,c.count);r&&i>y.x&&i<y.x+y.width&&c.posx.push(r)},data:{items:this.data.items,max:this.max,min:this.min},count:c.count}),c.posx&&c.posx.length>0&&c.posx.forEach(function(t){t.props=x({},L,{textAlign:u.a.textAlign.center})}),c.wxSearchStyle){var P=4*this.props.devicePixelRatio;new o.a(r,{wxSearchStyle:!0,hline:{color:b.hline,lineWidth:M.hline,linestyle:"dash",dashGapArray:[P,P],count:3}},y)}else new o.a(r,{border:{color:b.border,lineWidth:M.border},hline:{color:b.hline,lineWidth:M.hline,count:c.hlineCount},vline:{color:b.vline,lineWidth:M.vline,posx:c.posx}},y);if(this.props.fq&&!this.props.crossLineItem){var I={color:this.props.colorProp.tip,font:this.props.textProp.font,baseLine:u.a.baseLine.mid};this.fqWidth=Object(u.d)(r,"前复权",I),Object(u.c)(r,["前","后","不"][this.props.fq-1]+"复权",y.x+this.region.chartAndChipWidth-this.fqWidth,y.y/2,I),this.props.isShowChip||this.drawSeparateLine()}this.getGappedGap(),c.fixedWidth?new s.a(this.ctx,this.data,this.props,y,["close"],this.max,this.min).draw():new n.a({ctx:r,region:x({},y,{height:y.height-this.extraInfoHeight}),drawCallback:function(t){return e.drawCandle(r,t,c,m,v)},data:{items:this.data.items,max:this.max,min:this.min},count:c.count}),this.drawMaxMin(m),new a.a(this.ctx,this.data,this.props,this.region).draw(),this.loadPos.show&&(Object(u.c)(r,d.c,this.loadPos.x,this.loadPos.y,{color:"#3077EC",font:this.props.textProp.font}),"kline-landscape"===this.props.layout&&Object(l.a)(r,0,this.region.chart.y,this.region.chart.x,this.region.chart.y+this.region.chart.height)),w&&new h.a(this.ctx,p.b.x,c.posx,w).draw(),new h.a(this.ctx,p.b.y,function(t,i,e){for(var r,o,n=(t-i)/(e-1),s=[],a=y.y,h=y.height,p=h/(e-1),l=0;l<e;l++){;s.push({text:(r=t-l*n,void 0===(o=c.fixNum||2)&&(o=2),r.toFixed(o)),y:l*p+a,props:{baseLine:0==l?u.a.baseLine.top:u.a.baseLine.btm,color:b.yAxis,font:C.font,textAlign:u.a.textAlign.left}})}return s}(this.max,this.min,c.yAixsCount),y).draw(),"day"===this.props.type&&(this.ctx.save(),this.getZXData(),this.ctx.translate(y.x,y.y+y.height/2),this.props.setting&&this.props.setting.zx&&this.drawAddZXData&&(t=this.drawAddZXData.currItem.time,i=this.drawAddZX(this.drawAddZXData)),this.drawTradePointData.length>0&&this.drawTradePointData.forEach(function(r){r.currItem.time===t&&(r.currItem.addZXShowDown=i),e.drawTradePoint(r)}),this.ctx.restore()),this.drawKlineExtraInfo(),c.setting.gap&&3===v.length&&this.drawGappedGap(v)},getZXData:function(){var t=this,i=this.props,e=this.region.chart;i.fixedWidth&&"day"===i.type&&this.data.items.map(function(r,o){if(r.fh&&r.fh.since_add_zdf){var n={getX:function(t){return e.width/i.count*t},getY:function(i){var r=(t.max+t.min)/2,o=Math.max(Math.abs(t.max-r),Math.abs(t.min-r));return 0===o?NaN:-(i-r)/o*(e.height/2)}};t.drawAddZXData={index:o,curr:n,currItem:r,x:n.getX(o)+i.itemWidth/2,topY:n.getY(r.high),bottomY:n.getY(r.low)}}})},drawMaxMin:function(t){var i,e,r,o,n,s,a,h=this.ctx,p=this.props.maxminDist,l=this.region.chart;for(l=x({},l,{height:l.height-this.extraInfoHeight}),h.save(),h.translate(l.x,l.y+l.height/2),i=0;i<t.length;i++)t[i]&&((o=t[i].text).toFixed&&(o=o.toFixed(this.props.fixNum||2)),n=t[i].rightRegion,e=Object(u.d)(h,o,{font:this.props.textProp.font}),s=t[i].x,a=t[i].y,p=this.props.maxminDist,s<16*this.props.devicePixelRatio?p*=s<5*this.props.devicePixelRatio?4:2.5:p=this.props.maxminDist,r=n?s-p-e:s+p,Object(u.c)(h,o,Math.max(r,0),a,{baseLine:t[i].baseline,color:this.props.colorProp.maxMin,font:this.props.textProp.font}),Object(c.c)(h,s,a,n?r+e:r,a,this.props.colorProp.maxMinLine,null,null,null,this.props.lineProp.maxmin));h.restore()},drawCandle:function(t,i,e,r,o){if(i.currItem.forBounce)return/^m\d/.test(this.props.type)&&"已到上市首日"===d.c?void(this.loadPos.show=!1):(this.loadPos.show=!0,void(this.loadPos.x=this.region.chart.x+3*(i.index-d.a+7)*this.props.devicePixelRatio));var n=e.colorProp,s=i.index,a=i.currItem,h=e.itemWidth,p=i.getX(s)+h/2,f=i.getY(a.open),g=i.getY(a.close),x=i.getY(a.low),y=i.getY(a.high),w=Object(l.b)(a.open,a.close,a.preClose,n.rise,n.drop,n.flat);Object(c.a)(t,e,a,p,{openY:f,closeY:g,highY:y,lowY:x},w,w),a.high===this.kmax&&(r[0]={text:this.kmax,x:p,y:y,rightRegion:s>i.length/2,baseline:u.a.baseLine.top}),a.low===this.kmin&&(r[1]={text:this.kmin,x:p,y:x,rightRegion:s>i.length/2,baseline:u.a.baseLine.btm}),e.setting.magicNine&&a.magicNine&&this.drawMagicNine({currItem:a,x:p,topY:y,bottomY:x}),e.setting.tradeSecret&&a.tradeSecret&&this.drawTradeSecret({currItem:a,x:p,bottomY:x}),a.fh&&a.fh.since_add_zdf&&(this.drawAddZXData={index:s,curr:i,currItem:a,x:p,topY:y,bottomY:x});var m=d.d.get(a.time);if(m&&(a.tradeType=m,this.drawTradePointData.push({currItem:a,x:p,topY:y,bottomY:x})),5===this.gapInfo.length&&3!==o.length&&(this.gapInfo[0]===s&&(o[0]=p),this.gapInfo[1]===s&&o.push(this.gapInfo[2]===this.data.items[s].high?y:x),this.gapInfo[3]===s&&o.push(this.gapInfo[4]===this.data.items[s].high?y:x),3===o.length)){var v=Math.min(o[1],o[2]),b=Math.abs(o[1]-o[2]);o[1]=v,o[2]=b}},drawMagicNine:function(t){var i=t.currItem,e=t.x,r=t.topY,o=t.bottomY,n=this.ctx,s=+i.magicNine,a=8*this.props.devicePixelRatio,h=10*this.props.devicePixelRatio,p=4*this.props.devicePixelRatio,l={color:"#FF891E",font:"500 "+9*this.props.devicePixelRatio+"px Arial",textAlign:u.a.textAlign.center,baseLine:u.a.baseLine.mid};if(s>0){var d=9===s;Object(c.f)(n,e-a/2,r-p-h,a,h,2,d?"rgba(28, 170, 60, 0.5)":"rgba(255, 137, 30, 0.5)",d?"rgba(28, 170, 60, 0.1)":"rgba(255, 137, 30, 0.1)",1),l.color=d?"#1CAA3C":"#FF891E",Object(u.c)(n,s,e,r-p-h/2,l)}else{var d=-9===s;Object(c.f)(n,e-a/2,o+p,a,h,2,d?"rgba(230, 53, 53, 0.5)":"rgba(255, 137, 30, 0.5)",d?"rgba(230, 53, 53, 0.1)":"rgba(255, 137, 30, 0.1)",1),l.color=d?"#E63535":"#FF891E",Object(u.c)(n,-s,e,o+p+h/2,l)}},drawTradeSecret:function(t){var i=t.currItem,e=t.x,r=t.bottomY,o=this.ctx,n=12*this.props.devicePixelRatio,s=12*this.props.devicePixelRatio,a=4*this.props.devicePixelRatio;this.props.setting.magicNine&&i.magicNine<0&&(r+=14*this.props.devicePixelRatio),o.drawImage(A,e-n/2,r+a,n,s)},drawAddZX:function(t){var i,e,r=t.index,o=t.curr,n=t.currItem,s=t.x,a=t.topY,h=t.bottomY,p=this.ctx,l=this.region.chart.width,d=this.region.chart.height,f=this.region.chart.y,g=f+d,x=30*this.props.devicePixelRatio,y=x,w=33*this.props.devicePixelRatio,m=14*this.props.devicePixelRatio,v=1*this.props.devicePixelRatio,b=2*this.props.devicePixelRatio,M=3.5*this.props.devicePixelRatio,C={color:"#3077EC",font:9*this.props.devicePixelRatio+"px Arial",textAlign:u.a.textAlign.center,baseLine:u.a.baseLine.mid},A=0;s-w/2<this.props.devicePixelRatio?A=this.props.devicePixelRatio-(s-w/2):s+w/2>l-this.props.devicePixelRatio&&(A=l-this.props.devicePixelRatio-(s+w/2)),0===r?(i=this.data.items[r+1]||n,e=this.data.items[r+2]||n):r===this.data.items.length-1?(i=this.data.items[r-2]||n,e=this.data.items[r-1]||n):(i=this.data.items[r-1]||n,e=this.data.items[r+1]||n);var L=!0;if(this.props.setting.magicNine&&(n.magicNine>0?a-=14*this.props.devicePixelRatio:n.magicNine<0&&(h+=14*this.props.devicePixelRatio)),this.props.setting.tradeSecret&&n.tradeSecret&&(h+=16*this.props.devicePixelRatio),h+x+m+d/2<g-10*this.props.devicePixelRatio){var P=o.getY(i.low),I=o.getY(e.low);x=Math.max(P-h+10*this.props.devicePixelRatio,y,I-h+10*this.props.devicePixelRatio),L=!0}if(h+x+m+d/2>=g-10*this.props.devicePixelRatio){var j=o.getY(i.high),S=o.getY(e.high);x=Math.min(x=Math.max(a-j+10*this.props.devicePixelRatio,y,a-S+10*this.props.devicePixelRatio),a+d/2-f-10*this.props.devicePixelRatio),L=!1}var T=Math.round(x/M)-1;if(L){for(var D=h,O=0;O<T;O++)D+=M,Object(c.g)(p,s-v/2,D-b/2,v,b,"#3077EC","#3077EC");Object(c.f)(p,s-w/2+A,h+x,w,m,2*this.props.devicePixelRatio,"#3077EC"),Object(u.c)(p,"加自选",s+A,h+x+m/2,C)}else{for(var D=a,O=0;O<T;O++)D-=M,Object(c.g)(p,s-v/2,D-b/2,v,b,"#3077EC","#3077EC");Object(c.f)(p,s-w/2+A,a-x-m,w,m,2*this.props.devicePixelRatio,"#3077EC"),Object(u.c)(p,"加自选",s+A,a-x-m/2,C)}return L},drawTradePoint:function(t){var i=t.currItem,e=t.x,r=t.topY,o=t.bottomY,n=this.ctx,s=this.region.chart.height,a=this.region.chart.y,h=4*this.props.devicePixelRatio,p=1.5*this.props.devicePixelRatio,u=8*this.props.devicePixelRatio,l=12*this.props.devicePixelRatio,d=7*this.props.devicePixelRatio,f=1*this.props.devicePixelRatio,g=1*this.props.devicePixelRatio,x=Math.round(d/3),C=h+2*p+d+l,A={B:"#3077EC",S:"#FF891E",T:"#B71EFF"}[i.tradeType],L={B:!0,S:!1,T:!0}[i.tradeType];if("boolean"==typeof i.addZXShowDown&&(L=!i.addZXShowDown),this.props.setting.magicNine&&(i.magicNine>0?r-=14*this.props.devicePixelRatio:i.magicNine<0&&(o+=14*this.props.devicePixelRatio)),this.props.setting.tradeSecret&&i.tradeSecret&&(o+=16*this.props.devicePixelRatio),L?o+C+s/2>=a+s-10*this.props.devicePixelRatio&&(L=!1):r-C+s/2<=a+10*this.props.devicePixelRatio&&(L=!0),L){Object(c.b)(n,e,o+h+p,p,A,A);for(var P=o+h+2*p,I=0;I<3;I++)P+=x,Object(c.g)(n,e-f/2,P-g/2,f,g,A,A);var j={B:y,S:m,T:b}[i.tradeType];n.drawImage(j,e-u/2,o+C-l,u,l)}else{Object(c.b)(n,e,r-h-p,p,A,A);for(var P=r-h-2*p,I=0;I<3;I++)P-=x,Object(c.g)(n,e-f/2,P-g/2,f,g,A,A);var j={B:w,S:v,T:M}[i.tradeType];n.drawImage(j,e-u/2,r-C,u,l)}},drawKlineExtraInfo:function(){var t=this,i=this.region.chart,e=this.props,r=e.type,o=e.setting,n=2*this.props.devicePixelRatio;"day"===r&&(void 0===o?{}:o).ds&&this.data.items.forEach(function(e,r){if(e.fh&&(e.fh.FHcontent||e.fh.HGcontent)){t.ctx.save();var o=t.props.count,s=t.props.itemWidth,a=i.width/o*r+s/2+i.x,h=i.y+i.height-n-n;Object(c.b)(t.ctx,a,h,n,"#FF891E","#FF891E"),t.ctx.restore()}})},getGappedGap:function(){for(var t=this.data.items.length-2;t>=0;t--){this.gapInfo=[];var i=this.data.items[t],e=this.data.items[t+1];if(this.gapInfo[0]=t,i.high<e.low?(this.gapInfo[1]=t,this.gapInfo[2]=i.high,this.gapInfo[3]=t+1,this.gapInfo[4]=e.low,this.dealGappedGapCutoff()):i.low>e.high&&(this.gapInfo[1]=t+1,this.gapInfo[2]=e.high,this.gapInfo[3]=t,this.gapInfo[4]=i.low,this.dealGappedGapCutoff()),5===this.gapInfo.length)break}},dealGappedGapCutoff:function(){for(var t=this.gapInfo[0]+2;t<this.data.items.length;t++){var i=this.data.items[t],e=this.gapInfo[4],r=this.gapInfo[2];if(i.high>=e&&i.low<=r)return void(this.gapInfo=[]);i.high<=r||i.low>=e||(i.high<e&&i.high>r?(this.gapInfo[1]=t,this.gapInfo[2]=i.high):i.low<e&&i.low>r&&(this.gapInfo[3]=t,this.gapInfo[4]=i.low))}},drawGappedGap:function(t){var i=this.region.chart,e="rgba(124, 156, 198, 0.4)";this.ctx.save(),this.ctx.translate(i.x,i.y+i.height/2);var r=t[0],o=t[1],n=this.region.chart.width-t[0],s=t[2];Object(c.g)(this.ctx,r,o,n,s,e,e),this.ctx.restore()},drawSeparateLine:function(){var t=this.ctx;t.save();var i=this.region.chart,e=i.x+i.width-this.fqWidth-2*this.props.devicePixelRatio,r=i.y,o=this.props.colorProp.border,n=1*this.props.devicePixelRatio;Object(c.c)(t,e,0,e,r,o,null,null,null,n),t.restore()}}},function(t,i){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADYAAAAkCAYAAADCW8lNAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAANqADAAQAAAABAAAAJAAAAADpY+pGAAACaElEQVRYCe3Yy0vcQBwH8N8krqBQKlZ8ILQ992rpyUt7EdcV7yJdaWtd1H+n4qulK4t38YUXvXiS9urNHrz4oA+XQovVZJxvcNIYN5vsZsYoZA47k5nJL7/PBJLJEqUlXYF0BXSuAPMHn/+82mMz6iNiPxpaHi69Her97Z9zF44/Lu88uDgtDxPxRwanzbHR3FdvXtdgM4trBbLtD2KCgUmM0UHGNPvfjGT3vCcl3f5UWn92blkbnNPjq1xsMozJwuuBGZmbC5td+dLMfx5+J05NctCpGZ00mubLu4ID6p9lbYs82315/mWtXW3jg8//oN+5M2hkyidPb6AwIAKcX1hbCIjDJItzp0QuN1BIStwQ49fRE5mfC+tspX1GrCwHvDUn6kgaJ1HIxZubbCP37jbjmzx2Ydls9owzmpID/jpJXBgKuSJ3GGTeLgwdhXyuZDA2KQf9dRK4KCjkjNy9+boPD2/nXHF1wuYcT8eKRZx0nGkwX+l+oERFvc/npv2JVoRhUtK4OCjkHwhLEhcXFQpLAqcCFQl2mzhVqMiw28CpRNUE04lTjaoZpgOnA1UXTCVOF6pumAqcTlQsWBycbhRyu7ZXREctBVuZsL0lvp3miuv9Mi7a6AvapWMeYlbaJskYUeqqO48oATAnbPvlxBEfrE7t/0B0Ov//qEAhmhIYAkXCYWKVogqFSyiDIVgcnEqUcli9ONUoLbBacTpQ2mBRcbpQWmFhOJ0oXDvWewwBqhW8i8S/RxPij1dLzkMbfXHfUzJeUK30qRh0kYXiygub2AjGDeKld/nB3aC5aX+6AukK3I8VuASME6Os4wz5UQAAAABJRU5ErkJggg=="},function(t,i){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADYAAAAkCAYAAADCW8lNAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAANqADAAQAAAABAAAAJAAAAADpY+pGAAACT0lEQVRYCe3YzUsbQRQA8DcTpaeSSiHppZd60qviqRd7EZYNvZdoQq1flV78V4ra+lFMDb0Xl4AXvXgSvLbXahFUrCiCRdSZzoS+ZZOuyX7MSwLdhfB2PjLzfjObbLIAyZGsQLIClCvAKAfHsVdKG0MCWF6XOcjym0JuF9uoIjns45ozA0y+lxJSGsEY3IFk76aK9iIVqjoP5eBLJeetkHLebw7O2OxkwV7wazNRR7ZjjVCYOCWOBBYERY0zDguDosQZhUVBUeGMweKgKHBGYIFQDE6qAAkZhPhFU18o3G/wMHVNUQrEWcqaLuSy+qXPAZE+E+nbgx7TpylUVawda4ZSgx9zzoYnxuzv3qyWPzt9QshtCZD11nvP4+5cZFhUFCZPjYsEi4tqBS40zBSKGhcKZhpFiQsMo0JR4QLBqFEUuKawVqFM4xrCWo0yibsX1i4U4j6VK/03t3dbUW/ivrAgqO6u1IvXeesbJkIR4+D+gXUKChcqKq4G9qHk5EHKdRy0PqrOvr/96vuZLgfBqadEo9MFu4xzu7BKpfLg54k4liDT2OiNGtWKy887p/e8GY4Bu3ia4VnLsq71+9y/LUdn0NupKJ2o/jzrhdULrMv1h8798FQ8w3oXdpPO/FD/k35jA8Z27xTmoWNDnMpd9DzZx/4ubCo3eAWMz6kGgY3q4eZBOy8/zMMbXZzKzVOvHjTzuarhb6XakNpjec0ZEAxG1DPbX12P0l/GXz6/rO3RGaXVrzsPb88vXgHIx1zC5kTR3uuMzJIskhX4P1bgD2JYkubzKI6kAAAAAElFTkSuQmCC"},function(t,i){t.exports="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDIxLjAuMCwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDYuMDAgQnVpbGQgMCkgIC0tPgo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IuWbvuWxgl8xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB4PSIwcHgiIHk9IjBweCIKCSB2aWV3Qm94PSIwIDAgMTMgMjAiIHN0eWxlPSJlbmFibGUtYmFja2dyb3VuZDpuZXcgMCAwIDEzIDIwOyIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+CjxzdHlsZSB0eXBlPSJ0ZXh0L2NzcyI+Cgkuc3Qwe2ZpbGw6I0I3MUVGRjt9Cgkuc3Qxe2ZpbGw6I0ZGRkZGRjt9Cjwvc3R5bGU+Cjx0aXRsZT7nvJbnu4QgMTXlpIfku70gMjwvdGl0bGU+CjxnIGlkPSJL57q/5Zu+5aKe5Yqg5Lmw5Y2W54K55qCH6K6wIj4KCTxnIGlkPSLnvJbnu4QtMTXlpIfku70tMiI+CgkJPHBhdGggaWQ9IuefqeW9oiIgY2xhc3M9InN0MCIgZD0iTTAsNS41bDAsMTMuMmMwLDAuNiwwLjQsMSwxLDFoMTFjMC42LDAsMS0wLjQsMS0xVjUuNWMwLTAuMy0wLjEtMC42LTAuNC0wLjhMNy4xLDAuMgoJCQljLTAuNC0wLjMtMC45LTAuMy0xLjMsMEwwLjQsNC43QzAuMSw0LjksMCw1LjIsMCw1LjV6Ii8+CgkJPHBvbHlnb24gaWQ9IlQiIGNsYXNzPSJzdDEiIHBvaW50cz0iNywxNS43IDcsNy43IDExLDcuNyAxMSw2LjcgMiw2LjcgMiw3LjcgNiw3LjcgNiwxNS43IAkJIi8+Cgk8L2c+CjwvZz4KPC9zdmc+Cg=="},function(t,i){t.exports="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDIxLjAuMCwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDYuMDAgQnVpbGQgMCkgIC0tPgo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IuWbvuWxgl8xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB4PSIwcHgiIHk9IjBweCIKCSB2aWV3Qm94PSIwIDAgMTMgMjAiIHN0eWxlPSJlbmFibGUtYmFja2dyb3VuZDpuZXcgMCAwIDEzIDIwOyIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+CjxzdHlsZSB0eXBlPSJ0ZXh0L2NzcyI+Cgkuc3Qwe2ZpbGw6I0I3MUVGRjt9Cgkuc3Qxe2ZpbGw6I0ZGRkZGRjt9Cjwvc3R5bGU+Cjx0aXRsZT7nvJbnu4QgMTXlpIfku70gMjwvdGl0bGU+CjxnIGlkPSJL57q/5Zu+5aKe5Yqg5Lmw5Y2W54K55qCH6K6wIj4KCTxnIGlkPSLnvJbnu4QtMTXlpIfku70tMiI+CgkJPHBhdGggaWQ9IuefqeW9oiIgY2xhc3M9InN0MCIgZD0iTTAuNCwxNWw1LjUsNC41YzAuNCwwLjMsMC45LDAuMywxLjMsMGw1LjUtNC41YzAuMi0wLjIsMC40LTAuNSwwLjQtMC44VjFjMC0wLjYtMC40LTEtMS0xSDEKCQkJQzAuNCwwLDAsMC40LDAsMXYxMy4yQzAsMTQuNSwwLjEsMTQuOCwwLjQsMTV6Ii8+CgkJPHBvbHlnb24gaWQ9IlQiIGNsYXNzPSJzdDEiIHBvaW50cz0iNywxMyA3LDUgMTEsNSAxMSw0IDIsNCAyLDUgNiw1IDYsMTMgCQkiLz4KCTwvZz4KPC9nPgo8L3N2Zz4K"},function(t,i){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAC0AAAAtCAYAAAA6GuKaAAAACXBIWXMAABCcAAAQnAEmzTo0AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAUBSURBVHgB7Vg9TFxHEJ7ZA9tgYSFBEX5DGlKGiFSuQG7jKJUjK0WoIKlsi1iEpMhVcUAmThs3TgWSGxfQWqZzhUJK0yQGDCmMhRLlsIHbyey+u7f73u7eu/dASmE+6e7e252Z/d68mdnZAzjDGYJAOCForrsDqH0MkIb5dpRNdvBvjyWyy58dltwAQWvwL25geWsHToDCpOnu4ChUYTIimlt7lR9gBW9vr0IB5CZ9MrIOdqCl+jVOv9jIo9Q0aR0G0MZk8bpnek1/pNyAN8nXT+WBXjiPPSD0Q9Y/aetLAAf3ceblP3BapOneuz1wSPchEavECyAvVllqdrHY1hFNAsHV1NQOHNCXzcR7JmkvYeSYrMBPJ0moAPmmiGM+wsq7wK9xawlOCTQ3wOGG09ZQJnEMG1Mx3L4INuGqnMJv8yVNM6Af+oahJH6plUsFJlz5PBR2LWFTKumskMggrBdGcRkEqnrdFU9I3AaUL5nUOt5+/tSnq+yy/pRFvLe2/oJX3ktggQkclxbtkVBI0PzQCJC8liAaxh4/1HKIvBMqEqZwdnMtLSd8ykzYfsKVIOE7AxNM+KsmCYOWkzRBPw5e801G63CSG3aTXjmXSP8YCHE3HjigT3xJwYSna6FQH6mwucccR+s482I7Hp3r6wcojfDVZZ63woY2cHbLef1R8svFOL493nY9LfBj627FS1h5yiZM8BgOjr/Dmc0Vm7CCulfjnBMLWs6sM+zzON56vhvV/5ih423hPCXgWDxwoMtdkrDyHMIVa2gZv9l8iOXdCjQAJ9ueklPyZhCu6AR2ULHDcTSqZAHS8Lr6vnW35q2VUnxmrump9mIOaHmidTMgrroyutSZkFBdZJC0QLsvcLKWvdKVDAu5DEUg5UNrzWFt14VZP2p7jUpSziIkXdLQCh/E1+wt9cqhALQeJ2I8UFKJmoJqvmKFZJOVTsTe+OoN7YJjqDRgDGF4o5nvG+Eke6B+QzLQIn8ztmjAmb9QemYMYoOYtnZAfzxTd3xdrW6Bj/Cdd4aASo/YOxP8kE+o3NkJXsHWV8YutKWnoyri8vKRbgwEUyHOuwtFFlvL/D1Uk++Eix3fe+WOj+xq47cVQE7SaBY6LnWnp7WXAb9IDEq8qTesNFqh32LxCnIgTTp+JfrE4UhbISHJjVdx7hF4Vym53q4KU15RPktPR3uGy0ubS4maVvAiuUX/CH43mlyqyj3tsabqQwACiUdjNDcYvwGthzhi2XWTukq203YakLbKnBQO6WSpor36LqiTTaA/dg3K9aSM9Cgql6HSWbXLHCUeKtlPE5cx00J5T9uqydFb76E0PcaFSzegnnxhDEHbpZsA++WIlFTNUlfCThJmffV/ic3BvqmdVp6Ykcp41qFVJ5849wc0A4J9oMMPcfavPxuKRcc8s9umOs1EeDh7PrRfhyxEJa45qBIozj/IlFMH3liHVtN7hlvyJNidndPM2OCTxqdOicsEJ6WvBCZE4KP4GslpyBzStYa75m3rFOEF3oMiECLL2/XQWPP9dRY+jXOdbnSMp/nBG+yRn6E4bnGbGtRX+RXKp0J/QNaSTyXsEBSFSsrXf7+H5f19yIl823isZfUXRdGoL8lUzYlcJa4ZSDmOs/n+8s3v6VB/URS+viRLJY9w4/6iKJJ9STPI5+ns/qIoynmEiyXi/4yWXNJSjPPXBJw6xK9whjO8xfgPdkAde714L78AAAAASUVORK5CYII="},function(t,i,e){"use strict";var r=e(19),o=e(1),n=e(9),s=e(7),a=e(0),h=this&&this.__assign||Object.assign||function(t){for(var i,e=1,r=arguments.length;e<r;e++)for(var o in i=arguments[e],i)Object.prototype.hasOwnProperty.call(i,o)&&(t[o]=i[o]);return t},c=function(){function t(t,i,e,r){this.ctx=t,this.data=i,this.props=e;var o=r.getChart(),n="kline-portrait"===this.props.layout?10:0;this.region={width:r.chipRegion.width-n,height:r.chipRegion.height,x:o.x+o.width+n,y:o.y,chart:{x:o.x+o.width+n,y:o.y,width:r.chipRegion.width-n,height:o.height}}}return t.prototype.draw=function(){this.drawGrid(),this.drawScale(),this.drawLinearShapes(),this.drawTipPrice(),this.drawText(),this.drawLegend()},t.prototype.drawGrid=function(){var t=this.props,i=t.colorProp,e=t.lineProp;new r.a(this.ctx,{border:{color:i.border,lineWidth:e.border},hline:{color:i.border,lineWidth:e.border},vline:{color:i.border,lineWidth:e.border,count:1,linestyle:"dash",dashGapArray:[3,3]}},this.region.chart)},t.prototype.getChartMaxMin=function(){if(this.data.maxMin)return{max:Math.max(this.data.maxMin.kline.max,this.data.maxMin.ma.max),min:Math.min(this.data.maxMin.kline.min,this.data.maxMin.ma.min)}},t.prototype.drawScale=function(){if(this.data.maxMin){var t=this.getChartMaxMin(),i=t.max,e=t.min,r={font:this.props.textProp.font,textAlign:a.a.textAlign.right,color:this.props.colorProp.yAxis};new n.a(this.ctx,s.b.y,[{text:i.toFixed(2),x:this.region.chart.x+this.region.chart.width-4*this.props.devicePixelRatio,y:this.region.chart.y+20,props:h({},r,{baseLine:a.a.baseLine.mid})},{text:e.toFixed(2),x:this.region.chart.x+this.region.chart.width-4*this.props.devicePixelRatio,y:this.region.chart.y+this.region.chart.height,props:h({},r,{baseLine:a.a.baseLine.btm})}],this.region.chart).draw()}},t.prototype.drawLegend=function(){var t=this;if(!this.data.chipData.crossPrice){var i=this.props.colorProp,e=[{color:i.drop,text:"套牢"},{color:i.rise,text:"获利"},{color:i.chip.blue,text:"平均"}];e.map(function(r,n){var s=r.color,h=r.text,c=t.region.x+t.region.width/e.length*n+10,p=t.region.chart.y+t.region.chart.height-4;Object(o.g)(t.ctx,c,p+9*t.props.devicePixelRatio,6*t.props.devicePixelRatio,6*t.props.devicePixelRatio,s,s,1),Object(a.c)(t.ctx,h,c+9*t.props.devicePixelRatio,p+12*t.props.devicePixelRatio,{color:i.chip.text,font:"500 "+11*t.props.devicePixelRatio+"px Arial",textAlign:a.a.textAlign.left,baseLine:a.a.baseLine.mid})})}},t.prototype.calcLineList=function(){var t=this.data.chipData.chip,i=this.getChartMaxMin(),e=i.max,r=i.min,o=[];return t.list.map(function(i,n){var s=+t.prices[n];s>=r&&s<=e&&o.push({data:i,index:n})}),o},t.prototype.drawLinearShapes=function(){var t=this,i=this.props,e=i.colorProp,r=i.lineProp,n=this.data.chipData,s=n.chip,a=n.currentPrice,h=n.avgPrice,c=this.calcLineList(),p=this.getChartMaxMin(),u=p.min,l=p.max;c.map(function(i){var n=s.prices[i.index]<a?e.rise:e.drop,c=t.region.chart.width*(i.data/s.maxValue),p=t.region.y+t.region.chart.height*(1-Math.abs(s.prices[i.index]-u)/Math.abs(l-u))+r.border/2,d={x:t.region.x+2,y:p},f={x:t.region.x+2+c,y:p};s.prices[i.index]==h?Object(o.c)(t.ctx,d.x,d.y,d.x+t.region.width,f.y,e.chip.blue,"dash",null,[3,3],r.border):Object(o.c)(t.ctx,d.x,d.y,f.x,f.y,n,null,null,null,r.border)})},t.prototype.drawTipPrice=function(){var t=this.props.colorProp,i=this.data.chipData,e=i.chip,r=i.avgPrice,n={color:"#fff",font:"400 "+9*this.props.devicePixelRatio+"px Arial",textAlign:a.a.textAlign.center,baseLine:a.a.baseLine.mid},s=this.calcLineList(),h=this.getChartMaxMin(),c=h.min,p=h.max,u=e.prices.findIndex(function(t){return t==r}),l=s.findIndex(function(t){return t.data===e.list[u]});if(Array.isArray(s)&&l>=0){var d=Object(a.d)(this.ctx,r.toFixed(2),n),f={x:this.region.x+this.region.width-(d+10*this.props.devicePixelRatio),y:this.region.y+this.region.chart.height*(1-Math.abs(e.prices[u]-c)/Math.abs(p-c))-14*this.props.devicePixelRatio};Object(o.g)(this.ctx,f.x,f.y,d+10*this.props.devicePixelRatio,15*this.props.devicePixelRatio,t.chip.lightBlue,t.chip.lightBlue,1),Object(a.c)(this.ctx,r.toFixed(2),f.x+(d+10*this.props.devicePixelRatio)/2,f.y+7*this.props.devicePixelRatio,{color:"#fff",font:"400 "+9*this.props.devicePixelRatio+"px Arial",textAlign:a.a.textAlign.center,baseLine:a.a.baseLine.mid})}},t.prototype.getPortraitTexts=function(){var t=this.props.devicePixelRatio,i=5*t,e=this.props.colorProp,r=this.data.chipData,o=r.avgPrice,n=r.profitPercent,s=r.p90,a=r.p70,h=r.showType,c="p90"===(void 0===h?"p90":h),p=this.region.height-this.region.chart.height-this.region.chart.y,u=this.region.chart.height+this.region.chart.y+p/2,l=isNaN(n.replace("%",""))?0:parseFloat(n.replace("%",""))/100,d=[];return 350>p?(d.push({x:this.region.x+3*t,y:u-11*t,value:"获利比例",fontSize:11,color:e.chip.subTitle,alwayShow:!0,showType:"kline-portrait"}),d.push({x:this.region.x+53*t,y:u-11*t,width:this.region.width-55*t,height:13*t,radius:2,color:e.drop,alwayShow:!0,ctrlType:"radiusRect",showType:"kline-portrait"}),d.push({x:this.region.x+53*t,y:u-11*t,width:(this.region.width-55*t)*l,height:13*t,radius:{borderTopLeftRadius:2,borderBottomLeftRadius:2},color:e.rise,alwayShow:!1,ctrlType:"radiusRect",showType:"kline-portrait"}),d.push({x:this.region.x+55*t,y:u-11*t,value:n,color:e.chip.white,fontWeight:600,fontSize:11,alwayShow:!1,showType:"kline-portrait"}),d.push({x:this.region.x+3*t,y:u+10*t,value:"平均成本",fontSize:11,color:e.chip.subTitle,alwayShow:!1,showType:"kline-portrait"}),d.push({x:this.region.x+55*t,y:u+10*t,value:isNaN(o)?"--":o.toFixed(2),fontSize:11,color:e.chip.subTitle,fontWeight:600,alwayShow:!1,showType:"kline-portrait"})):(d.push({x:this.region.x+3*t,y:u-24*t-2*i,value:"获利比例",fontSize:11,color:e.chip.subTitle,alwayShow:!0,showType:"kline-portrait"}),d.push({x:this.region.x+53*t,y:u-24*t-2*i,width:this.region.width-55*t,height:13*t,radius:2,color:e.drop,alwayShow:!0,ctrlType:"radiusRect",showType:"kline-portrait"}),d.push({x:this.region.x+53*t,y:u-24*t-2*i,width:(this.region.width-55*t)*l,height:13*t,radius:{borderTopLeftRadius:2,borderBottomLeftRadius:2},color:e.rise,alwayShow:!1,ctrlType:"radiusRect",showType:"kline-portrait"}),d.push({x:this.region.x+55*t,y:u-24*t-2*i,value:n,color:e.chip.white,fontWeight:600,fontSize:11,alwayShow:!1,showType:"kline-portrait"}),d.push({x:this.region.x+3*t,y:u-12*t-i,value:"平均成本",fontSize:11,color:e.chip.subTitle,alwayShow:!1,showType:"kline-portrait"}),d.push({x:this.region.x+55*t,y:u-12*t-i,value:isNaN(o)?"--":o.toFixed(2),fontSize:11,color:e.chip.subTitle,fontWeight:600,alwayShow:!1,showType:"kline-portrait"}),d.push({x:this.region.x+2*t,y:u-5,width:(this.region.chart.width-3*t)/2,height:45,radius:{borderTopLeftRadius:4,borderBottomLeftRadius:4},color:e.chip.tabBg,fillColor:c?e.chip.tabBg:e.chip.white,alwayShow:!1,ctrlType:"radiusRect",showType:"kline-portrait"}),d.push({x:this.region.x+2*t+(this.region.chart.width-3*t)/2,y:u-5,width:(this.region.chart.width-3*t)/2,height:45,radius:{borderTopRightRadius:4,borderBottomRightRadius:4},color:e.chip.tabBg,fillColor:c?e.chip.white:e.chip.tabBg,alwayShow:!1,ctrlType:"radiusRect",showType:"kline-portrait"}),d.push({x:this.region.x+Math.min(200,(this.region.width-50)/2)/2-60,y:u,color:c?e.chip.white:e.chip.tabBg,value:"90%筹码",fontSize:11,showType:"kline-portrait"}),d.push({x:this.region.x+3*t+1.5*Math.min(200,(this.region.width-50)/2)-60,y:u,color:c?e.chip.tabBg:e.chip.white,value:"70%筹码",fontSize:11,showType:"kline-portrait"}),d.push({x:this.region.x+3*t,y:u+12*t+i,color:e.chip.subTitle,value:"价格:"+(c?s.price:a.price),fontSize:11,alwayShow:!1,showType:"kline-portrait"}),d.push({x:this.region.x+3*t,y:u+24*t+2*i,value:"集中度:"+(c?s.percent:a.percent),fontSize:11,color:e.chip.subTitle,alwayShow:!1,showType:"kline-portrait"})),d.push({x:this.region.x+3*t,y:this.region.height-12*t,value:this.data.chipTime,color:e.chip.subText,fontSize:11,showType:"kline-portrait",alwayShow:!1}),d},t.prototype.getLandscapeTexts=function(){var t=this.props.colorProp,i=this.data.chipData,e=i.avgPrice,r=i.profitPercent,o=i.p90,n=i.p70,s=i.showType,a="p90"===(void 0===s?"p90":s),h=this.region.height-this.region.chart.height-this.region.chart.y,c=this.region.chart.height+this.region.chart.y+h/2,p=h/5,u=(Math.max(p,30)-30)/2,l=isNaN(r.replace("%",""))?0:parseFloat(r.replace("%",""))/100;return[{x:this.region.x+3*this.props.devicePixelRatio,y:c-1.5*p+u,value:"获利比例",fontSize:11,color:t.chip.subTitle,alwayShow:!0,showType:"kline-landscape"},{x:this.region.x+53*this.props.devicePixelRatio,y:c-1.5*p+u,width:this.region.width-55*this.props.devicePixelRatio,height:13*this.props.devicePixelRatio,radius:2,color:t.drop,alwayShow:!0,ctrlType:"radiusRect",showType:"kline-landscape"},{x:this.region.x+53*this.props.devicePixelRatio,y:c-1.5*p+u,width:(this.region.width-55*this.props.devicePixelRatio)*l,height:13*this.props.devicePixelRatio,radius:{borderTopLeftRadius:2,borderBottomLeftRadius:2},color:t.rise,alwayShow:!1,ctrlType:"radiusRect",showType:"kline-landscape"},{x:this.region.x+55*this.props.devicePixelRatio,y:c-1.5*p+u,value:r,color:t.chip.white,fontWeight:600,fontSize:11,alwayShow:!1,showType:"kline-landscape"},{x:this.region.x+3*this.props.devicePixelRatio,y:c-.5*p+u,value:"平均成本",fontSize:11,color:t.chip.subTitle,alwayShow:!1,showType:"kline-landscape"},{x:this.region.x+55*this.props.devicePixelRatio,y:c-.5*p+u,value:isNaN(e)?"--":e.toFixed(2),fontSize:11,color:t.chip.subTitle,fontWeight:600,alwayShow:!1,showType:"kline-landscape"},{x:this.region.x+3*this.props.devicePixelRatio,y:c+.5*p+3*this.props.devicePixelRatio,color:t.chip.tabBg,fillColor:a?t.chip.tabBg:t.chip.white,width:30*this.props.devicePixelRatio,height:15*this.props.devicePixelRatio,radius:{borderTopLeftRadius:8,borderTopRightRadius:8},ctrlType:"radiusRect",showType:"kline-landscape"},{x:this.region.x+3*this.props.devicePixelRatio,y:c+1.5*p+1*this.props.devicePixelRatio,color:t.chip.tabBg,fillColor:a?t.chip.white:t.chip.tabBg,width:30*this.props.devicePixelRatio,height:15*this.props.devicePixelRatio,radius:{borderBottomLeftRadius:8,borderBottomRightRadius:8},ctrlType:"radiusRect",showType:"kline-landscape"},{x:this.region.x+8*this.props.devicePixelRatio,y:c+p/2+u,color:a?t.chip.white:t.chip.tabBg,value:"90%",fontSize:11,showType:"kline-landscape"},{x:this.region.x+8*this.props.devicePixelRatio,y:c+1.5*p+u,color:a?t.chip.tabBg:t.chip.white,value:"70%",fontSize:11,showType:"kline-landscape"},{x:this.region.x+38*this.props.devicePixelRatio,y:c+.5*p+u,color:t.chip.subTitle,value:"价格:"+(a?o.price:n.price),fontSize:11,alwayShow:!1,showType:"kline-landscape"},{x:this.region.x+38*this.props.devicePixelRatio,y:c+1.5*p+u,value:"集中度:"+(a?o.percent:n.percent),fontSize:11,color:t.chip.subTitle,alwayShow:!1,showType:"kline-landscape"}]},t.prototype.drawText=function(){var t=this,i=this.props.colorProp,e=!this.data.chipData.crossPrice,r="kline-landscape"===this.props.layout?this.getLandscapeTexts():this.getPortraitTexts();if(!e){var n=+this.data.chipData.crossPrice>1e4?1:2;r.push({x:this.region.x+3*this.props.devicePixelRatio,y:this.region.chart.y+this.region.chart.height+15,color:i.chip.subTitle,fontSize:11,value:this.data.chipData.crossPrice.toFixed(n)+"处获利："+(100*this.data.chipData.crossProfitPercent).toFixed(2)+"%",alwayShow:!0})}r.map(function(i){var e=i.value,r=i.x,n=i.y,s=i.fontSize,h=i.fontWeight,c=i.color,p=i.ctrlType;if(i.alwayShow||i.showType===t.props.layout)switch(void 0===p?"text":p){case"radiusRect":Object(o.f)(t.ctx,r,n,i.width,i.height,i.radius,c,i.fillColor||c,1*t.props.devicePixelRatio);break;case"rect":Object(o.g)(t.ctx,r,n,i.width,i.height,c,c,1);break;default:Object(a.c)(t.ctx,e,r,n,{color:c,font:(void 0===h?400:h)+" "+(void 0===s?11:s)*t.props.devicePixelRatio+"px Arial",textAlign:a.a.textAlign.left,baseLine:a.a.baseLine.top})}})},t}();i.a=c},function(t,i,e){"use strict";var r=e(35),o=r.a.red_e63,n=r.a.green_2db,s=r.a.gray_929,a=r.a.gray_b2b,h=r.a.blue_007,c=r.a.yellow_ff8,p=r.a.purple_d90;i.a={rise:o,drop:n,flat:s,border:"#E9EBF0",vline:"#E9EBF0",hline:"#E9EBF0",xAxis:"#98A0B3",yAxis:"#98A0B3",tip:"#7A8499",maxMin:a,maxMinLine:a,defaultGray:"#7A8499",button:{bg:"#F5F6FA",text:"#262E40",tri:"#475166"},macd:{dif:c,dea:h,macd:r.a.purple_9f2},dmi:{pdi:r.a.gray_8,mdi:h,adx:p,adxr:n},wr:{wr1:c,wr2:h},boll:{upper:n,mid:c,lower:p,ochl:r.a.blue_428},kdj:{k:c,d:h,j:p},obv:c,rsi:{rsi1:c,rsi2:h,rsi3:p},sar:{sar:h,low:h,high:h},cci:h,bias:{bias1:c,bias2:h,bias3:p},bbi:c,trix:{trix:c,trma:h},ene:{ene:c,upper:h,lower:p},vr:{vr:c,vrma:h},arbr:{ar:c,br:h},psy:{psy:c,psyma:h},dma:{ddd:c,ama:h},dpo:{dpo:c,dpoma:h},rally:h,ma:["#D800FE","#DBC000","#00B4FE","#8175f3","#B8A89F","#F1931D","#76A9FF","#FF6D5E","#34C27C","#FF76B0"],ema:["#D800FE","#DBC000","#00B4FE","#8175f3","#B8A89F","#F1931D","#76A9FF","#FF6D5E","#34C27C","#FF76B0"],emaOchl:r.a.blue_428,crossLine:"#262E40",chip:{text:"#262E40",subText:r.a.gray_98a,subTitle:"#475166",blue:r.a.blue_307,lightBlue:"rgba(0, 122, 255, 0.5)",tabBg:r.a.gray_e9e,white:r.a.white},close:"#0B9BFF"}},function(t,i,e){"use strict";var r=e(35),o=r.a.yellow_ff8,n=r.a.blue_007,s=r.a.purple_d90;i.a={rise:r.a.red_e63,drop:r.a.green_2db,flat:r.a.gray_b2b,transparent:r.a.transparent,border:"#E9EBF0",vline:"#E9EBF0",hline:"#E9EBF0",mline:"#98A0B3",xAxis:"#98A0B3",yAxis:"#98A0B3",tip:"#7A8499",defaultGray:"#7A8499",chart:{priceLine:"#3077EC",avgPriceLine:"#CCB714",iopvLine:"#E085DC",shinePoint:"rgb(51, 146, 255)",fill:r.a.trans,phRectFill:r.a.blue_9ff,phText:r.a.gray_e9e},button:{bg:"#F5F6FA",text:"#262E40",tri:"#475166"},macd:{dif:o,dea:n,macd:s},rsi:{rsi1:o,rsi2:n,rsi3:s},crossLine:"#262E40"}},function(t,i,e){"use strict";var r=e(35),o=r.a.red_e63,n=r.a.green_2db,s=r.a.gray_929,a=r.a.gray_b2b,h=r.a.blue_007,c=r.a.yellow_ff8,p=r.a.purple_d90;i.a={rise:o,drop:n,flat:s,border:"#191E27",vline:"#191E27",hline:"#191E27",xAxis:"#69738C",yAxis:"#69738C",tip:"#7A8499",maxMin:a,maxMinLine:a,defaultGray:"#7A8499",button:{bg:"#171D28",text:"#F0F1F5",tri:"#F0F1F5"},macd:{dif:c,dea:h,macd:r.a.purple_9f2},dmi:{pdi:r.a.gray_8,mdi:h,adx:p,adxr:n},wr:{wr1:c,wr2:h},boll:{upper:n,mid:c,lower:p,ochl:r.a.blue_428},kdj:{k:c,d:h,j:p},obv:c,rsi:{rsi1:c,rsi2:h,rsi3:p},sar:{sar:h,low:h,high:h},cci:h,bias:{bias1:c,bias2:h,bias3:p},bbi:c,trix:{trix:c,trma:h},ene:{ene:c,upper:h,lower:p},vr:{vr:c,vrma:h},arbr:{ar:c,br:h},psy:{psy:c,psyma:h},dma:{ddd:c,ama:h},dpo:{dpo:c,dpoma:h},rally:h,ma:["#D800FE","#DBC000","#00B4FE","#8175f3","#B8A89F","#F1931D","#76A9FF","#FF6D5E","#34C27C","#FF76B0"],ema:["#D800FE","#DBC000","#00B4FE","#8175f3","#B8A89F","#F1931D","#76A9FF","#FF6D5E","#34C27C","#FF76B0"],emaOchl:r.a.blue_428,crossLine:"#262E40"}},function(t,i,e){"use strict";var r=e(35),o=r.a.yellow_ff8,n=r.a.blue_007,s=r.a.purple_d90;i.a={rise:r.a.red_e63,drop:r.a.green_2db,flat:r.a.gray_b2b,transparent:r.a.transparent,border:"#191E27",vline:"#191E27",hline:"#191E27",mline:"#98A0B3",xAxis:"#69738C",yAxis:"#69738C",tip:"#7A8499",defaultGray:"#7A8499",chart:{priceLine:"#AFD8FF",avgPriceLine:"#DAC100",iopvLine:"#A3299D",shinePoint:"rgb(0, 234, 255)",fill:"rgba(74, 176, 255, 0.1)",phRectFill:"rgba(74, 176, 255, 0.1)",phText:"#69738C"},button:{bg:"#171D28",text:"#F0F1F5",tri:"#F0F1F5"},macd:{dif:o,dea:n,macd:s},rsi:{rsi1:o,rsi2:n,rsi3:s},crossLine:"#98A0B3"}},function(t,i,e){"use strict";var r=Object(e(3).d)(),o=1*r;i.a={border:o,vline:o,hline:o,mline:o,ma:o,maxmin:o,indicator:o,ochl:o,crossLine:2*r,minsPriceLine:o,minsAvgLine:o}},function(t,i,e){"use strict";var r=Object(e(3).d)();i.a={font:10*r+"px Arial"}},function(t,i){t.exports="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDIxLjAuMCwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDYuMDAgQnVpbGQgMCkgIC0tPgo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IuWbvuWxgl8xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB4PSIwcHgiIHk9IjBweCIKCSB2aWV3Qm94PSIwIDAgMTAgMTgiIHN0eWxlPSJlbmFibGUtYmFja2dyb3VuZDpuZXcgMCAwIDEwIDE4OyIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+CjxzdHlsZSB0eXBlPSJ0ZXh0L2NzcyI+Cgkuc3Qwe2ZpbGw6d2hpdGU7fQo8L3N0eWxlPgo8dGl0bGU+6Lev5b6EPC90aXRsZT4KPGcgaWQ9Ikvnur/lm77lop7liqDkubDljZbngrnmoIforrAiPgoJPGcgaWQ9IueUu+adv+Wkh+S7vS0xMiIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoLTcyNC4wMDAwMDAsIC02ODQuMDAwMDAwKSI+CgkJPGcgaWQ9Iue8lue7hC055aSH5Lu9IiB0cmFuc2Zvcm09InRyYW5zbGF0ZSg2MTcuMDAwMDAwLCA2NzAuMDAwMDAwKSI+CgkJCTxnIGlkPSLnvJbnu4QiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDEzLjAwMDAwMCwgMTIuMDAwMDAwKSI+CgkJCQk8ZyBpZD0i6Lev5b6ELTMiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDg2LjUxNDcxOSwgMi41MTQ3MTkpIj4KCQkJCQk8cGF0aCBpZD0i6Lev5b6EIiBjbGFzcz0ic3QwIiBkPSJNMTMuNCw5LjJsLTUuNyw1LjdsMCwwYy0wLjQsMC40LTAuNCwxLDAsMS40YzAuNCwwLjQsMSwwLjQsMS40LDBsNy4xLTcuMQoJCQkJCQljMC40LTAuNCwwLjQtMSwwLTEuNEw5LjIsMC43bDAsMGMtMC40LTAuNC0xLTAuNC0xLjQsMGMtMC40LDAuNC0wLjQsMSwwLDEuNGw1LjcsNS43QzEzLjgsOC4yLDEzLjgsOC44LDEzLjQsOS4yeiIvPgoJCQkJPC9nPgoJCQk8L2c+CgkJPC9nPgoJPC9nPgo8L2c+Cjwvc3ZnPgo="},function(t,i,e){"use strict";var r,o=e(36),n=e(1),s=this&&this.__extends||(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var e in i)i.hasOwnProperty(e)&&(t[e]=i[e])},function(t,i){function e(){this.constructor=t}r(t,i),t.prototype=null===i?Object.create(i):(e.prototype=i.prototype,new e)}),a=function(t){function i(i,e,r,o,n){return t.call(this,i,e,r,o,n)||this}return s(i,t),i.prototype.draw=function(){if(this.getIndicatorData(),"number"==typeof this.lastestPrice){var t=this.getLastestPriceCoord();t&&this.drawLastestPriceLine(t)}},i.prototype.getIndicatorData=function(){this.props.setting&&this.props.setting.lastestPrice&&(this.lastestPrice=this.mainViewData.lastestPrice)},i.prototype.getLastestPriceCoord=function(){var t;return this.lastestPrice>=this.min&&this.lastestPrice<=this.max&&(t=this.dataToCoord().getY(this.lastestPrice)),t},i.prototype.drawLastestPriceLine=function(t){this.ctx.save();var i=this.ctx,e=this.region.chart,r=e.width,o=1*this.props.devicePixelRatio;i.translate(e.x,e.y+e.height/2),Object(n.c)(this.ctx,0,t,r,t,"rgba(48, 119, 236, 0.6)","dash",null,null,o),this.ctx.restore()},i}(o.a);i.a=a},function(t,i,e){"use strict";var r,o=e(36),n=e(1),s=e(0),a=e(3),h=this&&this.__extends||(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var e in i)i.hasOwnProperty(e)&&(t[e]=i[e])},function(t,i){function e(){this.constructor=t}r(t,i),t.prototype=null===i?Object.create(i):(e.prototype=i.prototype,new e)}),c=document.createElement("img");c.src=e(94);var p=function(t){function i(i,e,r,o,n){return t.call(this,i,e,r,o,n)||this}return h(i,t),i.prototype.draw=function(){this.getIndicatorData(),Object(a.e)(this.remindPrice)||(this.getRemindPriceCoord(),this.upPriceY&&this.drawRemindPriceLine("upPrice"),this.downPriceY&&this.drawRemindPriceLine("downPrice"))},i.prototype.getIndicatorData=function(){this.remindPrice=this.mainViewData.remindPrice,this.upPriceY=void 0,this.downPriceY=void 0},i.prototype.getRemindPriceCoord=function(){var t=this.remindPrice,i=t.upPrice,e=t.downPrice;i>=this.min&&i<=this.max&&(this.upPriceY=this.dataToCoord().getY(+i)),e>=this.min&&e<=this.max&&(this.downPriceY=this.dataToCoord().getY(+e))},i.prototype.drawRemindPriceLine=function(t){var i=this.ctx,e=this.remindPrice,r=e.upPrice,o=e.downPrice,a="upPrice"===t?this.upPriceY:this.downPriceY;i.save();var h=this.props.devicePixelRatio,p=""+("upPrice"===t?r:o),u=10*h+"px Arial",l=Object(s.d)(i,p,{font:u}),d=10*h,f=11*h,g=11*h,x=this.region.chart,y=a,w=a-g/2,m=1*this.props.devicePixelRatio;a<-x.height/2+d/2?y=-x.height/2+d/2:a>x.height/2-d/2&&(y=x.height/2-d/2),w<-x.height/2?w=-x.height/2:w>x.height/2-g&&(w=x.height/2-g),i.translate(x.x,x.y+x.height/2),Object(s.c)(i,p,l/2,y,{color:"#FFB470",font:u,textAlign:s.a.textAlign.center,baseLine:s.a.baseLine.mid}),i.drawImage(c,l,w,f,g),Object(n.c)(i,l+f,a,x.width,a,"rgba(255, 137, 30, 0.6)","dash",null,null,m),i.restore()},i}(o.a);i.a=p},function(t,i,e){"use strict";var r,o=e(36),n=e(1),s=e(3),a=this&&this.__extends||(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var e in i)i.hasOwnProperty(e)&&(t[e]=i[e])},function(t,i){function e(){this.constructor=t}r(t,i),t.prototype=null===i?Object.create(i):(e.prototype=i.prototype,new e)}),h=function(t){function i(i,e,r,o,n){return t.call(this,i,e,r,o,n)||this}return a(i,t),i.prototype.draw=function(){var t=this;this.getIndicatorData(),Object(s.e)(this.trendline)||(this.traverseData(function(i){return t.getTrendlineCoord(i)}),this.processTrendlineCoord(),this.trendlineCoord.point.length>1&&this.drawTrendline())},i.prototype.getIndicatorData=function(){this.trendline=this.mainViewData.currTrendline,this.trendlineCoord={left:{},point:[],right:{}},this.firstTime=!0},i.prototype.getTrendlineCoord=function(t){var i=this.props.itemWidth,e=this.region.chart.width,r=t.index,o=t.x,n=t.y2;if(-1!==this.trendline.point.findIndex(function(t){return t===r})&&this.trendlineCoord.point.push({x:o,y:n}),this.firstTime){if(this.firstTime=!1,Object.keys(this.trendline.left).length){var s=this.trendline.left,a=s.intervalNum,h=s.close,c=i/2-this.dataToCoord().getX(a),p=this.dataToCoord().getY(h);this.trendlineCoord.left.x=c,this.trendlineCoord.left.y=p}if(Object.keys(this.trendline.right).length){var u=this.trendline.right,a=u.intervalNum,l=u.close,d=e+this.dataToCoord().getX(a)-i/2,p=this.dataToCoord().getY(l);this.trendlineCoord.right.x=d,this.trendlineCoord.right.y=p}}},i.prototype.processTrendlineCoord=function(){var t,i,e=Object.keys(this.trendlineCoord.left).length,r=Object.keys(this.trendlineCoord.right).length,o=this.trendlineCoord.point.length;if(e&&(o||r)){t=this.trendlineCoord.left,i=o?this.trendlineCoord.point[0]:this.trendlineCoord.right;var n=this.getAxisIntersection(t,i,"left"),s=n.x,a=n.y;s!==1/0?(this.trendlineCoord.left.x=s,this.trendlineCoord.left.y=a,this.trendlineCoord.point.unshift({x:s,y:a}),o++):this.trendlineCoord.left={}}if(r&&o){t=this.trendlineCoord.right,i=this.trendlineCoord.point[o-1];var h=this.getAxisIntersection(t,i,"right"),s=h.x,a=h.y;s!==1/0?(this.trendlineCoord.right.x=s,this.trendlineCoord.right.y=a,this.trendlineCoord.point.push({x:s,y:a})):this.trendlineCoord.right={}}},i.prototype.getAxisIntersection=function(t,i,e){var r={x:1/0,y:1/0},o=this.region.chart,n=o.width,s=o.height,a=n/this.props.count,h=(i.y-t.y)/(i.x-t.x),c=t.y-h*t.x,p="left"===e?a/2:n-a/2,u=h*p+c;if(Math.abs(u)<=s/2)r.x=p,r.y=u;else if(u<-s/2){var l=(-s/2-c)/h;l>=0&&l<=n&&(r.x=l,r.y=-s/2)}else if(u>s/2){var d=(s/2-c)/h;d>=0&&d<=n&&(r.x=d,r.y=s/2)}return r},i.prototype.drawTrendline=function(){var t=this.ctx,i=this.region.chart,e=this.trendlineCoord.point.length,r=Object.keys(this.trendlineCoord.left).length,o=Object.keys(this.trendlineCoord.right).length,s=this.props.devicePixelRatio,a=2.5*s,h=1.5*s;t.save(),t.translate(i.x,i.y+i.height/2),this.trendlineCoord.point.forEach(function(i,s){var h=i.x,c=i.y,p=o&&s===e-1;r&&0===s||p||Object(n.b)(t,h,c,a,"#3077EC","#3077EC")}),this.trendlineCoord.point.forEach(function(i,r){var o=i.x,s=i.y;Object(n.d)(t,o,s,"#3077EC",h,r,e)}),t.restore()},i}(o.a);i.a=h},function(t,i,e){"use strict";var r,o=e(36),n=e(1),s=e(0),a=this&&this.__extends||(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var e in i)i.hasOwnProperty(e)&&(t[e]=i[e])},function(t,i){function e(){this.constructor=t}r(t,i),t.prototype=null===i?Object.create(i):(e.prototype=i.prototype,new e)}),h=function(t){function i(i,e,r,o,n){var s=t.call(this,i,e,r,o,n)||this;return s.supportLine={},s.pressureLine={},s}return a(i,t),i.prototype.draw=function(){if(this.getIndicatorData(),this.boll){this.getSupportPressureLineCoord();var t=this.region.chart.height;Math.abs(this.pressureLine&&this.pressureLine.coord)<=t/2&&this.drawSupportOrPressureLine("pressure"),Math.abs(this.supportLine&&this.supportLine.coord)<=t/2&&this.drawSupportOrPressureLine("support")}},i.prototype.getIndicatorData=function(){this.boll=this.data.length&&this.data[this.data.length-1]&&this.data[this.data.length-1].defboll},i.prototype.getSupportPressureLineCoord=function(){var t=this.boll,i=t.upper,e=t.mid,r=t.lower,o=this.data[this.data.length-1].close;if(i&&e&&r){var n=o>e?e:r,s=o>e?i:e;this.supportLine.price=n,this.supportLine.coord=this.dataToCoord().getY(n),this.pressureLine.price=s,this.pressureLine.coord=this.dataToCoord().getY(s)}},i.prototype.drawSupportOrPressureLine=function(t){var i=this.ctx,e=this.region.chart,r=e.x,o=e.y,a=e.width,h=e.height,c="support"===t?"230, 53, 53":"28, 170, 60",p=this.props.devicePixelRatio,u="support"===t?this.supportLine.coord:this.pressureLine.coord;if(!(Math.abs(u)>h/2)){i.save(),i.translate(r,o+h/2),Object(n.c)(i,0,u,a,u,"rgb("+c+")","dash",null,null,1*p);var l=Math.min(.3*(isNaN(Math.abs(this.supportLine.coord-this.pressureLine.coord))?1/0:Math.abs(this.supportLine.coord-this.pressureLine.coord)),24*p),d="support"===t?u-l:u+l,f="support"===t?u-l:u,g=i.createLinearGradient(0,u,0,d);g.addColorStop(0,"rgba("+c+", 0.2)"),g.addColorStop(1,"rgba("+c+", 0.0)"),Object(n.g)(i,0,f,a,l,"",g,0);var x="support"===t?"支撑位 "+this.formatMoney(this.supportLine.price,this.props.fixNum||2):"压力位 "+this.formatMoney(this.pressureLine.price,this.props.fixNum||2),y=12.5*p,w={color:"rgb("+c+")",font:"500 "+9*p+"px/"+y+"px Arial",baseLine:s.a.baseLine.mid};i.setTextStyle(w);var m=i.measureText(x).width,v=4*p,b=y+1*p*2,M="support"===t?u-b:u,C=2*p;Object(n.f)(i,0,M,m+2*v,b,C,"support"===t?"#F08585":"#76CC8A","support"===t?"#FCEBEB":"#E8F6EB",.5*C),Object(s.c)(i,x,v,M+b/2,w),i.restore()}},i}(o.a);i.a=h},function(t,i){t.exports="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDIxLjAuMCwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDYuMDAgQnVpbGQgMCkgIC0tPgo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IuWbvuWxgl8xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB4PSIwcHgiIHk9IjBweCIKCSB2aWV3Qm94PSIwIDAgMTAgMTgiIHN0eWxlPSJlbmFibGUtYmFja2dyb3VuZDpuZXcgMCAwIDEwIDE4OyIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+CjxzdHlsZSB0eXBlPSJ0ZXh0L2NzcyI+Cgkuc3Qwe2ZpbGw6I0Q1OEVGNzt9Cjwvc3R5bGU+Cjx0aXRsZT7ot6/lvoQ8L3RpdGxlPgo8ZyBpZD0iS+e6v+WbvuWinuWKoOS5sOWNlueCueagh+iusCI+Cgk8ZyBpZD0i55S75p2/5aSH5Lu9LTEyIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgtNzI0LjAwMDAwMCwgLTY4NC4wMDAwMDApIj4KCQk8ZyBpZD0i57yW57uELTnlpIfku70iIHRyYW5zZm9ybT0idHJhbnNsYXRlKDYxNy4wMDAwMDAsIDY3MC4wMDAwMDApIj4KCQkJPGcgaWQ9Iue8lue7hCIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMTMuMDAwMDAwLCAxMi4wMDAwMDApIj4KCQkJCTxnIGlkPSLot6/lvoQtMyIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoODYuNTE0NzE5LCAyLjUxNDcxOSkiPgoJCQkJCTxwYXRoIGlkPSLot6/lvoQiIGNsYXNzPSJzdDAiIGQ9Ik0xMy40LDkuMmwtNS43LDUuN2wwLDBjLTAuNCwwLjQtMC40LDEsMCwxLjRjMC40LDAuNCwxLDAuNCwxLjQsMGw3LjEtNy4xCgkJCQkJCWMwLjQtMC40LDAuNC0xLDAtMS40TDkuMiwwLjdsMCwwYy0wLjQtMC40LTEtMC40LTEuNCwwYy0wLjQsMC40LTAuNCwxLDAsMS40bDUuNyw1LjdDMTMuOCw4LjIsMTMuOCw4LjgsMTMuNCw5LjJ6Ii8+CgkJCQk8L2c+CgkJCTwvZz4KCQk8L2c+Cgk8L2c+CjwvZz4KPC9zdmc+Cg=="},function(t,i){t.exports="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDIxLjAuMCwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDYuMDAgQnVpbGQgMCkgIC0tPgo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IuWbvuWxgl8xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB4PSIwcHgiIHk9IjBweCIKCSB2aWV3Qm94PSIwIDAgMTAgMTgiIHN0eWxlPSJlbmFibGUtYmFja2dyb3VuZDpuZXcgMCAwIDEwIDE4OyIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+CjxzdHlsZSB0eXBlPSJ0ZXh0L2NzcyI+Cgkuc3Qwe2ZpbGw6IzI2MkU0MDt9Cjwvc3R5bGU+Cjx0aXRsZT7ot6/lvoQ8L3RpdGxlPgo8ZyBpZD0iS+e6v+WbvuWinuWKoOS5sOWNlueCueagh+iusCI+Cgk8ZyBpZD0i55S75p2/5aSH5Lu9LTEyIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgtNzI0LjAwMDAwMCwgLTY4NC4wMDAwMDApIj4KCQk8ZyBpZD0i57yW57uELTnlpIfku70iIHRyYW5zZm9ybT0idHJhbnNsYXRlKDYxNy4wMDAwMDAsIDY3MC4wMDAwMDApIj4KCQkJPGcgaWQ9Iue8lue7hCIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMTMuMDAwMDAwLCAxMi4wMDAwMDApIj4KCQkJCTxnIGlkPSLot6/lvoQtMyIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoODYuNTE0NzE5LCAyLjUxNDcxOSkiPgoJCQkJCTxwYXRoIGlkPSLot6/lvoQiIGNsYXNzPSJzdDAiIGQ9Ik0xMy40LDkuMmwtNS43LDUuN2wwLDBjLTAuNCwwLjQtMC40LDEsMCwxLjRjMC40LDAuNCwxLDAuNCwxLjQsMGw3LjEtNy4xCgkJCQkJCWMwLjQtMC40LDAuNC0xLDAtMS40TDkuMiwwLjdsMCwwYy0wLjQtMC40LTEtMC40LTEuNCwwYy0wLjQsMC40LTAuNCwxLDAsMS40bDUuNyw1LjdDMTMuOCw4LjIsMTMuOCw4LjgsMTMuNCw5LjJ6Ii8+CgkJCQk8L2c+CgkJCTwvZz4KCQk8L2c+Cgk8L2c+CjwvZz4KPC9zdmc+Cg=="},function(t,i){t.exports={render:function(){var t=this.$createElement,i=this._self._c||t;return i("div",{staticClass:"kline-comp"},[i("canvas",{ref:"kline-canvas",staticClass:"kline-content",style:{width:this.width+"px",height:this.height+"px"},attrs:{width:this.width*this.ratio,height:this.height*this.ratio},on:{touchstart:this.dispatchEvent,touchmove:this.dispatchEvent,touchend:this.dispatchEvent,mousedown:this.dispatchEvent,mousemove:this.dispatchEvent,mouseup:this.dispatchEvent,mousewheel:this.dispatchEvent}},[this._v(this._s(this.options.options.type+"k-chart"))]),this._v(" "),this._t("default")],2)},staticRenderFns:[]}},function(t,i,e){var r=e(56)(e(197),e(204),null,null);i.__esModule=!0,i.default=r.exports},function(t,i,e){"use strict";Object.defineProperty(i,"__esModule",{value:!0});var r=e(46),o=e.n(r),n=e(73),s=e.n(n),a=e(75),h=e.n(a),c=e(198),p=e(34),u=e(55);i.default={props:{options:{type:Object,default:{}},width:{type:Number},height:{type:Number}},computed:{elem:function(){return this.$refs["mins-canvas"+this.options.options.type]},refName:function(){return"mins-canvas"+this.options.options.type}},watch:{options:function(t,i){var e=this.event&&this.event.isCrossLine;if(t.options.minsIndicator!==i.options.minsIndicator)e&&this.event.hideCrossLine(),this.view.switchIndicator(t.options.minsIndicator);else if(t.options.showAuction!==i.options.showAuction)this.view.clearShine(),this.init();else if(t.options.labels[0]!==i.options.labels[0])this.init();else{if(e)return;this.view.draw({data:t.data,auctionData:t.auctionData,isTrading:t.isTrading,isAuctionTime:t.isAuctionTime})}}},data:function(){return{view:null,event:null,ratio:window.devicePixelRatio}},mounted:function(){this.init()},beforeDestroy:function(){this.view.clearShine()},methods:{init:function(){var t=this;this.options.options.minsIndicator&&!this.options.options.isHistoryMins||(this.options.options.minsIndicator="volume");try{var i=getComputedStyle(document.documentElement),e=i.getPropertyValue("--color-rise"),r=i.getPropertyValue("--color-drop"),n=i.getPropertyValue("--color-blue");if(e&&r&&n){p.a.mins.plain.rise=e,p.a.mins.plain.drop=r,p.a.mins.plain.chart.priceLine=n;var a=parseInt(n.trim().slice(1,3),16),u=parseInt(n.trim().slice(3,5),16),l=parseInt(n.trim().slice(5,7),16);p.a.mins.plain.chart.shinePoint="rgb("+a+", "+u+", "+l+")",p.a.mins.plain.chart.avgPriceLine="#dac100"}for(var d=["plain","dark"],f=0;f<d.length;f++){var g=d[f];!function(i){var e={plain:"customColor",dark:"customDarkColor"}[i];t.options.options[e]&&h()(t.options.options[e]).forEach(function(r){"object"===s()(t.options.options[e][r])?o()(p.a.mins[i][r],t.options.options[e][r]):p.a.mins[i][r]=t.options.options[e][r]})}(g)}}catch(t){}this.view=new c.a(this.elem,this.$refs.shineCanvas,this.options.options),this.view.draw({data:this.options.data,auctionData:this.options.auctionData,isTrading:this.options.isTrading,isAuctionTime:this.options.isAuctionTime});var x=this.view.layout,y=x.height,w=x.indicatorHeight;this.firstPercent=1-w/y,this.initEvent()},dispatchEvent:function(t){this.options.options.disableInteract||(this.event||this.initEvent(),this.event.handleEvent(t))},cancelEvent:function(){this.event&&this.event.cancleAll()},initEvent:function(){var t=this,i=this.options.options;this.event||(this.event=new u.a(this.elem,this.options.options,{onTap:function(e){if("fmins"!==i.type&&!i.isHistoryMins){var r=e.changedTouches[0];if(/portrait/.test(i.layout)){if(t.view.isTapButtonRegion(r))return e.preventDefault&&e.preventDefault(),t.$emit("onPopup",1),!0;if(t.view.isTapIndicatorRegion(r)){var o=t.view.switchIndicator();return t.$emit("onChange",o),!0}}if(t.view.isTapTipRegion(r))return t.$emit("onTipTap"),!0}t.$emit("onTap",e)},onCrossLineTap:function(i){var e=i.changedTouches[0];if(t.view.isTapTradeBarRegion(e))return t.$emit("onBarTap","trade",t.view.tradeBarData),!0},onDoubleTap:function(i){t.$emit("onDoubleTap",i)},onTouchMove:function(i){t.view.showCrossLine(i,function(i){t.$emit("onTouchMove",i)})},onTouchCancle:function(){t.view.draw(),t.$emit("onTouchCancle")}}))},getPointPosition:function(t,i){return this.view.getPointPosition(t,i)},updateTradeData:function(t){this.view.updateTradeData(t)}}}},function(t,i,e){"use strict";var r=e(199),o=e(34),n=e(78),s=e(19),a=e(200),h=e(77),c=e(8),p=e(95),u=e(3),l=e(0),d=e(24),f=e(53),g=e(7),x=e(96),y=e(89),w=e(1),m=e(79),v=e(82),b=this&&this.__assign||Object.assign||function(t){for(var i,e=1,r=arguments.length;e<r;e++)for(var o in i=arguments[e],i)Object.prototype.hasOwnProperty.call(i,o)&&(t[o]=i[o]);return t},M=document.createElement("img");M.src=e(201);var C=document.createElement("img"),A=document.createElement("img"),L=document.createElement("img"),P=document.createElement("img"),I=document.createElement("img"),j=document.createElement("img");C.src=e(90),A.src=e(91),L.src=e(92),P.src=e(93),I.src=e(202),j.src=e(203);var S=document.createElement("img"),T=document.createElement("img");S.src=e(54),T.src=e(97);var D,O,N,E=new Map,R=function(){function t(t,i,e,r,s){var a=this;this.drawTradePointData=[],this.ctx=new n.a(t,r,s);var c=Object(y.a)(e),d=Object(u.d)();this.props=b({barWidth:d,spaceWidth:0,count:200,labels:["09:30","11:30/13:00","15:00"],daysConf:{multiDays:!1},layout:"mins-portrait",colorProp:o.a.mins[e.skin],lineProp:p.a.lineProp,lineJoin:"round",textProp:p.a.textProp,showCrossLine:!0,showCrossLineTips:!1,showCrossLineValue:!0,devicePixelRatio:d},e),this.props.yAixsCount=c.yAixsCount,this.props.hlineCount=c.hlineCount,this.props.vlineCount=c.vlineCount;var f={font:p.a.textProp.font,baseLine:l.a.baseLine.mid,color:this.props.colorProp.xAxis};this.props.wxSearchStyle&&(this.props.yAixsCount=3,f.baseLine=l.a.baseLine.btm);var g=this.props.labels;g&&g.length>0&&(this.props.labels=[],g.forEach(function(t,i){a.props.labels.push({text:t,props:b({},f,{textAlign:a.getTextAlign(i,g.length)})})})),this.layout=Object(h.a)(this.ctx,this.props.layout,this.props);var w=this.layout.chart,m=this.layout.mainChart,v=this.layout.auctionChart;if(this.props.itemWidth=m.width/this.props.count,this.props.auctionItemWidth=v.width/this.props.auctionCount,this.props.spaceWidth=this.props.itemWidth-this.props.barWidth,i&&(this.ctxShine=i.getContext("2d"),this.ctxShine.setFillStyle=function(t){return a.ctxShine.fillStyle=t},this.ctxShine.setStrokeStyle=function(t){return a.ctxShine.strokeStyle=t},this.ctxShine.setTransform(1,0,0,1,0,0),this.ctxShine.translate(w.x,w.y+w.height/2)),this.props.showCrossLine){var M=this.layout.mainIndicator;this.crossLine=new x.a(this.ctx,{x:w.x,y:w.y,width:w.width,height:M.y+M.height-w.y,auctionWidth:v.width,xAxis:this.layout.xAxis},this.props)}D!==e.market||O!==e.scode?(D=e.market,O=e.scode,E.clear()):(e.isHistoryMins||N!==e.isHistoryMins)&&(N=e.isHistoryMins,E.clear())}return t.prototype.draw=function(t){void 0===t&&(t={});var i,e,o,n,h,u=t.data,f=t.auctionData,x=t.isTrading,y=t.isAuctionTime,m=t.indicatorItem,v=t.indicatorIndex,b=this,M=this.ctx,C=this.layout,A=this.props,L=C.getChart(),P=C.mainChart,I=C.mainIndicator,j=C.getXAxis(),S=[],T=[];this.data=u||this.data,this.auctionData=f||this.auctionData,this.isTrading=void 0===x?this.isTrading:x,this.isAuctionTime=void 0===y?this.isAuctionTime:y,this.initialize(),u&&u.items&&u.items.length>0&&this.formatIndicator(),u=this.data,this.drawTradePointData=[],Object(d.a)(M,0,0,C.getWidth(),C.getHeight()),A.showAuction&&this.drawAuctionGridLayer();var D=this.props.kch||this.props.chy,O=D?L.width*((D[1]-D[0])/D[1]):0,N={x:P.x,y:P.y,width:P.width-O,height:P.height};if(A.wxSearchStyle){var R=4*this.props.devicePixelRatio;new s.a(M,{wxSearchStyle:!this.isMultiDays(),hline:{lineWidth:this.props.lineProp.hline,linestyle:"dash",dashGapArray:[R,R],count:this.isMultiDays()?0:3,color:this.props.colorProp.hline},vline:{lineWidth:this.props.lineProp.vline,linestyle:"dash",dashGapArray:[R,R],count:this.isMultiDays()?4:0,color:this.props.colorProp.vline}},N)}else new s.a(M,{border:{lineWidth:this.props.lineProp.border,color:this.props.colorProp.border},hline:{skipMiddle:!0,lineWidth:this.props.lineProp.hline,count:this.props.hlineCount,color:this.props.colorProp.hline},vline:{lineWidth:this.props.lineProp.vline,count:this.isMultiDays()?4:this.props.vlineCount,color:this.props.colorProp.vline}},N),new s.a(M,{hline:{lineWidth:0,count:1,color:this.props.colorProp.hline},mline:{lineWidth:this.props.lineProp.mline,count:1,linestyle:"dash",color:this.props.colorProp.mline}},L);if(!A.hideIndicator){var k={x:I.x,y:I.y,width:I.width-O,height:I.height};new s.a(M,{border:{lineWidth:this.props.lineProp.border,color:this.props.colorProp.border},vline:{lineWidth:this.props.lineProp.vline,count:this.isMultiDays()?4:this.props.vlineCount,color:this.props.colorProp.vline}},k)}if(A.showAuction&&this.drawAuctionDataLayer(),new c.a({ctx:M,region:P,drawCallback:function(t){var i,e,r=t.index,o=t.currItem,n=A.colorProp.chart,s=t.length,a=t.getX(r),h=t.getY(o.price)||0;if(b.isStartIndex(r)?(M.beginPath(),M.moveTo(a,h)):M.lineTo(a,h),b.isEndIndex(r,s)){if(i=t.getX(b.getBeginIndex(r)),e=t.getX(r+1),M.setStrokeStyle(n.priceLine),M.setLineWidth(A.lineProp.minsPriceLine),M.setLineJoin(A.lineJoin),M.lineTo(e-A.lineProp.minsPriceLine,h),M.stroke(),M.setStrokeStyle(A.colorProp.transparent),M.lineTo(e-A.lineProp.minsPriceLine,L.height/2),M.lineTo(i,L.height/2),M.stroke(),Array.isArray(n.fill)){var c=M.createLinearGradient(0,t.getY(b.data.max)||0,0,L.height/2);c.addColorStop(0,n.fill[0]),c.addColorStop(1,n.fill[1]),M.setFillStyle(c)}else M.setFillStyle(n.fill);M.closePath(),M.fill(),b.isMultiDays()||b.isAuctionTime||(b.isTrading?b.drawShine(C.auctionWidth+a,h):b.clearShine())}var p=b.isMultiDays()?E.get(o.date+" "+o.time):E.get(o.time);p&&(o.tradeType=p,b.drawTradePointData.push({currItem:o,x:a,y:h}))},data:u,count:A.count}),D){var _=P.x,W=P.y,B=P.height,z=P.width,F=P.width*((D[1]-D[0])/D[1]),Z=A.colorProp.chart;Object(w.g)(M,_+z-F,W,F,B,Z.phRectFill,Z.phRectFill);var Y=A.isHistoryMins?5/8:.75;Object(l.c)(M,"盘后",_+z-F/2,W+B*Y,{color:Z.phText,font:p.a.textProp.font,baseLine:l.a.baseLine.btm,textAlign:l.a.textAlign.center})}if(D){var Q={x:P.x+P.width-O,y:I.y,width:O,height:I.height};new s.a(M,{border:{lineWidth:this.props.lineProp.border,color:this.props.colorProp.border}},Q)}if(u.maxVol>0&&new c.a({ctx:M,region:P,drawCallback:function(t){var i=t.index,e=t.currItem,r=A.colorProp.chart,o=t.length,n=t.getX(i),s=t.getY(e.avgPrice);M.setStrokeStyle(r.avgPriceLine),M.setFillStyle(r.avgPriceLine),M.setLineWidth(A.lineProp.minsAvgLine),b.isStartIndex(i)?(M.beginPath(),M.moveTo(n,s)):M.lineTo(n,s),b.isEndIndex(i,o)&&M.stroke()},data:u,count:A.count}),A.crossLineItem=m,A.showIOPV&&this.drawIOPV(),A.hideIndicator||this.drawIndicator(m,v),this.isMultiDays()?new r.a(M,g.b.x,this.props.labels,j).draw():new a.a(M,g.b.x,this.props.labels,j,O).draw(),A.yAixsCount){i=2*u.diff/(A.yAixsCount-1);var H=L.yAxisLeft.props,G=L.yAxisRight.props,U=this.props.fixNum||2;for(o=0;o<A.yAixsCount;o++)n=u.middle+u.diff-i*o,e=A.colorProp.yAxis,h=this.getBaselineType(o,A.yAixsCount),S.push({text:isNaN(n)?2==U?"0.00":"0.000":n.toFixed(U),props:{baseLine:h,font:this.props.textProp.font,textAlign:H.textAlign,color:e}}),T.push({text:isNaN(n)?"0.00%":((n-u.preClose)/u.preClose*100).toFixed(2)+"%",props:{baseLine:h,font:this.props.textProp.font,textAlign:G.textAlign,color:e}});new a.a(M,g.b.y,S,L.yAxisLeft).draw(),new a.a(M,g.b.y,T,L.yAxisRight).draw()}if(this.drawTradePointData.length>0){M.save(),M.translate(P.x,P.y+P.height/2);for(var J=0,V=this.drawTradePointData;J<V.length;J++){var X=V[J];this.drawTradePoint(X)}M.restore()}},t.prototype.drawIOPV=function(){var t=this,i=this.ctx,e=this.layout.mainChart.x,r=this.layout.mainChart.y/2,o={font:p.a.textProp.font,baseLine:l.a.baseLine.mid,textAlign:l.a.textAlign.left},n=this.props.colorProp.chart,s=this.props.crossLineItem||this.data.items[this.data.items.length-1],a=this.props.fixNum||2,h="均价: "+(+s.avgPrice).toFixed(a);if(Object(l.c)(i,h,e,r,b({},o,{color:n.avgPriceLine})),s.iopv){e+=Object(l.d)(i,h,o)+8*this.props.devicePixelRatio;var u="实时参考净值(IOPV): "+(+s.iopv).toFixed(a);Object(l.c)(i,u,e,r,b({},o,{color:n.iopvLine})),e+=Object(l.d)(i,u,o)+5*this.props.devicePixelRatio;var d=12*this.props.devicePixelRatio,f=12*this.props.devicePixelRatio,g=(this.layout.mainChart.y-f)/2;i.drawImage(M,e,g,d,f),this.tipRegion={x:e,y:g,width:d,height:f}}new c.a({ctx:i,region:this.layout.mainChart,drawCallback:function(e){var r=e.index,o=e.getX(r),s=e.getY(e.currItem.iopv);i.setStrokeStyle(n.iopvLine),i.setFillStyle(n.iopvLine),i.setLineWidth(t.props.lineProp.minsAvgLine),0===r?(i.beginPath(),i.moveTo(o,s)):i.lineTo(o,s),r===e.length-1&&i.stroke()},data:this.data,count:this.props.count})},t.prototype.formatIndicator=function(){this.data.maxMin={volume:{max:this.data.maxVol,min:0},macd:{max:Number.MIN_SAFE_INTEGER,min:Number.MAX_SAFE_INTEGER},rsi:{max:Number.MIN_SAFE_INTEGER,min:Number.MAX_SAFE_INTEGER}};for(var t={close:this.data.items.map(function(t){return t.price})},i={macd:v.a.macd(t,this.props.setting.macdParams),rsi:v.a.rsi(t,this.props.setting.rsiParams)},e=0;e<this.data.items.length;e++){var r=this.data.items[e];for(var o in i){var n=i[o],s={};for(var a in n)s[a]=n[a][e],isNaN(s[a])||void 0===s[a]||(this.data.maxMin[o].max=Math.max(this.data.maxMin[o].max,s[a]),this.data.maxMin[o].min=Math.min(this.data.maxMin[o].min,s[a]));r[o]=s}}},t.prototype.drawIndicator=function(t,i){var e,r;t?new m.a[this.props.minsIndicator](this.ctx,this.data,b({},this.props,{index:i,indicator:((e={})[this.props.minsIndicator]=t[this.props.minsIndicator],e)}),this.layout.mainIndicator).draw():new m.a[this.props.minsIndicator](this.ctx,this.data,b({},this.props,{indicator:((r={})[this.props.minsIndicator]=this.data.items[this.data.items.length-1][this.props.minsIndicator],r)}),this.layout.mainIndicator).draw()},t.prototype.drawAuctionGridLayer=function(){var t=this.ctx,i=this.props,e=this.layout.auctionChart,r=this.layout.xAxis,o=this.layout.auctionIndicator,n="rgba(48, 119, 236, 0.05)";Object(w.g)(t,e.x,e.y,e.width,e.height,n,n),new s.a(t,{border:{lineWidth:i.lineProp.border,color:i.colorProp.border},hline:{skipMiddle:!0,lineWidth:i.lineProp.hline,count:i.hlineCount,color:i.colorProp.hline},vline:{lineWidth:this.props.lineProp.vline,count:1,color:this.props.colorProp.vline}},e),Object(l.c)(t,i.auctionLabel,e.x,r.y+r.height/2,{font:p.a.textProp.font,textAlign:l.a.textAlign.left,baseLine:l.a.baseLine.mid,color:i.colorProp.xAxis}),Object(w.g)(t,o.x,o.y,o.width,o.height,n,n),new s.a(t,{border:{lineWidth:i.lineProp.border,color:i.colorProp.border},vline:{lineWidth:i.lineProp.vline,count:1,color:i.colorProp.vline}},o)},t.prototype.drawAuctionDataLayer=function(){var t=this,i=this.ctx,e=this.props,r=this.layout.auctionChart,o=this.layout.auctionIndicator,n=!1,s=0,a=0;new c.a({ctx:i,region:r,drawCallback:function(r){if(r.currItem){n||(n=!0,i.beginPath(),i.moveTo(0,0));var o=r.index,h=r.getX(o),c=r.getY(r.currItem.p);if(0===o&&i.moveTo(0,c),o-s>1&&i.lineTo(h,a),i.lineTo(h,c),o===r.length-1){if(!t.isAuctionTime){var p=r.getX(e.auctionCount);i.lineTo(p,c)}i.setStrokeStyle(e.colorProp.chart.priceLine),i.setLineWidth(e.lineProp.minsPriceLine),i.setLineJoin(e.lineJoin),i.stroke(),t.isAuctionTime?t.drawShine(h,c):t.clearShine()}s=o,a=c}},data:this.auctionData,count:e.auctionCount}),"volume"===e.minsIndicator&&"sec"===this.auctionData.dType&&new c.a({ctx:i,region:o,drawCallback:function(t){if(t.currItem){var r=t.index,n=t.currItem,s=n.b1v,a=n.b2v,h=n.s2v,c=t.getX(r),p=t.getY(+s),u=t.getY(+a+ +h),l=a>h?e.colorProp.rise:e.colorProp.drop;Object(w.g)(i,c-1,p,2,o.height/2-p,l,l),Object(w.g)(i,c-1,-o.height/2,2,o.height/2-u,l,l)}},data:{items:this.auctionData.items,max:1.4*this.auctionData.maxVol,min:0},count:e.auctionCount})},t.prototype.drawShine=function(t,i){var e=this,r=this.props.colorProp.chart.shinePoint,o=r.slice(0,-1),n=2*this.props.devicePixelRatio,s=n,a=1;this.interval&&clearInterval(this.interval),this.interval=setInterval(function(){s=a<0?n:s+.12*n,a=a<0?1:a-.04,Object(d.a)(e.ctxShine,-e.layout.chart.x,-e.layout.chart.y-e.layout.chart.height/2,e.layout.width,e.layout.height),Object(w.b)(e.ctxShine,t,i,n,r,r),Object(w.b)(e.ctxShine,t,i,s,o+", "+a+")",o+", "+a+")")},120)},t.prototype.clearShine=function(){this.interval&&clearInterval(this.interval),Object(d.a)(this.ctxShine,-this.layout.chart.x,-this.layout.chart.y-this.layout.chart.height/2,this.layout.width,this.layout.height)},t.prototype.updateTradeData=function(t){for(var i=0;i<t.length;i++){var e=t[i],r=this.isMultiDays()?e.d+" "+e.time:e.time,o=E.get(r),n=o&&o!==e.t?"T":e.t;E.set(r,n)}this.draw()},t.prototype.drawTradePoint=function(t){var i=t.currItem,e=t.x,r=t.y,o=this.ctx,n=this.layout.chart.height,s=this.layout.chart.y,a=4*this.props.devicePixelRatio,h=1.5*this.props.devicePixelRatio,c={B:8*this.props.devicePixelRatio,S:8*this.props.devicePixelRatio,T:18*this.props.devicePixelRatio}[i.tradeType],p="mins-landscape"===this.props.layout?e-c/2:Math.min(Math.max(e-c/2,-this.layout.auctionChart.width),this.layout.mainChart.width-c),u=12*this.props.devicePixelRatio,l=7*this.props.devicePixelRatio,d=1*this.props.devicePixelRatio,f=1*this.props.devicePixelRatio,g=Math.round(l/3),x=(p+c/2-e)/3,y=a+2*h+l+u,m={B:"#3077EC",S:"#FF891E",T:"#3077EC"}[i.tradeType],v={B:!0,S:!1,T:!0}[i.tradeType];if(v?r+y+n/2>=s+n-10*this.props.devicePixelRatio&&(v=!1):r-y+n/2<=s+10*this.props.devicePixelRatio&&(v=!0),v){Object(w.b)(o,e,r+a+h,h,m,m);for(var b=e-d/2,M=r+a+2*h,S=0;S<3;S++)b+=x,M+=g,Object(w.g)(o,b,M-f/2,d,f,m,m);var T={B:C,S:L,T:I}[i.tradeType];o.drawImage(T,p,r+y-u,c,u)}else{Object(w.b)(o,e,r-a-h,h,m,m);for(var b=e-d/2,M=r-a-2*h,S=0;S<3;S++)b+=x,M-=g,Object(w.g)(o,b,M-f/2,d,f,m,m);var T={B:A,S:P,T:j}[i.tradeType];o.drawImage(T,p,r-y,c,u)}},t.prototype.getBaselineType=function(t,i){return 0===t?l.a.baseLine.top:l.a.baseLine.btm},t.prototype.isStartIndex=function(t){return this.isMultiDays()?0===t||t%this.props.daysConf.eachDayCount==0:0===t},t.prototype.isEndIndex=function(t,i){return this.isMultiDays()?t===i-1||(t+1)%this.props.daysConf.eachDayCount==0:t===i-1},t.prototype.getBeginIndex=function(t){return this.isMultiDays()?Math.floor(t/this.props.daysConf.eachDayCount)*this.props.daysConf.eachDayCount:0},t.prototype.isMultiDays=function(){return this.props.daysConf.multiDays&&this.props.daysConf.eachDayCount>0},t.prototype.initialize=function(){var t=0,i=0,e=this;if(this.data.maxVol=0,this.data.max=0,this.data.min=Number.MAX_SAFE_INTEGER,this.data.items.forEach(function(r,o){r.amount=r.volume*r.price,e.props.isHKOrZsOrFundOrNhg||void 0===r.totalAmount?(t+=r.volume,i+=r.amount):(t=r.totalVolume,i=r.totalAmount),r.avgPrice||(r.avgPrice=0==+t?+r.price:i/t),e.data.maxVol=Math.max(e.data.maxVol,r.volume),e.props.showIOPV&&r.iopv?(e.data.max=Math.max(e.data.max,r.price,r.iopv),e.data.min=Math.min(e.data.min,r.price,r.iopv)):(e.data.max=Math.max(e.data.max,r.price),e.data.min=Math.min(e.data.min,r.price)),e.isMultiDays()&&(o+1)%e.props.daysConf.eachDayCount==0&&(t=0,i=0)}),this.props.showAuction&&(this.data.max=this.auctionData.max=Math.max(this.data.max,this.auctionData.max),this.data.min=this.auctionData.min=Math.min(this.data.min,this.auctionData.min),this.auctionData.middle=+this.data.preClose),this.props.wxSearchStyle){var r=.05*(this.data.max-this.data.min);this.data.max+=r,this.data.min-=r}this.data.middle=+this.data.preClose,this.props.wxSearchStyle&&(this.data.max=Math.max(+this.data.preClose,this.data.max),this.data.min=Math.min(+this.data.preClose,this.data.min),this.data.max===this.data.min&&(this.data.max=1.01*+this.data.preClose,this.data.min=.99*+this.data.preClose,this.data.max||(this.data.max=.01)),this.data.middle=(this.data.max+this.data.min)/2,this.props.showAuction&&(this.auctionData.max=this.data.max,this.auctionData.min=this.data.min,this.auctionData.middle=this.data.middle)),this.data.diff=Math.max(Math.abs(this.data.max-this.data.middle),Math.abs(this.data.min-this.data.middle))},t.prototype.getTextAlign=function(t,i){return this.isMultiDays()?l.a.textAlign.center:0===t?l.a.textAlign.left:t===i-1?l.a.textAlign.right:l.a.textAlign.center},t.prototype.showCrossLine=function(t,i){var e=this.crossLine.changeCoordsForMins(t);e.auction?this.showAuctionCrossLine(e,i):this.showMainCrossLine(e,i)},t.prototype.showMainCrossLine=function(t,i){var e,r=this.data.items[t.index],o=this.layout.getChart();if(r){var n=r.price&&r.price.split&&r.price.split(".")[1];n=n&&n.length||2,r.leftval="",r.rightval="",t.y<=o.height+o.y&&(e=this.data.middle+this.data.diff-2*this.data.diff/o.height*(t.y-o.y),r.leftval=e.toFixed(n),r.rightval=((r.leftval-this.data.middle)/this.data.middle*100).toFixed(2)+"%",0===this.data.middle&&(r.rightval="0.00%"));var s=this.layout.mainIndicator,a=this.props.minsIndicator;if(t.y>=s.y&&t.y<=s.y+s.height){var h=this.data.maxMin[a],c=h.max;e=c-(c-h.min)/s.height*(t.y-s.y),e="volume"===a?0==+(e=Math.abs(e)>1e8?(e/1e8).toFixed(2)+"亿":Math.abs(e)>1e4?(e/1e4).toFixed(2)+"万":e.toFixed(2))?"0.00":e:0==+(e=e.toFixed(3))?"0.000":e,r.leftval=e}r.bottomval=r.time&&r.time.slice(0,2)+":"+r.time.slice(2),this.draw({indicatorItem:r,indicatorIndex:t.index}),this.crossLine.draw(t,r),this.drawTradeBar(r),i&&i(r)}},t.prototype.showAuctionCrossLine=function(t,i){var e,r=t.index,o=t.index;do e=this.auctionData.items[r]||this.auctionData.items[o];while(!e&&--r>=0&&++o<=this.props.auctionCount-1);if(e){e.leftval="",e.rightval="";var n=this.layout.auctionChart;if(t.y>=n.y&&t.y<=n.y+n.height){var s=e.p&&e.p.split&&e.p.split(".")[1];s=s&&s.length||2,e.leftval=(this.auctionData.middle+this.auctionData.diff-2*this.auctionData.diff/n.height*(t.y-n.y)).toFixed(s),e.rightval=((e.leftval-this.auctionData.middle)/this.auctionData.middle*100).toFixed(2)+"%"}var a=this.layout.auctionIndicator,h=this.props.minsIndicator;if(t.y>=a.y&&t.y<=a.y+a.height){var c=this.data.maxMin[h],p=c.max,u=p-(p-c.min)/a.height*(t.y-a.y);u="volume"===h?0==+(u=Math.abs(u)>1e8?(u/1e8).toFixed(2)+"亿":Math.abs(u)>1e4?(u/1e4).toFixed(2)+"万":u.toFixed(2))?"0.00":u:0==+(u=u.toFixed(3))?"0.000":u,e.leftval=u}e.bottomval=e.tm.slice(0,2)+":"+e.tm.slice(2,4)+(e.tm.length>4?":"+e.tm.slice(4):""),this.draw(),this.crossLine.draw(t,e),e.auction=!0,i&&i(e)}},t.prototype.drawTradeBar=function(t){if(t.tradeType){var i={B:"买入明细",S:"卖出明细",T:"买卖明细"}[t.tradeType],e={color:{B:"#3077EC",S:"#FAB06D",T:"#3077EC"}[t.tradeType],font:"500 "+11*this.props.devicePixelRatio+"px Arial",textAlign:l.a.textAlign.left,baseLine:l.a.baseLine.mid},r=this.layout.getChart(),o=Object(l.d)(this.ctx,i,e),n=4.5*this.props.devicePixelRatio,s=9*this.props.devicePixelRatio,a=o+n+20*this.props.devicePixelRatio,h=23*this.props.devicePixelRatio,c={B:"rgba(48, 119, 236, 0.3)",S:"rgba(255, 137, 30, 0.3)",T:"rgba(48, 119, 236, 0.3)"}[t.tradeType],p={B:"#F1F7FF",S:"#FFFAF5",T:"#F1F7FF"}[t.tradeType],u=t.showLeft?r.x:r.x+r.width-a;Object(w.f)(this.ctx,u,0,a,h,2*this.props.devicePixelRatio,c,p);var d=u+8*this.props.devicePixelRatio;Object(l.c)(this.ctx,i,d,0+h/2,e);var f={B:S,S:T,T:S}[t.tradeType],g=d+o+4*this.props.devicePixelRatio;this.ctx.drawImage(f,g,0+(h-s)/2,n,s),this.tradeBarRegion={x:u,y:0,width:a,height:h},this.tradeBarData=t}else this.tradeBarRegion=null,this.tradeBarData=null},t.prototype.isPointInRegion=function(t,i){return i&&t.x>=i.x&&t.x<=i.x+i.width&&t.y>=i.y&&t.y<=i.y+i.height},t.prototype.isTapTipRegion=function(t){var i=Object(f.a)(this.ctx.ctx.canvas,t);return i.x=i.x*this.props.devicePixelRatio,i.y=i.y*this.props.devicePixelRatio,this.isPointInRegion(i,this.tipRegion)},t.prototype.isTapTradeBarRegion=function(t){var i=Object(f.a)(this.ctx.ctx.canvas,t);return i.x=i.x*this.props.devicePixelRatio,i.y=i.y*this.props.devicePixelRatio,this.isPointInRegion(i,this.tradeBarRegion)},t.prototype.isTapButtonRegion=function(t){var i,e,r=this.props.devicePixelRatio,o=Object(f.a)(this.ctx.ctx.canvas,t);return o.x=o.x*r,o.y=o.y*r,this.isPointInRegion(o,(i=this.layout.mainIndicator.bar,e=10*r,{x:i.x-e,y:i.y-e,width:i.buttonWidth+2*e,height:i.height+2*e}))},t.prototype.isTapIndicatorRegion=function(t){var i=Object(f.a)(this.ctx.ctx.canvas,t);return i.x=i.x*this.props.devicePixelRatio,i.y=i.y*this.props.devicePixelRatio,this.isPointInRegion(i,this.layout.mainIndicator)},t.prototype.switchIndicator=function(t){if(t)this.props.minsIndicator=t;else{var i=this.props.useIndicators.length,e=this.props.useIndicators.indexOf(this.props.minsIndicator);this.props.minsIndicator=this.props.useIndicators[(e+1)%i]}return this.draw(),this.props.minsIndicator},t.prototype.getPointPosition=function(t,i){if(-1===(e=this.data.items.findIndex(function(i){return+i.time==+t}))){if("up"===i){var e,r=Number.MIN_SAFE_INTEGER;this.data.items.forEach(function(t,i){t.price>=r&&(r=t.price,e=i)})}else if("down"===i){var o=Number.MAX_SAFE_INTEGER;this.data.items.forEach(function(t,i){t.price<=o&&(o=t.price,e=i)})}}var n=this.layout.mainChart,s=n.x+n.width/this.props.count*e,a=n.y+n.height/2-(this.data.items[e].price-this.data.middle)/this.data.diff*(n.height/2);return{x:s/this.props.devicePixelRatio,y:a/this.props.devicePixelRatio}},t}();i.a=R},function(t,i,e){"use strict";var r,o=e(9),n=e(7),s=e(0),a=this&&this.__extends||(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var e in i)i.hasOwnProperty(e)&&(t[e]=i[e])},function(t,i){function e(){this.constructor=t}r(t,i),t.prototype=null===i?Object.create(i):(e.prototype=i.prototype,new e)}),h=function(t){function i(i,e,r,o){return t.call(this,i,e,r,o)||this}return a(i,t),i.prototype.getX=function(t,i){var e=i.props;if(this.type==n.b.y)return e.textAlign==s.a.textAlign.left?this.region.x:e.textAlign==s.a.textAlign.right?this.region.x+this.region.width:this.region.x;if(this.type==n.b.x){var r=this.region.width/this.labels.length;return r*t+r/2+this.region.x}},i.prototype.getY=function(t,i){var e=i.props;if(this.type===n.b.x)return e.baseLine==s.a.baseLine.top?this.region.y:e.baseLine==s.a.baseLine.btm?this.region.y+this.region.height:this.region.y+this.region.height/2;var r=this.region.height/this.labels.length;return r*t+r/2+this.region.y},i}(o.a);i.a=h},function(t,i,e){"use strict";var r,o=e(9),n=e(7),s=e(0),a=this&&this.__extends||(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var e in i)i.hasOwnProperty(e)&&(t[e]=i[e])},function(t,i){function e(){this.constructor=t}r(t,i),t.prototype=null===i?Object.create(i):(e.prototype=i.prototype,new e)}),h=function(t){function i(i,e,r,o,n){var s=t.call(this,i,e,r,o)||this;return s.panhouWidth=n||0,s}return a(i,t),i.prototype.getX=function(t,i){var e=i.props;if(this.type==n.b.y)return e.textAlign==s.a.textAlign.left?this.region.x:e.textAlign==s.a.textAlign.right?this.region.x+this.region.width:this.region.x;if(this.type==n.b.x){var r=this.region.width/(this.labels.length-1);return 3===this.labels.length&&this.panhouWidth&&1===t?r*t+this.region.x-this.panhouWidth/2:"5:15/9:00"===i.text?this.region.x+this.region.width/1187*735:r*t+this.region.x}},i.prototype.getY=function(t,i){var e=i.props;return this.type===n.b.x?e.baseLine==s.a.baseLine.top?this.region.y:e.baseLine==s.a.baseLine.btm?this.region.y+this.region.height:this.region.y+this.region.height/2:this.region.height/(this.labels.length-1)*t+this.region.y},i}(o.a);i.a=h},function(t,i){t.exports="data:image/png;base64,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"},function(t,i){t.exports="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDI2LjAuMiwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDYuMDAgQnVpbGQgMCkgIC0tPgo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IuWbvuWxgl8xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB4PSIwcHgiIHk9IjBweCIKCSB2aWV3Qm94PSIwIDAgMjkgMjEiIHN0eWxlPSJlbmFibGUtYmFja2dyb3VuZDpuZXcgMCAwIDI5IDIxOyIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+CjxzdHlsZSB0eXBlPSJ0ZXh0L2NzcyI+Cgkuc3Qwe2ZpbGwtcnVsZTpldmVub2RkO2NsaXAtcnVsZTpldmVub2RkO2ZpbGw6I0ZGODkxRTt9Cgkuc3Qxe2ZpbGwtcnVsZTpldmVub2RkO2NsaXAtcnVsZTpldmVub2RkO2ZpbGw6IzMwNzdFQzt9Cgkuc3Qye2ZpbGw6I0ZGRkZGRjt9Cgkuc3Qze2ZvbnQtZmFtaWx5OidQaW5nRmFuZ1NDLU1lZGl1bS1HQnBjLUVVQy1IJzt9Cgkuc3Q0e2ZvbnQtc2l6ZToxNXB4O30KPC9zdHlsZT4KPHBhdGggaWQ9IuefqeW9oiIgY2xhc3M9InN0MCIgZD0iTTAuNyw1LjFsMTMuNS01YzAuMi0wLjEsMC41LTAuMSwwLjcsMGwxMy41LDVDMjguNyw1LjIsMjksNS42LDI5LDZ2MTRjMCwwLjYtMC40LDEtMSwxSDEKCWMtMC42LDAtMS0wLjQtMS0xVjZDMCw1LjYsMC4zLDUuMiwwLjcsNS4xeiIvPgo8cGF0aCBpZD0i55+p5b2iXzAwMDAwMDI4Mjg4MDY0OTAzNzc4MTMzOTkwMDAwMDA5MDg1NzE3MTQ1MDA5NjMyMTc4XyIgY2xhc3M9InN0MSIgZD0iTTAuNyw1LjFMMTQuNSwwbDAsMHYyMUgxYy0wLjYsMC0xLTAuNC0xLTFWNgoJQzAsNS42LDAuMyw1LjIsMC43LDUuMXoiLz4KPHRleHQgdHJhbnNmb3JtPSJtYXRyaXgoMSAwIDAgMSAxNyAxOCkiIGNsYXNzPSJzdDIgc3QzIHN0NCI+UzwvdGV4dD4KPHRleHQgdHJhbnNmb3JtPSJtYXRyaXgoMSAwIDAgMSAyIDE4KSIgY2xhc3M9InN0MiBzdDMgc3Q0Ij5CPC90ZXh0Pgo8L3N2Zz4K"},function(t,i){t.exports="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDI2LjAuMiwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDYuMDAgQnVpbGQgMCkgIC0tPgo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IuWbvuWxgl8xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB4PSIwcHgiIHk9IjBweCIKCSB2aWV3Qm94PSIwIDAgMjkgMjEiIHN0eWxlPSJlbmFibGUtYmFja2dyb3VuZDpuZXcgMCAwIDI5IDIxOyIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+CjxzdHlsZSB0eXBlPSJ0ZXh0L2NzcyI+Cgkuc3Qwe2ZpbGwtcnVsZTpldmVub2RkO2NsaXAtcnVsZTpldmVub2RkO2ZpbGw6I0ZGODkxRTt9Cgkuc3Qxe2ZpbGwtcnVsZTpldmVub2RkO2NsaXAtcnVsZTpldmVub2RkO2ZpbGw6IzMwNzdFQzt9Cgkuc3Qye2ZpbGw6I0ZGRkZGRjt9Cgkuc3Qze2ZvbnQtZmFtaWx5OidQaW5nRmFuZ1NDLU1lZGl1bS1HQnBjLUVVQy1IJzt9Cgkuc3Q0e2ZvbnQtc2l6ZToxNXB4O30KPC9zdHlsZT4KPGcgaWQ9Iue8lue7hC0xNeWkh+S7vSI+Cgk8cGF0aCBpZD0i55+p5b2iIiBjbGFzcz0ic3QwIiBkPSJNMC43LDE1LjlsMTMuNSw1YzAuMiwwLjEsMC41LDAuMSwwLjcsMGwxMy41LTVjMC40LTAuMSwwLjctMC41LDAuNy0wLjlWMWMwLTAuNi0wLjQtMS0xLTFIMQoJCUMwLjQsMCwwLDAuNCwwLDF2MTRDMCwxNS40LDAuMywxNS44LDAuNywxNS45eiIvPgoJPHBhdGggaWQ9IuefqeW9ol8wMDAwMDEwODI5MjM4NTkzODc1OTQyNzI5MDAwMDAxMTUwODY3NTQ2Njk3NTEzNjM5MV8iIGNsYXNzPSJzdDEiIGQ9Ik0wLjcsMTUuOUwxNC41LDIxbDAsMFYwSDFDMC40LDAsMCwwLjQsMCwxCgkJdjE0QzAsMTUuNCwwLjMsMTUuOCwwLjcsMTUuOXoiLz4KCTx0ZXh0IHRyYW5zZm9ybT0ibWF0cml4KDEgMCAwIDEgMTcgMTMpIiBjbGFzcz0ic3QyIHN0MyBzdDQiPlM8L3RleHQ+Cgk8dGV4dCB0cmFuc2Zvcm09Im1hdHJpeCgxIDAgMCAxIDIgMTMpIiBjbGFzcz0ic3QyIHN0MyBzdDQiPkI8L3RleHQ+CjwvZz4KPC9zdmc+Cg=="},function(t,i){t.exports={render:function(){var t=this.$createElement,i=this._self._c||t;return i("div",{staticClass:"mins-comp",staticStyle:{position:"relative"}},[i("canvas",{ref:this.refName,style:{width:this.width+"px",height:this.height+"px"},attrs:{width:this.width*this.ratio,height:this.height*this.ratio}},[this._v(this._s(this.options.options.type+"-chart"))]),this._v(" "),i("canvas",{ref:"shineCanvas",staticClass:"shine-canvas",staticStyle:{position:"absolute",top:"0",left:"0"},style:{width:this.width+"px",height:this.height+"px"},attrs:{width:this.width*this.ratio,height:this.height*this.ratio},on:{touchstart:this.dispatchEvent,touchmove:this.dispatchEvent,touchend:this.dispatchEvent,mousedown:this.dispatchEvent,mousemove:this.dispatchEvent,mouseup:this.dispatchEvent}})])},staticRenderFns:[]}}]).default},t.exports=e()}}]);