"use strict";(self.webpackChunkweixin_search=self.webpackChunkweixin_search||[]).push([["src_components_general_official-zone_swiper_vue"],{587934:function(t,e,n){var i=n(9638),o=n(487090),r=n(551900),a=n(430062),c=(0,r.Z)(o.Z,i.s,i.x,!1,null,"0006f316",null);"function"==typeof a.Z&&(0,a.Z)(c),e.Z=c.exports},148385:function(t,e,n){n.d(e,{Z:function(){return i}});function i(t){t.options.__wxs_id="eb2bebfe"}},566198:function(t,e,n){var i=n(538302),o=n(188933),r=n(551900),a=n(699477),c=(0,r.Z)(o.Z,i.s,i.x,!1,null,"03fff366",null);"function"==typeof a.Z&&(0,a.Z)(c),e.Z=c.exports},736217:function(t,e,n){n.d(e,{Z:function(){return i}});function i(t){t.options.__wxs_id="4ce83c11"}},719962:function(t,e,n){var i=n(246774),o=n(395112),r=n(551900),a=n(340428),c=(0,r.Z)(o.Z,i.s,i.x,!1,null,"164bd762",null);"function"==typeof a.Z&&(0,a.Z)(c),e.Z=c.exports},236305:function(t,e,n){n.d(e,{Z:function(){return i}});function i(t){t.options.__wxs_id="371da258"}},788860:function(t,e,n){n.r(e);var i=n(171197),o=n(990836),r=n(551900),a=n(631494),c=n(502805),s=(0,r.Z)(o.Z,i.s,i.x,!1,null,"aa931112",null);"function"==typeof a.Z&&(0,a.Z)(s),"function"==typeof c.Z&&(0,c.Z)(s),e.default=s.exports},486848:function(t,e,n){n.d(e,{Z:function(){return i}});function i(t){t.options.__ruleConfig={version:2,componentPath:"1cd980d7e",componentType:1,rules:[{model:"cnProps.list[$index]",path:".activity-card-container-wrap",isReportIdFromJson:!0,event:0,type:2,isInformal:!0,aliasValue:"轮播banner"}]}}},847626:function(t,e,n){n.d(e,{Z:function(){return i}});function i(t){t.options.__wxs_id="450056b1"}},485162:function(t,e,n){n.r(e);var i=n(941188),o=n(98794),r=n(551900),a=n(879046),c=(0,r.Z)(o.Z,i.s,i.x,!1,null,"69df4e30",null);"function"==typeof a.Z&&(0,a.Z)(c),e.default=c.exports},881666:function(t,e,n){n.d(e,{Z:function(){return i}});function i(t){t.options.__wxs_id="788848ed"}},182630:function(t,e,n){var i=n(664406),o=n(770938),r=n(551900),a=n(902485),c=(0,r.Z)(o.Z,i.s,i.x,!1,null,"1b23da56",null);"function"==typeof a.Z&&(0,a.Z)(c),e.Z=c.exports},402496:function(t,e,n){n.d(e,{Z:function(){return i}});function i(t){t.options.__wxs_id="a7706846"}},182638:function(t,e,n){var i=n(711308),o=n(98761),r=n(551900),a=n(877815),c=(0,r.Z)(o.Z,i.s,i.x,!1,null,"31dba360",null);"function"==typeof a.Z&&(0,a.Z)(c),e.Z=c.exports},820526:function(t,e,n){n.d(e,{Z:function(){return i}});function i(t){t.options.__wxs_id="464118a3"}},430062:function(t,e,n){var i=n(148385);e.Z=i.Z},699477:function(t,e,n){var i=n(736217);e.Z=i.Z},340428:function(t,e,n){var i=n(236305);e.Z=i.Z},631494:function(t,e,n){var i=n(486848);e.Z=i.Z},502805:function(t,e,n){var i=n(847626);e.Z=i.Z},879046:function(t,e,n){var i=n(881666);e.Z=i.Z},902485:function(t,e,n){var i=n(402496);e.Z=i.Z},877815:function(t,e,n){var i=n(820526);e.Z=i.Z},487090:function(t,e,n){var i=n(652188);e.Z=i.Z},188933:function(t,e,n){var i=n(699416);e.Z=i.Z},395112:function(t,e,n){var i=n(784173);e.Z=i.Z},990836:function(t,e,n){var i=n(341201);e.Z=i.Z},98794:function(t,e,n){var i=n(652284);e.Z=i.Z},770938:function(t,e,n){var i=n(865026);e.Z=i.Z},98761:function(t,e,n){var i=n(270013);e.Z=i.Z},9638:function(t,e,n){n.d(e,{s:function(){return i},x:function(){return o}});var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"activity-card-container-wrap",attrs:{"data-fcn-activity-card-container":"","data-fr-170c295a0":""},on:{click:function(e){return e.stopPropagation(),t.onTapCard(e)}}},[n("video-player",{staticClass:"activity-card-container",attrs:{src:t.videoUrl,poster:t.imageUrl,feed:t.exportId&&t.nonceId?{exportId:t.exportId,nonceId:t.nonceId}:null,"export-id":t.exportId,"nonce-id":t.nonceId,"round-corner":!0,"aspect-ratio":t.aspectRatio,"round-corner-size":t.roundCornerSize,"polling-cgi":t.pollingCgi,tag:t.tag,controls:t.noPlayIcon?t.hasSoundIcon?["sound"]:[]:["sound","play"],"data-fc-12c37f101":""}},[n("div",{staticClass:"slot-wrapper"},[t._t("default")],2)]),t.likeNum||t.duration||t.strDuration||t.readNum||t.activityBlock?n("v-mask",{attrs:{text:t.likeNum||t.duration||t.strDuration||t.readNum||t.activityBlock&&t.activityBlock.readNum||t.activityBlock.link&&t.activityBlock.link.title,like:!!t.likeNum,"no-icon":!0,size:40,"gradient-padding":12,source:t.activityBlock.source,title:t.activityBlock.title,desc:t.activityBlock.desc,"desc-list":t.activityBlock.descList,link:t.activityBlock.link,"use-ui-source":"","special-shadow":t.specialShadow,"data-fc-12c37f102":""},nativeOn:{click:function(e){return t.onTapMask(e)}}}):t._e()],1)},o=[]},538302:function(t,e,n){n.d(e,{s:function(){return i},x:function(){return o}});var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return t.list?n("div",{staticClass:"activity-card-slide",attrs:{"data-fcn-activity-card-slide":"","data-fr-109551c40":""}},[t.isOneCard?n("div",{staticClass:"only-media-card"},[n("activity-card",{attrs:{"card-info":t.list&&t.list[0],"data-report-id":t.M_itemReportId(t.list[0],1),"data-fc-16f58ca51":""},on:{"video:error":function(e){return t.onVideoError(e,0)},"tap:play":function(e){return t.onTapPlay(e,0)},"tap:card":function(e){return t.onTapCard(e,0)},"tap:button":function(e){return t.onTapButton(e,0)},"tap:menu":function(e){return t.onTapMenu(e,0)},"tap:info":function(e){return t.onTapInfo(e,0)},"shake:card":function(e){return t.onShakeCard(arguments,0)}}}),t.list[0].pk?n("pk",t._b({staticClass:"mt-8",attrs:{source:t.list[0].pk,"parent-item-pos":t.M_getItemPos(t.list[0]),"data-fc-16f58ca52":""}},"pk",t.$props,!1)):t._e(),t.list[0].cardFooterInfo?n("activity-info-footer",{key:"carshow_footer_0",ref:"footerArea",attrs:{id:"footer_0","card-footer-info":Object.assign({},t.list[0].cardFooterInfo),"container-pos-info":t.containerPosInfo,"prev-index":t.prevIndex,"current-index":t.currentIndex,index:0,"data-fc-16f58ca53":""},on:{"tap:footer":function(e){return t.onTapFooter(e,0)}}}):t._e()],1):n("ui-scroll",{directives:[{name:"arrow-scroll",rawName:"v-arrow-scroll",value:{step:t.getItemWidth},expression:"{ step: getItemWidth }"}],ref:"scrollContainer",attrs:{snap:"",padding:t.padding||16,items:t.list,wait:50,role:"listbox","decay-factor":t.decayFactor,"data-fc-16f58ca54":""},on:{scrollEnd:t.onScrollEnd,scrollEndImm:t.scrollEndImm,scroll:t.onScroll},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.item,o=e.index;return[n("activity-card",{ref:"card",attrs:{"card-info":i,"data-report-id":t.M_itemReportId(i,o+1),"data-fc-16f58ca55":""},on:{"video:error":function(e){return t.onVideoError(e,o)},"tap:play":function(e){return t.onTapPlay(e,o)},"tap:card":function(e){return t.onTapCard(e,o)},"tap:button":function(e){return t.onTapButton(e,o)},"tap:menu":function(e){return t.onTapMenu(e,o)},"tap:info":function(e){return t.onTapInfo(e,o)},"shake:card":function(e){return t.onShakeCard(arguments,o)}}}),i.pk?n("pk",t._b({staticClass:"mt-8",attrs:{source:i.pk,"parent-item-pos":t.M_getItemPos(i),"data-fc-16f58ca56":""}},"pk",t.$props,!1)):t._e(),i.cardFooterInfo?n("activity-info-footer",{key:"carshow_footer_"+o,ref:"footerArea",attrs:{id:"footer_"+o,"card-footer-info":Object.assign({},i.cardFooterInfo),"container-pos-info":t.containerPosInfo,"prev-index":t.prevIndex,"current-index":t.currentIndex,index:o,"data-fc-16f58ca57":""},on:{"tap:footer":function(e){return t.onTapFooter(e,o)}}}):t._e(),i.fashionShowSourceInfo?n("fashion-show-source",{attrs:{"fashion-show-source-info":i.fashionShowSourceInfo,index:o,"data-fc-16f58ca58":""}}):t._e()]}}],null,!1,1535395480)})],1):t._e()},o=[]},246774:function(t,e,n){n.d(e,{s:function(){return i},x:function(){return o}});var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return t.cardInfo?n("activity-card-container",t._b({directives:[{name:"active",rawName:"v-active"}],staticClass:"media-card",attrs:{"read-num":"","no-play":t.noPlay,"special-shadow":!t.cardInfo.fashionShowSourceInfo,"data-fc-1ea46eb16":"","data-cli":"","data-fcn-activity-card":"","data-fr-136071d3c":""},on:{"tap:card":function(e){return t.onTapCard(t.cardInfo)},"tap:play":function(e){return t.onTapPlay(t.cardInfo)},"video:error":function(e){return t.onVideoError(t.cardInfo)}}},"activity-card-container",t.cardInfo,!1),[t.cardInfo.fashionShowSourceInfo?n("div",{staticClass:"fashion-show-shadow"}):t._e(),t.cardInfo.complaint?n("complaint-popover",{staticClass:"complaint-popover",attrs:{complaint:t.cardInfo.complaint,"data-fc-1ea46eb14":""},on:{"confirm:negative":function(e){return t.$emit("confirm:negative")}},scopedSlots:t._u([{key:"trigger",fn:function(){return[n("div",{directives:[{name:"active",rawName:"v-active.stop",modifiers:{stop:!0}}],staticClass:"complaint-entry",attrs:{"data-cli":""}},[n("div",[t._v(t._s(t.cardInfo.complaint.entryText))]),n("ui-arrow",{staticClass:"complaint-entry__arrow",attrs:{size:12,align:"flex",direction:"down","data-fc-1ea46eb12":""}})],1)]},proxy:!0}],null,!1,1803406142)}):t._e(),t.activity&&(t.activity.title||t.activity.descList||t.activity.button||t.activity.source)||t.cardInfo.readNum?n("div",{staticClass:"activity",class:{overSpread:t.isOverSpread,shakeCardType:t.isShakeType}},[n("div",{staticClass:"activity-inner"},[n("div",{directives:[{name:"active",rawName:"v-active.stop",value:t.isShakeType,expression:"isShakeType",modifiers:{stop:!0}}],staticClass:"activity-info",attrs:{role:"button","data-cli":""},on:{click:function(e){return t.onTapInfo(t.cardInfo,e)}}},[t.isShakeType?n("div",{directives:[{name:"active",rawName:"v-active.stop",modifiers:{stop:!0}}],staticClass:"activity-icon",attrs:{"data-cli":""},on:{transitionend:t.transitionEnd}},[n("div",{directives:[{name:"show",rawName:"v-show",value:t.showBubble,expression:"showBubble"},{name:"image",rawName:"v-image:custom",value:t.bubbleImg,expression:"bubbleImg",arg:"custom"}],staticClass:"gif-icon-bubble"}),n("svg-icon",{directives:[{name:"show",rawName:"v-show",value:!t.useGif,expression:"!useGif"}],attrs:{name:"shake","data-fc-1ea46eb10":""}}),n("div",{directives:[{name:"show",rawName:"v-show",value:t.useGif,expression:"useGif"},{name:"image",rawName:"v-image:custom",value:t.shakeImg,expression:"shakeImg",arg:"custom"}],staticClass:"gif-icon-shake"})],1):t._e()]),t.activity.button?n("ui-button",{staticStyle:{"margin-left":"12px"},attrs:{mini:"",loading:t.cardInfo.loading,title:t.activity.button.title,"data-report-id":t.M_itemReportId(t.activity.button),"data-fc-1ea46eb0e":""},nativeOn:{click:function(e){return e.stopPropagation(),t.onTapButton(t.activity.button)}}}):t.cardInfo.readNum&&0!=t.cardInfo.readNum?n("div",{staticClass:"text-mask"},[t._v(t._s(t.cardInfo.readNum))]):t._e()],1)]):t.menus&&t.menus.length?n("div",{staticClass:"menus"},[n("div",{staticClass:"menus-inner"},t._l(t.menus,function(e,i){return n("div",{directives:[{name:"active",rawName:"v-active.stop",modifiers:{stop:!0}}],key:e.title+i,staticClass:"menu-item active__opacity",attrs:{role:"button","data-report-id":t.M_itemReportId(e,i+1),"data-cli":""},on:{click:function(n){return n.stopPropagation(),t.onTapMenu(e)}}},[t._v("\n          "+t._s(e.title)+"\n        ")])}),0)]):t._e()],1):t._e()},o=[]},171197:function(t,e,n){n.d(e,{s:function(){return i},x:function(){return o}});var i=function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"slide oz__margin-dense oz__item oz__body",class:{"service-swiper":this.isEliminatingPaddingH},attrs:{"data-id":this.M_exposeId(),"data-fcn-swiper":"","data-fr-1cd980d7e":""}},[e("activity-card-slide",this._b({ref:"slide",attrs:{list:this.renderList,"parent-box-report-id":this.boxReportId,"parent-doc-report-id":this.docReportId,"parent-item-pos":this.parentItemPos,"decay-factor":this.decayFactor,padding:this.itemInfo.padding,"data-fc-1e569a1e0":""},on:{"tap:card":this.onTapCard,"tap:button":this.onTapButton,"tap:menu":this.onTapMenu,"tap:info":this.onTapInfo,"tap:footer":this.onTapFooter,"expose:card":this.onExposeCard,"onShow:card":this.onShowCard,"shake:card":this.onShakeCard}},"activity-card-slide",this.$props,!1))],1)},o=[]},941188:function(t,e,n){n.d(e,{s:function(){return i},x:function(){return o}});var i=function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"lpl-pk-wrap",class:{"lpl-pk-wrap--no-header-title":!this.hasHeaderTitle,"lpl-pk-wrap--header-title":this.hasHeaderTitle},attrs:{"data-fcn-pk-doc":"","data-fr-136707c7c":""}},[e("pk",this._b({ref:"pk",attrs:{"data-fc-1979f12a2":""}},"pk",this.$props,!1)),this.canShowPkTitle?e("div",{staticClass:"title"},[this._v(this._s(this.source.pkTitle))]):this._e()],1)},o=[]},664406:function(t,e,n){n.d(e,{s:function(){return i},x:function(){return o}});var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{directives:[{name:"active",rawName:"v-active"}],ref:"footer-"+t.index,staticClass:"ad-car-show-footer ad-car-show-active",attrs:{"data-cli":"","data-fcn-activity-info-footer":"","data-fr-120ec5dce":""},on:{click:function(e){return t.onTap(t.cardFooterInfo)},scroll:t.onScroll,scrollend:t.onScrollEnd}},[n("div",{ref:"footer-left-"+t.index,staticClass:"ad-car-show-footer__left"},[n("div",{staticClass:"ad-car-show-footer__title text-animation"},[t._v(t._s(t.title))]),n("div",{staticClass:"ad-car-show-footer__source text-animation"},[n("ui-source",{attrs:{"icon-url":t.source.iconUrl,title:t.source.title,"data-fc-106c147ba":""},scopedSlots:t._u([t.source.mark?{key:"after-text",fn:function(){return[n("ui-tag",{attrs:{type:"8","data-fc-106c147b8":""}})]},proxy:!0}:null],null,!0)})],1)]),n("div",{ref:"footer-right-"+t.index,staticClass:"ad-car-show-footer__right car-animation"},[n("div",{staticClass:"ad-car-show-footer__right__car",style:{backgroundImage:"url('"+t.cardProductUrl+"')"}}),n("canvas",{staticClass:"ad-car-show-footer__right__shadow",attrs:{id:"ad-car-shadow-"+t.index}})])])},o=[]},711308:function(t,e,n){n.d(e,{s:function(){return i},x:function(){return o}});var i=function(){var t=this.$createElement,e=this._self._c||t;return e("div",{ref:"footer-"+this.index,staticClass:"ad-fashion-show-container",attrs:{"data-fcn-fashion-show-source":"","data-fr-16acb5954":""}},[e("div",{ref:"footer-left-"+this.index,staticClass:"ad-fashion-show"},[e("div",{staticClass:"ad-fashion-show__title"},[this._v(this._s(this.title))]),e("div",{staticClass:"ad-fashion-show__source"},[e("ui-source",{attrs:{"icon-url":this.source.iconUrl,title:this.source.title,"data-fc-122c05d46":""},scopedSlots:this._u([this.source.mark?{key:"after-text",fn:function(){return[e("ui-tag",{attrs:{type:"8","data-fc-122c05d44":""}})]},proxy:!0}:null],null,!0)})],1)])])},o=[]},652188:function(t,e,n){var i=n(647849),o=n(386657);e.Z={name:"ActivityCardContainer",components:{"v-mask":i.Z,VideoPlayer:o.Z},props:{aspectRatio:{type:Number,default:9/16},roundCornerSize:{type:Number,default:8},imageUrl:String,videoUrl:String,duration:[String,Number],strDuration:String,likeNum:String,noPlayIcon:{type:Boolean,default:!0},tag:Object,readNum:String,nonceId:String,exportId:String,pollingCgi:Object,activityBlock:Object,hasSoundIcon:{type:Boolean},specialShadow:{type:Boolean,default:!0}},methods:{onVideoError:function(){this.$emit("video:error")},onTapMask:function(){this.$emit("tap:card")},onTapCard:function(){this.$emit("tap:card")}}}},699416:function(t,e,n){var i=n(719962),o=n(151285),r=n(809561),a=n(798509),c=n(737133),s=n(485162),u=n(182630),d=n(182638);function l(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=Array(e);n<e;n++)i[n]=t[n];return i}function f(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter(function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable}))),i.forEach(function(e){var i,o,r;i=t,o=e,r=n[e],o in i?Object.defineProperty(i,o,{value:r,enumerable:!0,configurable:!0,writable:!0}):i[o]=r})}return t}function p(t,e){return e=null!=e?e:{},Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):(function(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);n.push.apply(n,i)}return n})(Object(e)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(e,n))}),t}e.Z={name:"ActivityCardSlide",components:{ActivityCard:i.Z,UiScroll:c.Z,Pk:s.default,ActivityInfoFooter:u.Z,FashionShowSource:d.Z},mixins:[o.ZP,r.Z,a.uW],provide:function(){return{onPkCalcHeight:this.onPkCalcHeight}},props:{data:{type:Object,default:function(){return{}}},item:{type:Object,default:function(){return{}}},pos:{type:Number,default:0},typePos:{type:Number,default:0},list:Array,decayFactor:{type:Number,default:1},padding:Number},data:function(){return{pkHeightDep:[],containerPosInfo:{},prevIndex:0,currentIndex:0}},computed:{isOneCard:function(){return 1===this.list.length},isCarShowCard:function(){return"carshowactivitycards"===this.item.docID}},mounted:function(){this.list.length>=1&&this.$emit("expose:card",p(f({},this.list[0]),{index:0})),!this.isOneCard&&this.calContainerPosInfo()},methods:{onPkCalcHeight:function(t){if(this.pkHeightDep.push(t),this.pkHeightDep.length===this.list.length){var e,n,i=(n=Math).max.apply(n,function(t){if(Array.isArray(t))return l(t)}(e=this.pkHeightDep)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(e)||function(t,e){if(t){if("string"==typeof t)return l(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);if("Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return l(t,e)}}(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}());a.Gc.$emit("update:pk-height",i)}},onVideoError:function(t,e){this.$emit("video:error",p(f({},t),{index:e}))},onTapPlay:function(t,e){this.$emit("tap:play",p(f({},t),{index:e}))},onTapCard:function(t,e){this.$emit("tap:card",p(f({},t),{index:e,itemPos:this.M_getItemPos(t.reportId)}))},onTapButton:function(t,e){this.$emit("tap:button",p(f({},t),{index:e}))},onTapMenu:function(t,e){this.$emit("tap:menu",p(f({},t),{index:e}))},onTapInfo:function(t,e){this.$emit("tap:info",p(f({},t),{index:e}))},onTapFooter:function(t,e){this.$emit("tap:footer",p(f({},t),{index:e}))},onShakeCard:function(t,e){this.$emit("shake:card",t,e)},onScroll:function(t,e){this.isCarShowCard&&this.carShowScrollHandler(t,e)},carShowScrollHandler:function(t,e){var n,i=f({},e),o=this.$refs.scrollContainer.$el.getBoundingClientRect().width,r=Math.abs(e.distance/o).toFixed(2),c=e.distance>=0?1:-1,s=0;s=e.distance>=0?Math.max(e.current-1,0):Math.min(e.current+1,this.list.length-1),i.scrollPercent=r,i.direction=c,i.relationIndex=s,i.currentIndex=e.current,a.Gc.$emit(a.U3.AD_ACTIVITY_CARD_SCROLL,{e:t,scrollInfo:i});var u=null===(n=this.$refs.footerArea)||void 0===n?void 0:n.$el;u&&u.dispatchEvent(new Event("scroll"))},scrollEndImm:function(t){this.prevIndex=t.prev,this.currentIndex=t.current;var e,n=null===(e=this.$refs.footerArea)||void 0===e?void 0:e.$el;n&&this.$nextTick(function(){n.dispatchEvent(new Event("scrollend"))}),this.isCarShowCard&&this.updateTopBgInfo(t)},onScrollEnd:function(t){var e=t.map(function(t,e){return p(f({},t),{index:e})}).filter(function(t){return t.include&&t.visible}),n=(e&&e[0]||{}).index;if(void 0!==n){var i=this.list[n];!i.isShow&&(this.$emit("onShow:card",{index:n,itemPos:this.M_getItemPos(i.reportId)}),this.$emit("expose:card",p(f({},this.list[n]),{index:n})))}},getItemWidth:function(){return this.$refs.card.$el.scrollWidth+8},calContainerPosInfo:function(){var t=this.$refs.scrollContainer.$el,e=t.getBoundingClientRect().left,n=t.getBoundingClientRect().right;this.containerPosInfo={left:e,right:n}},updateTopBgInfo:function(t){var e=t.prev,n=t.current,i=this.list[n].backgroundInfo.backgroundColor;a.Gc.$emit(a.U3.AD_ACTIVITY_CARD_SCROLL_END,{prev:e,current:n,duration:300,bgColor:i})}}}},784173:function(t,e,n){var i=n(798509),o=n(783466);n(231097);var r=n(984928),a=n(525949),c=n(587934);function s(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter(function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable}))),i.forEach(function(e){var i,o,r;i=t,o=e,r=n[e],o in i?Object.defineProperty(i,o,{value:r,enumerable:!0,configurable:!0,writable:!0}):i[o]=r})}return t}function u(t,e){return e=null!=e?e:{},Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):(function(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);n.push.apply(n,i)}return n})(Object(e)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(e,n))}),t}e.Z={name:"ActivityCard",components:{ComplaintPopover:a.default,ActivityCardContainer:c.Z},mixins:[i.jB,i.uW],props:{cardInfo:Object,noPlay:Boolean},data:function(){return{bubbleImg:"https://res.wx.qq.com/t/fed_upload/cda467f7-0a71-4cb8-91cc-4aabbc7a45e9/bubble.gif",shakeImg:"https://res.wx.qq.com/t/fed_upload/8e4d6632-fd0a-462a-8d38-9a5aec4e34d7/shake.gif?",endAction:"suspend",isShakeCardExpose:!1,isOverSpread:!1,openADFlag:!0,transitionFlag:!0,playingFlag:!1,showBubble:!1,useGif:!1,isFirst:!0}},computed:{activity:function(){return this.cardInfo.activityBlock},menus:function(){return this.cardInfo.menus},isShakeType:function(){return 51005==this.cardInfo.cardType}},mounted:function(){var t=this;this.isShakeType&&(new IntersectionObserver(this.handleObserve,{threshold:.95}).observe(this.$el),this.preloadAD(),i.Gc.$on(i.U3.APP_VIEW_CHANGED,function(e){"index"==e.page&&t.suspend()}),o.N.onDeviceMotionFired(this.onMotionFired),o.N.onWebviewResume(this.onResume),o.N.onWebviewPause(this.suspend))},beforeDestroy:function(){this.isShakeType&&(o.N.offDeviceMotionFired(this.onMotionFired),o.N.offWebviewResume(this.onResume),o.N.offWebviewPause(this.suspend))},methods:{onVideoError:function(t){this.$emit("video:error",t)},onTapPlay:function(t){this.$emit("tap:play",t)},onTapCard:function(t){this.$emit("tap:card",t)},onTapButton:function(t){this.$emit("tap:button",u(s({},t),{itemPos:this.M_itemReportIdMap[t]}))},onTapMenu:function(t){this.$emit("tap:menu",u(s({},t),{itemPos:this.M_itemReportIdMap[t]}))},onTapInfo:function(t,e){if(this.isShakeType&&!this.playingFlag){this.transitionFlag=!0,this.playingFlag=!0,this.isOverSpread=!0,this.refreshAnimation(),this.$emit("tap:info",t);this.$emit("shake:card",this.cardInfo,{actionId:2,actionCarrier:3}),e.stopPropagation()}},onMotionFired:function(){if(this.openADFlag){this.openADFlag=!1;this.$emit("shake:card",this.cardInfo,{actionId:7,actionCarrier:3})}},activate:function(){i.hi.deviceMotionMonitor({action:"activate",amplitude:.65})},suspend:function(){i.hi.deviceMotionMonitor({action:this.endAction})},preloadAD:function(){var t=r.ZP.jumpType,e=this.cardInfo,n=e.jumpInfoForShake,i=e.jumpInfo;i.jumpType==t.AD_NATIVE&&this.M_go({jumpInfo:s({preLoad:1},i)}),n.jumpType==t.AD_NATIVE&&this.M_go({jumpInfo:s({preLoad:1},n)})},handleObserve:function(t,e){var n=this;t.forEach(function(t){var e=t.isIntersecting;n.isShakeCardExpose=e,e?n.activate():n.suspend(),n.isFirst&&e&&n.startAnimation()})},startAnimation:function(){var t=this,e=new Image;e.onload=function(){t.isFirst=!1,t.playingFlag=!0,t.useGif=!0,setTimeout(function(){t.useGif=!1,t.isOverSpread=!0,t.refreshAnimation();t.$emit("shake:card",t.cardInfo,{actionId:2,actionCarrier:3})},1200)},e.src=t.shakeImg},onResume:function(){this.isShakeCardExpose&&this.activate(),this.openADFlag=!0},transitionEnd:function(t){var e=this;if(e.transitionFlag&&t.target===t.currentTarget){e.transitionFlag=!1;var n=new Image;n.onload=function(){e.useGif=!0,e.showBubble=!0,setTimeout(function(){e.useGif=!1,e.showBubble=!1,e.isOverSpread=!1,setTimeout(function(){e.playingFlag=!1},400)},3600)},n.src=e.shakeImg}},refreshAnimation:function(){var t=new Image;t.onload=function(){};var e=this.shakeImg.split("?")[0];this.shakeImg="".concat(e,"?time=").concat(Math.random()),t.src=this.shakeImg}}}},341201:function(t,e,n){var i=n(566198),o=n(798509),r=n(521190),a=n(185048),c=n(462474),s=n(984928),u=n(728223),d=n(825800);function l(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=Array(e);n<e;n++)i[n]=t[n];return i}function f(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter(function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable}))),i.forEach(function(e){var i,o,r;i=t,o=e,r=n[e],o in i?Object.defineProperty(i,o,{value:r,enumerable:!0,configurable:!0,writable:!0}):i[o]=r})}return t}function p(t,e){return e=null!=e?e:{},Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):(function(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);n.push.apply(n,i)}return n})(Object(e)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(e,n))}),t}e.Z={name:"Swiper",components:{ActivityCardSlide:i.Z},mixins:[o.jB,o.uW],props:{data:{type:Object,default:function(){return{}}},item:{type:Object,default:function(){return{}}},source:{type:Object,default:function(){return{}}},pos:{type:Number,default:0},typePos:{type:Number,default:0},reservePaddingH:{type:Boolean,default:!1},decayFactor:{type:Number,default:1}},data:function(){return{renderList:[]}},computed:{itemInfo:function(){return o.Zr.isObjectEmpty(this.source)?this.item:this.source},isService:function(){return!o.Zr.isObjectEmpty(this.source)},isEliminatingPaddingH:function(){return this.isService&&!this.reservePaddingH}},watch:{item:{handler:function(){this.renderList=(this.itemInfo.list||[]).map(function(t,e){return p(f({videoUrl:""},t),{loading:!1,isShow:0===e,id:o.Zr.generateGuuId()})})},immediate:!0}},methods:{onTapCard:function(t){if((0,r.Z)(t)){var e=t.index;this.M_go(t),this.clickReport({clickZone:2,clickContent:"",index:e,reportItem:{expand:t.jumpInfo&&t.jumpInfo.expandReport,target:t}}),this.useReportCgi(e),this.$emit("tap")}},onTapButton:function(t){var e=this,n=t.index,i=this.renderList[n];if(t.cgiName){var a=t.cgiName,s=t.reportId;if(this.clickReport({clickZone:3,clickContent:t.title||"",index:n,reportItem:{target:t}}),i.loading)return;this.$set(i,"loading",!0);var l=setTimeout(function(){e.$set(i,"loading",!1)},3e3);o.hi.getCommonCgiData({cgiName:t.cgiName,data:t.param}).then(function(t){if(e.$set(i,"loading",!1),c.Z.$emit(o.U3.hideToast),0!==t.errCode&&t.errMsg){e.$store.commit("updateDialog",{confirm:"确定",cancel:!1,title:t.errMsg});return}var n=i.cardType==d.E.SUBSCRIBE||"finderLivingSubscribe"===a;if(n&&t.errCode<0){e.$store.commit("updateDialog",{confirm:"确定",cancel:!1,title:"操作失败"});return}""!==t.successMsg&&c.Z.$emit(o.U3.showToast,{action:n?u.g.pure:u.g.done,text:t.successMsg||"已领取到卡包"});var r=t.activityBlock&&t.activityBlock.button||t.button;r&&e.$set(i.activityBlock,"button",f({reportId:s},r)),l&&clearTimeout(l)})}else(0,r.Z)(t)?(this.clickReport({clickZone:3,clickContent:t.title||"",index:n,reportItem:{target:t}}),this.M_go(t),this.useReportCgi(n)):(0,r.Z)(i)&&(this.clickReport({clickZone:3,clickContent:t.title||"",index:n,reportItem:{expand:i.jumpInfo&&i.jumpInfo.expandReport,target:p(f({},i),{itemPos:t.itemPos||this.M_itemReportId(i.reportId,n+1),reportId:t.reportId||i.reportId})}}),this.M_go(i),this.useReportCgi(n));this.$emit("tap")},onTapMenu:function(t){if((0,r.Z)(t)){this.clickReport({clickZone:3,clickContent:t.title||"",index:e,reportItem:{expand:t.jumpInfo&&t.jumpInfo.expandReport,target:t}});var e=t.index;this.M_go(t),this.useReportCgi(e),this.$emit("tap")}},onTapInfo:function(t){var e=t.index;this.clickReport({clickZone:1,clickContent:t.activityBlock.title,index:e})},onTapFooter:function(t){var e=t.index,n=this.renderList[e];(0,r.Z)(t)?(this.clickReport({clickZone:3,clickContent:t.title||"",index:e,reportItem:{target:t}}),this.M_go(t),this.useReportCgi(e)):(0,r.Z)(n)&&(this.clickReport({clickZone:3,clickContent:t.title||"",index:e,reportItem:{expand:n.jumpInfo&&n.jumpInfo.expandReport,target:p(f({},n),{itemPos:t.itemPos||this.M_itemReportId(n.reportId,e+1),reportId:t.reportId||n.reportId})}}),this.M_go(n),this.useReportCgi(e)),this.$emit("tap:footer")},useReportCgi:function(t){var e=this,n=this.renderList[t],i=n.reportCgi;i&&i.cgiName&&o.hi.getCommonCgiData({cgiName:i.cgiName,data:i.param}).then(function(t){t.activityBlock&&t.activityBlock.button&&e.$set(n.activityBlock,"button",t.activityBlock.button)})},clickReport:function(t){var e=t.clickZone,n=t.clickContent,i=t.index,o=void 0===i?0:i,r=t.reportItem,c=void 0===r?{}:r,s=this.renderList[o],u=this.M_clickReport({clickZone:e,clickContent:void 0===n?"":n,reportItem:c},c.target).clickId;a.ZP.activitySlide.reportClick({docID:this.item.docID,cardPos:o,businessType:this.data.real_type?this.data.real_type:this.data.type,cardType:s.cardType,itemTitle:s.activityBlock&&s.activityBlock.title,itemType:s.realJumpType,logKeyId:u})},onExposeCard:function(t){c.Z.$emit(o.U3.exposeAnalysis),a.ZP.activitySlide.reportExpose({docID:this.item.docID,cardPos:t.index,businessType:this.data.real_type?this.data.real_type:this.data.type,cardType:t.cardType,itemTitle:t.activityBlock&&t.activityBlock.title,itemType:t.realJumpType})},onShowCard:function(t){var e=this,n=t.index,i=t.itemPos,o=this.renderList.findIndex(function(t){return t.isShow});if(o>-1){var r=this.renderList[o];r.isShow&&this.M_clickReport({ban12721Report:!0,banAdReport:!0,clickContent:r.activityBlock&&r.activityBlock.title,actionType:s.At.HOR_TOUCHMOVE,itemPos:i},r)}this.renderList.forEach(function(t,i){e.$set(t,"isShow",i===n)}),this.$emit("tap")},onShakeCard:function(t,e){var n,i,r=(i=2,function(t){if(Array.isArray(t))return t}(n=t)||function(t,e){var n,i,o=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=o){var r=[],a=!0,c=!1;try{for(o=o.call(t);!(a=(n=o.next()).done)&&(r.push(n.value),!e||r.length!==e);a=!0);}catch(t){c=!0,i=t}finally{try{!a&&null!=o.return&&o.return()}finally{if(c)throw i}}return r}}(n,2)||function(t,e){if(t){if("string"==typeof t)return l(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);if("Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return l(t,e)}}(n,i)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),c=r[0],s=r[1];if(c.jumpInfoForShake){var u=s.actionCarrier,d=s.actionId;7==d&&this.M_go({jumpInfo:c.jumpInfoForShake});var f=o.Zr.generateGuuId();a.ZP.activitySlide.reportShake({docID:this.item.docID,cardPos:e,logKeyId:f,businessType:this.data.real_type?this.data.real_type:this.data.type,cardType:c.cardType,actionId:d,actionCarrier:u}),this.$emit("tap")}}}}},652284:function(t,e,n){var i=n(798509),o=n(3090);e.Z={components:{Pk:o.Z},mixins:[i.jB,i.uW,i.Sx],provide:function(){var t=this;return{onPkMembersChange:function(e,n){"left"===e?t.leftMembers=n:t.rightMembers=n}}},data:function(){return{leftMembers:[],rightMembers:[]}},computed:{canShowPkTitle:function(){var t,e;return(null===(t=this.leftMembers)||void 0===t?!void 0:!t.length)&&(null===(e=this.rightMembers)||void 0===e?!void 0:!e.length)&&!!this.source.pkTitle},hasHeaderTitle:function(){var t;return!!(null===(t=this.source.header)||void 0===t?void 0:t.title)}},methods:{}}},865026:function(t,e){e.Z={name:"ActivityInfoFooter",props:{cardFooterInfo:{type:Object,default:function(){return{}}},containerPosInfo:{type:Object,default:function(){return{}}},duration:{type:Number,default:function(){return 300}},prevIndex:{type:Number,default:function(){return 0}},currentIndex:{type:Number,default:function(){return 0}},index:{type:Number,default:function(){return 0}}},computed:{source:function(){return this.cardFooterInfo.source},title:function(){return this.cardFooterInfo.title},cardProductUrl:function(){return this.cardFooterInfo.cardProductUrl},cLeft:function(){return this.containerPosInfo.left},cRight:function(){return this.containerPosInfo.right}},mounted:function(){this.drawShadow(),this.getInitStyleByIndex()},methods:{onTap:function(t){this.$emit("tap:footer",t)},getInitStyleByIndex:function(){var t=this.$refs["footer-".concat(this.index)].querySelectorAll(".text-animation");this.index===this.currentIndex?t.forEach(function(t){return t.style.opacity=1}):t.forEach(function(t){return t.style.opacity=0})},setTextMaxWidth:function(){var t=this.$refs["footer-".concat(this.index)].getBoundingClientRect().width,e=this.$refs["footer-right-".concat(this.index)].getBoundingClientRect().width;this.$refs["footer-left-".concat(this.index)].style.maxWidth="".concat(t-e-8,"px")},drawShadow:function(){var t=this,e="ad-car-shadow-".concat(this.index),n=document.getElementById(e),i=n.getContext("2d"),o=new Image;o.src=this.cardProductUrl,o.onload=function(){i.scale(1,-1),i.drawImage(o,0,-n.height,n.width,n.height),i.restore(),t.setTextMaxWidth()}},onScroll:function(){var t=this,e=document.querySelectorAll(".ad-car-show-footer"),n=this.cLeft+12+4,i=n+e[0].getBoundingClientRect().width+8;console.log("ttttt scroll",new Date().getTime()),e.forEach(function(e){var o=e.getBoundingClientRect().left,r=e.getBoundingClientRect().right;if(o<n&&r<i&&r>n){var a=i-n,c=Math.abs(o-n),s=Number(((r-n)/a).toFixed(4)),u=Number((c/a*64).toFixed(4));t.setCarElement({element:e,opacity:s,translateX:"-".concat(u,"px")}),t.setTextElement({element:e,opacity:s})}if(o>n&&r>i&&o<i){var d=i-n,l=Number(((i-o)/d).toFixed(4)),f=Number(((r-i)/d*64).toFixed(4));t.setCarElement({element:e,opacity:l,translateX:"".concat(f,"px")}),t.setTextElement({element:e,opacity:l})}})},onScrollEndImm:function(){var t=this,e=this.prevIndex,n=this.currentIndex;document.querySelectorAll(".ad-car-show-footer").forEach(function(i,o){if(o===e)e>n?(t.setCarElement({element:i,translateX:"64px",opacity:0,isAnimate:!0}),t.setTextElement({element:i,opacity:0,isAnimate:!0})):e===n?(t.setCarElement({element:i,translateX:"0px",opacity:1,isAnimate:!0}),t.setTextElement({element:i,opacity:1,isAnimate:!0})):(t.setCarElement({element:i,translateX:"-64px",opacity:0,isAnimate:!0}),t.setTextElement({element:i,opacity:0,isAnimate:!0}));else if(o===n)t.setCarElement({element:i,translateX:"0px",opacity:1,isAnimate:!0}),t.setTextElement({element:i,opacity:1,isAnimate:!0});else{var r="-64px";o>n&&(r="64px"),t.setCarElement({element:i,translateX:r,opacity:0,isAnimate:!0}),t.setTextElement({element:i,opacity:0,isAnimate:!0})}})},setCarElement:function(t){var e=t.element,n=t.opacity,i=t.translateX,o=t.isAnimate,r=e.querySelector(".ad-car-show-footer__right");void 0!==o&&o?(r.style.transition="".concat(this.duration,"ms"),r.style["-webkit-transition"]="".concat(this.duration,"ms")):(r.style.transition="0ms",r.style["-webkit-transition"]="0ms"),r.style.transform="translateX(".concat(i,")"),r.style.opacity=n},setTextElement:function(t){var e=this,n=t.element,i=t.opacity,o=t.isAnimate,r=n.querySelectorAll(".text-animation");void 0!==o&&o&&r.forEach(function(t){t.style.transition="".concat(e.duration,"ms"),t.style["-webkit-transition"]="".concat(e.duration,"ms")}),r.forEach(function(t){t.style.opacity=i})},onScrollEnd:function(){this.onScrollEndImm()}}}},270013:function(t,e){e.Z={name:"FashionShowSource",props:{fashionShowSourceInfo:{type:Object,default:function(){return{}}},index:{type:Number,default:function(){return 0}}},data:function(){return{scrolls:[]}},computed:{source:function(){return this.fashionShowSourceInfo.source},title:function(){return this.fashionShowSourceInfo.title}},mounted:function(){},methods:{}}},825800:function(t,e,n){n.d(e,{E:function(){return i}});var i=new(n(116746)).J({COMMON:51001,MENUS:51002,STOCK:51003,SINGLE:51004,SHAKE:51005,VIDEO:51006,SUBSCRIBE:51007})},809561:function(t,e){e.Z={methods:{ozJump:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.M_confirm_go(t.dialog,t)}},computed:{M_canHeaderJump:function(){return!!this.item.header&&!!(this.item.header.jumpUrl||this.item.header.weappUrl||this.item.header.jumpInfo)}}}}}]);