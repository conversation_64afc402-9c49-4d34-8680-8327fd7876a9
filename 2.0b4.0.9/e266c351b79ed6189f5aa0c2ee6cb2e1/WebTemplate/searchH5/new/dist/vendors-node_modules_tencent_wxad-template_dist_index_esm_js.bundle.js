"use strict";(self.webpackChunkweixin_search=self.webpackChunkweixin_search||[]).push([["vendors-node_modules_tencent_wxad-template_dist_index_esm_js"],{783970:function(e,t,n){let i,a,r,l,s,o,u,d,c,h,f,p,E,m,_,g,T,C,S,y,A,v,P,b,I,O,R,D,k,x,N,L,M,w,U,W,V,G,F,H,Y,B,z,j,Z,$,K,Q,X,q,J,ee,et,en,ei,ea,er,el,es,eo,eu,ed,ec,eh,ef,ep,eE,em,e_,eg,eT,eC,eS,ey,eA,ev,eP,eb,eI,eO,eR,eD,ek,ex,eN,eL,eM,ew,eU,eW,eV,eG,eF,eH,eY,eB,ez,ej,eZ,e$,eK;n.d(t,{A$:function(){return ip},Vv:function(){return aJ},iF:function(){return aq}});var eQ=Object.create,eX=Object.defineProperty,eq=Object.defineProperties,eJ=Object.getOwnPropertyDescriptor,e0=Object.getOwnPropertyDescriptors,e1=Object.getOwnPropertyNames,e2=Object.getOwnPropertySymbols,e5=Object.getPrototypeOf,e3=Object.prototype.hasOwnProperty,e4=Object.prototype.propertyIsEnumerable,e6=(e,t,n)=>t in e?eX(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,e8=(e,t)=>{for(var n in t||(t={}))e3.call(t,n)&&e6(e,n,t[n]);if(e2)for(var n of e2(t))e4.call(t,n)&&e6(e,n,t[n]);return e},e9=(e,t)=>eq(e,e0(t)),e7=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),te=(e,t,n,i)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let a of e1(t))e3.call(e,a)||a===n||eX(e,a,{get:()=>t[a],enumerable:!(i=eJ(t,a))||i.enumerable});return e},tt=(e,t,n)=>(n=null!=e?eQ(e5(e)):{},te(!t&&e&&e.__esModule?n:eX(n,"default",{value:e,enumerable:!0}),e)),tn=(e,t,n,i)=>{for(var a,r=i>1?void 0:i?eJ(t,n):t,l=e.length-1;l>=0;l--)(a=e[l])&&(r=(i?a(t,n,r):a(r))||r);return i&&r&&eX(t,n,r),r},ti=(e,t,n)=>new Promise((i,a)=>{var r=e=>{try{s(n.next(e))}catch(e){a(e)}},l=e=>{try{s(n.throw(e))}catch(e){a(e)}},s=e=>e.done?i(e.value):Promise.resolve(e.value).then(r,l);s((n=n.apply(e,t)).next())}),ta=e7((e,t)=>{var n=Object.getOwnPropertySymbols,i=Object.prototype.hasOwnProperty,a=Object.prototype.propertyIsEnumerable;t.exports=!function(){try{if(!Object.assign)return!1;var e=new String("abc");if(e[5]="de","5"===Object.getOwnPropertyNames(e)[0])return!1;for(var t={},n=0;n<10;n++)t["_"+String.fromCharCode(n)]=n;var i=Object.getOwnPropertyNames(t).map(function(e){return t[e]});if("0123456789"!==i.join(""))return!1;var a={};return"abcdefghijklmnopqrst".split("").forEach(function(e){a[e]=e}),"abcdefghijklmnopqrst"===Object.keys(Object.assign({},a)).join("")}catch(e){return!1}}()?function(e,t){for(var r,l,s=function(e){if(null==e)throw TypeError("Object.assign cannot be called with null or undefined");return Object(e)}(e),o=1;o<arguments.length;o++){for(var u in r=Object(arguments[o]),r)i.call(r,u)&&(s[u]=r[u]);if(n){l=n(r);for(var d=0;d<l.length;d++)a.call(r,l[d])&&(s[l[d]]=r[l[d]])}}return s}:Object.assign}),tr=e7(e=>{var t=ta(),n=60103,i=60106;e.Fragment=60107,e.StrictMode=60108,e.Profiler=60114;var a=60109,r=60110,l=60112;e.Suspense=60113;var s=60115,o=60116;"function"==typeof Symbol&&Symbol.for&&(n=(u=Symbol.for)("react.element"),i=u("react.portal"),e.Fragment=u("react.fragment"),e.StrictMode=u("react.strict_mode"),e.Profiler=u("react.profiler"),a=u("react.provider"),r=u("react.context"),l=u("react.forward_ref"),e.Suspense=u("react.suspense"),s=u("react.memo"),o=u("react.lazy"));var u,d="function"==typeof Symbol&&Symbol.iterator;function c(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var h={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},f={};function p(e,t,n){this.props=e,this.context=t,this.refs=f,this.updater=n||h}function E(){}function m(e,t,n){this.props=e,this.context=t,this.refs=f,this.updater=n||h}p.prototype.isReactComponent={},p.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error(c(85));this.updater.enqueueSetState(this,e,t,"setState")},p.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},E.prototype=p.prototype;var _=m.prototype=new E;_.constructor=m,t(_,p.prototype),_.isPureReactComponent=!0;var g={current:null},T=Object.prototype.hasOwnProperty,C={key:!0,ref:!0,__self:!0,__source:!0};function S(e,t,i){var a,r={},l=null,s=null;if(null!=t)for(a in void 0!==t.ref&&(s=t.ref),void 0!==t.key&&(l=""+t.key),t)T.call(t,a)&&!C.hasOwnProperty(a)&&(r[a]=t[a]);var o=arguments.length-2;if(1===o)r.children=i;else if(1<o){for(var u=Array(o),d=0;d<o;d++)u[d]=arguments[d+2];r.children=u}if(e&&e.defaultProps)for(a in o=e.defaultProps)void 0===r[a]&&(r[a]=o[a]);return{$$typeof:n,type:e,key:l,ref:s,props:r,_owner:g.current}}function y(e){return"object"==typeof e&&null!==e&&e.$$typeof===n}var A=/\/+/g;function v(e,t){var n,i;return"object"==typeof e&&null!==e&&null!=e.key?(n=""+e.key,i={"=":"=0",":":"=2"},"$"+n.replace(/[=:]/g,function(e){return i[e]})):t.toString(36)}function P(e,t,a){if(null==e)return e;var r=[],l=0;return function e(t,a,r,l,s){var o,u,h,f=typeof t;("undefined"===f||"boolean"===f)&&(t=null);var p=!1;if(null===t)p=!0;else switch(f){case"string":case"number":p=!0;break;case"object":switch(t.$$typeof){case n:case i:p=!0}}if(p){;return s=s(p=t),t=""===l?"."+v(p,0):l,Array.isArray(s)?(r="",null!=t&&(r=t.replace(A,"$&/")+"/"),e(s,a,r,"",function(e){return e})):null!=s&&(y(s)&&(o=s,u=r+(!s.key||p&&p.key===s.key?"":(""+s.key).replace(A,"$&/")+"/")+t,s={$$typeof:n,type:o.type,key:u,ref:o.ref,props:o.props,_owner:o._owner}),a.push(s)),1}if(p=0,l=""===l?".":l+":",Array.isArray(t))for(var E=0;E<t.length;E++){var m=l+v(f=t[E],E);p+=e(f,a,r,m,s)}else{;if("function"==typeof(m=null===(h=t)||"object"!=typeof h?null:"function"==typeof(h=d&&h[d]||h["@@iterator"])?h:null))for(t=m.call(t),E=0;!(f=t.next()).done;)m=l+v(f=f.value,E++),p+=e(f,a,r,m,s);else if("object"===f)throw Error(c(31,"[object Object]"==(a=""+t)?"object with keys {"+Object.keys(t).join(", ")+"}":a))}return p}(e,r,"","",function(e){return t.call(a,e,l++)}),r}function b(e){if(-1===e._status){var t=e._result;t=t(),e._status=0,e._result=t,t.then(function(t){0===e._status&&(t=t.default,e._status=1,e._result=t)},function(t){0===e._status&&(e._status=2,e._result=t)})}if(1===e._status)return e._result;throw e._result}var I={current:null};function O(){var e=I.current;if(null===e)throw Error(c(321));return e}e.Children={map:P,forEach:function(e,t,n){P(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return P(e,function(){t++}),t},toArray:function(e){return P(e,function(e){return e})||[]},only:function(e){if(!y(e))throw Error(c(143));return e}},e.Component=p,e.PureComponent=m,e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED={ReactCurrentDispatcher:I,ReactCurrentBatchConfig:{transition:0},ReactCurrentOwner:g,IsSomeRendererActing:{current:!1},assign:t},e.cloneElement=function(e,i,a){if(null==e)throw Error(c(267,e));var r=t({},e.props),l=e.key,s=e.ref,o=e._owner;if(null!=i){if(void 0!==i.ref&&(s=i.ref,o=g.current),void 0!==i.key&&(l=""+i.key),e.type&&e.type.defaultProps)var u=e.type.defaultProps;for(d in i)T.call(i,d)&&!C.hasOwnProperty(d)&&(r[d]=void 0===i[d]&&void 0!==u?u[d]:i[d])}var d=arguments.length-2;if(1===d)r.children=a;else if(1<d){u=Array(d);for(var h=0;h<d;h++)u[h]=arguments[h+2];r.children=u}return{$$typeof:n,type:e.type,key:l,ref:s,props:r,_owner:o}},e.createContext=function(e,t){return void 0===t&&(t=null),(e={$$typeof:r,_calculateChangedBits:t,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null}).Provider={$$typeof:a,_context:e},e.Consumer=e},e.createElement=S,e.createFactory=function(e){var t=S.bind(null,e);return t.type=e,t},e.createRef=function(){return{current:null}},e.forwardRef=function(e){return{$$typeof:l,render:e}},e.isValidElement=y,e.lazy=function(e){return{$$typeof:o,_payload:{_status:-1,_result:e},_init:b}},e.memo=function(e,t){return{$$typeof:s,type:e,compare:void 0===t?null:t}},e.useCallback=function(e,t){return O().useCallback(e,t)},e.useContext=function(e,t){return O().useContext(e,t)},e.useDebugValue=function(){},e.useEffect=function(e,t){return O().useEffect(e,t)},e.useImperativeHandle=function(e,t,n){return O().useImperativeHandle(e,t,n)},e.useLayoutEffect=function(e,t){return O().useLayoutEffect(e,t)},e.useMemo=function(e,t){return O().useMemo(e,t)},e.useReducer=function(e,t,n){return O().useReducer(e,t,n)},e.useRef=function(e){return O().useRef(e)},e.useState=function(e){return O().useState(e)},e.version="17.0.2"}),tl=e7((e,t)=>{t.exports=tr()}),ts=e7(e=>{function t(e,t){var n=e.length;for(e.push(t);;){var i=n-1>>>1,r=e[i];if(void 0!==r&&0<a(r,t))e[i]=t,e[n]=r,n=i;else break}}function n(e){return void 0===(e=e[0])?null:e}function i(e){var t=e[0];if(void 0!==t){var n=e.pop();if(n!==t){e[0]=n;for(var i=0,r=e.length;i<r;){var l=2*(i+1)-1,s=e[l],o=l+1,u=e[o];if(void 0!==s&&0>a(s,n))void 0!==u&&0>a(u,s)?(e[i]=u,e[o]=n,i=o):(e[i]=s,e[l]=n,i=l);else if(void 0!==u&&0>a(u,n))e[i]=u,e[o]=n,i=o;else break}}return t}return null}function a(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}"object"==typeof performance&&"function"==typeof performance.now?(u=performance,e.unstable_now=function(){return u.now()}):(c=(d=Date).now(),e.unstable_now=function(){return d.now()-c}),"undefined"==typeof window||"function"!=typeof MessageChannel?(h=null,f=null,p=function(){if(null!==h)try{var t=e.unstable_now();h(!0,t),h=null}catch(e){throw setTimeout(p,0),e}},r=function(e){null!==h?setTimeout(r,0,e):(h=e,setTimeout(p,0))},l=function(e,t){f=setTimeout(e,t)},s=function(){clearTimeout(f)},e.unstable_shouldYield=function(){return!1},o=e.unstable_forceFrameRate=function(){}):(E=window.setTimeout,m=window.clearTimeout,"undefined"!=typeof console&&(_=window.cancelAnimationFrame,"function"!=typeof window.requestAnimationFrame&&console.error("This browser doesn't support requestAnimationFrame. Make sure that you load a polyfill in older browsers. https://reactjs.org/link/react-polyfills"),"function"!=typeof _&&console.error("This browser doesn't support cancelAnimationFrame. Make sure that you load a polyfill in older browsers. https://reactjs.org/link/react-polyfills")),g=!1,T=null,C=-1,S=5,y=0,e.unstable_shouldYield=function(){return e.unstable_now()>=y},o=function(){},e.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):S=0<e?Math.floor(1e3/e):5},v=(A=new MessageChannel).port2,A.port1.onmessage=function(){if(null!==T){var t=e.unstable_now();y=t+S;try{T(!0,t)?v.postMessage(null):(g=!1,T=null)}catch(e){throw v.postMessage(null),e}}else g=!1},r=function(e){T=e,g||(g=!0,v.postMessage(null))},l=function(t,n){C=E(function(){t(e.unstable_now())},n)},s=function(){m(C),C=-1});var r,l,s,o,u,d,c,h,f,p,E,m,_,g,T,C,S,y,A,v,P=[],b=[],I=1,O=null,R=3,D=!1,k=!1,x=!1;function N(e){for(var a=n(b);null!==a;){if(null===a.callback)i(b);else if(a.startTime<=e)i(b),a.sortIndex=a.expirationTime,t(P,a);else break;a=n(b)}}function L(e){if(x=!1,N(e),!k){if(null!==n(P))k=!0,r(M);else{var t=n(b);null!==t&&l(L,t.startTime-e)}}}function M(t,a){k=!1,x&&(x=!1,s()),D=!0;var r=R;try{for(N(a),O=n(P);null!==O&&(!(O.expirationTime>a)||t&&!e.unstable_shouldYield());){var o=O.callback;if("function"==typeof o){O.callback=null,R=O.priorityLevel;var u=o(O.expirationTime<=a);a=e.unstable_now(),"function"==typeof u?O.callback=u:O===n(P)&&i(P),N(a)}else i(P);O=n(P)}if(null!==O)var d=!0;else{var c=n(b);null!==c&&l(L,c.startTime-a),d=!1}return d}finally{O=null,R=r,D=!1}}var w=o;e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(e){e.callback=null},e.unstable_continueExecution=function(){k||D||(k=!0,r(M))},e.unstable_getCurrentPriorityLevel=function(){return R},e.unstable_getFirstCallbackNode=function(){return n(P)},e.unstable_next=function(e){switch(R){case 1:case 2:case 3:var t=3;break;default:t=R}var n=R;R=t;try{return e()}finally{R=n}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=w,e.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=R;R=e;try{return t()}finally{R=n}},e.unstable_scheduleCallback=function(i,a,o){var u=e.unstable_now();switch(o="object"==typeof o&&null!==o?"number"==typeof(o=o.delay)&&0<o?u+o:u:u,i){case 1:var d=-1;break;case 2:d=250;break;case 5:d=1073741823;break;case 4:d=1e4;break;default:d=5e3}return d=o+d,i={id:I++,callback:a,priorityLevel:i,startTime:o,expirationTime:d,sortIndex:-1},o>u?(i.sortIndex=o,t(b,i),null===n(P)&&i===n(b)&&(x?s():x=!0,l(L,o-u))):(i.sortIndex=d,t(P,i),k||D||(k=!0,r(M))),i},e.unstable_wrapCallback=function(e){var t=R;return function(){var n=R;R=t;try{return e.apply(this,arguments)}finally{R=n}}}}),to=e7((e,t)=>{t.exports=ts()}),tu=e7((e,t)=>{t.exports=function(e){var n,i,a,r,l,s={},o=ta(),u=tl(),d=to();function c(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var h=u.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,f=60103,p=60106,E=60107,m=60108,_=60114,g=60109,T=60110,C=60112,S=60113,y=60120,A=60115,v=60116,P=60121,b=60129,I=60130,O=60131;if("function"==typeof Symbol&&Symbol.for){var R=Symbol.for;f=R("react.element"),p=R("react.portal"),E=R("react.fragment"),m=R("react.strict_mode"),_=R("react.profiler"),g=R("react.provider"),T=R("react.context"),C=R("react.forward_ref"),S=R("react.suspense"),y=R("react.suspense_list"),A=R("react.memo"),v=R("react.lazy"),P=R("react.block"),R("react.scope"),b=R("react.debug_trace_mode"),I=R("react.offscreen"),O=R("react.legacy_hidden")}var D="function"==typeof Symbol&&Symbol.iterator;function k(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=D&&e[D]||e["@@iterator"])?e:null}function x(e){if(null==e)return null;if("function"==typeof e)return e.displayName||e.name||null;if("string"==typeof e)return e;switch(e){case E:return"Fragment";case p:return"Portal";case _:return"Profiler";case m:return"StrictMode";case S:return"Suspense";case y:return"SuspenseList"}if("object"==typeof e)switch(e.$$typeof){case T:return(e.displayName||"Context")+".Consumer";case g:return(e._context.displayName||"Context")+".Provider";case C:var t=e.render;return t=t.displayName||t.name||"",e.displayName||(""!==t?"ForwardRef("+t+")":"ForwardRef");case A:return x(e.type);case P:return x(e._render);case v:t=e._payload,e=e._init;try{return x(e(t))}catch(e){}}return null}function N(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do(1026&(t=e).flags)!=0&&(n=t.return),e=t.return;while(e)}return 3===t.tag?n:null}function L(e){if(N(e)!==e)throw Error(c(188))}function M(e){var t=e.alternate;if(!t){if(null===(t=N(e)))throw Error(c(188));return t!==e?null:e}for(var n=e,i=t;;){var a=n.return;if(null===a)break;var r=a.alternate;if(null===r){if(null!==(i=a.return)){n=i;continue}break}if(a.child===r.child){for(r=a.child;r;){if(r===n)return L(a),e;if(r===i)return L(a),t;r=r.sibling}throw Error(c(188))}if(n.return!==i.return)n=a,i=r;else{for(var l=!1,s=a.child;s;){if(s===n){l=!0,n=a,i=r;break}if(s===i){l=!0,i=a,n=r;break}s=s.sibling}if(!l){for(s=r.child;s;){if(s===n){l=!0,n=r,i=a;break}if(s===i){l=!0,i=r,n=a;break}s=s.sibling}if(!l)throw Error(c(189))}}if(n.alternate!==i)throw Error(c(190))}if(3!==n.tag)throw Error(c(188));return n.stateNode.current===n?e:t}function w(e){if(!(e=M(e)))return null;for(var t=e;;){if(5===t.tag||6===t.tag)return t;if(t.child)t.child.return=t,t=t.child;else{if(t===e)break;for(;!t.sibling;){if(!t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}}return null}function U(e,t){for(var n=e.alternate;null!==t;){if(t===e||t===n)return!0;t=t.return}return!1}var W,V=e.getPublicInstance,G=e.getRootHostContext,F=e.getChildHostContext,H=e.prepareForCommit,Y=e.resetAfterCommit,B=e.createInstance,z=e.appendInitialChild,j=e.finalizeInitialChildren,Z=e.prepareUpdate,$=e.shouldSetTextContent,K=e.createTextInstance,Q=e.scheduleTimeout,X=e.cancelTimeout,q=e.noTimeout,J=e.isPrimaryRenderer,ee=e.supportsMutation,et=e.supportsPersistence,en=e.supportsHydration,ei=e.getInstanceFromNode,ea=e.makeOpaqueHydratingObject,er=e.makeClientId,el=e.beforeActiveInstanceBlur,es=e.afterActiveInstanceBlur,eo=e.preparePortalMount,eu=e.supportsTestSelectors,ed=e.findFiberRoot,ec=e.getBoundingRect,eh=e.getTextContent,ef=e.isHiddenSubtree,ep=e.matchAccessibilityRole,eE=e.setFocusIfFocusable,em=e.setupIntersectionObserver,e_=e.appendChild,eg=e.appendChildToContainer,eT=e.commitTextUpdate,eC=e.commitMount,eS=e.commitUpdate,ey=e.insertBefore,eA=e.insertInContainerBefore,ev=e.removeChild,eP=e.removeChildFromContainer,eb=e.resetTextContent,eI=e.hideInstance,eO=e.hideTextInstance,eR=e.unhideInstance,eD=e.unhideTextInstance,ek=e.clearContainer,ex=e.cloneInstance,eN=e.createContainerChildSet,eL=e.appendChildToContainerChildSet,eM=e.finalizeContainerChildren,ew=e.replaceContainerChildren,eU=e.cloneHiddenInstance,eW=e.cloneHiddenTextInstance,eV=e.canHydrateInstance,eG=e.canHydrateTextInstance,eF=e.isSuspenseInstancePending,eH=e.isSuspenseInstanceFallback,eY=e.getNextHydratableSibling,eB=e.getFirstHydratableChild,ez=e.hydrateInstance,ej=e.hydrateTextInstance,eZ=e.getNextHydratableInstanceAfterSuspenseInstance,e$=e.commitHydratedContainer,eK=e.commitHydratedSuspenseInstance;function eQ(e){if(void 0===W)try{throw Error()}catch(e){var t=e.stack.trim().match(/\n( *(at )?)/);W=t&&t[1]||""}return`
`+W+e}var eX=!1;function eq(e,t){if(!e||eX)return"";eX=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t){if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"==typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(e){var i=e}Reflect.construct(e,[],t)}else{try{t.call()}catch(e){i=e}e.call(t.prototype)}}else{try{throw Error()}catch(e){i=e}e()}}catch(e){if(e&&i&&"string"==typeof e.stack){for(var a=e.stack.split(`
`),r=i.stack.split(`
`),l=a.length-1,s=r.length-1;1<=l&&0<=s&&a[l]!==r[s];)s--;for(;1<=l&&0<=s;l--,s--)if(a[l]!==r[s]){if(1!==l||1!==s)do if(l--,0>--s||a[l]!==r[s])return`
`+a[l].replace(" at new "," at ");while(1<=l&&0<=s);break}}}finally{eX=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?eQ(e):""}var eJ=[],e0=-1;function e1(e){return{current:e}}function e2(e){0>e0||(e.current=eJ[e0],eJ[e0]=null,e0--)}function e5(e,t){eJ[++e0]=e.current,e.current=t}var e3={},e4=e1(e3),e6=e1(!1),e8=e3;function e9(e,t){var n=e.type.contextTypes;if(!n)return e3;var i=e.stateNode;if(i&&i.__reactInternalMemoizedUnmaskedChildContext===t)return i.__reactInternalMemoizedMaskedChildContext;var a,r={};for(a in n)r[a]=t[a];return i&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=r),r}function e7(e){return null!=(e=e.childContextTypes)}function te(){e2(e6),e2(e4)}function tt(e,t,n){if(e4.current!==e3)throw Error(c(168));e5(e4,t),e5(e6,n)}function tn(e,t,n){var i=e.stateNode;if(e=t.childContextTypes,"function"!=typeof i.getChildContext)return n;for(var a in i=i.getChildContext())if(!(a in e))throw Error(c(108,x(t)||"Unknown",a));return o({},n,i)}function ti(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||e3,e8=e4.current,e5(e4,e),e5(e6,e6.current),!0}function tr(e,t,n){var i=e.stateNode;if(!i)throw Error(c(169));n?(e=tn(e,t,e8),i.__reactInternalMemoizedMergedChildContext=e,e2(e6),e2(e4),e5(e4,e)):e2(e6),e5(e6,n)}var ts=null,tu=null;(0,d.unstable_now)();var td=0,tc=8;function th(e){if((1&e)!=0)return tc=15,1;if((2&e)!=0)return tc=14,2;if((4&e)!=0)return tc=13,4;var t=24&e;return 0!==t?(tc=12,t):(32&e)!=0?(tc=11,32):0!=(t=192&e)?(tc=10,t):(256&e)!=0?(tc=9,256):0!=(t=3584&e)?(tc=8,t):(4096&e)!=0?(tc=7,4096):0!=(t=4186112&e)?(tc=6,t):0!=(t=62914560&e)?(tc=5,t):67108864&e?(tc=4,67108864):(134217728&e)!=0?(tc=3,134217728):0!=(t=805306368&e)?(tc=2,t):(1073741824&e)!=0?(tc=1,1073741824):(tc=8,e)}function tf(e,t){var n=e.pendingLanes;if(0===n)return tc=0;var i=0,a=0,r=e.expiredLanes,l=e.suspendedLanes,s=e.pingedLanes;if(0!==r)i=r,a=tc=15;else if(0!=(r=134217727&n)){var o=r&~l;0!==o?(i=th(o),a=tc):0!=(s&=r)&&(i=th(s),a=tc)}else 0!=(r=n&~l)?(i=th(r),a=tc):0!==s&&(i=th(s),a=tc);if(0===i)return 0;if(i=n&((0>(i=31-tT(i))?0:1<<i)<<1)-1,0!==t&&t!==i&&(t&l)==0){if(th(t),a<=tc)return t;tc=a}if(0!==(t=e.entangledLanes))for(e=e.entanglements,t&=i;0<t;)a=1<<(n=31-tT(t)),i|=e[n],t&=~a;return i}function tp(e){return 0!=(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function tE(e,t){switch(e){case 15:return 1;case 14:return 2;case 12:return 0===(e=function(e){return e&-e}(24&~t))?tE(10,t):e;case 10:return 0===(e=function(e){return e&-e}(192&~t))?tE(8,t):e;case 8:return 0===(e=function(e){return e&-e}(3584&~t))&&0===(e=function(e){return e&-e}(4186112&~t))&&(e=512),e;case 2:return 0===(t=function(e){return e&-e}(805306368&~t))&&(t=268435456),t}throw Error(c(358,e))}function tm(e){return e&-e}function t_(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function tg(e,t,n){e.pendingLanes|=t;var i=t-1;e.suspendedLanes&=i,e.pingedLanes&=i,e=e.eventTimes,e[t=31-tT(t)]=n}var tT=Math.clz32?Math.clz32:function(e){return 0===e?32:31-(tC(e)/tS|0)|0},tC=Math.log,tS=Math.LN2,ty=d.unstable_runWithPriority,tA=d.unstable_scheduleCallback,tv=d.unstable_cancelCallback,tP=d.unstable_shouldYield,tb=d.unstable_requestPaint,tI=d.unstable_now,tO=d.unstable_getCurrentPriorityLevel,tR=d.unstable_ImmediatePriority,tD=d.unstable_UserBlockingPriority,tk=d.unstable_NormalPriority,tx=d.unstable_LowPriority,tN=d.unstable_IdlePriority,tL={},tM=void 0!==tb?tb:function(){},tw=null,tU=null,tW=!1,tV=tI(),tG=1e4>tV?tI:function(){return tI()-tV};function tF(){switch(tO()){case tR:return 99;case tD:return 98;case tk:return 97;case tx:return 96;case tN:return 95;default:throw Error(c(332))}}function tH(e){switch(e){case 99:return tR;case 98:return tD;case 97:return tk;case 96:return tx;case 95:return tN;default:throw Error(c(332))}}function tY(e,t){return ty(e=tH(e),t)}function tB(e,t,n){return tA(e=tH(e),t,n)}function tz(){if(null!==tU){var e=tU;tU=null,tv(e)}tj()}function tj(){if(!tW&&null!==tw){tW=!0;var e=0;try{var t=tw;tY(99,function(){for(;e<t.length;e++){var n=t[e];do n=n(!0);while(null!==n)}}),tw=null}catch(t){throw null!==tw&&(tw=tw.slice(e+1)),tA(tR,tz),t}finally{tW=!1}}}var tZ=h.ReactCurrentBatchConfig,t$="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},tK=Object.prototype.hasOwnProperty;function tQ(e,t){if(t$(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;var n=Object.keys(e),i=Object.keys(t);if(n.length!==i.length)return!1;for(i=0;i<n.length;i++)if(!tK.call(t,n[i])||!t$(e[n[i]],t[n[i]]))return!1;return!0}function tX(e,t){if(e&&e.defaultProps)for(var n in t=o({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}var tq=e1(null),tJ=null,t0=null,t1=null;function t2(){t1=t0=tJ=null}function t5(e,t){e=e.type._context,J?(e5(tq,e._currentValue),e._currentValue=t):(e5(tq,e._currentValue2),e._currentValue2=t)}function t3(e){var t=tq.current;e2(tq),e=e.type._context,J?e._currentValue=t:e._currentValue2=t}function t4(e,t){for(;null!==e;){var n=e.alternate;if((e.childLanes&t)===t){if(null===n||(n.childLanes&t)===t)break;n.childLanes|=t}else e.childLanes|=t,null!==n&&(n.childLanes|=t);e=e.return}}function t6(e,t){tJ=e,t1=t0=null,null!==(e=e.dependencies)&&null!==e.firstContext&&((e.lanes&t)!=0&&(iT=!0),e.firstContext=null)}function t8(e,t){if(t1!==e&&!1!==t&&0!==t){if(("number"!=typeof t||1073741823===t)&&(t1=e,t=1073741823),t={context:e,observedBits:t,next:null},null===t0){if(null===tJ)throw Error(c(308));t0=t,tJ.dependencies={lanes:0,firstContext:t,responders:null}}else t0=t0.next=t}return J?e._currentValue:e._currentValue2}var t9=!1;function t7(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null},effects:null}}function ne(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function nt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function nn(e,t){if(null!==(e=e.updateQueue)){var n=(e=e.shared).pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}}function ni(e,t){var n=e.updateQueue,i=e.alternate;if(null!==i&&n===(i=i.updateQueue)){var a=null,r=null;if(null!==(n=n.firstBaseUpdate)){do{var l={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===r?a=r=l:r=r.next=l,n=n.next}while(null!==n);null===r?a=r=t:r=r.next=t}else a=r=t;n={baseState:i.baseState,firstBaseUpdate:a,lastBaseUpdate:r,shared:i.shared,effects:i.effects},e.updateQueue=n;return}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function na(e,t,n,i){var a=e.updateQueue;t9=!1;var r=a.firstBaseUpdate,l=a.lastBaseUpdate,s=a.shared.pending;if(null!==s){a.shared.pending=null;var u=s,d=u.next;u.next=null,null===l?r=d:l.next=d,l=u;var c=e.alternate;if(null!==c){var h=(c=c.updateQueue).lastBaseUpdate;h!==l&&(null===h?c.firstBaseUpdate=d:h.next=d,c.lastBaseUpdate=u)}}if(null!==r){for(h=a.baseState,l=0,c=d=u=null;;){s=r.lane;var f=r.eventTime;if((i&s)===s){null!==c&&(c=c.next={eventTime:f,lane:0,tag:r.tag,payload:r.payload,callback:r.callback,next:null});e:{var p=e,E=r;switch(s=t,f=n,E.tag){case 1:if("function"==typeof(p=E.payload)){h=p.call(f,h,s);break e}h=p;break e;case 3:p.flags=-4097&p.flags|64;case 0:if(null==(s="function"==typeof(p=E.payload)?p.call(f,h,s):p))break e;h=o({},h,s);break e;case 2:t9=!0}}null!==r.callback&&(e.flags|=32,null===(s=a.effects)?a.effects=[r]:s.push(r))}else f={eventTime:f,lane:s,tag:r.tag,payload:r.payload,callback:r.callback,next:null},null===c?(d=c=f,u=h):c=c.next=f,l|=s;if(null===(r=r.next)){if(null===(s=a.shared.pending))break;r=s.next,s.next=null,a.lastBaseUpdate=s,a.shared.pending=null}}null===c&&(u=h),a.baseState=u,a.firstBaseUpdate=d,a.lastBaseUpdate=c,aS|=l,e.lanes=l,e.memoizedState=h}}function nr(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var i=e[t],a=i.callback;if(null!==a){if(i.callback=null,i=n,"function"!=typeof a)throw Error(c(191,a));a.call(i)}}}var nl=new u.Component().refs;function ns(e,t,n,i){n=null==(n=n(i,t=e.memoizedState))?t:o({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var no={isMounted:function(e){return!!(e=e._reactInternals)&&N(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var i=az(),a=aj(e),r=nt(i,a);r.payload=t,null!=n&&(r.callback=n),nn(e,r),aZ(e,a,i)},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var i=az(),a=aj(e),r=nt(i,a);r.tag=1,r.payload=t,null!=n&&(r.callback=n),nn(e,r),aZ(e,a,i)},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=az(),i=aj(e),a=nt(n,i);a.tag=2,null!=t&&(a.callback=t),nn(e,a),aZ(e,i,n)}};function nu(e,t,n,i,a,r,l){return"function"==typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(i,r,l):!t.prototype||!t.prototype.isPureReactComponent||!tQ(n,i)||!tQ(a,r)}function nd(e,t,n){var i=!1,a=e3,r=t.contextType;return"object"==typeof r&&null!==r?r=t8(r):(a=e7(t)?e8:e4.current,r=(i=null!=(i=t.contextTypes))?e9(e,a):e3),t=new t(n,r),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=no,e.stateNode=t,t._reactInternals=e,i&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=a,e.__reactInternalMemoizedMaskedChildContext=r),t}function nc(e,t,n,i){e=t.state,"function"==typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,i),"function"==typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,i),t.state!==e&&no.enqueueReplaceState(t,t.state,null)}function nh(e,t,n,i){var a=e.stateNode;a.props=n,a.state=e.memoizedState,a.refs=nl,t7(e);var r=t.contextType;"object"==typeof r&&null!==r?a.context=t8(r):(r=e7(t)?e8:e4.current,a.context=e9(e,r)),na(e,n,a,i),a.state=e.memoizedState,"function"==typeof(r=t.getDerivedStateFromProps)&&(ns(e,t,r,n),a.state=e.memoizedState),"function"==typeof t.getDerivedStateFromProps||"function"==typeof a.getSnapshotBeforeUpdate||"function"!=typeof a.UNSAFE_componentWillMount&&"function"!=typeof a.componentWillMount||(t=a.state,"function"==typeof a.componentWillMount&&a.componentWillMount(),"function"==typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),t!==a.state&&no.enqueueReplaceState(a,a.state,null),na(e,n,a,i),a.state=e.memoizedState),"function"==typeof a.componentDidMount&&(e.flags|=4)}var nf=Array.isArray;function np(e,t,n){if(null!==(e=n.ref)&&"function"!=typeof e&&"object"!=typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(c(309));var i=n.stateNode}if(!i)throw Error(c(147,e));var a=""+e;return null!==t&&null!==t.ref&&"function"==typeof t.ref&&t.ref._stringRef===a?t.ref:((t=function(e){var t=i.refs;t===nl&&(t=i.refs={}),null===e?delete t[a]:t[a]=e})._stringRef=a,t)}if("string"!=typeof e)throw Error(c(284));if(!n._owner)throw Error(c(290,e))}return e}function nE(e,t){if("textarea"!==e.type)throw Error(c(31,"[object Object]"===Object.prototype.toString.call(t)?"object with keys {"+Object.keys(t).join(", ")+"}":t))}function nm(e){function t(t,n){if(e){var i=t.lastEffect;null!==i?(i.nextEffect=n,t.lastEffect=n):t.firstEffect=t.lastEffect=n,n.nextEffect=null,n.flags=8}}function n(n,i){if(!e)return null;for(;null!==i;)t(n,i),i=i.sibling;return null}function i(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function a(e,t){return(e=r_(e,t)).index=0,e.sibling=null,e}function r(t,n,i){return t.index=i,e?null!==(i=t.alternate)?(i=i.index)<n?(t.flags=2,n):i:(t.flags=2,n):n}function l(t){return e&&null===t.alternate&&(t.flags=2),t}function s(e,t,n,i){return null===t||6!==t.tag?(t=rS(n,e.mode,i)).return=e:(t=a(t,n)).return=e,t}function o(e,t,n,i){return null!==t&&t.elementType===n.type?(i=a(t,n.props)).ref=np(e,t,n):(i=rg(n.type,n.key,n.props,null,e.mode,i)).ref=np(e,t,n),i.return=e,i}function u(e,t,n,i){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?(t=ry(n,e.mode,i)).return=e:(t=a(t,n.children||[])).return=e,t}function d(e,t,n,i,r){return null===t||7!==t.tag?(t=rT(n,e.mode,i,r)).return=e:(t=a(t,n)).return=e,t}function h(e,t,n){if("string"==typeof t||"number"==typeof t)return(t=rS(""+t,e.mode,n)).return=e,t;if("object"==typeof t&&null!==t){switch(t.$$typeof){case f:return(n=rg(t.type,t.key,t.props,null,e.mode,n)).ref=np(e,null,t),n.return=e,n;case p:return(t=ry(t,e.mode,n)).return=e,t}if(nf(t)||k(t))return(t=rT(t,e.mode,n,null)).return=e,t;nE(e,t)}return null}function m(e,t,n,i){var a=null!==t?t.key:null;if("string"==typeof n||"number"==typeof n)return null!==a?null:s(e,t,""+n,i);if("object"==typeof n&&null!==n){switch(n.$$typeof){case f:return n.key===a?n.type===E?d(e,t,n.props.children,i,a):o(e,t,n,i):null;case p:return n.key===a?u(e,t,n,i):null}if(nf(n)||k(n))return null!==a?null:d(e,t,n,i,null);nE(e,n)}return null}function _(e,t,n,i,a){if("string"==typeof i||"number"==typeof i)return s(t,e=e.get(n)||null,""+i,a);if("object"==typeof i&&null!==i){switch(i.$$typeof){case f:return e=e.get(null===i.key?n:i.key)||null,i.type===E?d(t,e,i.props.children,a,i.key):o(t,e,i,a);case p:return u(t,e=e.get(null===i.key?n:i.key)||null,i,a)}if(nf(i)||k(i))return d(t,e=e.get(n)||null,i,a,null);nE(t,i)}return null}return function(s,o,u,d){var g="object"==typeof u&&null!==u&&u.type===E&&null===u.key;g&&(u=u.props.children);var T="object"==typeof u&&null!==u;if(T)switch(u.$$typeof){case f:e:{for(T=u.key,g=o;null!==g;){if(g.key===T){if(7===g.tag){if(u.type===E){n(s,g.sibling),(o=a(g,u.props.children)).return=s,s=o;break e}}else if(g.elementType===u.type){n(s,g.sibling),(o=a(g,u.props)).ref=np(s,g,u),o.return=s,s=o;break e}n(s,g);break}t(s,g);g=g.sibling}u.type===E?((o=rT(u.props.children,s.mode,d,u.key)).return=s,s=o):((d=rg(u.type,u.key,u.props,null,s.mode,d)).ref=np(s,o,u),d.return=s,s=d)}return l(s);case p:e:{for(g=u.key;null!==o;){if(o.key===g){if(4===o.tag&&o.stateNode.containerInfo===u.containerInfo&&o.stateNode.implementation===u.implementation){n(s,o.sibling),(o=a(o,u.children||[])).return=s,s=o;break e}else{n(s,o);break}}t(s,o);o=o.sibling}(o=ry(u,s.mode,d)).return=s,s=o}return l(s)}if("string"==typeof u||"number"==typeof u)return u=""+u,null!==o&&6===o.tag?(n(s,o.sibling),(o=a(o,u)).return=s):(n(s,o),(o=rS(u,s.mode,d)).return=s),l(s=o);if(nf(u))return function(a,l,s,o){for(var u=null,d=null,c=l,f=l=0,p=null;null!==c&&f<s.length;f++){c.index>f?(p=c,c=null):p=c.sibling;var E=m(a,c,s[f],o);if(null===E){null===c&&(c=p);break}e&&c&&null===E.alternate&&t(a,c),l=r(E,l,f),null===d?u=E:d.sibling=E,d=E,c=p}if(f===s.length)return n(a,c),u;if(null===c){for(;f<s.length;f++)null!==(c=h(a,s[f],o))&&(l=r(c,l,f),null===d?u=c:d.sibling=c,d=c);return u}for(c=i(a,c);f<s.length;f++)null!==(p=_(c,a,f,s[f],o))&&(e&&null!==p.alternate&&c.delete(null===p.key?f:p.key),l=r(p,l,f),null===d?u=p:d.sibling=p,d=p);return e&&c.forEach(function(e){return t(a,e)}),u}(s,o,u,d);if(k(u))return function(a,l,s,o){var u=k(s);if("function"!=typeof u)throw Error(c(150));if(null==(s=u.call(s)))throw Error(c(151));for(var d=u=null,f=l,p=l=0,E=null,g=s.next();null!==f&&!g.done;p++,g=s.next()){f.index>p?(E=f,f=null):E=f.sibling;var T=m(a,f,g.value,o);if(null===T){null===f&&(f=E);break}e&&f&&null===T.alternate&&t(a,f),l=r(T,l,p),null===d?u=T:d.sibling=T,d=T,f=E}if(g.done)return n(a,f),u;if(null===f){for(;!g.done;p++,g=s.next())null!==(g=h(a,g.value,o))&&(l=r(g,l,p),null===d?u=g:d.sibling=g,d=g);return u}for(f=i(a,f);!g.done;p++,g=s.next())null!==(g=_(f,a,p,g.value,o))&&(e&&null!==g.alternate&&f.delete(null===g.key?p:g.key),l=r(g,l,p),null===d?u=g:d.sibling=g,d=g);return e&&f.forEach(function(e){return t(a,e)}),u}(s,o,u,d);if(T&&nE(s,u),void 0===u&&!g)switch(s.tag){case 1:case 22:case 0:case 11:case 15:throw Error(c(152,x(s.type)||"Component"))}return n(s,o)}}var n_=nm(!0),ng=nm(!1),nT={},nC=e1(nT),nS=e1(nT),ny=e1(nT);function nA(e){if(e===nT)throw Error(c(174));return e}function nv(e,t){e5(ny,t),e5(nS,e),e5(nC,nT),e=G(t),e2(nC),e5(nC,e)}function nP(){e2(nC),e2(nS),e2(ny)}function nb(e){var t=nA(ny.current),n=nA(nC.current);t=F(n,e.type,t),n!==t&&(e5(nS,e),e5(nC,t))}function nI(e){nS.current===e&&(e2(nC),e2(nS))}var nO=e1(0);function nR(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||eF(n)||eH(n)))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if((64&t.flags)!=0)return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var nD=null,nk=null,nx=!1;function nN(e,t){var n=rE(5,null,null,0);n.elementType="DELETED",n.type="DELETED",n.stateNode=t,n.return=e,n.flags=8,null!==e.lastEffect?(e.lastEffect.nextEffect=n,e.lastEffect=n):e.firstEffect=e.lastEffect=n}function nL(e,t){switch(e.tag){case 5:return null!==(t=eV(t,e.type,e.pendingProps))&&(e.stateNode=t,!0);case 6:return null!==(t=eG(t,e.pendingProps))&&(e.stateNode=t,!0);default:return!1}}function nM(e){if(nx){var t=nk;if(t){var n=t;if(!nL(e,t)){if(!(t=eY(n))||!nL(e,t)){e.flags=-1025&e.flags|2,nx=!1,nD=e;return}nN(nD,n)}nD=e,nk=eB(t)}else e.flags=-1025&e.flags|2,nx=!1,nD=e}}function nw(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;nD=e}function nU(e){if(!en||e!==nD)return!1;if(!nx)return nw(e),nx=!0,!1;var t=e.type;if(5!==e.tag||"head"!==t&&"body"!==t&&!$(t,e.memoizedProps))for(t=nk;t;)nN(e,t),t=eY(t);if(nw(e),13===e.tag){if(!en)throw Error(c(316));if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(c(317));nk=eZ(e)}else nk=nD?eY(e.stateNode):null;return!0}function nW(){en&&(nk=nD=null,nx=!1)}var nV=[];function nG(){for(var e=0;e<nV.length;e++){var t=nV[e];J?t._workInProgressVersionPrimary=null:t._workInProgressVersionSecondary=null}nV.length=0}var nF=h.ReactCurrentDispatcher,nH=h.ReactCurrentBatchConfig,nY=0,nB=null,nz=null,nj=null,nZ=!1,n$=!1;function nK(){throw Error(c(321))}function nQ(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!t$(e[n],t[n]))return!1;return!0}function nX(e,t,n,i,a,r){if(nY=r,nB=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,nF.current=null===e||null===e.memoizedState?iE:im,e=n(i,a),n$){r=0;do{if(n$=!1,!(25>r))throw Error(c(301));r+=1,nj=nz=null,t.updateQueue=null,nF.current=i_,e=n(i,a)}while(n$)}if(nF.current=ip,t=null!==nz&&null!==nz.next,nY=0,nj=nz=nB=null,nZ=!1,t)throw Error(c(300));return e}function nq(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===nj?nB.memoizedState=nj=e:nj=nj.next=e,nj}function nJ(){if(null===nz){var e=nB.alternate;e=null!==e?e.memoizedState:null}else e=nz.next;var t=null===nj?nB.memoizedState:nj.next;if(null!==t)nj=t,nz=e;else{if(null===e)throw Error(c(310));e={memoizedState:(nz=e).memoizedState,baseState:nz.baseState,baseQueue:nz.baseQueue,queue:nz.queue,next:null},null===nj?nB.memoizedState=nj=e:nj=nj.next=e}return nj}function n0(e,t){return"function"==typeof t?t(e):t}function n1(e){var t=nJ(),n=t.queue;if(null===n)throw Error(c(311));n.lastRenderedReducer=e;var i=nz,a=i.baseQueue,r=n.pending;if(null!==r){if(null!==a){var l=a.next;a.next=r.next,r.next=l}i.baseQueue=a=r,n.pending=null}if(null!==a){a=a.next,i=i.baseState;var s=l=r=null,o=a;do{var u=o.lane;if((nY&u)===u)null!==s&&(s=s.next={lane:0,action:o.action,eagerReducer:o.eagerReducer,eagerState:o.eagerState,next:null}),i=o.eagerReducer===e?o.eagerState:e(i,o.action);else{var d={lane:u,action:o.action,eagerReducer:o.eagerReducer,eagerState:o.eagerState,next:null};null===s?(l=s=d,r=i):s=s.next=d,nB.lanes|=u,aS|=u}o=o.next}while(null!==o&&o!==a);null===s?r=i:s.next=l,t$(i,t.memoizedState)||(iT=!0),t.memoizedState=i,t.baseState=r,t.baseQueue=s,n.lastRenderedState=i}return[t.memoizedState,n.dispatch]}function n2(e){var t=nJ(),n=t.queue;if(null===n)throw Error(c(311));n.lastRenderedReducer=e;var i=n.dispatch,a=n.pending,r=t.memoizedState;if(null!==a){n.pending=null;var l=a=a.next;do r=e(r,l.action),l=l.next;while(l!==a);t$(r,t.memoizedState)||(iT=!0),t.memoizedState=r,null===t.baseQueue&&(t.baseState=r),n.lastRenderedState=r}return[r,i]}function n5(e,t,n){var i=t._getVersion;i=i(t._source);var a=J?t._workInProgressVersionPrimary:t._workInProgressVersionSecondary;if(null!==a?e=a===i:(e=e.mutableReadLanes,(e=(nY&e)===e)&&(J?t._workInProgressVersionPrimary=i:t._workInProgressVersionSecondary=i,nV.push(t))),e)return n(t._source);throw nV.push(t),Error(c(350))}function n3(e,t,n,i){var a=af;if(null===a)throw Error(c(349));var r=t._getVersion,l=r(t._source),s=nF.current,o=s.useState(function(){return n5(a,t,n)}),u=o[1],d=o[0];o=nj;var h=e.memoizedState,f=h.refs,p=f.getSnapshot,E=h.source;h=h.subscribe;var m=nB;return e.memoizedState={refs:f,source:t,subscribe:i},s.useEffect(function(){f.getSnapshot=n,f.setSnapshot=u;var e=r(t._source);if(!t$(l,e)){e=n(t._source),t$(d,e)||(u(e),e=aj(m),a.mutableReadLanes|=e&a.pendingLanes),e=a.mutableReadLanes,a.entangledLanes|=e;for(var i=a.entanglements,s=e;0<s;){var o=31-tT(s),c=1<<o;i[o]|=e,s&=~c}}},[n,t,i]),s.useEffect(function(){return i(t._source,function(){var e=f.getSnapshot,n=f.setSnapshot;try{n(e(t._source));var i=aj(m);a.mutableReadLanes|=i&a.pendingLanes}catch(e){n(function(){throw e})}})},[t,i]),t$(p,n)&&t$(E,t)&&t$(h,i)||((e={pending:null,dispatch:null,lastRenderedReducer:n0,lastRenderedState:d}).dispatch=u=ih.bind(null,nB,e),o.queue=e,o.baseQueue=null,d=n5(a,t,n),o.memoizedState=o.baseState=d),d}function n4(e,t,n){return n3(nJ(),e,t,n)}function n6(e){var t=nq();return"function"==typeof e&&(e=e()),t.memoizedState=t.baseState=e,e=(e=t.queue={pending:null,dispatch:null,lastRenderedReducer:n0,lastRenderedState:e}).dispatch=ih.bind(null,nB,e),[t.memoizedState,e]}function n8(e,t,n,i){return e={tag:e,create:t,destroy:n,deps:i,next:null},null===(t=nB.updateQueue)?(t={lastEffect:null},nB.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(i=n.next,n.next=e,e.next=i,t.lastEffect=e),e}function n9(e){return e={current:e},nq().memoizedState=e}function n7(){return nJ().memoizedState}function ie(e,t,n,i){var a=nq();nB.flags|=e,a.memoizedState=n8(1|t,n,void 0,void 0===i?null:i)}function it(e,t,n,i){var a=nJ();i=void 0===i?null:i;var r=void 0;if(null!==nz){var l=nz.memoizedState;if(r=l.destroy,null!==i&&nQ(i,l.deps)){n8(t,n,r,i);return}}nB.flags|=e,a.memoizedState=n8(1|t,n,r,i)}function ii(e,t){return ie(516,4,e,t)}function ia(e,t){return it(516,4,e,t)}function ir(e,t){return it(4,2,e,t)}function il(e,t){return"function"==typeof t?(t(e=e()),function(){t(null)}):null!=t?(e=e(),t.current=e,function(){t.current=null}):void 0}function is(e,t,n){return n=null!=n?n.concat([e]):null,it(4,2,il.bind(null,t,e),n)}function io(){}function iu(e,t){var n=nJ();t=void 0===t?null:t;var i=n.memoizedState;return null!==i&&null!==t&&nQ(t,i[1])?i[0]:(n.memoizedState=[e,t],e)}function id(e,t){var n=nJ();t=void 0===t?null:t;var i=n.memoizedState;return null!==i&&null!==t&&nQ(t,i[1])?i[0]:(e=e(),n.memoizedState=[e,t],e)}function ic(e,t){var n=tF();tY(98>n?98:n,function(){e(!0)}),tY(97<n?97:n,function(){var n=nH.transition;nH.transition=1;try{e(!1),t()}finally{nH.transition=n}})}function ih(e,t,n){var i=az(),a=aj(e),r={lane:a,action:n,eagerReducer:null,eagerState:null,next:null},l=t.pending;if(null===l?r.next=r:(r.next=l.next,l.next=r),t.pending=r,l=e.alternate,e===nB||null!==l&&l===nB)n$=nZ=!0;else{if(0===e.lanes&&(null===l||0===l.lanes)&&null!==(l=t.lastRenderedReducer))try{var s=t.lastRenderedState,o=l(s,n);if(r.eagerReducer=l,r.eagerState=o,t$(o,s))return}catch(e){}finally{}aZ(e,a,i)}}var ip={readContext:t8,useCallback:nK,useContext:nK,useEffect:nK,useImperativeHandle:nK,useLayoutEffect:nK,useMemo:nK,useReducer:nK,useRef:nK,useState:nK,useDebugValue:nK,useDeferredValue:nK,useTransition:nK,useMutableSource:nK,useOpaqueIdentifier:nK,unstable_isNewReconciler:!1},iE={readContext:t8,useCallback:function(e,t){return nq().memoizedState=[e,void 0===t?null:t],e},useContext:t8,useEffect:ii,useImperativeHandle:function(e,t,n){return n=null!=n?n.concat([e]):null,ie(4,2,il.bind(null,t,e),n)},useLayoutEffect:function(e,t){return ie(4,2,e,t)},useMemo:function(e,t){var n=nq();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var i=nq();return t=void 0!==n?n(t):t,i.memoizedState=i.baseState=t,e=(e=i.queue={pending:null,dispatch:null,lastRenderedReducer:e,lastRenderedState:t}).dispatch=ih.bind(null,nB,e),[i.memoizedState,e]},useRef:n9,useState:n6,useDebugValue:io,useDeferredValue:function(e){var t=n6(e),n=t[0],i=t[1];return ii(function(){var t=nH.transition;nH.transition=1;try{i(e)}finally{nH.transition=t}},[e]),n},useTransition:function(){var e=n6(!1),t=e[0];return n9(e=ic.bind(null,e[1])),[e,t]},useMutableSource:function(e,t,n){var i=nq();return i.memoizedState={refs:{getSnapshot:t,setSnapshot:null},source:e,subscribe:n},n3(i,e,t,n)},useOpaqueIdentifier:function(){if(nx){var e=!1,t=ea(function(){throw e||(e=!0,n(er())),Error(c(355))}),n=n6(t)[1];return(2&nB.mode)==0&&(nB.flags|=516,n8(5,function(){n(er())},void 0,null)),t}return n6(t=er()),t},unstable_isNewReconciler:!1},im={readContext:t8,useCallback:iu,useContext:t8,useEffect:ia,useImperativeHandle:is,useLayoutEffect:ir,useMemo:id,useReducer:n1,useRef:n7,useState:function(){return n1(n0)},useDebugValue:io,useDeferredValue:function(e){var t=n1(n0),n=t[0],i=t[1];return ia(function(){var t=nH.transition;nH.transition=1;try{i(e)}finally{nH.transition=t}},[e]),n},useTransition:function(){var e=n1(n0)[0];return[n7().current,e]},useMutableSource:n4,useOpaqueIdentifier:function(){return n1(n0)[0]},unstable_isNewReconciler:!1},i_={readContext:t8,useCallback:iu,useContext:t8,useEffect:ia,useImperativeHandle:is,useLayoutEffect:ir,useMemo:id,useReducer:n2,useRef:n7,useState:function(){return n2(n0)},useDebugValue:io,useDeferredValue:function(e){var t=n2(n0),n=t[0],i=t[1];return ia(function(){var t=nH.transition;nH.transition=1;try{i(e)}finally{nH.transition=t}},[e]),n},useTransition:function(){var e=n2(n0)[0];return[n7().current,e]},useMutableSource:n4,useOpaqueIdentifier:function(){return n2(n0)[0]},unstable_isNewReconciler:!1},ig=h.ReactCurrentOwner,iT=!1;function iC(e,t,n,i){t.child=null===e?ng(t,null,n,i):n_(t,e.child,n,i)}function iS(e,t,n,i,a){n=n.render;var r=t.ref;return t6(t,a),i=nX(e,t,n,i,r,a),null===e||iT?(t.flags|=1,iC(e,t,i,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-517,e.lanes&=~a,iW(e,t,a))}function iy(e,t,n,i,a,r){if(null===e){var l=n.type;return"function"!=typeof l||rm(l)||void 0!==l.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=rg(n.type,null,i,t,t.mode,r)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=l,iA(e,t,l,i,a,r))}return l=e.child,(a&r)==0&&(a=l.memoizedProps,(n=null!==(n=n.compare)?n:tQ)(a,i)&&e.ref===t.ref)?iW(e,t,r):(t.flags|=1,(e=r_(l,i)).ref=t.ref,e.return=t,t.child=e)}function iA(e,t,n,i,a,r){if(null!==e&&tQ(e.memoizedProps,i)&&e.ref===t.ref){if(iT=!1,(r&a)==0)return t.lanes=e.lanes,iW(e,t,r);else(16384&e.flags)!=0&&(iT=!0)}return ib(e,t,n,i,r)}function iv(e,t,n){var i=t.pendingProps,a=i.children,r=null!==e?e.memoizedState:null;if("hidden"===i.mode||"unstable-defer-without-hiding"===i.mode){if((4&t.mode)==0)t.memoizedState={baseLanes:0},a1(t,n);else{if((1073741824&n)==0)return e=null!==r?r.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e},a1(t,e),null;t.memoizedState={baseLanes:0},a1(t,null!==r?r.baseLanes:n)}}else null!==r?(i=r.baseLanes|n,t.memoizedState=null):i=n,a1(t,i);return iC(e,t,a,n),t.child}function iP(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=128)}function ib(e,t,n,i,a){var r=e7(n)?e8:e4.current;return r=e9(t,r),t6(t,a),n=nX(e,t,n,i,r,a),null===e||iT?(t.flags|=1,iC(e,t,n,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-517,e.lanes&=~a,iW(e,t,a))}function iI(e,t,n,i,a){if(e7(n)){var r=!0;ti(t)}else r=!1;if(t6(t,a),null===t.stateNode)null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2),nd(t,n,i),nh(t,n,i,a),i=!0;else if(null===e){var l=t.stateNode,s=t.memoizedProps;l.props=s;var o=l.context,u=n.contextType;u="object"==typeof u&&null!==u?t8(u):e9(t,u=e7(n)?e8:e4.current);var d=n.getDerivedStateFromProps,c="function"==typeof d||"function"==typeof l.getSnapshotBeforeUpdate;c||"function"!=typeof l.UNSAFE_componentWillReceiveProps&&"function"!=typeof l.componentWillReceiveProps||(s!==i||o!==u)&&nc(t,l,i,u),t9=!1;var h=t.memoizedState;l.state=h,na(t,i,l,a),o=t.memoizedState,s!==i||h!==o||e6.current||t9?("function"==typeof d&&(ns(t,n,d,i),o=t.memoizedState),(s=t9||nu(t,n,s,i,h,o,u))?(c||"function"!=typeof l.UNSAFE_componentWillMount&&"function"!=typeof l.componentWillMount||("function"==typeof l.componentWillMount&&l.componentWillMount(),"function"==typeof l.UNSAFE_componentWillMount&&l.UNSAFE_componentWillMount()),"function"==typeof l.componentDidMount&&(t.flags|=4)):("function"==typeof l.componentDidMount&&(t.flags|=4),t.memoizedProps=i,t.memoizedState=o),l.props=i,l.state=o,l.context=u,i=s):("function"==typeof l.componentDidMount&&(t.flags|=4),i=!1)}else{l=t.stateNode,ne(e,t),s=t.memoizedProps,u=t.type===t.elementType?s:tX(t.type,s),l.props=u,c=t.pendingProps,h=l.context,o="object"==typeof(o=n.contextType)&&null!==o?t8(o):e9(t,o=e7(n)?e8:e4.current);var f=n.getDerivedStateFromProps;(d="function"==typeof f||"function"==typeof l.getSnapshotBeforeUpdate)||"function"!=typeof l.UNSAFE_componentWillReceiveProps&&"function"!=typeof l.componentWillReceiveProps||(s!==c||h!==o)&&nc(t,l,i,o),t9=!1,h=t.memoizedState,l.state=h,na(t,i,l,a);var p=t.memoizedState;s!==c||h!==p||e6.current||t9?("function"==typeof f&&(ns(t,n,f,i),p=t.memoizedState),(u=t9||nu(t,n,u,i,h,p,o))?(d||"function"!=typeof l.UNSAFE_componentWillUpdate&&"function"!=typeof l.componentWillUpdate||("function"==typeof l.componentWillUpdate&&l.componentWillUpdate(i,p,o),"function"==typeof l.UNSAFE_componentWillUpdate&&l.UNSAFE_componentWillUpdate(i,p,o)),"function"==typeof l.componentDidUpdate&&(t.flags|=4),"function"==typeof l.getSnapshotBeforeUpdate&&(t.flags|=256)):("function"!=typeof l.componentDidUpdate||s===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),"function"!=typeof l.getSnapshotBeforeUpdate||s===e.memoizedProps&&h===e.memoizedState||(t.flags|=256),t.memoizedProps=i,t.memoizedState=p),l.props=i,l.state=p,l.context=o,i=u):("function"!=typeof l.componentDidUpdate||s===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),"function"!=typeof l.getSnapshotBeforeUpdate||s===e.memoizedProps&&h===e.memoizedState||(t.flags|=256),i=!1)}return iO(e,t,n,i,r,a)}function iO(e,t,n,i,a,r){iP(e,t);var l=(64&t.flags)!=0;if(!i&&!l)return a&&tr(t,n,!1),iW(e,t,r);i=t.stateNode,ig.current=t;var s=l&&"function"!=typeof n.getDerivedStateFromError?null:i.render();return t.flags|=1,null!==e&&l?(t.child=n_(t,e.child,null,r),t.child=n_(t,null,s,r)):iC(e,t,s,r),t.memoizedState=i.state,a&&tr(t,n,!0),t.child}function iR(e){var t=e.stateNode;t.pendingContext?tt(e,t.pendingContext,t.pendingContext!==t.context):t.context&&tt(e,t.context,!1),nv(e,t.containerInfo)}var iD={dehydrated:null,retryLane:0};function ik(e,t,n){var i,a=t.pendingProps,r=nO.current,l=!1;return(i=(64&t.flags)!=0)||(i=(null===e||null!==e.memoizedState)&&(2&r)!=0),i?(l=!0,t.flags&=-65):null!==e&&null===e.memoizedState||void 0===a.fallback||!0===a.unstable_avoidThisFallback||(r|=1),e5(nO,1&r),null===e?(void 0!==a.fallback&&nM(t),e=a.children,r=a.fallback,l?(e=ix(t,e,r,n),t.child.memoizedState={baseLanes:n},t.memoizedState=iD,e):"number"==typeof a.unstable_expectedLoadTime?(e=ix(t,e,r,n),t.child.memoizedState={baseLanes:n},t.memoizedState=iD,t.lanes=33554432,e):((n=rC({mode:"visible",children:e},t.mode,n,null)).return=t,t.child=n)):(e.memoizedState,l?(a=iL(e,t,a.children,a.fallback,n),l=t.child,r=e.child.memoizedState,l.memoizedState=null===r?{baseLanes:n}:{baseLanes:r.baseLanes|n},l.childLanes=e.childLanes&~n,t.memoizedState=iD,a):(n=iN(e,t,a.children,n),t.memoizedState=null,n))}function ix(e,t,n,i){var a=e.mode,r=e.child;return t={mode:"hidden",children:t},(2&a)==0&&null!==r?(r.childLanes=0,r.pendingProps=t):r=rC(t,a,0,null),n=rT(n,a,i,null),r.return=e,n.return=e,r.sibling=n,e.child=r,n}function iN(e,t,n,i){var a=e.child;return e=a.sibling,n=r_(a,{mode:"visible",children:n}),(2&t.mode)==0&&(n.lanes=i),n.return=t,n.sibling=null,null!==e&&(e.nextEffect=null,e.flags=8,t.firstEffect=t.lastEffect=e),t.child=n}function iL(e,t,n,i,a){var r=t.mode,l=e.child;e=l.sibling;var s={mode:"hidden",children:n};return(2&r)==0&&t.child!==l?((n=t.child).childLanes=0,n.pendingProps=s,null!==(l=n.lastEffect)?(t.firstEffect=n.firstEffect,t.lastEffect=l,l.nextEffect=null):t.firstEffect=t.lastEffect=null):n=r_(l,s),null!==e?i=r_(e,i):(i=rT(i,r,a,null),i.flags|=2),i.return=t,n.return=t,n.sibling=i,t.child=n,i}function iM(e,t){e.lanes|=t;var n=e.alternate;null!==n&&(n.lanes|=t),t4(e.return,t)}function iw(e,t,n,i,a,r){var l=e.memoizedState;null===l?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:i,tail:n,tailMode:a,lastEffect:r}:(l.isBackwards=t,l.rendering=null,l.renderingStartTime=0,l.last=i,l.tail=n,l.tailMode=a,l.lastEffect=r)}function iU(e,t,n){var i=t.pendingProps,a=i.revealOrder,r=i.tail;if(iC(e,t,i.children,n),(2&(i=nO.current))!=0)i=1&i|2,t.flags|=64;else{if(null!==e&&(64&e.flags)!=0)e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&iM(e,n);else if(19===e.tag)iM(e,n);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}i&=1}if(e5(nO,i),(2&t.mode)==0)t.memoizedState=null;else switch(a){case"forwards":for(n=t.child,a=null;null!==n;)null!==(e=n.alternate)&&null===nR(e)&&(a=n),n=n.sibling;null===(n=a)?(a=t.child,t.child=null):(a=n.sibling,n.sibling=null),iw(t,!1,a,n,r,t.lastEffect);break;case"backwards":for(n=null,a=t.child,t.child=null;null!==a;){if(null!==(e=a.alternate)&&null===nR(e)){t.child=a;break}e=a.sibling,a.sibling=n,n=a,a=e}iw(t,!0,n,null,r,t.lastEffect);break;case"together":iw(t,!1,null,null,void 0,t.lastEffect);break;default:t.memoizedState=null}return t.child}function iW(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),aS|=t.lanes,(n&t.childLanes)!=0){if(null!==e&&t.child!==e.child)throw Error(c(153));if(null!==t.child){for(n=r_(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=r_(e,e.pendingProps)).return=t;n.sibling=null}return t.child}return null}function iV(e){e.flags|=4}if(ee)n=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)z(e,n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},i=function(){},a=function(e,t,n,i,a){(e=e.memoizedProps)!==i&&(n=Z(t.stateNode,n,e,i,a,nA(nC.current)),(t.updateQueue=n)&&iV(t))},r=function(e,t,n,i){n!==i&&iV(t)};else if(et){n=function(e,t,i,a){for(var r=t.child;null!==r;){if(5===r.tag){var l=r.stateNode;i&&a&&(l=eU(l,r.type,r.memoizedProps,r)),z(e,l)}else if(6===r.tag)l=r.stateNode,i&&a&&(l=eW(l,r.memoizedProps,r)),z(e,l);else if(4!==r.tag){if(13===r.tag&&(4&r.flags)!=0&&(l=null!==r.memoizedState)){var s=r.child;if(null!==s&&(null!==s.child&&(s.child.return=s,n(e,s,!0,l)),null!==(l=s.sibling))){l.return=r,r=l;continue}}if(null!==r.child){r.child.return=r,r=r.child;continue}}if(r===t)break;for(;null===r.sibling;){if(null===r.return||r.return===t)return;r=r.return}r.sibling.return=r.return,r=r.sibling}};var iG=function(e,t,n,i){for(var a=t.child;null!==a;){if(5===a.tag){var r=a.stateNode;n&&i&&(r=eU(r,a.type,a.memoizedProps,a)),eL(e,r)}else if(6===a.tag)r=a.stateNode,n&&i&&(r=eW(r,a.memoizedProps,a)),eL(e,r);else if(4!==a.tag){if(13===a.tag&&(4&a.flags)!=0&&(r=null!==a.memoizedState)){var l=a.child;if(null!==l&&(null!==l.child&&(l.child.return=l,iG(e,l,!0,r)),null!==(r=l.sibling))){r.return=a,a=r;continue}}if(null!==a.child){a.child.return=a,a=a.child;continue}}if(a===t)break;for(;null===a.sibling;){if(null===a.return||a.return===t)return;a=a.return}a.sibling.return=a.return,a=a.sibling}};i=function(e){var t=e.stateNode;if(null!==e.firstEffect){var n=t.containerInfo,i=eN(n);iG(i,e,!1,!1),t.pendingChildren=i,iV(e),eM(n,i)}},a=function(e,t,i,a,r){var l=e.stateNode,s=e.memoizedProps;if((e=null===t.firstEffect)&&s===a)t.stateNode=l;else{var o=t.stateNode,u=nA(nC.current),d=null;s!==a&&(d=Z(o,i,s,a,r,u)),e&&null===d?t.stateNode=l:(j(l=ex(l,d,i,s,a,t,e,o),i,a,r,u)&&iV(t),t.stateNode=l,e?iV(t):n(l,t,!1,!1))}},r=function(e,t,n,i){n!==i?(e=nA(ny.current),n=nA(nC.current),t.stateNode=K(i,e,n,t),iV(t)):t.stateNode=e.stateNode}}else i=function(){},a=function(){},r=function(){};function iF(e,t){if(!nx)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var i=null;null!==n;)null!==n.alternate&&(i=n),n=n.sibling;null===i?t||null===e.tail?e.tail=null:e.tail.sibling=null:i.sibling=null}}function iH(e,t){try{var n="",i=t;do n+=function(e){switch(e.tag){case 5:return eQ(e.type);case 16:return eQ("Lazy");case 13:return eQ("Suspense");case 19:return eQ("SuspenseList");case 0:case 2:case 15:return e=eq(e.type,!1);case 11:return e=eq(e.type.render,!1);case 22:return e=eq(e.type._render,!1);case 1:return e=eq(e.type,!0);default:return""}}(i),i=i.return;while(i);var a=n}catch(e){a=`
Error generating stack: `+e.message+`
`+e.stack}return{value:e,source:t,stack:a}}function iY(e,t){try{console.error(t.value)}catch(e){setTimeout(function(){throw e})}}var iB="function"==typeof WeakMap?WeakMap:Map;function iz(e,t,n){(n=nt(-1,n)).tag=3,n.payload={element:null};var i=t.value;return n.callback=function(){aR||(aR=!0,aD=i),iY(e,t)},n}function ij(e,t,n){(n=nt(-1,n)).tag=3;var i=e.type.getDerivedStateFromError;if("function"==typeof i){var a=t.value;n.payload=function(){return iY(e,t),i(a)}}var r=e.stateNode;return null!==r&&"function"==typeof r.componentDidCatch&&(n.callback=function(){"function"!=typeof i&&(null===ak?ak=new Set([this]):ak.add(this),iY(e,t));var n=t.stack;this.componentDidCatch(t.value,{componentStack:null!==n?n:""})}),n}var iZ="function"==typeof WeakSet?WeakSet:Set;function i$(e){var t=e.ref;if(null!==t){if("function"==typeof t)try{t(null)}catch(t){rr(e,t)}else t.current=null}}function iK(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var i=n.destroy;n.destroy=void 0,void 0!==i&&i()}n=n.next}while(n!==t)}}function iQ(e,t){if(ee)for(var n=e;;){if(5===n.tag){var i=n.stateNode;t?eI(i):eR(n.stateNode,n.memoizedProps)}else if(6===n.tag)i=n.stateNode,t?eO(i):eD(i,n.memoizedProps);else if((23!==n.tag&&24!==n.tag||null===n.memoizedState||n===e)&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===e)break;for(;null===n.sibling;){if(null===n.return||n.return===e)return;n=n.return}n.sibling.return=n.return,n=n.sibling}}function iX(e,t){if(tu&&"function"==typeof tu.onCommitFiberUnmount)try{tu.onCommitFiberUnmount(ts,t)}catch(e){}switch(t.tag){case 0:case 11:case 14:case 15:case 22:if(null!==(e=t.updateQueue)&&null!==(e=e.lastEffect)){var n=e=e.next;do{var i=n,a=i.destroy;if(i=i.tag,void 0!==a){if((4&i)!=0)rn(t,n);else{i=t;try{a()}catch(e){rr(i,e)}}}n=n.next}while(n!==e)}break;case 1:if(i$(t),"function"==typeof(e=t.stateNode).componentWillUnmount)try{e.props=t.memoizedProps,e.state=t.memoizedState,e.componentWillUnmount()}catch(e){rr(t,e)}break;case 5:i$(t);break;case 4:ee?i2(e,t):et&&et&&(e=eN(t=t.stateNode.containerInfo),ew(t,e))}}function iq(e,t){for(var n=t;;)if(iX(e,n),null===n.child||ee&&4===n.tag){if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}else n.child.return=n,n=n.child}function iJ(e){e.alternate=null,e.child=null,e.dependencies=null,e.firstEffect=null,e.lastEffect=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.return=null,e.updateQueue=null}function i0(e){return 5===e.tag||3===e.tag||4===e.tag}function i1(e){if(ee){e:{for(var t=e.return;null!==t;){if(i0(t))break e;t=t.return}throw Error(c(160))}var n=t;switch(t=n.stateNode,n.tag){case 5:var i=!1;break;case 3:case 4:t=t.containerInfo,i=!0;break;default:throw Error(c(161))}16&n.flags&&(eb(t),n.flags&=-17);e:t:for(n=e;;){for(;null===n.sibling;){if(null===n.return||i0(n.return)){n=null;break e}n=n.return}for(n.sibling.return=n.return,n=n.sibling;5!==n.tag&&6!==n.tag&&18!==n.tag;){if(2&n.flags||null===n.child||4===n.tag)continue t;n.child.return=n,n=n.child}if(!(2&n.flags)){n=n.stateNode;break e}}i?function e(t,n,i){var a=t.tag,r=5===a||6===a;if(r)t=r?t.stateNode:t.stateNode.instance,n?eA(i,t,n):eg(i,t);else if(4!==a&&null!==(t=t.child))for(e(t,n,i),t=t.sibling;null!==t;)e(t,n,i),t=t.sibling}(e,n,t):function e(t,n,i){var a=t.tag,r=5===a||6===a;if(r)t=r?t.stateNode:t.stateNode.instance,n?ey(i,t,n):e_(i,t);else if(4!==a&&null!==(t=t.child))for(e(t,n,i),t=t.sibling;null!==t;)e(t,n,i),t=t.sibling}(e,n,t)}}function i2(e,t){for(var n,i,a=t,r=!1;;){if(!r){r=a.return;e:for(;;){if(null===r)throw Error(c(160));switch(n=r.stateNode,r.tag){case 5:i=!1;break e;case 3:case 4:n=n.containerInfo,i=!0;break e}r=r.return}r=!0}if(5===a.tag||6===a.tag)iq(e,a),i?eP(n,a.stateNode):ev(n,a.stateNode);else if(4===a.tag){if(null!==a.child){n=a.stateNode.containerInfo,i=!0,a.child.return=a,a=a.child;continue}}else if(iX(e,a),null!==a.child){a.child.return=a,a=a.child;continue}if(a===t)break;for(;null===a.sibling;){if(null===a.return||a.return===t)return;4===(a=a.return).tag&&(r=!1)}a.sibling.return=a.return,a=a.sibling}}function i5(e,t){if(ee){switch(t.tag){case 0:case 11:case 14:case 15:case 22:iK(3,t);return;case 1:case 12:case 17:return;case 5:var n=t.stateNode;if(null!=n){var i=t.memoizedProps;e=null!==e?e.memoizedProps:i;var a=t.type,r=t.updateQueue;t.updateQueue=null,null!==r&&eS(n,r,a,e,i,t)}return;case 6:if(null===t.stateNode)throw Error(c(162));n=t.memoizedProps,eT(t.stateNode,null!==e?e.memoizedProps:n,n);return;case 3:en&&(t=t.stateNode).hydrate&&(t.hydrate=!1,e$(t.containerInfo));return;case 13:i3(t),i4(t);return;case 19:i4(t);return;case 23:case 24:iQ(t,null!==t.memoizedState);return}throw Error(c(163))}switch(t.tag){case 0:case 11:case 14:case 15:case 22:iK(3,t);return;case 12:case 23:case 24:return;case 13:i3(t),i4(t);return;case 19:i4(t);return;case 3:en&&(n=t.stateNode).hydrate&&(n.hydrate=!1,e$(n.containerInfo))}e:if(et){switch(t.tag){case 1:case 5:case 6:case 20:break e;case 3:case 4:ew((t=t.stateNode).containerInfo,t.pendingChildren);break e}throw Error(c(163))}}function i3(e){null!==e.memoizedState&&(aP=tG(),ee&&iQ(e.child,!0))}function i4(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new iZ),t.forEach(function(t){var i=rs.bind(null,e,t);n.has(t)||(n.add(t),t.then(i,i))})}}var i6=0,i8=1,i9=2,i7=3,ae=4;if("function"==typeof Symbol&&Symbol.for){var at=Symbol.for;i6=at("selector.component"),i8=at("selector.has_pseudo_class"),i9=at("selector.role"),i7=at("selector.test_id"),ae=at("selector.text")}function an(e){var t=ei(e);if(null!=t){if("string"!=typeof t.memoizedProps["data-testname"])throw Error(c(364));return t}if(null===(e=ed(e)))throw Error(c(362));return e.stateNode.current}function ai(e,t){switch(t.$$typeof){case i6:if(e.type===t.value)return!0;break;case i8:e:{t=t.value,e=[e,0];for(var n=0;n<e.length;){var i=e[n++],a=e[n++],r=t[a];if(5!==i.tag||!ef(i)){for(;null!=r&&ai(i,r);)r=t[++a];if(a===t.length){t=!0;break e}for(i=i.child;null!==i;)e.push(i,a),i=i.sibling}}t=!1}return t;case i9:if(5===e.tag&&ep(e.stateNode,t.value))return!0;break;case ae:if((5===e.tag||6===e.tag)&&null!==(e=eh(e))&&0<=e.indexOf(t.value))return!0;break;case i7:if(5===e.tag&&"string"==typeof(e=e.memoizedProps["data-testname"])&&e.toLowerCase()===t.value.toLowerCase())return!0;break;default:throw Error(c(365,t))}return!1}function aa(e){switch(e.$$typeof){case i6:return"<"+(x(e.value)||"Unknown")+">";case i8:return":has("+(aa(e)||"")+")";case i9:return'[role="'+e.value+'"]';case ae:return'"'+e.value+'"';case i7:return'[data-testname="'+e.value+'"]';default:throw Error(c(365,e))}}function ar(e,t){var n=[];e=[e,0];for(var i=0;i<e.length;){var a=e[i++],r=e[i++],l=t[r];if(5!==a.tag||!ef(a)){for(;null!=l&&ai(a,l);)l=t[++r];if(r===t.length)n.push(a);else for(a=a.child;null!==a;)e.push(a,r),a=a.sibling}}return n}function al(e,t){if(!eu)throw Error(c(363));e=ar(e=an(e),t),t=[],e=Array.from(e);for(var n=0;n<e.length;){var i=e[n++];if(5===i.tag)ef(i)||t.push(i.stateNode);else for(i=i.child;null!==i;)e.push(i),i=i.sibling}return t}var as=null,ao=Math.ceil,au=h.ReactCurrentDispatcher,ad=h.ReactCurrentOwner,ac=h.IsSomeRendererActing,ah=0,af=null,ap=null,aE=0,am=0,a_=e1(0),ag=0,aT=null,aC=0,aS=0,ay=0,aA=0,av=null,aP=0,ab=1/0;function aI(){ab=tG()+500}var aO=null,aR=!1,aD=null,ak=null,ax=!1,aN=null,aL=90,aM=[],aw=[],aU=null,aW=0,aV=null,aG=-1,aF=0,aH=0,aY=null,aB=!1;function az(){return(48&ah)!=0?tG():-1!==aG?aG:aG=tG()}function aj(e){if((2&(e=e.mode))==0)return 1;if((4&e)==0)return 99===tF()?1:2;if(0===aF&&(aF=aC),0!==tZ.transition){0!==aH&&(aH=null!==av?av.pendingLanes:0),e=aF;var t=4186112&~aH;return 0==(t&=-t)&&0==(t=(e=4186112&~e)&-e)&&(t=8192),t}return e=tF(),e=(4&ah)!=0&&98===e?tE(12,aF):tE(e=function(e){switch(e){case 99:return 15;case 98:return 10;case 97:case 96:return 8;case 95:return 2;default:return 0}}(e),aF)}function aZ(e,t,n){if(50<aW)throw aW=0,aV=null,Error(c(185));if(null===(e=a$(e,t)))return null;tg(e,t,n),e===af&&(ay|=t,4===ag&&aX(e,aE));var i=tF();1===t?(8&ah)!=0&&(48&ah)==0?aq(e):(aK(e,n),0===ah&&(aI(),tz())):((4&ah)==0||98!==i&&99!==i||(null===aU?aU=new Set([e]):aU.add(e)),aK(e,n)),av=e}function a$(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}function aK(e,t){for(var n=e.callbackNode,i=e.suspendedLanes,a=e.pingedLanes,r=e.expirationTimes,l=e.pendingLanes;0<l;){var s=31-tT(l),o=1<<s,u=r[s];if(-1===u){if((o&i)==0||(o&a)!=0){u=t,th(o);var d=tc;r[s]=10<=d?u+250:6<=d?u+5e3:-1}}else u<=t&&(e.expiredLanes|=o);l&=~o}if(i=tf(e,e===af?aE:0),t=tc,0===i)null!==n&&(n!==tL&&tv(n),e.callbackNode=null,e.callbackPriority=0);else{if(null!==n){if(e.callbackPriority===t)return;n!==tL&&tv(n)}15===t?(n=aq.bind(null,e),null===tw?(tw=[n],tU=tA(tR,tj)):tw.push(n),n=tL):n=14===t?tB(99,aq.bind(null,e)):tB(n=function(e){switch(e){case 15:case 14:return 99;case 13:case 12:case 11:case 10:return 98;case 9:case 8:case 7:case 6:case 4:case 5:return 97;case 3:case 2:case 1:return 95;case 0:return 90;default:throw Error(c(358,e))}}(t),aQ.bind(null,e)),e.callbackPriority=t,e.callbackNode=n}}function aQ(e){if(aG=-1,aH=aF=0,(48&ah)!=0)throw Error(c(327));var t=e.callbackNode;if(rt()&&e.callbackNode!==t)return null;var n=tf(e,e===af?aE:0);if(0===n)return null;var i=n,a=ah;ah|=16;var r=a4();for((af!==e||aE!==i)&&(aI(),a5(e,i));;)try{(function(){for(;null!==ap&&!tP();)a8(ap)})();break}catch(t){a3(e,t)}if(t2(),au.current=r,ah=a,null!==ap?i=0:(af=null,aE=0,i=ag),(aC&ay)!=0)a5(e,0);else if(0!==i){if(2===i&&(ah|=64,e.hydrate&&(e.hydrate=!1,ek(e.containerInfo)),0!==(n=tp(e))&&(i=a6(e,n))),1===i)throw t=aT,a5(e,0),aX(e,n),aK(e,tG()),t;switch(e.finishedWork=e.current.alternate,e.finishedLanes=n,i){case 0:case 1:throw Error(c(345));case 2:case 5:a7(e);break;case 3:if(aX(e,n),(62914560&n)===n&&10<(i=aP+500-tG())){if(0!==tf(e,0))break;if(((a=e.suspendedLanes)&n)!==n){az(),e.pingedLanes|=e.suspendedLanes&a;break}e.timeoutHandle=Q(a7.bind(null,e),i);break}a7(e);break;case 4:if(aX(e,n),(4186112&n)===n)break;for(i=e.eventTimes,a=-1;0<n;){var l=31-tT(n);r=1<<l,(l=i[l])>a&&(a=l),n&=~r}if(n=a,10<(n=(120>(n=tG()-n)?120:480>n?480:1080>n?1080:1920>n?1920:3e3>n?3e3:4320>n?4320:1960*ao(n/1960))-n)){e.timeoutHandle=Q(a7.bind(null,e),n);break}a7(e);break;default:throw Error(c(329))}}return aK(e,tG()),e.callbackNode===t?aQ.bind(null,e):null}function aX(e,t){for(t&=~aA,t&=~ay,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-tT(t),i=1<<n;e[n]=-1,t&=~i}}function aq(e){if((48&ah)!=0)throw Error(c(327));if(rt(),e===af&&(e.expiredLanes&aE)!=0){var t=aE,n=a6(e,t);(aC&ay)!=0&&(t=tf(e,t),n=a6(e,t))}else t=tf(e,0),n=a6(e,t);if(0!==e.tag&&2===n&&(ah|=64,e.hydrate&&(e.hydrate=!1,ek(e.containerInfo)),0!==(t=tp(e))&&(n=a6(e,t))),1===n)throw n=aT,a5(e,0),aX(e,t),aK(e,tG()),n;return e.finishedWork=e.current.alternate,e.finishedLanes=t,a7(e),aK(e,tG()),null}function aJ(e,t){var n=ah;ah|=1;try{return e(t)}finally{0===(ah=n)&&(aI(),tz())}}function a0(e,t){var n=ah;if((48&n)!=0)return e(t);ah|=1;try{if(e)return tY(99,e.bind(null,t))}finally{ah=n,tz()}}function a1(e,t){e5(a_,am),am|=t,aC|=t}function a2(){am=a_.current,e2(a_)}function a5(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==q&&(e.timeoutHandle=q,X(n)),null!==ap)for(n=ap.return;null!==n;){var i=n;switch(i.tag){case 1:null!=(i=i.type.childContextTypes)&&te();break;case 3:nP(),e2(e6),e2(e4),nG();break;case 5:nI(i);break;case 4:nP();break;case 13:case 19:e2(nO);break;case 10:t3(i);break;case 23:case 24:a2()}n=n.return}af=e,ap=r_(e.current,null),aE=am=aC=t,ag=0,aT=null,aA=ay=aS=0}function a3(e,t){for(;;){var n=ap;try{if(t2(),nF.current=ip,nZ){for(var i=nB.memoizedState;null!==i;){var a=i.queue;null!==a&&(a.pending=null),i=i.next}nZ=!1}if(nY=0,nj=nz=nB=null,n$=!1,ad.current=null,null===n||null===n.return){ag=1,aT=t,ap=null;break}e:{var r=e,l=n.return,s=n,o=t;if(t=aE,s.flags|=2048,s.firstEffect=s.lastEffect=null,null!==o&&"object"==typeof o&&"function"==typeof o.then){var u,d=o;if((2&s.mode)==0){var c=s.alternate;c?(s.updateQueue=c.updateQueue,s.memoizedState=c.memoizedState,s.lanes=c.lanes):(s.updateQueue=null,s.memoizedState=null)}var h=(1&nO.current)!=0,f=l;do{if(u=13===f.tag){var p=f.memoizedState;if(null!==p)u=null!==p.dehydrated;else{var E=f.memoizedProps;u=void 0!==E.fallback&&(!0!==E.unstable_avoidThisFallback||!h)}}if(u){var m=f.updateQueue;if(null===m){var _=new Set;_.add(d),f.updateQueue=_}else m.add(d);if((2&f.mode)==0){if(f.flags|=64,s.flags|=16384,s.flags&=-2981,1===s.tag){if(null===s.alternate)s.tag=17;else{var g=nt(-1,1);g.tag=2,nn(s,g)}}s.lanes|=1;break e}o=void 0,s=t;var T=r.pingCache;if(null===T?(T=r.pingCache=new iB,o=new Set,T.set(d,o)):(o=T.get(d),void 0===o&&(o=new Set,T.set(d,o))),!o.has(s)){o.add(s);var C=rl.bind(null,r,d,s);d.then(C,C)}f.flags|=4096,f.lanes=t;break e}f=f.return}while(null!==f);o=Error((x(s.type)||"A React component")+` suspended while rendering, but no fallback UI was specified.

Add a <Suspense fallback=...> component higher in the tree to provide a loading indicator or placeholder to display.`)}5!==ag&&(ag=2),o=iH(o,s),f=l;do{switch(f.tag){case 3:r=o,f.flags|=4096,t&=-t,f.lanes|=t;var S=iz(f,r,t);ni(f,S);break e;case 1:r=o;var y=f.type,A=f.stateNode;if((64&f.flags)==0&&("function"==typeof y.getDerivedStateFromError||null!==A&&"function"==typeof A.componentDidCatch&&(null===ak||!ak.has(A)))){f.flags|=4096,t&=-t,f.lanes|=t;var v=ij(f,r,t);ni(f,v);break e}}f=f.return}while(null!==f)}a9(n)}catch(e){t=e,ap===n&&null!==n&&(ap=n=n.return);continue}break}}function a4(){var e=au.current;return au.current=ip,null===e?ip:e}function a6(e,t){var n=ah;ah|=16;var i=a4();for(af===e&&aE===t||a5(e,t);;)try{(function(){for(;null!==ap;)a8(ap)})();break}catch(t){a3(e,t)}if(t2(),ah=n,au.current=i,null!==ap)throw Error(c(261));return af=null,aE=0,ag}function a8(e){var t=l(e.alternate,e,am);e.memoizedProps=e.pendingProps,null===t?a9(e):ap=t,ad.current=null}function a9(e){var t=e;do{var l=t.alternate;if(e=t.return,(2048&t.flags)==0){if(null!==(l=function(e,t,l){var s=t.pendingProps;switch(t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return null;case 1:case 17:return e7(t.type)&&te(),null;case 3:return nP(),e2(e6),e2(e4),nG(),(s=t.stateNode).pendingContext&&(s.context=s.pendingContext,s.pendingContext=null),(null===e||null===e.child)&&(nU(t)?iV(t):s.hydrate||(t.flags|=256)),i(t),null;case 5:nI(t);var o=nA(ny.current);if(l=t.type,null!==e&&null!=t.stateNode)a(e,t,l,s,o),e.ref!==t.ref&&(t.flags|=128);else{if(!s){if(null===t.stateNode)throw Error(c(166));return null}if(e=nA(nC.current),nU(t)){if(!en)throw Error(c(175));e=ez(t.stateNode,t.type,t.memoizedProps,o,e,t),t.updateQueue=e,null!==e&&iV(t)}else{var u=B(l,s,o,e,t);n(u,t,!1,!1),t.stateNode=u,j(u,l,s,o,e)&&iV(t)}null!==t.ref&&(t.flags|=128)}return null;case 6:if(e&&null!=t.stateNode)r(e,t,e.memoizedProps,s);else{if("string"!=typeof s&&null===t.stateNode)throw Error(c(166));if(e=nA(ny.current),o=nA(nC.current),nU(t)){if(!en)throw Error(c(176));ej(t.stateNode,t.memoizedProps,t)&&iV(t)}else t.stateNode=K(s,e,o,t)}return null;case 13:return e2(nO),s=t.memoizedState,(64&t.flags)!=0?(t.lanes=l,t):(s=null!==s,o=!1,null===e?void 0!==t.memoizedProps.fallback&&nU(t):o=null!==e.memoizedState,s&&!o&&(2&t.mode)!=0&&(null===e&&!0!==t.memoizedProps.unstable_avoidThisFallback||(1&nO.current)!=0?0===ag&&(ag=3):((0===ag||3===ag)&&(ag=4),null===af||(134217727&aS)==0&&(134217727&ay)==0||aX(af,aE))),et&&s&&(t.flags|=4),ee&&(s||o)&&(t.flags|=4),null);case 4:return nP(),i(t),null===e&&eo(t.stateNode.containerInfo),null;case 10:return t3(t),null;case 19:if(e2(nO),null===(s=t.memoizedState))return null;if(o=(64&t.flags)!=0,null===(u=s.rendering)){if(o)iF(s,!1);else{if(0!==ag||null!==e&&(64&e.flags)!=0)for(e=t.child;null!==e;){if(null!==(u=nR(e))){for(t.flags|=64,iF(s,!1),null!==(e=u.updateQueue)&&(t.updateQueue=e,t.flags|=4),null===s.lastEffect&&(t.firstEffect=null),t.lastEffect=s.lastEffect,e=l,s=t.child;null!==s;)o=s,l=e,o.flags&=2,o.nextEffect=null,o.firstEffect=null,o.lastEffect=null,null===(u=o.alternate)?(o.childLanes=0,o.lanes=l,o.child=null,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=u.childLanes,o.lanes=u.lanes,o.child=u.child,o.memoizedProps=u.memoizedProps,o.memoizedState=u.memoizedState,o.updateQueue=u.updateQueue,o.type=u.type,l=u.dependencies,o.dependencies=null===l?null:{lanes:l.lanes,firstContext:l.firstContext}),s=s.sibling;return e5(nO,1&nO.current|2),t.child}e=e.sibling}null!==s.tail&&tG()>ab&&(t.flags|=64,o=!0,iF(s,!1),t.lanes=33554432)}}else{if(!o){if(null!==(e=nR(u))){if(t.flags|=64,o=!0,null!==(e=e.updateQueue)&&(t.updateQueue=e,t.flags|=4),iF(s,!0),null===s.tail&&"hidden"===s.tailMode&&!u.alternate&&!nx)return null!==(t=t.lastEffect=s.lastEffect)&&(t.nextEffect=null),null}else 2*tG()-s.renderingStartTime>ab&&1073741824!==l&&(t.flags|=64,o=!0,iF(s,!1),t.lanes=33554432)}s.isBackwards?(u.sibling=t.child,t.child=u):(null!==(e=s.last)?e.sibling=u:t.child=u,s.last=u)}return null!==s.tail?(e=s.tail,s.rendering=e,s.tail=e.sibling,s.lastEffect=t.lastEffect,s.renderingStartTime=tG(),e.sibling=null,t=nO.current,e5(nO,o?1&t|2:1&t),e):null;case 23:case 24:return a2(),null!==e&&null!==e.memoizedState!=(null!==t.memoizedState)&&"unstable-defer-without-hiding"!==s.mode&&(t.flags|=4),null}throw Error(c(156,t.tag))}(l,t,am))){ap=l;return}if(24!==(l=t).tag&&23!==l.tag||null===l.memoizedState||(1073741824&am)!=0||(4&l.mode)==0){for(var s=0,o=l.child;null!==o;)s|=o.lanes|o.childLanes,o=o.sibling;l.childLanes=s}null!==e&&(2048&e.flags)==0&&(null===e.firstEffect&&(e.firstEffect=t.firstEffect),null!==t.lastEffect&&(null!==e.lastEffect&&(e.lastEffect.nextEffect=t.firstEffect),e.lastEffect=t.lastEffect),1<t.flags&&(null!==e.lastEffect?e.lastEffect.nextEffect=t:e.firstEffect=t,e.lastEffect=t))}else{if(null!==(l=function(e){switch(e.tag){case 1:e7(e.type)&&te();var t=e.flags;return 4096&t?(e.flags=-4097&t|64,e):null;case 3:if(nP(),e2(e6),e2(e4),nG(),(64&(t=e.flags))!=0)throw Error(c(285));return e.flags=-4097&t|64,e;case 5:return nI(e),null;case 13:return e2(nO),4096&(t=e.flags)?(e.flags=-4097&t|64,e):null;case 19:return e2(nO),null;case 4:return nP(),null;case 10:return t3(e),null;case 23:case 24:return a2(),null;default:return null}}(t))){l.flags&=2047,ap=l;return}null!==e&&(e.firstEffect=e.lastEffect=null,e.flags|=2048)}if(null!==(t=t.sibling)){ap=t;return}ap=t=e}while(null!==t);0===ag&&(ag=5)}function a7(e){return tY(99,re.bind(null,e,tF())),null}function re(e,t){do rt();while(null!==aN);if((48&ah)!=0)throw Error(c(327));var n=e.finishedWork;if(null===n)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(c(177));e.callbackNode=null;var i=n.lanes|n.childLanes,a=i,r=e.pendingLanes&~a;e.pendingLanes=a,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=a,e.mutableReadLanes&=a,e.entangledLanes&=a,a=e.entanglements;for(var l=e.eventTimes,s=e.expirationTimes;0<r;){var o=31-tT(r),u=1<<o;a[o]=0,l[o]=-1,s[o]=-1,r&=~u}if(null!==aU&&(24&i)==0&&aU.has(e)&&aU.delete(e),e===af&&(ap=af=null,aE=0),1<n.flags?null!==n.lastEffect?(n.lastEffect.nextEffect=n,i=n.firstEffect):i=n:i=n.firstEffect,null!==i){a=ah,ah|=32,ad.current=null,aY=H(e.containerInfo),aB=!1,aO=i;do try{(function(){for(;null!==aO;){var e,t,n=aO.alternate;aB||null===aY||((8&aO.flags)!=0?U(aO,aY)&&(aB=!0,el()):13===aO.tag&&(e=n,t=aO,null!==e&&(null===(e=e.memoizedState)||null!==e.dehydrated)&&null!==(t=t.memoizedState)&&null===t.dehydrated)&&U(aO,aY)&&(aB=!0,el()));var i=aO.flags;(256&i)!=0&&function(e,t){switch(t.tag){case 0:case 11:case 15:case 22:case 5:case 6:case 4:case 17:return;case 1:if(256&t.flags&&null!==e){var n=e.memoizedProps,i=e.memoizedState;t=(e=t.stateNode).getSnapshotBeforeUpdate(t.elementType===t.type?n:tX(t.type,n),i),e.__reactInternalSnapshotBeforeUpdate=t}return;case 3:ee&&256&t.flags&&ek(t.stateNode.containerInfo);return}throw Error(c(163))}(n,aO),(512&i)==0||ax||(ax=!0,tB(97,function(){return rt(),null})),aO=aO.nextEffect}})()}catch(e){if(null===aO)throw Error(c(330));rr(aO,e),aO=aO.nextEffect}while(null!==aO);aY=null,aO=i;do try{for(l=e;null!==aO;){var d=aO.flags;if(16&d&&ee&&eb(aO.stateNode),128&d){var h=aO.alternate;if(null!==h){var f=h.ref;null!==f&&("function"==typeof f?f(null):f.current=null)}}switch(1038&d){case 2:i1(aO),aO.flags&=-3;break;case 6:i1(aO),aO.flags&=-3,i5(aO.alternate,aO);break;case 1024:aO.flags&=-1025;break;case 1028:aO.flags&=-1025,i5(aO.alternate,aO);break;case 4:i5(aO.alternate,aO);break;case 8:s=l,r=aO,ee?i2(s,r):iq(s,r);var p=r.alternate;iJ(r),null!==p&&iJ(p)}aO=aO.nextEffect}}catch(e){if(null===aO)throw Error(c(330));rr(aO,e),aO=aO.nextEffect}while(null!==aO);aB&&es(),Y(e.containerInfo),e.current=n,aO=i;do try{for(d=e;null!==aO;){var E=aO.flags;if(36&E&&function(e,t,n){switch(n.tag){case 0:case 11:case 15:case 22:if(null!==(t=null!==(t=n.updateQueue)?t.lastEffect:null)){e=t=t.next;do{if((3&e.tag)==3){var i=e.create;e.destroy=i()}e=e.next}while(e!==t)}if(null!==(t=null!==(t=n.updateQueue)?t.lastEffect:null)){e=t=t.next;do{var a=e;i=a.next,(4&(a=a.tag))!=0&&(1&a)!=0&&(rn(n,e),function(e,t){aM.push(t,e),ax||(ax=!0,tB(97,function(){return rt(),null}))}(n,e)),e=i}while(e!==t)}return;case 1:e=n.stateNode,4&n.flags&&(null===t?e.componentDidMount():(i=n.elementType===n.type?t.memoizedProps:tX(n.type,t.memoizedProps),e.componentDidUpdate(i,t.memoizedState,e.__reactInternalSnapshotBeforeUpdate))),null!==(t=n.updateQueue)&&nr(n,t,e);return;case 3:if(null!==(t=n.updateQueue)){if(e=null,null!==n.child)switch(n.child.tag){case 5:e=V(n.child.stateNode);break;case 1:e=n.child.stateNode}nr(n,t,e)}return;case 5:e=n.stateNode,null===t&&4&n.flags&&eC(e,n.type,n.memoizedProps,n);return;case 6:case 4:case 12:case 19:case 17:case 20:case 21:case 23:case 24:return;case 13:en&&null===n.memoizedState&&null!==(n=n.alternate)&&null!==(n=n.memoizedState)&&null!==(n=n.dehydrated)&&eK(n);return}throw Error(c(163))}(d,aO.alternate,aO),128&E){h=void 0;var m=aO.ref;if(null!==m){var _=aO.stateNode;if(5===aO.tag)h=V(_);else h=_;"function"==typeof m?m(h):m.current=h}}aO=aO.nextEffect}}catch(e){if(null===aO)throw Error(c(330));rr(aO,e),aO=aO.nextEffect}while(null!==aO);aO=null,tM(),ah=a}else e.current=n;if(ax)ax=!1,aN=e,aL=t;else for(aO=i;null!==aO;)t=aO.nextEffect,aO.nextEffect=null,8&aO.flags&&((E=aO).sibling=null,E.stateNode=null),aO=t;if(0===(i=e.pendingLanes)&&(ak=null),1===i?e===aV?aW++:(aW=0,aV=e):aW=0,n=n.stateNode,tu&&"function"==typeof tu.onCommitFiberRoot)try{tu.onCommitFiberRoot(ts,n,void 0,(64&n.current.flags)==64)}catch(e){}if(aK(e,tG()),aR)throw aR=!1,e=aD,aD=null,e;return(8&ah)!=0||tz(),null}function rt(){if(90!==aL){var e=97<aL?97:aL;return aL=90,tY(e,ri)}return!1}function rn(e,t){aw.push(t,e),ax||(ax=!0,tB(97,function(){return rt(),null}))}function ri(){if(null===aN)return!1;var e=aN;if(aN=null,(48&ah)!=0)throw Error(c(331));var t=ah;ah|=32;var n=aw;aw=[];for(var i=0;i<n.length;i+=2){var a=n[i],r=n[i+1],l=a.destroy;if(a.destroy=void 0,"function"==typeof l)try{l()}catch(e){if(null===r)throw Error(c(330));rr(r,e)}}for(n=aM,aM=[],i=0;i<n.length;i+=2){a=n[i],r=n[i+1];try{var s=a.create;a.destroy=s()}catch(e){if(null===r)throw Error(c(330));rr(r,e)}}for(s=e.current.firstEffect;null!==s;)e=s.nextEffect,s.nextEffect=null,8&s.flags&&(s.sibling=null,s.stateNode=null),s=e;return ah=t,tz(),!0}function ra(e,t,n){t=iz(e,t=iH(n,t),1),nn(e,t),t=az(),null!==(e=a$(e,1))&&(tg(e,1,t),aK(e,t))}function rr(e,t){if(3===e.tag)ra(e,e,t);else for(var n=e.return;null!==n;){if(3===n.tag){ra(n,e,t);break}if(1===n.tag){var i=n.stateNode;if("function"==typeof n.type.getDerivedStateFromError||"function"==typeof i.componentDidCatch&&(null===ak||!ak.has(i))){var a=ij(n,e=iH(t,e),1);if(nn(n,a),a=az(),null!==(n=a$(n,1)))tg(n,1,a),aK(n,a);else if("function"==typeof i.componentDidCatch&&(null===ak||!ak.has(i)))try{i.componentDidCatch(t,e)}catch(e){}break}}n=n.return}}function rl(e,t,n){var i=e.pingCache;null!==i&&i.delete(t),t=az(),e.pingedLanes|=e.suspendedLanes&n,af===e&&(aE&n)===n&&(4===ag||3===ag&&(62914560&aE)===aE&&500>tG()-aP?a5(e,0):aA|=n),aK(e,t)}function rs(e,t){var n,i=e.stateNode;null!==i&&i.delete(t),0==(t=0)&&((2&(t=e.mode))==0?t=1:(4&t)==0?t=99===tF()?1:2:(0===aF&&(aF=aC),0==(t=(n=62914560&~aF)&-n)&&(t=4194304))),i=az(),null!==(e=a$(e,t))&&(tg(e,t,i),aK(e,i))}l=function(e,t,n){var i=t.lanes;if(null!==e){if(e.memoizedProps!==t.pendingProps||e6.current)iT=!0;else if((n&i)!=0)iT=(16384&e.flags)!=0;else{switch(iT=!1,t.tag){case 3:iR(t),nW();break;case 5:nb(t);break;case 1:e7(t.type)&&ti(t);break;case 4:nv(t,t.stateNode.containerInfo);break;case 10:t5(t,t.memoizedProps.value);break;case 13:if(null!==t.memoizedState)return(n&t.child.childLanes)!=0?ik(e,t,n):(e5(nO,1&nO.current),null!==(t=iW(e,t,n))?t.sibling:null);e5(nO,1&nO.current);break;case 19:if(i=(n&t.childLanes)!=0,(64&e.flags)!=0){if(i)return iU(e,t,n);t.flags|=64}var a=t.memoizedState;if(null!==a&&(a.rendering=null,a.tail=null,a.lastEffect=null),e5(nO,nO.current),i)break;return null;case 23:case 24:return t.lanes=0,iv(e,t,n)}return iW(e,t,n)}}else iT=!1;switch(t.lanes=0,t.tag){case 2:if(i=t.type,null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2),e=t.pendingProps,a=e9(t,e4.current),t6(t,n),a=nX(null,t,i,e,a,n),t.flags|=1,"object"==typeof a&&null!==a&&"function"==typeof a.render&&void 0===a.$$typeof){if(t.tag=1,t.memoizedState=null,t.updateQueue=null,e7(i)){var r=!0;ti(t)}else r=!1;t.memoizedState=null!==a.state&&void 0!==a.state?a.state:null,t7(t);var l=i.getDerivedStateFromProps;"function"==typeof l&&ns(t,i,l,e),a.updater=no,t.stateNode=a,a._reactInternals=t,nh(t,i,e,n),t=iO(null,t,i,!0,r,n)}else t.tag=0,iC(null,t,a,n),t=t.child;return t;case 16:a=t.elementType;e:{switch(null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2),e=t.pendingProps,a=(r=a._init)(a._payload),t.type=a,r=t.tag=function(e){if("function"==typeof e)return rm(e)?1:0;if(null!=e){if((e=e.$$typeof)===C)return 11;if(e===A)return 14}return 2}(a),e=tX(a,e),r){case 0:t=ib(null,t,a,e,n);break e;case 1:t=iI(null,t,a,e,n);break e;case 11:t=iS(null,t,a,e,n);break e;case 14:t=iy(null,t,a,tX(a.type,e),i,n);break e}throw Error(c(306,a,""))}return t;case 0:return i=t.type,a=t.pendingProps,a=t.elementType===i?a:tX(i,a),ib(e,t,i,a,n);case 1:return i=t.type,a=t.pendingProps,a=t.elementType===i?a:tX(i,a),iI(e,t,i,a,n);case 3:if(iR(t),i=t.updateQueue,null===e||null===i)throw Error(c(282));if(i=t.pendingProps,a=null!==(a=t.memoizedState)?a.element:null,ne(e,t),na(t,i,null,n),(i=t.memoizedState.element)===a)nW(),t=iW(e,t,n);else{if((r=(a=t.stateNode).hydrate)&&(en?(nk=eB(t.stateNode.containerInfo),nD=t,r=nx=!0):r=!1),r){if(en&&null!=(e=a.mutableSourceEagerHydrationData))for(a=0;a<e.length;a+=2)r=e[a],l=e[a+1],J?r._workInProgressVersionPrimary=l:r._workInProgressVersionSecondary=l,nV.push(r);for(n=ng(t,null,i,n),t.child=n;n;)n.flags=-3&n.flags|1024,n=n.sibling}else iC(e,t,i,n),nW();t=t.child}return t;case 5:return nb(t),null===e&&nM(t),i=t.type,a=t.pendingProps,r=null!==e?e.memoizedProps:null,l=a.children,$(i,a)?l=null:null!==r&&$(i,r)&&(t.flags|=16),iP(e,t),iC(e,t,l,n),t.child;case 6:return null===e&&nM(t),null;case 13:return ik(e,t,n);case 4:return nv(t,t.stateNode.containerInfo),i=t.pendingProps,null===e?t.child=n_(t,null,i,n):iC(e,t,i,n),t.child;case 11:return i=t.type,a=t.pendingProps,a=t.elementType===i?a:tX(i,a),iS(e,t,i,a,n);case 7:return iC(e,t,t.pendingProps,n),t.child;case 8:case 12:return iC(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(i=t.type._context,a=t.pendingProps,l=t.memoizedProps,t5(t,r=a.value),null!==l){var s=l.value;if(0==(r=t$(s,r)?0:("function"==typeof i._calculateChangedBits?i._calculateChangedBits(s,r):1073741823)|0)){if(l.children===a.children&&!e6.current){t=iW(e,t,n);break e}}else for(null!==(s=t.child)&&(s.return=t);null!==s;){var o=s.dependencies;if(null!==o){l=s.child;for(var u=o.firstContext;null!==u;){if(u.context===i&&(u.observedBits&r)!=0){1===s.tag&&((u=nt(-1,n&-n)).tag=2,nn(s,u)),s.lanes|=n,null!==(u=s.alternate)&&(u.lanes|=n),t4(s.return,n),o.lanes|=n;break}u=u.next}}else l=10===s.tag&&s.type===t.type?null:s.child;if(null!==l)l.return=s;else for(l=s;null!==l;){if(l===t){l=null;break}if(null!==(s=l.sibling)){s.return=l.return,l=s;break}l=l.return}s=l}}iC(e,t,a.children,n),t=t.child}return t;case 9:return a=t.type,i=(r=t.pendingProps).children,t6(t,n),i=i(a=t8(a,r.unstable_observedBits)),t.flags|=1,iC(e,t,i,n),t.child;case 14:return r=tX(a=t.type,t.pendingProps),r=tX(a.type,r),iy(e,t,a,r,i,n);case 15:return iA(e,t,t.type,t.pendingProps,i,n);case 17:return i=t.type,a=t.pendingProps,a=t.elementType===i?a:tX(i,a),null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2),t.tag=1,e7(i)?(e=!0,ti(t)):e=!1,t6(t,n),nd(t,i,a),nh(t,i,a,n),iO(null,t,i,!0,e,n);case 19:return iU(e,t,n);case 23:case 24:return iv(e,t,n)}throw Error(c(156,t.tag))};var ro={current:!1},ru=d.unstable_flushAllWithoutAsserting,rd="function"==typeof ru;function rc(){if(void 0!==ru)return ru();for(var e=!1;rt();)e=!0;return e}var rh=0,rf=!1;function rp(e,t,n,i){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=i,this.flags=0,this.lastEffect=this.firstEffect=this.nextEffect=null,this.childLanes=this.lanes=0,this.alternate=null}function rE(e,t,n,i){return new rp(e,t,n,i)}function rm(e){return!(!(e=e.prototype)||!e.isReactComponent)}function r_(e,t){var n=e.alternate;return null===n?((n=rE(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.nextEffect=null,n.firstEffect=null,n.lastEffect=null),n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function rg(e,t,n,i,a,r){var l=2;if(i=e,"function"==typeof e)rm(e)&&(l=1);else if("string"==typeof e)l=5;else e:switch(e){case E:return rT(n.children,a,r,t);case b:l=8,a|=16;break;case m:l=8,a|=1;break;case _:return(e=rE(12,n,t,8|a)).elementType=_,e.type=_,e.lanes=r,e;case S:return(e=rE(13,n,t,a)).type=S,e.elementType=S,e.lanes=r,e;case y:return(e=rE(19,n,t,a)).elementType=y,e.lanes=r,e;case I:return rC(n,a,r,t);case O:return(e=rE(24,n,t,a)).elementType=O,e.lanes=r,e;default:if("object"==typeof e&&null!==e)switch(e.$$typeof){case g:l=10;break e;case T:l=9;break e;case C:l=11;break e;case A:l=14;break e;case v:l=16,i=null;break e;case P:l=22;break e}throw Error(c(130,null==e?e:typeof e,""))}return(t=rE(l,n,t,a)).elementType=e,t.type=i,t.lanes=r,t}function rT(e,t,n,i){return(e=rE(7,e,i,t)).lanes=n,e}function rC(e,t,n,i){return(e=rE(23,e,i,t)).elementType=I,e.lanes=n,e}function rS(e,t,n){return(e=rE(6,e,null,t)).lanes=n,e}function ry(e,t,n){return(t=rE(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function rA(e,t,n){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=q,this.pendingContext=this.context=null,this.hydrate=n,this.callbackNode=null,this.callbackPriority=0,this.eventTimes=t_(0),this.expirationTimes=t_(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=t_(0),en&&(this.mutableSourceEagerHydrationData=null)}function rv(e){var t=e._reactInternals;if(void 0===t)throw"function"==typeof e.render?Error(c(188)):Error(c(268,Object.keys(e)));return null===(e=w(t))?null:e.stateNode}function rP(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function rb(e,t){rP(e,t),(e=e.alternate)&&rP(e,t)}function rI(e){return null===(e=w(e))?null:e.stateNode}function rO(){return null}return s.IsThisRendererActing=ro,s.act=function(e){function n(){rh--,ac.current=i,ro.current=a}!1===rf&&(rf=!0,console.error("act(...) is not supported in production builds of React, and might not behave as expected.")),rh++;var i=ac.current,a=ro.current;ac.current=!0,ro.current=!0;try{var r=aJ(e)}catch(e){throw n(),e}if(null!==r&&"object"==typeof r&&"function"==typeof r.then)return{then:function(e,a){r.then(function(){1<rh||!0===rd&&!0===i?(n(),e()):function e(n){try{rc(),function(e){if(null===as)try{var n=("require"+Math.random()).slice(0,7);as=(t&&t[n]).call(t,"timers").setImmediate}catch(e){as=function(e){var t=new MessageChannel;t.port1.onmessage=e,t.port2.postMessage(void 0)}}as(e)}(function(){rc()?e(n):n()})}catch(e){n(e)}}(function(t){n(),t?a(t):e()})},function(e){n(),a(e)})}};try{1!==rh||!1!==rd&&!1!==i||rc(),n()}catch(e){throw n(),e}return{then:function(e){e()}}},s.attemptContinuousHydration=function(e){13===e.tag&&(aZ(e,67108864,az()),rb(e,67108864))},s.attemptHydrationAtCurrentPriority=function(e){if(13===e.tag){var t=az(),n=aj(e);aZ(e,n,t),rb(e,n)}},s.attemptSynchronousHydration=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.hydrate){var n=th(t.pendingLanes);t.expiredLanes|=n&t.pendingLanes,aK(t,tG()),(48&ah)==0&&(aI(),tz())}break;case 13:var i=az();a0(function(){return aZ(e,1,i)}),rb(e,4)}},s.attemptUserBlockingHydration=function(e){13===e.tag&&(aZ(e,4,az()),rb(e,4))},s.batchedEventUpdates=function(e,t){var n=ah;ah|=2;try{return e(t)}finally{0===(ah=n)&&(aI(),tz())}},s.batchedUpdates=aJ,s.createComponentSelector=function(e){return{$$typeof:i6,value:e}},s.createContainer=function(e,t,n){return e=new rA(e,t,n),t=rE(3,null,null,2===t?7:1===t?3:0),e.current=t,t.stateNode=e,t7(t),e},s.createHasPsuedoClassSelector=function(e){return{$$typeof:i8,value:e}},s.createPortal=function(e,t,n){var i=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:p,key:null==i?null:""+i,children:e,containerInfo:t,implementation:n}},s.createRoleSelector=function(e){return{$$typeof:i9,value:e}},s.createTestNameSelector=function(e){return{$$typeof:i7,value:e}},s.createTextSelector=function(e){return{$$typeof:ae,value:e}},s.deferredUpdates=function(e){return tY(97,e)},s.discreteUpdates=function(e,t,n,i,a){var r=ah;ah|=4;try{return tY(98,e.bind(null,t,n,i,a))}finally{0===(ah=r)&&(aI(),tz())}},s.findAllNodes=al,s.findBoundingRects=function(e,t){if(!eu)throw Error(c(363));t=al(e,t),e=[];for(var n=0;n<t.length;n++)e.push(ec(t[n]));for(t=e.length-1;0<t;t--){n=e[t];for(var i=n.x,a=i+n.width,r=n.y,l=r+n.height,s=t-1;0<=s;s--)if(t!==s){var o=e[s],u=o.x,d=u+o.width,h=o.y,f=h+o.height;if(i>=u&&r>=h&&a<=d&&l<=f){e.splice(t,1);break}if(i!==u||n.width!==o.width||f<r||h>l){if(!(r!==h||n.height!==o.height||d<i||u>a)){u>i&&(o.width+=u-i,o.x=i),d<a&&(o.width=a-u),e.splice(t,1);break}}else{h>r&&(o.height+=h-r,o.y=r),f<l&&(o.height=l-h),e.splice(t,1);break}}}return e},s.findHostInstance=rv,s.findHostInstanceWithNoPortals=function(e){return null===(e=function(e){if(!(e=M(e)))return null;for(var t=e;;){if(5===t.tag||6===t.tag)return t;if(t.child&&4!==t.tag)t.child.return=t,t=t.child;else{if(t===e)break;for(;!t.sibling;){if(!t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}}return null}(e))?null:20===e.tag?e.stateNode.instance:e.stateNode},s.findHostInstanceWithWarning=function(e){return rv(e)},s.flushControlled=function(e){var t=ah;ah|=1;try{tY(99,e)}finally{0===(ah=t)&&(aI(),tz())}},s.flushDiscreteUpdates=function(){(49&ah)==0&&(function(){if(null!==aU){var e=aU;aU=null,e.forEach(function(e){e.expiredLanes|=24&e.pendingLanes,aK(e,tG())})}tz()}(),rt())},s.flushPassiveEffects=rt,s.flushSync=a0,s.focusWithin=function(e,t){if(!eu)throw Error(c(363));for(t=Array.from(t=ar(e=an(e),t)),e=0;e<t.length;){var n=t[e++];if(!ef(n)){if(5===n.tag&&eE(n.stateNode))return!0;for(n=n.child;null!==n;)t.push(n),n=n.sibling}}return!1},s.getCurrentUpdateLanePriority=function(){return td},s.getFindAllNodesFailureDescription=function(e,t){if(!eu)throw Error(c(363));var n=0,i=[];e=[an(e),0];for(var a=0;a<e.length;){var r=e[a++],l=e[a++],s=t[l];if((5!==r.tag||!ef(r))&&(ai(r,s)&&(i.push(aa(s)),++l>n&&(n=l)),l<t.length))for(r=r.child;null!==r;)e.push(r,l),r=r.sibling}if(n<t.length){for(e=[];n<t.length;n++)e.push(aa(t[n]));return`findAllNodes was able to match part of the selector:
  `+i.join(" > ")+`

No matching component was found for:
  `+e.join(" > ")}return null},s.getPublicRootInstance=function(e){if(!(e=e.current).child)return null;if(5===e.child.tag)return V(e.child.stateNode);return e.child.stateNode},s.injectIntoDevTools=function(e){if(e={bundleType:e.bundleType,version:e.version,rendererPackageName:e.rendererPackageName,rendererConfig:e.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:h.ReactCurrentDispatcher,findHostInstanceByFiber:rI,findFiberByHostInstance:e.findFiberByHostInstance||rO,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null},"undefined"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__)e=!1;else{var t=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!t.isDisabled&&t.supportsFiber)try{ts=t.inject(e),tu=t}catch(e){}e=!0}return e},s.observeVisibleRects=function(e,t,n,i){if(!eu)throw Error(c(363));var a=em(e=al(e,t),n,i).disconnect;return{disconnect:function(){a()}}},s.registerMutableSourceForHydration=function(e,t){var n=t._getVersion;n=n(t._source),null==e.mutableSourceEagerHydrationData?e.mutableSourceEagerHydrationData=[t,n]:e.mutableSourceEagerHydrationData.push(t,n)},s.runWithPriority=function(e,t){var n=td;try{return td=e,t()}finally{td=n}},s.shouldSuspend=function(){return!1},s.unbatchedUpdates=function(e,t){var n=ah;ah&=-2,ah|=8;try{return e(t)}finally{0===(ah=n)&&(aI(),tz())}},s.updateContainer=function(e,t,n,i){var a=t.current,r=az(),l=aj(a);e:if(n){n=n._reactInternals;t:{if(N(n)!==n||1!==n.tag)throw Error(c(170));var s=n;do{switch(s.tag){case 3:s=s.stateNode.context;break t;case 1:if(e7(s.type)){s=s.stateNode.__reactInternalMemoizedMergedChildContext;break t}}s=s.return}while(null!==s);throw Error(c(171))}if(1===n.tag){var o=n.type;if(e7(o)){n=tn(n,o,s);break e}}n=s}else n=e3;return null===t.context?t.context=n:t.pendingContext=n,(t=nt(r,l)).payload={element:e},null!==(i=void 0===i?null:i)&&(t.callback=i),nn(a,t),aZ(a,l,r),l},s}}),td=e7((e,t)=>{t.exports=tu()}),tc=e7((e,t)=>{!function(n){var i=Array.isArray?Array.isArray:function(e){return"[object Array]"===Object.prototype.toString.call(e)};function a(){this._events={},this._conf&&r.call(this,this._conf)}function r(e){e?(this._conf=e,e.delimiter&&(this.delimiter=e.delimiter),this._events.maxListeners=e.maxListeners!==n?e.maxListeners:10,e.wildcard&&(this.wildcard=e.wildcard),e.newListener&&(this.newListener=e.newListener),e.verboseMemoryLeak&&(this.verboseMemoryLeak=e.verboseMemoryLeak),this.wildcard&&(this.listenerTree={})):this._events.maxListeners=10}function l(e,t){var n="(node) warning: possible EventEmitter memory leak detected. %d listeners added. Use emitter.setMaxListeners() to increase limit.";this.verboseMemoryLeak?console.error(n+=" Event name: %s.",e,t):console.error(n,e),console.trace&&console.trace()}function s(e){this._events={},this.newListener=!1,this.verboseMemoryLeak=!1,r.call(this,e)}function o(e,t,n,i){if(!n)return[];var a,r,l,s,u,d,c,h=[],f=t.length,p=t[i],E=t[i+1];if(i===f&&n._listeners){if("function"==typeof n._listeners)return e&&e.push(n._listeners),[n];for(a=0,r=n._listeners.length;a<r;a++)e&&e.push(n._listeners[a]);return[n]}if("*"===p||"**"===p||n[p]){if("*"===p){for(l in n)"_listeners"!==l&&n.hasOwnProperty(l)&&(h=h.concat(o(e,t,n[l],i+1)));return h}if("**"===p){for(l in(c=i+1===f||i+2===f&&"*"===E)&&n._listeners&&(h=h.concat(o(e,t,n,f))),n)"_listeners"!==l&&n.hasOwnProperty(l)&&("*"===l||"**"===l?(n[l]._listeners&&!c&&(h=h.concat(o(e,t,n[l],f))),h=h.concat(o(e,t,n[l],i))):h=l===E?h.concat(o(e,t,n[l],i+2)):h.concat(o(e,t,n[l],i)));return h}h=h.concat(o(e,t,n[p],i+1))}if((s=n["*"])&&o(e,t,s,i+1),u=n["**"]){if(i<f)for(l in u._listeners&&o(e,t,u,f),u)"_listeners"!==l&&u.hasOwnProperty(l)&&(l===E?o(e,t,u[l],i+2):l===p?o(e,t,u[l],i+1):((d={})[l]=u[l],o(e,t,{"**":d},i+1)));else u._listeners?o(e,t,u,f):u["*"]&&u["*"]._listeners&&o(e,t,u["*"],f)}return h}function u(e,t){e="string"==typeof e?e.split(this.delimiter):e.slice();for(var i=0,a=e.length;i+1<a;i++)if("**"===e[i]&&"**"===e[i+1])return;for(var r=this.listenerTree,s=e.shift();s!==n;){if(r[s]||(r[s]={}),r=r[s],0===e.length)return r._listeners?("function"==typeof r._listeners&&(r._listeners=[r._listeners]),r._listeners.push(t),!r._listeners.warned&&this._events.maxListeners>0&&r._listeners.length>this._events.maxListeners&&(r._listeners.warned=!0,l.call(this,r._listeners.length,s))):r._listeners=t,!0;s=e.shift()}return!0}s.EventEmitter2=s,s.prototype.delimiter=".",s.prototype.setMaxListeners=function(e){e!==n&&(this._events||a.call(this),this._events.maxListeners=e,this._conf||(this._conf={}),this._conf.maxListeners=e)},s.prototype.event="",s.prototype.once=function(e,t){return this.many(e,1,t),this},s.prototype.many=function(e,t,n){var i=this;if("function"!=typeof n)throw Error("many only accepts instances of Function");function a(){0==--t&&i.off(e,a),n.apply(this,arguments)}return a._origin=n,this.on(e,a),i},s.prototype.emit=function(){this._events||a.call(this);var e=arguments[0];if("newListener"===e&&!this.newListener&&!this._events.newListener)return!1;var t,n,i,r,l,s=arguments.length;if(this._all&&this._all.length){if(l=this._all.slice(),s>3)for(t=Array(s),r=0;r<s;r++)t[r]=arguments[r];for(i=0,n=l.length;i<n;i++)switch(this.event=e,s){case 1:l[i].call(this,e);break;case 2:l[i].call(this,e,arguments[1]);break;case 3:l[i].call(this,e,arguments[1],arguments[2]);break;default:l[i].apply(this,t)}}if(this.wildcard){l=[];var u="string"==typeof e?e.split(this.delimiter):e.slice();o.call(this,l,u,this.listenerTree,0)}else if("function"==typeof(l=this._events[e])){switch(this.event=e,s){case 1:l.call(this);break;case 2:l.call(this,arguments[1]);break;case 3:l.call(this,arguments[1],arguments[2]);break;default:for(t=Array(s-1),r=1;r<s;r++)t[r-1]=arguments[r];l.apply(this,t)}return!0}else l&&(l=l.slice());if(l&&l.length){if(s>3)for(t=Array(s-1),r=1;r<s;r++)t[r-1]=arguments[r];for(i=0,n=l.length;i<n;i++)switch(this.event=e,s){case 1:l[i].call(this);break;case 2:l[i].call(this,arguments[1]);break;case 3:l[i].call(this,arguments[1],arguments[2]);break;default:l[i].apply(this,t)}return!0}if(!this._all&&"error"===e)throw arguments[1]instanceof Error?arguments[1]:Error("Uncaught, unspecified 'error' event.");return!!this._all},s.prototype.emitAsync=function(){this._events||a.call(this);var e=arguments[0];if("newListener"===e&&!this.newListener&&!this._events.newListener)return Promise.resolve([!1]);var t,n,i,r,l,s=[],u=arguments.length;if(this._all){if(u>3)for(t=Array(u),r=1;r<u;r++)t[r]=arguments[r];for(i=0,n=this._all.length;i<n;i++)switch(this.event=e,u){case 1:s.push(this._all[i].call(this,e));break;case 2:s.push(this._all[i].call(this,e,arguments[1]));break;case 3:s.push(this._all[i].call(this,e,arguments[1],arguments[2]));break;default:s.push(this._all[i].apply(this,t))}}if(this.wildcard){l=[];var d="string"==typeof e?e.split(this.delimiter):e.slice();o.call(this,l,d,this.listenerTree,0)}else l=this._events[e];if("function"==typeof l)switch(this.event=e,u){case 1:s.push(l.call(this));break;case 2:s.push(l.call(this,arguments[1]));break;case 3:s.push(l.call(this,arguments[1],arguments[2]));break;default:for(t=Array(u-1),r=1;r<u;r++)t[r-1]=arguments[r];s.push(l.apply(this,t))}else if(l&&l.length){if(u>3)for(t=Array(u-1),r=1;r<u;r++)t[r-1]=arguments[r];for(i=0,n=l.length;i<n;i++)switch(this.event=e,u){case 1:s.push(l[i].call(this));break;case 2:s.push(l[i].call(this,arguments[1]));break;case 3:s.push(l[i].call(this,arguments[1],arguments[2]));break;default:s.push(l[i].apply(this,t))}}else if(!this._all&&"error"===e)return arguments[1]instanceof Error?Promise.reject(arguments[1]):Promise.reject("Uncaught, unspecified 'error' event.");return Promise.all(s)},s.prototype.on=function(e,t){if("function"==typeof e)return this.onAny(e),this;if("function"!=typeof t)throw Error("on only accepts instances of Function");return this._events||a.call(this),this.emit("newListener",e,t),this.wildcard?u.call(this,e,t):this._events[e]?("function"==typeof this._events[e]&&(this._events[e]=[this._events[e]]),this._events[e].push(t),!this._events[e].warned&&this._events.maxListeners>0&&this._events[e].length>this._events.maxListeners&&(this._events[e].warned=!0,l.call(this,this._events[e].length,e))):this._events[e]=t,this},s.prototype.onAny=function(e){if("function"!=typeof e)throw Error("onAny only accepts instances of Function");return this._all||(this._all=[]),this._all.push(e),this},s.prototype.addListener=s.prototype.on,s.prototype.off=function(e,t){if("function"!=typeof t)throw Error("removeListener only takes instances of Function");var a,r=[];if(this.wildcard){var l="string"==typeof e?e.split(this.delimiter):e.slice();r=o.call(this,null,l,this.listenerTree,0)}else{if(!this._events[e])return this;a=this._events[e],r.push({_listeners:a})}for(var s=0;s<r.length;s++){var u=r[s];if(i(a=u._listeners)){for(var d=-1,c=0,h=a.length;c<h;c++)if(a[c]===t||a[c].listener&&a[c].listener===t||a[c]._origin&&a[c]._origin===t){d=c;break}if(d<0)continue;return this.wildcard?u._listeners.splice(d,1):this._events[e].splice(d,1),0===a.length&&(this.wildcard?delete u._listeners:delete this._events[e]),this.emit("removeListener",e,t),this}(a===t||a.listener&&a.listener===t||a._origin&&a._origin===t)&&(this.wildcard?delete u._listeners:delete this._events[e],this.emit("removeListener",e,t))}return function e(t){if(t!==n){var i=Object.keys(t);for(var a in i){var r=i[a],l=t[r];l instanceof Function||"object"!=typeof l||null===l||(Object.keys(l).length>0&&e(t[r]),0===Object.keys(l).length&&delete t[r])}}}(this.listenerTree),this},s.prototype.offAny=function(e){var t,n=0,i=0;if(e&&this._all&&this._all.length>0){for(t=this._all,n=0,i=t.length;n<i;n++)if(e===t[n])return t.splice(n,1),this.emit("removeListenerAny",e),this}else{for(t=this._all,n=0,i=t.length;n<i;n++)this.emit("removeListenerAny",t[n]);this._all=[]}return this},s.prototype.removeListener=s.prototype.off,s.prototype.removeAllListeners=function(e){if(0==arguments.length)return this._events&&a.call(this),this;if(this.wildcard)for(var t="string"==typeof e?e.split(this.delimiter):e.slice(),n=o.call(this,null,t,this.listenerTree,0),i=0;i<n.length;i++)n[i]._listeners=null;else this._events&&(this._events[e]=null);return this},s.prototype.listeners=function(e){if(this.wildcard){var t=[],n="string"==typeof e?e.split(this.delimiter):e.slice();return o.call(this,t,n,this.listenerTree,0),t}return this._events||a.call(this),this._events[e]||(this._events[e]=[]),i(this._events[e])||(this._events[e]=[this._events[e]]),this._events[e]},s.prototype.listenerCount=function(e){return this.listeners(e).length},s.prototype.listenersAny=function(){return this._all?this._all:[]},"function"==typeof define&&define.amd?define(function(){return s}):"object"==typeof e?t.exports=s:window.EventEmitter2=s}()}),th=tt(tl()),tf=tt(td()),tp=class{constructor(e){this.presetHostConfig={shouldSetTextContent:()=>!1,getRootHostContext:()=>null,getChildHostContext:e=>e,getPublicInstance:e=>e,prepareForCommit:()=>null,resetAfterCommit:()=>{},preparePortalMount:()=>{},supportsMutation:!0,now:Date.now,scheduleTimeout:(e,t)=>setTimeout(e,t),cancelTimeout:e=>clearTimeout(e),noTimeout:-1,isPrimaryRenderer:!1,commitMount:()=>{}},this.renderer=(0,tf.default)(e8(e8({},this.presetHostConfig),e))}render(e,t){return new Promise((n,i)=>{try{t._rootContainer||(t._rootContainer=this.renderer.createContainer(t,!1,!1)),this.renderer.updateContainer(e,t._rootContainer,null,()=>n())}catch(e){i(e)}})}unmount(e){return new Promise((t,n)=>{if(e._rootContainer){try{this.renderer.updateContainer(null,e._rootContainer,null,()=>t())}catch(e){n(e)}delete e._rootContainer}})}findDOMNode(e){return null==e?null:this.renderer.findHostInstance(e)}},tE=/([+-]?\d+(?:\.\d+)?)apx/g,tm=(e,t)=>null!=e&&e.replace?e.replace(tE,(e,n)=>`${Number(n*t)}px`):e,t_=/^\s*#[0-9a-fA-F]{3}\s*$/,tg=/^\s*#[0-9a-fA-F]{3,6}\s*$/,tT=/^\s*rgb\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)\s*$/,tC=/^\s*rgba\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*([\d.]+)\s*\)\s*$/,tS=/px$/,ty=e=>Number(e).toString()===e,tA=(e,t)=>!1===t||"number"==typeof e?e:tS.test(e)?Number(e.replace(tS,""))||0:ty(e)?parseInt(e,10):e,tv=e=>{if("string"==typeof e&&t_.test(e)){let[t,n,i]=e.slice(1);return`#${t}${t}${n}${n}${i}${i}`}return e},tP=(e,t,n,i)=>{let a=Array.isArray(n)?n:(null==n?"":n).toString().split(/\s+/),r=null,l=null,s=null,o=null;switch(a.length){case 1:[r]=a,[l]=a,[s]=a,[o]=a;break;case 2:[r,l]=a,[s,o]=a;break;case 3:[r,l,s]=a,o=l;break;case 4:[r,o,s,l]=a;break;default:return!0}return Object.assign(e,{[`${t}Top`]:tA(r,i),[`${t}Left`]:tA(l,i),[`${t}Bottom`]:tA(s,i),[`${t}Right`]:tA(o,i)}),!0},tb=(e,t,n,i)=>{let a=(n||"").toString().split(/\s+/);switch(a.length){case 1:return e.borderColor=tv(a[0]),!0;case 2:return e.borderWidth=tA(a[0],i),e.borderColor=tv(a[1]),!0;case 3:return e.borderWidth=tA(a[0],i),e.borderColor=tv(a[2]),!0;default:return!0}},tI=(e,t,n,i)=>{if("string"!=typeof n||!tC.test(n))return!1;let[a,r,l,s]=tC.exec(n).slice(1,5).map(e=>Number(e)),o=e=>e.toString(16);return e.color=`#${o(a)}${o(r)}${o(l)}`,null==i.opacity&&(e.opacity=Number(s)),!0},tO=(e,t,n,i,a)=>["padding","margin"].includes(t)?tP(e,t,n,i):"border"===t?tb(e,t,n,i):"background"===t&&(tg.test(a[t])?e.backgroundColor=tv(a[t]):(tT.test(a[t])||tC.test(a[t])||a[t].includes("linear-gradient"))&&(e.backgroundColor=a[t]),!0),tR=(e={},{removePx:t,splitShorthand:n=!1,convertRgbaColor:i=!1,convertDisplayName:a=!1,convertRotate:r=!0,ignorePositionStatic:l=!1}={})=>{let s={};for(let o of Object.keys(e)){let u=e[o],d=!1;if(i&&"color"===o?d=tI(s,o,u,e):n&&(d=tO(s,o,u,t,e)),a&&"display"===o)s.hidden="none"===e[o];else if(r&&"rotate"===o){let t=e[o];s.rotate="string"==typeof t?Number(t.replace("deg",""))||0:t}else l&&"position"===o&&"static"===e[o]||d||(s[o]=tv(tA(e[o],t)))}return s};((e,t)=>{for(var n in t)eX(e,n,{get:t[n],enumerable:!0})})({},{APN:()=>ns,AccountStatus:()=>tq,AccountType:()=>t9,ActionType:()=>t8,AdFreeType:()=>nn,AdStrategyType:()=>tk,AddrType:()=>nC,AppServiceType:()=>ni,BackgroundColor:()=>tV,CarrierType:()=>nl,CompressJsonEntry:()=>ne,ContentTagType:()=>tF,ContentWordColor:()=>tH,CouponTag:()=>tY,CrtSize:()=>tL,DestType:()=>tN,DeviceType:()=>t7,EffectStateType:()=>t3,EntityVersion:()=>nm,FeedJumpVisibleOption:()=>nh,FinderRedPacketCoverStatus:()=>np,Format:()=>nu,ItemType:()=>t6,MMFinderJumpConfigPrefix:()=>nf,MaterialContentType:()=>tU,MonetizeMethod:()=>tj,MonetizeMethodOp:()=>tZ,MonetizeMethodStatus:()=>t$,MonetizeOperationSrc:()=>tK,NameType:()=>nT,NetType:()=>nc,NetworkType:()=>nr,NoAdReason:()=>tz,OperatorSystem:()=>nd,OsType:()=>na,PlayableOrientation:()=>t4,PosType:()=>tx,ProductType:()=>tw,PublisherAdSlot:()=>tQ,PublisherAdSlotStatus:()=>tX,QueryType:()=>ng,QuestSettProgress:()=>t0,QuestSettStatus:()=>tJ,RequestSource:()=>tD,RequestType:()=>nt,RewriteSenGradeType:()=>n_,SegVersion:()=>nE,ShowType:()=>tB,SlotOpenStateType:()=>t5,Source:()=>no,TitleTagType:()=>tW,TitleWordColor:()=>tG,VideoPlayReportType:()=>t2,VideoPlayType:()=>t1,enPageType:()=>tM});var tD=((i=tD||{})[i.SOURCE_UNKNOW=0]="SOURCE_UNKNOW",i[i.SOURCE_MP=1]="SOURCE_MP",i[i.SOURCE_OSS=2]="SOURCE_OSS",i[i.SOURCE_MP_OSS=3]="SOURCE_MP_OSS",i[i.SOURCE_OPEN_API=4]="SOURCE_OPEN_API",i[i.SOURCE_MARKETING_API=5]="SOURCE_MARKETING_API",i[i.SOURCE_WEPAY=6]="SOURCE_WEPAY",i[i.SOURCE_PRODUCT_API=7]="SOURCE_PRODUCT_API",i[i.SOURCE_WEAPP=8]="SOURCE_WEAPP",i[i.SOURCE_LOCAL_BUSINESS=9]="SOURCE_LOCAL_BUSINESS",i),tk=((a=tk||{})[a.NORMAL_AD=0]="NORMAL_AD",a[a.LOCAL_BUSINESS_AD=1]="LOCAL_BUSINESS_AD",a),tx=((r=tx||{})[r.POS_TYPE_APPMSG_BOTTOM=0]="POS_TYPE_APPMSG_BOTTOM",r[r.POS_TYPE_APPMSG_TOP=1]="POS_TYPE_APPMSG_TOP",r[r.POS_TYPE_NEWS=2]="POS_TYPE_NEWS",r[r.POS_TYPE_APPMSG_VIDEO=3]="POS_TYPE_APPMSG_VIDEO",r[r.POS_TYPE_FREE_TRADE=4]="POS_TYPE_FREE_TRADE",r[r.POS_TYPE_APPMSG_MIDDLE=5]="POS_TYPE_APPMSG_MIDDLE",r[r.POS_TYPE_MAIL=6]="POS_TYPE_MAIL",r[r.POS_TYPE_WX_APPLICATION=7]="POS_TYPE_WX_APPLICATION",r[r.POS_TYPE_WX_GAME_VIDEO=8]="POS_TYPE_WX_GAME_VIDEO",r[r.POS_TYPE_MP_PRODUCT=9]="POS_TYPE_MP_PRODUCT",r[r.POS_TYPE_WEAPP_INTERSTITIAL=10]="POS_TYPE_WEAPP_INTERSTITIAL",r[r.POS_TYPE_BOX=11]="POS_TYPE_BOX",r[r.POS_TYPE_WEAPP_COVER=12]="POS_TYPE_WEAPP_COVER",r[r.POS_TYPE_WEAPP_TEMPLATE=13]="POS_TYPE_WEAPP_TEMPLATE",r[r.POS_TYPE_WX_PAY=14]="POS_TYPE_WX_PAY",r[r.POS_TYPE_BIZ_TIME_LINE=15]="POS_TYPE_BIZ_TIME_LINE",r[r.POS_TYPE_FINDER_FREE_TRADE=16]="POS_TYPE_FINDER_FREE_TRADE",r[r.POS_TYPE_MP_MAX=100]="POS_TYPE_MP_MAX",r[r.POS_TYPE_MULTI_SLOT=101]="POS_TYPE_MULTI_SLOT",r[r.POS_TYPE_CHANNELS=113]="POS_TYPE_CHANNELS",r[r.POS_TYPE_SEARCH=115]="POS_TYPE_SEARCH",r[r.POS_TYPE_WX_APPLET=997]="POS_TYPE_WX_APPLET",r[r.POS_TYPE_MP_ALL=998]="POS_TYPE_MP_ALL",r[r.POS_TYPE_SNS_FEED=999]="POS_TYPE_SNS_FEED",r),tN=((l=tN||{})[l.DEST_TYPE_AD=0]="DEST_TYPE_AD",l[l.DEST_TYPE_OUTER=1]="DEST_TYPE_OUTER",l[l.DEST_TYPE_APPDETAIL=2]="DEST_TYPE_APPDETAIL",l[l.DEST_TYPE_BIZ=3]="DEST_TYPE_BIZ",l[l.DEST_TYPE_APPINFO_PAGE=4]="DEST_TYPE_APPINFO_PAGE",l[l.DEST_TYPE_WECHAT_SHOP=5]="DEST_TYPE_WECHAT_SHOP",l[l.DEST_TYPE_WECHAT_APPLET=6]="DEST_TYPE_WECHAT_APPLET",l[l.DEST_TYPE_CANVAS=9]="DEST_TYPE_CANVAS",l[l.DEST_TYPE_CHANNELS=101]="DEST_TYPE_CHANNELS",l[l.DEST_TYPE_CHANNELS_LIVE=102]="DEST_TYPE_CHANNELS_LIVE",l[l.DEST_TYPE_CHANNELS_USER_PROFILE=103]="DEST_TYPE_CHANNELS_USER_PROFILE",l[l.DEST_TYPE_CHANNELS_HALF_CARD=104]="DEST_TYPE_CHANNELS_HALF_CARD",l[l.DEST_TYPE_ONEKEY_ATTENTION=40024]="DEST_TYPE_ONEKEY_ATTENTION",l[l.DEST_TYPE_WECOM_CONSULT=60013]="DEST_TYPE_WECOM_CONSULT",l),tL=((s=tL||{})[s.RT_SIZE_WORD=91]="RT_SIZE_WORD",s[s.CRT_SIZE_PIC=133]="CRT_SIZE_PIC",s[s.CRT_SIZE_APPMSG=134]="CRT_SIZE_APPMSG",s[s.CRT_SIZE_CARD=135]="CRT_SIZE_CARD",s[s.CRT_SIZE_TOP_PIC=166]="CRT_SIZE_TOP_PIC",s[s.CRT_SIZE_APPDETAIL=167]="CRT_SIZE_APPDETAIL",s[s.CRT_SIZE_WECHAT_SHOP=267]="CRT_SIZE_WECHAT_SHOP",s[s.CRT_SIZE_COPY_CARD=354]="CRT_SIZE_COPY_CARD",s[s.CRT_SIZE_PIC_CARD=355]="CRT_SIZE_PIC_CARD",s[s.CRT_SIZE_WECHAT_ARTICLE_GRAND_PIC=998]="CRT_SIZE_WECHAT_ARTICLE_GRAND_PIC",s[s.CRT_SIZE_OFF_ACCOUNT_PIC=117]="CRT_SIZE_OFF_ACCOUNT_PIC",s[s.CRT_SIZE_WECHAT_APPLET_GRAND_PIC=484]="CRT_SIZE_WECHAT_APPLET_GRAND_PIC",s[s.CRT_SIZE_WECHAT_VIDEO=538]="CRT_SIZE_WECHAT_VIDEO",s[s.CRT_SIZE_H5VIDEO_PIC_6S=555]="CRT_SIZE_H5VIDEO_PIC_6S",s[s.CRT_SIZE_H5VIDEO_VIDEO_15S=1515]="CRT_SIZE_H5VIDEO_VIDEO_15S",s[s.CRT_SIZE_MUTUAL_VIDEO_169=996]="CRT_SIZE_MUTUAL_VIDEO_169",s[s.CRT_SIZE_MUTUAL_VIDEO_43=997]="CRT_SIZE_MUTUAL_VIDEO_43",s[s.CRT_SIZE_IMAGE=666]="CRT_SIZE_IMAGE",s[s.CRT_SIZE_NORMAL_IMAGE=2106]="CRT_SIZE_NORMAL_IMAGE",s[s.CRT_SIZE_VIDEO=888]="CRT_SIZE_VIDEO",s[s.CRT_SIZE_CARD_IMAGE=450]="CRT_SIZE_CARD_IMAGE",s[s.CRT_SIZE_CARD_VIDEO=452]="CRT_SIZE_CARD_VIDEO",s[s.CRT_SIZE_LINK=454]="CRT_SIZE_LINK",s[s.CRT_SIZE_CARD_SELECT_IMAGE=480]="CRT_SIZE_CARD_SELECT_IMAGE",s[s.CRT_SIZE_CARD_SELECT_VIDEO=482]="CRT_SIZE_CARD_SELECT_VIDEO",s[s.CRT_SIZE_CARD_VOTE_IMAGE=516]="CRT_SIZE_CARD_VOTE_IMAGE",s[s.CRT_SIZE_CARD_VOTE_VIDEO=519]="CRT_SIZE_CARD_VOTE_VIDEO",s[s.CRT_SIZE_WXAPP_PIC=532]="CRT_SIZE_WXAPP_PIC",s[s.CRT_SIZE_CARD_TURN_IMAGE=539]="CRT_SIZE_CARD_TURN_IMAGE",s[s.CRT_SIZE_CARD_TURN_VIDEO=540]="CRT_SIZE_CARD_TURN_VIDEO",s[s.CRT_SIZE_WXAPP_CARD_PIC=556]="CRT_SIZE_WXAPP_CARD_PIC",s[s.CRT_SIZE_WXGAME_VIDEO_0=559]="CRT_SIZE_WXGAME_VIDEO_0",s[s.CRT_SIZE_WXGAME_VIDEO_1=560]="CRT_SIZE_WXGAME_VIDEO_1",s[s.CRT_SIZE_WXAPP_BANNER_608=608]="CRT_SIZE_WXAPP_BANNER_608",s[s.CRT_SIZE_MULTI_POS_960_334=567]="CRT_SIZE_MULTI_POS_960_334",s[s.CRT_SIZE_MULTI_POS_OFF_ACCOUNT_PIC=568]="CRT_SIZE_MULTI_POS_OFF_ACCOUNT_PIC",s[s.CRT_SIZE_MULTI_POS_CARD=569]="CRT_SIZE_MULTI_POS_CARD",s[s.CRT_SIZE_MULTI_POS_COPY_CARD=570]="CRT_SIZE_MULTI_POS_COPY_CARD",s[s.CRT_SIZE_CARD_RATING_IMAGE=588]="CRT_SIZE_CARD_RATING_IMAGE",s[s.CRT_SIZE_CARD_RATING_VIDEO=589]="CRT_SIZE_CARD_RATING_VIDEO",s[s.CRT_SIZE_CARD_FULL_IMAGE=598]="CRT_SIZE_CARD_FULL_IMAGE",s[s.CRT_SIZE_CARD_FULL_VIDEO=599]="CRT_SIZE_CARD_FULL_VIDEO",s[s.CRT_SIZE_MP_TAGS=677]="CRT_SIZE_MP_TAGS",s[s.CRT_SIZE_SCREEN_701=701]="CRT_SIZE_SCREEN_701",s[s.CRT_SIZE_WEAPP_INTERSTITIAL_VIDEO=913]="CRT_SIZE_WEAPP_INTERSTITIAL_VIDEO",s[s.CRT_SIZE_CARD_FULL_GESTURE_VIDEO=698]="CRT_SIZE_CARD_FULL_GESTURE_VIDEO",s[s.CRT_SIZE_BOTTOM_708=708]="CRT_SIZE_BOTTOM_708",s[s.CRT_SIZE_IN_TEXT_841=841]="CRT_SIZE_IN_TEXT_841",s[s.CRT_SIZE_CARD_COMPONENT_IMAGE=1707]="CRT_SIZE_CARD_COMPONENT_IMAGE",s[s.CRT_SIZE_CARD_COMPONENT_VIDEO=1708]="CRT_SIZE_CARD_COMPONENT_VIDEO",s[s.CRT_SIZE_PRODUCT=1501]="CRT_SIZE_PRODUCT",s[s.CRT_SIZE_CARD_SPHERE_IMAGE=699]="CRT_SIZE_CARD_SPHERE_IMAGE",s[s.CRT_SIZE_IMAGE_TEXT_CARD=904]="CRT_SIZE_IMAGE_TEXT_CARD",s[s.CRT_SIZE_MULTI_SLOT_BANNER=925]="CRT_SIZE_MULTI_SLOT_BANNER",s[s.CRT_SIZE_MULTI_SLOT_BANNER_MP=926]="CRT_SIZE_MULTI_SLOT_BANNER_MP",s[s.CRT_SIZE_MULTI_SLOT_IMAGE=927]="CRT_SIZE_MULTI_SLOT_IMAGE",s[s.CRT_SIZE_MULTI_SLOT_IMAGE_MP=928]="CRT_SIZE_MULTI_SLOT_IMAGE_MP",s[s.CRT_SIZE_MULTI_SLOT_VIDEO=929]="CRT_SIZE_MULTI_SLOT_VIDEO",s[s.CRT_SIZE_MULTI_SLOT_BOX=972]="CRT_SIZE_MULTI_SLOT_BOX",s[s.CRT_SIZE_BUTTON_CARD_IMAGE=955]="CRT_SIZE_BUTTON_CARD_IMAGE",s[s.CRT_SIZE_BUTTON_CARD_VIDEO=957]="CRT_SIZE_BUTTON_CARD_VIDEO",s[s.CRT_SIZE_WXGAME_LAND_PAGE=910]="CRT_SIZE_WXGAME_LAND_PAGE",s[s.CRT_SIZE_WEAPP_COVER=1003]="CRT_SIZE_WEAPP_COVER",s[s.CRT_SIZE_SNS_PORTRAIL_VIDEO=1064]="CRT_SIZE_SNS_PORTRAIL_VIDEO",s[s.CRT_SIZE_CARD_FULL_PRESS_VIDEO=1065]="CRT_SIZE_CARD_FULL_PRESS_VIDEO",s[s.CRT_SIZE_SNS_PORTRAIL_VIDEO_1=1479]="CRT_SIZE_SNS_PORTRAIL_VIDEO_1",s[s.CRT_SIZE_SNS_PORTRAIL_VIDEO_2=1480]="CRT_SIZE_SNS_PORTRAIL_VIDEO_2",s[s.CRT_SIZE_CHANNELS_IMAGE=1530]="CRT_SIZE_CHANNELS_IMAGE",s[s.CRT_SIZE_CHANNELS_PORTRAIL_IMAGE=1531]="CRT_SIZE_CHANNELS_PORTRAIL_IMAGE",s[s.CRT_SIZE_CARD_FULL_TWIST_VIDEO=1733]="CRT_SIZE_CARD_FULL_TWIST_VIDEO",s[s.CRT_SIZE_CARD_FULL_SHAKE_VIDEO=1814]="CRT_SIZE_CARD_FULL_SHAKE_VIDEO",s[s.CRT_SIZE_CARD_FULL_BREAK_FRAME=1885]="CRT_SIZE_CARD_FULL_BREAK_FRAME",s[s.CRT_SIZE_CARD_FULL_OUT_BREAK_FRAME=1945]="CRT_SIZE_CARD_FULL_OUT_BREAK_FRAME",s[s.CRT_SIZE_IMAGE_711_1280_720=711]="CRT_SIZE_IMAGE_711_1280_720",s[s.CRT_SIZE_IMAGE_712_720_1280=712]="CRT_SIZE_IMAGE_712_720_1280",s[s.CRT_SIZE_SNS_VIDEO_1280_720=720]="CRT_SIZE_SNS_VIDEO_1280_720",s[s.CRT_SIZE_SNS_PORTRAIL_VIDEO_720_1280=721]="CRT_SIZE_SNS_PORTRAIL_VIDEO_720_1280",s[s.CRT_SIZE_CARD_FINDER_EVENT_VIDEO=1748]="CRT_SIZE_CARD_FINDER_EVENT_VIDEO",s[s.CRT_SIZE_SLIDER_CARD_VIDEO=1765]="CRT_SIZE_SLIDER_CARD_VIDEO",s[s.CRT_SIZE_SLIDER_CARD_IMAGE=1766]="CRT_SIZE_SLIDER_CARD_IMAGE",s[s.CRT_SIZE_WINDOWS_IMAGE_1_IMAGE_3=2107]="CRT_SIZE_WINDOWS_IMAGE_1_IMAGE_3",s[s.CRT_SIZE_WINDOWS_VIDEO_1_IMAGE_3=2109]="CRT_SIZE_WINDOWS_VIDEO_1_IMAGE_3",s[s.CRT_SIZE_FINDER_FREE_TRADE_EMPTY=1009]="CRT_SIZE_FINDER_FREE_TRADE_EMPTY",s[s.CRT_SIZE_PC_MOUSE_INTERACTION_VIDEO=1846]="CRT_SIZE_PC_MOUSE_INTERACTION_VIDEO",s[s.CRT_SIZE_SEARCH_BRAND_VIDEO=2013]="CRT_SIZE_SEARCH_BRAND_VIDEO",s[s.CRT_SIZE_FINDER_IMAGE=2014]="CRT_SIZE_FINDER_IMAGE",s[s.CRT_SIZE_LOOKBOOK_IMAGE=2174]="CRT_SIZE_LOOKBOOK_IMAGE",s[s.CRT_SIZE_LOOKBOOK_VIDEO=2177]="CRT_SIZE_LOOKBOOK_VIDEO",s),tM=((o=tM||{})[o.PAGE_TYPE_NONE=0]="PAGE_TYPE_NONE",o[o.PAGE_TYPE_OUTLINK=1]="PAGE_TYPE_OUTLINK",o[o.PAGE_TYPE_ARTICLE=2]="PAGE_TYPE_ARTICLE",o[o.PAGE_TYPE_H5CANVAS=3]="PAGE_TYPE_H5CANVAS",o[o.PAGE_TYPE_CANVAS=4]="PAGE_TYPE_CANVAS",o[o.PAGE_TYPE_CARD=5]="PAGE_TYPE_CARD",o[o.PAGE_TYPE_ACCOUNT=6]="PAGE_TYPE_ACCOUNT",o[o.PAGE_TYPE_APPSTORE=7]="PAGE_TYPE_APPSTORE",o[o.PAGE_TYPE_TMPL=8]="PAGE_TYPE_TMPL",o[o.PAGE_TYPE_SIMPLE_CANVAS=9]="PAGE_TYPE_SIMPLE_CANVAS",o[o.PAGE_TYPE_AD=10]="PAGE_TYPE_AD",o[o.PAGE_TYPE_DIRECT=11]="PAGE_TYPE_DIRECT",o[o.PAGE_TYPE_APPDETAIL=12]="PAGE_TYPE_APPDETAIL",o[o.PAGE_TYPE_APPINFO_PAGE=13]="PAGE_TYPE_APPINFO_PAGE",o[o.PAGE_TYPE_WECHAT_SHOP=14]="PAGE_TYPE_WECHAT_SHOP",o[o.PAGE_TYPE_WEAPP=15]="PAGE_TYPE_WEAPP",o[o.PAGE_TYPE_ACTIVE=16]="PAGE_TYPE_ACTIVE",o[o.PAGE_TYPE_FORM_IMAGE=17]="PAGE_TYPE_FORM_IMAGE",o[o.PAGE_TYPE_FORM_VIDEO=18]="PAGE_TYPE_FORM_VIDEO",o[o.PAGE_TYPE_APP_IMAGE=19]="PAGE_TYPE_APP_IMAGE",o[o.PAGE_TYPE_APP_VIDEO=20]="PAGE_TYPE_APP_VIDEO",o[o.PAGE_TYPE_CANVAS_QUES=21]="PAGE_TYPE_CANVAS_QUES",o[o.PAGE_TYPE_CANVAS_VOTE=22]="PAGE_TYPE_CANVAS_VOTE",o[o.PAGE_TYPE_WEPAY_CARD=23]="PAGE_TYPE_WEPAY_CARD",o[o.PAGE_TYPE_WXGAME=24]="PAGE_TYPE_WXGAME",o[o.PAGE_TYPE_FY_EGOODS=25]="PAGE_TYPE_FY_EGOODS",o[o.PAGE_TYPE_ALITA=26]="PAGE_TYPE_ALITA",o[o.PAGE_TYPE_PHONE_CALL=27]="PAGE_TYPE_PHONE_CALL",o[o.PAGE_TYPE_SUBSCRIPTION=40027]="PAGE_TYPE_SUBSCRIPTION",o[o.PAGE_TYPE_YOUZAN=20003]="PAGE_TYPE_YOUZAN",o[o.PAGE_TYPE_WEIMENG=20305]="PAGE_TYPE_WEIMENG",o[o.PAGE_TYPE_FENGYE_EC_SINGLE=10001]="PAGE_TYPE_FENGYE_EC_SINGLE",o[o.PAGE_TYPE_FENGYE_EC_TOGETHER=10002]="PAGE_TYPE_FENGYE_EC_TOGETHER",o[o.PAGE_TYPE_FENGYE_EC_FOCUS=10003]="PAGE_TYPE_FENGYE_EC_FOCUS",o[o.PAGE_TYPE_PLAY_H5=18001]="PAGE_TYPE_PLAY_H5",o[o.PAGE_TYPE_SCAN=40026]="PAGE_TYPE_SCAN",o[o.PAGE_TYPE_CHANNELS=60003]="PAGE_TYPE_CHANNELS",o[o.PAGE_TYPE_CHANNELS_WATCH_LIVE=60005]="PAGE_TYPE_CHANNELS_WATCH_LIVE",o[o.PAGE_TYPE_CHANNELS_RESERVE_LIVE=60006]="PAGE_TYPE_CHANNELS_RESERVE_LIVE",o[o.PAGE_TYPE_CHANNELS_USER_PROFILE=60007]="PAGE_TYPE_CHANNELS_USER_PROFILE",o[o.PAGE_TYPE_CHANNELS_FOLLOW_CARD=60008]="PAGE_TYPE_CHANNELS_FOLLOW_CARD",o[o.PAGE_TYPE_AD_APPOINTMENT=60009]="PAGE_TYPE_AD_APPOINTMENT",o[o.PAGE_TYPE_WECHAT_XJ_ANDROID_APP_H5=40028]="PAGE_TYPE_WECHAT_XJ_ANDROID_APP_H5",o[o.PAGE_TYPE_WECHAT_XJ_IOS_APP_H5=40029]="PAGE_TYPE_WECHAT_XJ_IOS_APP_H5",o[o.PAGE_TYPE_WECHAT_XJ_WEBSITE_H5=40030]="PAGE_TYPE_WECHAT_XJ_WEBSITE_H5",o[o.PAGE_TYPE_WECHAT_RED_PACKET_COVER=40031]="PAGE_TYPE_WECHAT_RED_PACKET_COVER",o[o.PAGE_TYPE_WECHAT_VIDEO_TOPIC=40032]="PAGE_TYPE_WECHAT_VIDEO_TOPIC",o[o.PAGE_TYPE_CHANNELS_HALF_CARD=40033]="PAGE_TYPE_CHANNELS_HALF_CARD",o[o.PAGE_TYPE_QYWX_WECHAT_KEFU=60011]="PAGE_TYPE_QYWX_WECHAT_KEFU",o[o.PAGE_TYPE_WECHAT_STATUS_FOOTER=60012]="PAGE_TYPE_WECHAT_STATUS_FOOTER",o[o.PAGE_TYPE_WECOM_CONSULT=60013]="PAGE_TYPE_WECOM_CONSULT",o[o.PAGE_TYPE_ANDORID_DOWNLOAD=60015]="PAGE_TYPE_ANDORID_DOWNLOAD",o[o.PAGE_TYPE_VANGOGH=70001]="PAGE_TYPE_VANGOGH",o[o.PAGE_TYPE_LINGQUE_MINI_PROGRAM=70002]="PAGE_TYPE_LINGQUE_MINI_PROGRAM",o[o.PAGE_TYPE_NOT_USED=99999]="PAGE_TYPE_NOT_USED",o[o.PAGE_TYPE_MAX=1e8]="PAGE_TYPE_MAX",o);var tw=((u=tw||{})[u.PRODUCTTYPE_UNKNOWN=0]="PRODUCTTYPE_UNKNOWN",u[u.PRODUCTTYPE_OPEN_PLATFORM_APP_MOB=12]="PRODUCTTYPE_OPEN_PLATFORM_APP_MOB",u[u.PRODUCTTYPE_APPLE_APP_STORE=19]="PRODUCTTYPE_APPLE_APP_STORE",u[u.PRODUCTTYPE_WECHAT=23]="PRODUCTTYPE_WECHAT",u[u.PRODUCTTYPE_JD_URL=25]="PRODUCTTYPE_JD_URL",u[u.PRODUCTTYPE_DP_SHOP=26]="PRODUCTTYPE_DP_SHOP",u[u.PRODUCTTYPE_WECHAT_ARTICLE=29]="PRODUCTTYPE_WECHAT_ARTICLE",u[u.PRODUCTTYPE_WECHAT_SHOP=30]="PRODUCTTYPE_WECHAT_SHOP",u[u.PRODUCTTYPE_WECHAT_URL=31]="PRODUCTTYPE_WECHAT_URL",u[u.PRODUCTTYPE_WECHAT_CARD=36]="PRODUCTTYPE_WECHAT_CARD",u[u.PRODUCTTYPE_WECHAT_LBS=37]="PRODUCTTYPE_WECHAT_LBS",u[u.PRODUCTTYPE_WECHAT_APP=38]="PRODUCTTYPE_WECHAT_APP",u[u.GDT_PRODUCTTYPE_WECHAT_LBS=39]="GDT_PRODUCTTYPE_WECHAT_LBS",u[u.PRODUCTTYPE_LEAD_AD=43]="PRODUCTTYPE_LEAD_AD",u[u.PRODUCTTYPE_WECHAT_MINI_GAME=46]="PRODUCTTYPE_WECHAT_MINI_GAME",u[u.PRODUCTTYPE_WECHAT_PAY_COUPON=47]="PRODUCTTYPE_WECHAT_PAY_COUPON",u[u.PRODUCT_TYPE_APP_PROMOTION=50]="PRODUCT_TYPE_APP_PROMOTION",u[u.PRODUCTTYPE_WECHAT_CHANNELS=51]="PRODUCTTYPE_WECHAT_CHANNELS",u[u.PRODUCTTYPE_MINI_PROGRAM_WECHAT=52]="PRODUCTTYPE_MINI_PROGRAM_WECHAT",u);var tU=((d=tU||{})[d.ContentTypeImage=2]="ContentTypeImage",d[d.ContentTypeVideo=3]="ContentTypeVideo",d);var tW=((c=tW||{})[c.TITLE_TAG_TYPE_DEFAULT=0]="TITLE_TAG_TYPE_DEFAULT",c[c.PLATFORM=1]="PLATFORM",c[c.ACTIVITY=2]="ACTIVITY",c[c.SOURCE_FROM=3]="SOURCE_FROM",c),tV=((h=tV||{})[h.BACK_DEFAULT=0]="BACK_DEFAULT",h[h.BACK_PRIMARY_15=1]="BACK_PRIMARY_15",h[h.BACK_PRIMARY_80=2]="BACK_PRIMARY_80",h[h.BACK_SUCCESS_80=3]="BACK_SUCCESS_80",h[h.BACK_ALERT_80=4]="BACK_ALERT_80",h[h.BACK_INFORMATION_80=5]="BACK_INFORMATION_80",h[h.BACK_MINI_PROGRAM_80=6]="BACK_MINI_PROGRAM_80",h[h.BACK_BLACK_10=7]="BACK_BLACK_10",h[h.BACK_BLACK_30=8]="BACK_BLACK_30",h[h.BACK_BLACK_50=9]="BACK_BLACK_50",h[h.BACK_BLACK_90=10]="BACK_BLACK_90",h[h.BACK_WHITE_10=11]="BACK_WHITE_10",h[h.BACK_WHITE_30=12]="BACK_WHITE_30",h[h.BACK_WHITE_50=13]="BACK_WHITE_50",h[h.BACK_WHITE=14]="BACK_WHITE",h),tG=((f=tG||{})[f.TITLE_WORD_DEFAULT=0]="TITLE_WORD_DEFAULT",f[f.TITLE_WORD_BLACK_10=1]="TITLE_WORD_BLACK_10",f[f.TITLE_WORD_BLACK_30=2]="TITLE_WORD_BLACK_30",f[f.TITLE_WORD_BLACK_50=3]="TITLE_WORD_BLACK_50",f[f.TITLE_WORD_BLACK_90=4]="TITLE_WORD_BLACK_90",f[f.TITLE_WORD_WHITE_10=5]="TITLE_WORD_WHITE_10",f[f.TITLE_WORD_WHITE_30=6]="TITLE_WORD_WHITE_30",f[f.TITLE_WORD_WHITE_50=7]="TITLE_WORD_WHITE_50",f[f.TITLE_WORD_WHITE=8]="TITLE_WORD_WHITE",f[f.TITLE_WORD_PRIMARY=9]="TITLE_WORD_PRIMARY",f),tF=((p=tF||{})[p.CONTENT_TAG_TYPE_DEFAULT=0]="CONTENT_TAG_TYPE_DEFAULT",p[p.DISCOUNT=1]="DISCOUNT",p[p.SERVICE=2]="SERVICE",p),tH=((E=tH||{})[E.CONTENT_WORD_DEFAULT=0]="CONTENT_WORD_DEFAULT",E[E.BLACK_10=1]="BLACK_10",E[E.BLACK_30=2]="BLACK_30",E[E.BLACK_50=3]="BLACK_50",E[E.BLACK_90=4]="BLACK_90",E[E.WHITE_10=5]="WHITE_10",E[E.WHITE_30=6]="WHITE_30",E[E.WHITE_50=7]="WHITE_50",E[E.WHITE=8]="WHITE",E[E.RED=9]="RED",E),tY=((m=tY||{})[m.COUPON_DEFAULT=0]="COUPON_DEFAULT",m[m.COUPON_NORMAL=1]="COUPON_NORMAL",m[m.COUPON_HIGH=10]="COUPON_HIGH",m),tB=((_=tB||{})[_.HIGH_VALUE=1]="HIGH_VALUE",_[_.LOW_VALUE=2]="LOW_VALUE",_);var tz=((g=tz||{})[g.kRankingResponseAdNotMatch=201012]="kRankingResponseAdNotMatch",g[g.kWechatOpenlistMaxError=500999]="kWechatOpenlistMaxError",g[g.kWechatRankingMaxError=501999]="kWechatRankingMaxError",g);var tj=((T=tj||{})[T.MONETIZE_METHOD_INVALID=0]="MONETIZE_METHOD_INVALID",T[T.MONETIZE_METHOD_ADS=1]="MONETIZE_METHOD_ADS",T[T.MONETIZE_METHOD_BIZ_ORIGINAL=2]="MONETIZE_METHOD_BIZ_ORIGINAL",T[T.MONETIZE_METHOD_BIZ_REPRODUCTION=3]="MONETIZE_METHOD_BIZ_REPRODUCTION",T[T.MONETIZE_METHOD_BIZ_CPS=4]="MONETIZE_METHOD_BIZ_CPS",T[T.MONETIZE_METHOD_BIZ_KANYIKANPLUS=5]="MONETIZE_METHOD_BIZ_KANYIKANPLUS",T),tZ=((C=tZ||{})[C.OP_UNKNOWN=0]="OP_UNKNOWN",C[C.OP_OPEN=1]="OP_OPEN",C[C.OP_CLOSE=2]="OP_CLOSE",C),t$=((S=t$||{})[S.MONETIZE_METHOD_STATUS_NOT_APPLY=0]="MONETIZE_METHOD_STATUS_NOT_APPLY",S[S.MONETIZE_METHOD_STATUS_OPEN=1]="MONETIZE_METHOD_STATUS_OPEN",S[S.MONETIZE_METHOD_STATUS_CLOSED=2]="MONETIZE_METHOD_STATUS_CLOSED",S),tK=((y=tK||{})[y.MONETIZE_METHOD_OPERATION_UNKNOWN=0]="MONETIZE_METHOD_OPERATION_UNKNOWN",y[y.MONETIZE_METHOD_OPERATION_SRC_EDITOR=1]="MONETIZE_METHOD_OPERATION_SRC_EDITOR",y[y.MONETIZE_METHOD_OPERATION_SRC_QUICK_OPEN=2]="MONETIZE_METHOD_OPERATION_SRC_QUICK_OPEN",y[y.MONETIZE_METHOD_OPERATION_SRC_SLOT_SET=3]="MONETIZE_METHOD_OPERATION_SRC_SLOT_SET",y[y.MONETIZE_METHOD_OPERATION_BAN=4]="MONETIZE_METHOD_OPERATION_BAN",y[y.MONETIZE_METHOD_OPERATION_UNBAN=5]="MONETIZE_METHOD_OPERATION_UNBAN",y[y.MONETIZE_METHOD_OPERATION_AUDIT=6]="MONETIZE_METHOD_OPERATION_AUDIT",y[y.MONETIZE_METHOD_OPERATION_OPERATOR=7]="MONETIZE_METHOD_OPERATION_OPERATOR",y),tQ=((A=tQ||{})[A.ADSLOT_BIZ_UNKNOWN=0]="ADSLOT_BIZ_UNKNOWN",A[A.ADSLOT_BIZ_BOTTOM=1]="ADSLOT_BIZ_BOTTOM",A[A.ADSLOT_BIZ_SPONSOR=2]="ADSLOT_BIZ_SPONSOR",A[A.ADSLOT_BIZ_MIDDLE=3]="ADSLOT_BIZ_MIDDLE",A[A.ADSLOT_BIZ_WEAPP=4]="ADSLOT_BIZ_WEAPP",A[A.ADSLOT_BIZ_WEAPP_REWARDED_VIDEO=5]="ADSLOT_BIZ_WEAPP_REWARDED_VIDEO",A[A.ADSLOT_BIZ_ORIG_VIDEO=6]="ADSLOT_BIZ_ORIG_VIDEO",A[A.ADSLOT_BIZ_VIDEO_END=7]="ADSLOT_BIZ_VIDEO_END",A[A.ADSLOT_BIZ_CPS=8]="ADSLOT_BIZ_CPS",A[A.ADSLOT_BIZ_WEAPP_INTERSTITIAL=9]="ADSLOT_BIZ_WEAPP_INTERSTITIAL",A[A.ADSLOT_WEAPP_VIDEO=10]="ADSLOT_WEAPP_VIDEO",A[A.ADSLOT_WEAPP_VIDEO_BEGIN=11]="ADSLOT_WEAPP_VIDEO_BEGIN",A[A.ADSLOT_WEAPP_COVER=12]="ADSLOT_WEAPP_COVER",A[A.ADSLOT_WEAPP_TEMPLATE=13]="ADSLOT_WEAPP_TEMPLATE",A[A.ADSLOT_WEAPP_CPS=14]="ADSLOT_WEAPP_CPS",A[A.ADSLOT_BIZ_KYK=15]="ADSLOT_BIZ_KYK",A[A.ADSLOT_BIZ_VIDEO_MID=16]="ADSLOT_BIZ_VIDEO_MID",A),tX=((v=tX||{})[v.ADSLOT_STATUS_NOT_APPLY=0]="ADSLOT_STATUS_NOT_APPLY",v[v.ADSLOT_STATUS_OPEN=1]="ADSLOT_STATUS_OPEN",v[v.ADSLOT_STATUS_REVIEWING=2]="ADSLOT_STATUS_REVIEWING",v[v.ADSLOT_STATUS_UNPASS=3]="ADSLOT_STATUS_UNPASS",v[v.ADSLOT_STATUS_CLOSED=4]="ADSLOT_STATUS_CLOSED",v),tq=((P=tq||{})[P.ACCOUNT_STATUS_NOT_APPLY=0]="ACCOUNT_STATUS_NOT_APPLY",P[P.ACCOUNT_STATUS_OPEN=1]="ACCOUNT_STATUS_OPEN",P),tJ=((b=tJ||{})[b.QUEST_SETT_STATUS_UNKNOWN=0]="QUEST_SETT_STATUS_UNKNOWN",b[b.QUEST_SETT_STATUS_ONLINE=1]="QUEST_SETT_STATUS_ONLINE",b[b.QUEST_SETT_STATUS_ENDED=2]="QUEST_SETT_STATUS_ENDED",b[b.QUEST_SETT_STATUS_SETTLED=3]="QUEST_SETT_STATUS_SETTLED",b),t0=((I=t0||{})[I.QUEST_SETT_PROGRESS_BEGIN=1]="QUEST_SETT_PROGRESS_BEGIN",I[I.QUEST_SETT_PROGRESS_SETTLED=2]="QUEST_SETT_PROGRESS_SETTLED",I[I.QUEST_SETT_PROGRESS_WAITINVOICE=3]="QUEST_SETT_PROGRESS_WAITINVOICE",I[I.QUEST_SETT_PROGRESS_PAY_IN_PROGRESS=4]="QUEST_SETT_PROGRESS_PAY_IN_PROGRESS",I[I.QUEST_SETT_PROGRESS_SUCC=5]="QUEST_SETT_PROGRESS_SUCC",I);var t1=((O=t1||{})[O.VIDEO_PLAY_UNKNOWN=0]="VIDEO_PLAY_UNKNOWN",O[O.VIDEO_PLAY_COMPLETE=1]="VIDEO_PLAY_COMPLETE",O[O.VIDEO_PLAY_PARTIAL=2]="VIDEO_PLAY_PARTIAL",O[O.VIDEO_PLAY_ERROR=3]="VIDEO_PLAY_ERROR",O[O.VIDEO_PLAY_VIEWABLE=4]="VIDEO_PLAY_VIEWABLE",O),t2=((R=t2||{})[R.REPORT_TYPE_UNKNOWN=0]="REPORT_TYPE_UNKNOWN",R[R.REPORT_TYPE_ON_EXIT=1]="REPORT_TYPE_ON_EXIT",R[R.REPORT_TYPE_ON_FINISH_PLAY=2]="REPORT_TYPE_ON_FINISH_PLAY",R[R.REPORT_TYPE_ON_START_PLAY=3]="REPORT_TYPE_ON_START_PLAY",R[R.REPORT_TYPE_ON_WEAPP_EXIT=4]="REPORT_TYPE_ON_WEAPP_EXIT",R[R.REPORT_TYPE_ON_CLOSE_AD=5]="REPORT_TYPE_ON_CLOSE_AD",R),t5=((D=t5||{})[D.SLOT_OPEN_STATE_NOT_FIRST=0]="SLOT_OPEN_STATE_NOT_FIRST",D[D.SLOT_OPEN_STATE_IS_FIRST=1]="SLOT_OPEN_STATE_IS_FIRST",D),t3=((k=t3||{})[k.NOT_EFFECT=0]="NOT_EFFECT",k[k.ALREADY_EFFECT=1]="ALREADY_EFFECT",k);var t4=((x=t4||{})[x.PORTRAIT=1]="PORTRAIT",x[x.LANDSCAPE=2]="LANDSCAPE",x),t6=((N=t6||{})[N.kUnknown=0]="kUnknown",N[N.kVideo=1]="kVideo",N[N.kArticle=2]="kArticle",N),t8=((L=t8||{})[L.EXPOSE=1]="EXPOSE",L[L.CLICK=2]="CLICK",L),t9=((M=t9||{})[M.kUnknown=0]="kUnknown",M[M.kBiz=1]="kBiz",M[M.kWxa=2]="kWxa",M[M.kFinder=3]="kFinder",M);var t7=((w=t7||{})[w.kDeviceUnknow=0]="kDeviceUnknow",w[w.kDeviceAndroid=1]="kDeviceAndroid",w[w.kDeviceIphone=2]="kDeviceIphone",w[w.kDeviceIpad=3]="kDeviceIpad",w[w.kDeviceTv=4]="kDeviceTv",w);var ne=((U=ne||{})[U.EXT_BACK_COMM=1]="EXT_BACK_COMM",U[U.TMPL_CONTENT=2]="TMPL_CONTENT",U[U.TMPL_INFO=3]="TMPL_INFO",U[U.AD_TEMPLATE_INFO=4]="AD_TEMPLATE_INFO",U),nt=((W=nt||{})[W.WXCOVER_NORMARL_REQUEST=1]="WXCOVER_NORMARL_REQUEST",W[W.WXCOVER_CHECK_FETCH_TOGETHER=2]="WXCOVER_CHECK_FETCH_TOGETHER",W[W.WXAVIDEO_WELfARE_REQUEST=3]="WXAVIDEO_WELfARE_REQUEST",W[W.WELFARE_MYLIST_REQUEST=4]="WELFARE_MYLIST_REQUEST",W[W.WELFARE_CHANGE_BATCH=5]="WELFARE_CHANGE_BATCH",W[W.WXCOVER_CHECK_AD_IS_ONLINE_REQUEST=6]="WXCOVER_CHECK_AD_IS_ONLINE_REQUEST",W),nn=((V=nn||{})[V.AD_FREE_TYPE_UNKNOWN=0]="AD_FREE_TYPE_UNKNOWN",V[V.AD_FREE_TYPE_ALL_EXCEPT_REWARD=1]="AD_FREE_TYPE_ALL_EXCEPT_REWARD",V);var ni=((G=ni||{})[G.AppServiceType_NORMAL=1]="AppServiceType_NORMAL",G[G.AppServiceType_GAME=2]="AppServiceType_GAME",G),na=((F=na||{})[F.AllOS=0]="AllOS",F[F.Ios=1]="Ios",F[F.Android=2]="Android",F[F.Winphone=3]="Winphone",F[F.JAVA=4]="JAVA",F[F.OS_UNKNOWN=5]="OS_UNKNOWN",F[F.SYMBIAN=6]="SYMBIAN",F[F.S40asha=7]="S40asha",F[F.Webwx=8]="Webwx",F[F.Blackberry=9]="Blackberry",F[F.Windows=10]="Windows",F),nr=((H=nr||{})[H.kUnknownNetwork=0]="kUnknownNetwork",H[H.kWIFI=1]="kWIFI",H[H.k2G=2]="k2G",H[H.k3G=3]="k3G",H[H.k4G=4]="k4G",H[H.k5G=5]="k5G",H[H.kAll=10]="kAll",H),nl=((Y=nl||{})[Y.kUnknownCarrier=0]="kUnknownCarrier",Y[Y.kCMCC=1]="kCMCC",Y[Y.kCUCC=2]="kCUCC",Y[Y.kCTCC=3]="kCTCC",Y),ns=((B=ns||{})[B.UNKNOWN=0]="UNKNOWN",B[B.CMNET=1]="CMNET",B[B.CMWAP=2]="CMWAP",B[B.THREEGNET=3]="THREEGNET",B[B.THREEGWAP=4]="THREEGWAP",B[B.UNINET=5]="UNINET",B[B.UNIWAP=6]="UNIWAP",B[B.WIFI=7]="WIFI",B[B.CTWAP=8]="CTWAP",B[B.CTNET=9]="CTNET",B[B.THREEGNET_UNINET=10]="THREEGNET_UNINET",B[B.THREEGWAP_UNIWAP=11]="THREEGWAP_UNIWAP",B[B.CTNET_CTWAP=12]="CTNET_CTWAP",B[B.THREEGNET_THREEGWAP_UNINET_UNIWAP=13]="THREEGNET_THREEGWAP_UNINET_UNIWAP",B[B.WIMAX=14]="WIMAX",B);var no=((z=no||{})[z.Default_Source=0]="Default_Source",z[z.MediaPlatform=1]="MediaPlatform",z[z.WeApp=2]="WeApp",z[z.Moments=3]="Moments",z[z.BizTimeLine=4]="BizTimeLine",z),nu=((j=nu||{})[j.JSON=0]="JSON",j[j.XML=1]="XML",j),nd=((Z=nd||{})[Z.Unknown_sys=-1]="Unknown_sys",Z[Z.Windows=0]="Windows",Z[Z.Ios=1]="Ios",Z[Z.Android=2]="Android",Z),nc=(($=nc||{})[$.Unknown_type=0]="Unknown_type",$[$.Wifi=1]="Wifi",$[$.TwoG=2]="TwoG",$[$.TreeG=3]="TreeG",$[$.FourG=4]="FourG",$[$.FiveG=5]="FiveG",$[$.Ethernet=6]="Ethernet",$);var nh=((K=nh||{})[K.FeedJumpVisibleOption_DefaultPublic=0]="FeedJumpVisibleOption_DefaultPublic",K),nf=((Q=nf||{})[Q.kMMFinderJumpConfigPrefix_Feed=1]="kMMFinderJumpConfigPrefix_Feed",Q[Q.kMMFinderJumpConfigPrefix_HotWord=2]="kMMFinderJumpConfigPrefix_HotWord",Q[Q.kMMFinderJumpConfigPrefix_Anchor=3]="kMMFinderJumpConfigPrefix_Anchor",Q[Q.kMMFinderJumpConfigPrefix_Tag=4]="kMMFinderJumpConfigPrefix_Tag",Q[Q.kMMFinderJumpConfigPrefix_Activity=5]="kMMFinderJumpConfigPrefix_Activity",Q[Q.kMMFinderJumpConfigPrefix_Finder=6]="kMMFinderJumpConfigPrefix_Finder",Q),np=((X=np||{})[X.FinderRedPacketCoverStatus_Enable=1]="FinderRedPacketCoverStatus_Enable",X[X.FinderRedPacketCoverStatus_Unable=2]="FinderRedPacketCoverStatus_Unable",X);var nE=((q=nE||{})[q.SEG_VERSION_DEFAULT=0]="SEG_VERSION_DEFAULT",q[q.SEG_VERSION_119368=1]="SEG_VERSION_119368",q[q.SEG_VERSION_NEISOU=2]="SEG_VERSION_NEISOU",q[q.SEG_VERSION_1141=3]="SEG_VERSION_1141",q[q.SEG_VERSION_BIG=4]="SEG_VERSION_BIG",q[q.SEG_VERSION_MINOR_LANGUAGE=5]="SEG_VERSION_MINOR_LANGUAGE",q[q.SEG_VERSION_WSEG_V1=6]="SEG_VERSION_WSEG_V1",q),nm=((J=nm||{})[J.ENTITY_VERSION_DEFAULT=0]="ENTITY_VERSION_DEFAULT",J[J.ENTITY_VERSION_20=1]="ENTITY_VERSION_20",J),n_=((ee=n_||{})[ee.REWRITE_SEN_TYPE_ONE=1]="REWRITE_SEN_TYPE_ONE",ee[ee.REWRITE_SEN_TYPE_RETRY=2]="REWRITE_SEN_TYPE_RETRY",ee[ee.REWRITE_SEN_TYPE_BIG_SEG=101]="REWRITE_SEN_TYPE_BIG_SEG",ee[ee.REWRITE_SEN_TYPE_BIG_SEG_RETRY=102]="REWRITE_SEN_TYPE_BIG_SEG_RETRY",ee[ee.REWRITE_SEN_TYPE_MULTI_WAY_OPT1=200]="REWRITE_SEN_TYPE_MULTI_WAY_OPT1",ee[ee.REWRITE_SEN_TYPE_MULTI_WAY_OPT2=201]="REWRITE_SEN_TYPE_MULTI_WAY_OPT2",ee[ee.REWRITE_SEN_TYPE_MULTI_WAY_OPT3=202]="REWRITE_SEN_TYPE_MULTI_WAY_OPT3",ee[ee.REWRITE_SEN_TYPE_MULTI_WAY_OPT4=203]="REWRITE_SEN_TYPE_MULTI_WAY_OPT4",ee[ee.REWRITE_SEN_TYPE_MULTI_WAY_OPT5=204]="REWRITE_SEN_TYPE_MULTI_WAY_OPT5",ee[ee.REWRITE_SEN_TYPE_VOB_OPT1=300]="REWRITE_SEN_TYPE_VOB_OPT1",ee[ee.REWRITE_SEN_TYPE_VOB_OPT2=301]="REWRITE_SEN_TYPE_VOB_OPT2",ee),ng=((et=ng||{})[et.RAW_QUERY=0]="RAW_QUERY",et[et.QC_QUERY=1]="QC_QUERY",et[et.QR_QUERY=2]="QR_QUERY",et[et.QR_QUERY_FROM_QC=3]="QR_QUERY_FROM_QC",et[et.QR_QUERY_FROM_NS=4]="QR_QUERY_FROM_NS",et[et.REPLACED_RAW_QUERY=5]="REPLACED_RAW_QUERY",et[et.QR_QUERY_FROM_BOXQRW=6]="QR_QUERY_FROM_BOXQRW",et[et.QR_QUERY_FROM_BOXQRW_END2END=7]="QR_QUERY_FROM_BOXQRW_END2END",et),nT=((en=nT||{})[en.INVALID=0]="INVALID",en[en.NAME=1]="NAME",en[en.NICK_NAME=2]="NICK_NAME",en[en.AREA_NAME=3]="AREA_NAME",en[en.HOTEL_NAME=4]="HOTEL_NAME",en[en.SCENIC_NAME=5]="SCENIC_NAME",en[en.CINEMA_NAME=6]="CINEMA_NAME",en),nC=((ei=nC||{})[ei.ROOT=0]="ROOT",ei[ei.PROVINCE=1]="PROVINCE",ei[ei.CITY=2]="CITY",ei[ei.DISTRICT=3]="DISTRICT",ei[ei.SUBDISTRICT=4]="SUBDISTRICT",ei[ei.SUBDISTRICT_NO=5]="SUBDISTRICT_NO",ei);var nS=((ea=nS||{})[ea.ANDROID=12]="ANDROID",ea[ea.IOS=19]="IOS",ea[ea.WE_CHAT=23]="WE_CHAT",ea[ea.MINI_GAME=46]="MINI_GAME",ea[ea.WE_CHAT_CARD=36]="WE_CHAT_CARD",ea[ea.WE_CHAT_SHOP=30]="WE_CHAT_SHOP",ea[ea.PRODUCTTYPE_WECHAT_PAY_COUPON=47]="PRODUCTTYPE_WECHAT_PAY_COUPON",ea[ea.WE_CHAT_ARTICLE=29]="WE_CHAT_ARTICLE",ea[ea.GDT_WE_CHAT_ARTICLE=31]="GDT_WE_CHAT_ARTICLE",ea[ea.WE_CHAT_APP=38]="WE_CHAT_APP",ea[ea.LBS=37]="LBS",ea[ea.LEAD_AD=43]="LEAD_AD",ea[ea.JD_URL=25]="JD_URL",ea[ea.CHANNELS=51]="CHANNELS",ea),ny=((er=ny||{})[er.MP_TAG_TYPE_NONE=0]="MP_TAG_TYPE_NONE",er[er.MP_TAG_TYPE_PLAY=1]="MP_TAG_TYPE_PLAY",er[er.MP_TAG_TYPE_DOWNLOAD=3]="MP_TAG_TYPE_DOWNLOAD",er[er.MP_TAG_TYPE_FOLLOW=4]="MP_TAG_TYPE_FOLLOW",er[er.MP_TAG_TYPE_FORM=5]="MP_TAG_TYPE_FORM",er[er.MP_TAG_TYPE_CATEGORY=2]="MP_TAG_TYPE_CATEGORY",er[er.MP_TAG_TYPE_ORDER=6]="MP_TAG_TYPE_ORDER",er[er.MP_TAG_TYPE_POI=7]="MP_TAG_TYPE_POI",er[er.MP_TAG_TYPE_CONVERTION=8]="MP_TAG_TYPE_CONVERTION",er[er.MP_TAG_TYPE_APP_CATEGORY=9]="MP_TAG_TYPE_APP_CATEGORY",er),nA=((el=nA||{})[el.DEFAULT=0]="DEFAULT",el[el.VIDEO=1]="VIDEO",el[el.LIVE=2]="LIVE",el),nv=((es=nv||{})[es.DEFAULT=0]="DEFAULT",es[es.ROOM=1]="ROOM",es[es.RESERVATION=2]="RESERVATION",es),nP=((eo=nP||{})[eo.NONE=1]="NONE",eo[eo.ON_AIR=2]="ON_AIR",eo[eo.ENDED=3]="ENDED",eo[eo.IN_PREPARE=4]="IN_PREPARE",eo),nb=((eu=nb||{})[eu.NONE=0]="NONE",eu[eu.JSONPACK=1]="JSONPACK",eu),nI=(...e)=>e.filter(Boolean).join(" "),nO=(e,t)=>t.reduce((t,n)=>(n in e&&(t[n]=e[n]),t),{}),nR=(e,t)=>t.reduce((t,n)=>(null!=e[n]&&(t[n]=e[n]),t),{}),nD=e=>Object.keys(e).reduce((t,n)=>e8(e8({},t),null==e[n]?{}:{[n]:e[n]}),{}),nk=e=>e&&"object"==typeof e&&!Array.isArray(e);function nx(e,...t){if(!t.length)return e;let n=t.shift();if(nk(e)&&nk(n))for(let t of Object.keys(n))nk(n[t])?(e[t]||Object.assign(e,{[t]:{}}),nx(e[t],n[t])):Object.assign(e,{[t]:n[t]});return nx(e,...t)}var nN=(e,t,n)=>{let i="string"==typeof t?t.split("."):t,a=e,r=0;for(;a&&r<i.length;)a=a[i[r]],r+=1;return r<i.length?null:null!=a?a:n},nL=(e,t,n)=>{let i="string"==typeof t?t.split("."):t,a=e,r=0;for(;a&&r<i.length-1;)a[i[r]]||(a[i[r]]={}),a=a[i[r]],r+=1;a[i[r]]=n},nM=(e,t)=>{for(let n of t)if((null==e?void 0:e[n])==null)return!1;return!0},nw=(e,t)=>{if(!e)throw Error(t)},nU=(e,t)=>{for(let{path:n,value:i,condition:a=!0}of t){if(!a)continue;let t=i||(n?nN(e,n):"");if(t)return t}return null},nW=e=>e.split("-").map((e,t)=>0===t?e:e[0].toUpperCase()+e.slice(1)).join(""),nV=e=>e.replace(/[A-Z]/g,e=>`-${e.toLowerCase()}`),nG=(e,t)=>{let n=0,i=0;for(let a=0;a<e.length;a++)(n+=/[\u4e00-\u9fa5]/.test(e[a])?2:1)<t-1&&(i=a);return n>t?`${e.substring(0,i+1)}...`:e};Function.prototype.call.bind(String.prototype.split),Function.prototype.call.bind(String.prototype.indexOf),Function.prototype.call.bind(String.prototype.concat),Function.prototype.call.bind(String.prototype.substring);var nF=(e,t)=>null!=e&&e.dataset?["templateId","id","preventMask"].reduce((n,i)=>{let a=t?nW(t(i)):i;return e.dataset[a]&&(n[i]=e.dataset[a]),n},{}):{templateId:"",id:"",preventMask:!1},nH=[{img:"smiley_0",text:"[微笑]"},{img:"smiley_1",text:"[撇嘴]"},{img:"smiley_2",text:"[色]"},{img:"smiley_3",text:"[发呆]"},{img:"smiley_5",text:"[流泪]"},{img:"smiley_6",text:"[害羞]"},{img:"smiley_8",text:"[睡]"},{img:"smiley_9",text:"[大哭]"},{img:"smiley_10",text:"[尴尬]"},{img:"smiley_11",text:"[发怒]"},{img:"smiley_12",text:"[调皮]"},{img:"smiley_13",text:"[呲牙]"},{img:"smiley_14",text:"[惊讶]"},{img:"smiley_15",text:"[难过]"},{img:"smiley_17",text:"[冷汗]"},{img:"smiley_18",text:"[抓狂]"},{img:"smiley_20",text:"[偷笑]"},{img:"smiley_21",text:"[愉快]"},{img:"smiley_22",text:"[白眼]"},{img:"smiley_23",text:"[傲慢]"},{img:"smiley_26",text:"[惊恐]"},{img:"smiley_27",text:"[流汗]"},{img:"smiley_28",text:"[憨笑]"},{img:"smiley_30",text:"[奋斗]"},{img:"smiley_32",text:"[疑问]"},{img:"smiley_34",text:"[晕]"},{img:"smiley_36",text:"[衰]"},{img:"smiley_38",text:"[敲打]"},{img:"smiley_39",text:"[再见]"},{img:"smiley_40",text:"[擦汗]"},{img:"smiley_42",text:"[鼓掌]"},{img:"smiley_44",text:"[坏笑]"},{img:"smiley_45",text:"[左哼哼]"},{img:"smiley_46",text:"[右哼哼]"},{img:"smiley_47",text:"[哈欠]"},{img:"smiley_49",text:"[委屈]"},{img:"smiley_50",text:"[快哭了]"},{img:"smiley_51",text:"[阴险]"},{img:"smiley_52",text:"[亲亲]"},{img:"smiley_54",text:"[可怜]"},{img:"smiley_56",text:"[西瓜]"},{img:"smiley_60",text:"[咖啡]"},{img:"smiley_62",text:"[猪头]"},{img:"smiley_63",text:"[玫瑰]"},{img:"smiley_65",text:"[嘴唇]"},{img:"smiley_66",text:"[爱心]"},{img:"smiley_68",text:"[蛋糕]"},{img:"smiley_75",text:"[月亮]"},{img:"smiley_76",text:"[太阳]"},{img:"smiley_78",text:"[拥抱]"},{img:"smiley_79",text:"[强]"},{img:"smiley_81",text:"[握手]"},{img:"smiley_82",text:"[胜利]"},{img:"smiley_83",text:"[抱拳]"},{img:"smiley_84",text:"[勾引]"},{img:"smiley_85",text:"[拳头]"},{img:"smiley_89",text:"[OK]"},{img:"smiley_92",text:"[跳跳]"},{img:"smiley_93",text:"[发抖]"},{img:"smiley_94",text:"[怄火]"},{img:"smiley_95",text:"[转圈]"},{img:"2_04",text:"[嘿哈]"},{img:"2_05",text:"[捂脸]"},{img:"2_02",text:"[奸笑]"},{img:"2_06",text:"[机智]"},{img:"2_12",text:"[皱眉]"},{img:"2_11",text:"[耶]"},{img:"2_20",text:"[加油]"},{img:"2_21",text:"[汗]"},{img:"2_22",text:"[天啊]"},{img:"2_23",text:"[社会社会]"},{img:"2_24",text:"[旺柴]"},{img:"2_25",text:"[好的]"},{img:"2_26",text:"[加油加油]"},{img:"2_27",text:"[哇]"},{img:"2_09",text:"[红包]"},{img:"2_16",text:"[發]"},{img:"2_15",text:"[福]"}],nY=class{constructor(e){this.options=e,this.handlers={},this.sep="<^.^>",this.handleEvent=e=>t=>{if(!t.target||!this.options.getPropertyOfElement)return;let n=!1,i=!1,a=e9(e8({},t),{touches:null==t?void 0:t.touches,stopPropagation:()=>{n=!0},stopImmediatePropagation:()=>{i=!0}}),r=t.target;for(;r&&!n;){let{templateId:t,id:n}=nF(r,this.options.getDatasetPrefix),l=`${e}${this.sep}${t}-${n}`;if(this.handlers[l])for(let e of this.handlers[l])i||e(a);r=this.options.getPropertyOfElement(r,"parent")}let l=`${e}${this.sep}__global`;if(this.handlers[l]&&!n)for(let e of this.handlers[l])i||e(a)},this.handleTouchStart=this.handleEvent("touchstart"),this.handleTouchMove=this.handleEvent("touchmove"),this.handleTouchEnd=this.handleEvent("touchend"),this.handleTouchCancel=this.handleEvent("touchcancel"),this.handleClick=this.handleEvent("click"),e.onGlobalEvent&&e.getPropertyOfElement&&(e.onGlobalEvent("touchstart",this.handleTouchStart),e.onGlobalEvent("touchmove",this.handleTouchMove),e.onGlobalEvent("touchend",this.handleTouchEnd),e.onGlobalEvent("touchcancel",this.handleTouchCancel),e.onGlobalEvent("click",this.handleClick))}on(e,t,n){var i;let a=`${e}${this.sep}${t}`;(i=this.handlers)[a]||(i[a]=new Set),this.handlers[a].add(n)}onGlobal(e,t){var n;let i=`${e}${this.sep}__global`;(n=this.handlers)[i]||(n[i]=new Set),this.handlers[i].add(t)}off(e,t,n){let i=`${e}${this.sep}${t}`;this.handlers[i]&&this.handlers[i].delete(n)}offGlobal(e,t){let n=`${e}${this.sep}__global`;this.handlers[n]&&this.handlers[n].delete(t)}destroy(){this.options.offGlobalEvent&&(this.options.offGlobalEvent("touchstart",this.handleTouchStart),this.options.offGlobalEvent("touchmove",this.handleTouchMove),this.options.offGlobalEvent("touchend",this.handleTouchEnd),this.options.offGlobalEvent("touchcancel",this.handleTouchCancel),this.options.offGlobalEvent("click",this.handleClick)),this.handlers={}}},nB={type:"EOF"},nz=class{constructor(){this.token=[],this.tokens=[],this.state=null,this.state=this.start}isEOF(e){return e&&"object"==typeof e&&"EOF"===e.type}start(e){return this.isEOF(e)?(this.emmitToken("EOF",nB),this.start):"#"===e?(this.token.push(e),this.inTplFunc):"$"===e?(this.token.push(e),this.inTplStr):"&"===e||"|"===e?(this.token.push(e),this.inLogic):/^[0-9]/.test(e)?(this.token.push(e),this.inNumber):"'"===e?this.inQuoteStr:-1!==[">","<","=","!"].indexOf(e)?(this.token.push(e),this.inCompare):"?"===e?(this.emmitToken("QUESTION",e),this.start):":"===e?(this.emmitToken("COLON",e),this.start):","===e?(this.emmitToken("COMMA",e),this.start):/^\s/.test(e)?this.start:null}inLogic(e){return this.isEOF(e)||"&"!==e&&"|"!==e?this.start(e):(this.token.push(e),this.emmitToken("LOGIC",this.token.join("")),this.token=[],this.start)}inNumber(e){return!this.isEOF(e)&&/^[0-9.]/.test(e)?(this.token.push(e),this.inNumber):(this.emmitToken("NUMBER",this.token.join("")),this.token=[],this.start(e))}inTplFunc(e){return this.isEOF(e)?(this.emmitToken("TPLFUNC",this.token.join("")),this.token=[],this.start(e)):(this.token.push(e),")"===e&&(()=>{let e=0,t=0;for(let n=0,i=this.token.length;n<i;n++){let i=this.token[n];"("===i?e+=1:")"===i&&(t+=1)}return e===t})()?(this.emmitToken("TPLFUNC",this.token.join("")),this.token=[],this.start):this.inTplFunc)}inTplStr(e){return!this.isEOF(e)&&/^[a-zA-Z0-9.-_$]/.test(e)?(this.token.push(e),this.inTplStr):(this.emmitToken("TPLSTR",this.token.join("")),this.token=[],this.start(e))}inCompare(e){return this.isEOF(e)||"="!==e?(this.emmitToken("COMPARE",this.token.join("")),this.token=[],this.start(e)):(this.token.push(e),this.inCompare)}inQuoteStr(e){return this.isEOF(e)||"'"!==e?(this.token.push(e),this.inQuoteStr):(this.emmitToken("STR",this.token.join("")),this.token=[],this.start)}emmitToken(e,t){this.tokens.push({type:e,value:t})}push(e){this.state&&(this.state=this.state(e))}end(){this.state&&this.state(nB)}},nj=e=>{let t=new nz,n=0,i=e.length;for(;n<i;){let i=e[n];n+=1,t.push(i)}return t.end(),t.tokens},nZ=e=>"ROOT"===e.type?{type:"ROOT",maxChildren:0,children:[],priority:-9999}:"TPLFUNC"===e.type?{type:"TPLFUNC",children:[e.value],maxChildren:1,priority:40}:"TPLSTR"===e.type?{type:"TPLSTR",children:[e.value],maxChildren:1,priority:40}:"NUMBER"===e.type?{type:"NUMBER",children:[e.value],maxChildren:1,priority:40}:"STR"===e.type?{type:"STR",children:[e.value],maxChildren:1,priority:40}:"COMPARE"===e.type?{type:"COMPARE",operator:e.value,children:[],maxChildren:2,priority:30,isSign:!0}:"LOGIC"===e.type?{type:"LOGIC",operator:e.value,children:[],maxChildren:2,priority:20,isSign:!0}:"QUESTION"===e.type?{type:"QUESTION",operator:e.value,children:[],maxChildren:3,priority:10,isSign:!0}:"COLON"===e.type?{type:"COLON",operator:e.value,children:[],maxChildren:0,priority:10,isSign:!0}:"EOF"===e.type?{type:"ROOT_END",children:[],maxChildren:0,priority:-9999}:null,n$=e=>0!==e.maxChildren&&(null==e?void 0:e.children)&&e.children.length>=e.maxChildren,nK=e=>0!==e.maxChildren&&(null==e?void 0:e.children)&&e.children.length<e.maxChildren,nQ=e=>{let t;t="string"==typeof e?nj(e):e;let n=[nZ({type:"ROOT"})],i=(e,t)=>{let i=t.children.pop();i&&e.children.push(i),n.push(e)},a=e=>{let t=n.pop();t&&e.children.push(t),n.push(e)},r=e=>{let t=e.priority;for(;n$(n[n.length-1])&&nK(n[n.length-2])&&t<=n[n.length-1].priority&&t<=n[n.length-2].priority;){let{children:e}=n[n.length-2];null==e||e.push(n.pop())}},l=e=>{let t=nZ(e),l=n[n.length-1];if("STR"===t.type||"TPLSTR"===t.type||"NUMBER"===t.type||"TPLFUNC"===t.type){if(n$(l))return;nK(l)?l.children.push(t):n.push(t)}else"COMPARE"===t.type||"LOGIC"===t.type||"QUESTION"===t.type?n$(l)&&(t.priority>l.priority?i(t,l):(r(t),a(t))):"ROOT_END"===t.type&&(r(t),n.push(t))};for(let e=0,n=t.length;e<n;e++)l(t[e]);return n},nX=(e,t)=>{let n=e.split("."),i="";return n.forEach(e=>{""===i?i=t[e]:("string"==typeof i&&(i=JSON.parse(i)),i=i[e])}),i},nq=(e,t,n)=>{let i=e.indexOf("("),a=e.substring(0,i),r=e.substring(i+1,e.length-1);if(!n[a])return null;let l=nj(r),s=[],o=[];for(let e=0,t=l.length;e<t;e++){let t=l[e];"COMMA"===t.type||"EOF"===t.type?(s.push(o),o=[]):o.push(t)}let u=[];return s.forEach(e=>{u.push(n1(e,t,n))}),n[a].apply(null,u)},nJ=({type:e,children:t,operator:n},i,a={})=>{let r=e=>nJ(t[e],i,a);if("COMPARE"===e){let e=n&&({">":()=>r(0)>r(1),"<":()=>r(0)<r(1),">=":()=>r(0)>=r(1),"<=":()=>r(0)<=r(1),"==":()=>r(0)==r(1),"!=":()=>r(0)!=r(1),"===":()=>r(0)===r(1)})[n];return e?e():null}if("LOGIC"===e){let e=n&&({"&&":()=>r(0)&&r(1),"||":()=>r(0)||r(1)})[n];return e?e():null}let l={NUMBER:()=>Number(t[0]),STR:()=>t[0],TPLSTR:()=>nX(t[0],i),QUESTION:()=>r(0)?r(1):r(2),TPLFUNC:()=>nq(t[0],i,a)}[e]||null;return l?l():null},n0=e=>({"#scope":e=>e,"#no":e=>!e,"#bool":e=>!!e,"#add":(e,t)=>e+t,"#sub":(e,t)=>e-t,"#multi":(e,t)=>e*t,"#division":(e,t)=>e/t,"#negative":e=>-e,"#mod":(e,t)=>e%t,"#toNumber":e=>Number(e),"#toArray":(...e)=>e,"#toString":e=>String(e),"#toRegExp":e=>new RegExp(e),"#test":(e,t)=>new RegExp(e).test(t),"#ceil":e=>Math.ceil(e),"#floor":e=>Math.floor(e),"#round":e=>Math.round(e),"#substring":(e,t,n)=>e.substring(t,n),"#like":(e,t)=>-1!==e.indexOf(t),"#toFixed":(e,t)=>parseFloat(e).toFixed(t),"#replace":(e,t,n)=>e.replace(t,n),"#replaceAll":(e,t,n)=>e.replaceAll(t,n),"#toLowerCase":e=>e.toLowerCase(),"#len":e=>e.length,"#hansLen":e=>{let t=0;for(let n of e)t+=/[\u4e00-\u9fa5]/.test(n)?2:1;return t/2},"#parse":e=>{try{return JSON.parse(e)}catch(e){return{}}},"#value":(e,t)=>e[t]||{},"#truncate":(e,t)=>{let n="",i=0;for(let a of e){if((i+=/[\u4e00-\u9fa5]/.test(a)?2:1)>t){n+="...";break}n+=a}return n},"#concat":(...e)=>e.join(""),"#split":(e,t,n)=>null==e?void 0:e.split(t,n),"#join":(e,t)=>null==e?void 0:e.join(t),"#abbrNum":e=>e<1e4?e.toString():`${(e/1e4).toFixed(1)}\u4E07`,"#trimDigit":e=>{let t=e.toString().split(".");if(t.length<2)return e;let n=t.slice(-1)[0];for(;"0"===n[n.length-1];)n=n.slice(0,-1);return t.slice(0,-1).concat(n?[n]:[]).join(".")},"#append":(e,t,n=!1)=>(n&&e.indexOf(t)>-1||e.push(t),e),"#remove":(e,t)=>e.filter(e=>e!==t),"#include":(e,t)=>e.indexOf(t)>-1,"#indexedArray":e=>{if(e<=0)return[];let t=[];for(let n=0;n<e;n+=1)t.push(n);return t},"#get":nN,"#slice":(e,t,n)=>e.slice(t,n),"#map":(t,n)=>t.map(t=>e.run(n,{$item:t})),"#reduce":(t,n,i)=>t.reduce((t,i)=>e.run(n,{$item1:t,$item2:i}),i),"#rand":()=>Math.random(),"#compareVersion":(e,t)=>(function(e,t){if("string"!=typeof e||"string"!=typeof t)return 0;let n=e.split("."),i=t.split("."),a=Math.max(n.length,i.length);for(;n.length<a;)n.push("0");for(;i.length<a;)i.push("0");for(let e=0;e<a;e++){let t=parseInt(n[e],10),a=parseInt(i[e],10);if(t>a)return 1;if(t<a)return -1}return 0})(e,t),"#fn":(e,...t)=>e(...t),"#hexToRgb":(e,t)=>{e=e.replace(/^#?([a-f\d])([a-f\d])([a-f\d])$/i,(e,t,n,i)=>`${t}${t}${n}${n}${i}${i}`);let n=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(e);return n?Number.isNaN(t)?`rgb(${parseInt(n[1],16)}, ${parseInt(n[2],16)}, ${parseInt(n[3],16)})`:`rgba(${parseInt(n[1],16)}, ${parseInt(n[2],16)}, ${parseInt(n[3],16)}, ${t})`:""},"#parseInt":(e,t=10)=>parseInt(e,t)}),n1=(e,t,n)=>{try{let i=nQ(e);return nJ(i[1],t,e8(e8({},n0({run:(e,i={})=>n1(e,e8(e8({},t),i),n)})),n))}catch(e){}return null},n2=tt(tc()),n5=class extends n2.default{};function n3(e){let t=[19,12].includes(e.product_type),n=[51].includes(e.product_type),i=972===e.crt_size,a=[30,25].includes(e.product_type),r=[46].includes(e.product_type),l=[559,560,721].includes(e.crt_size);return nU(e,[{path:["extBackComm","head_img"]},{path:["weapp_info","head_img"],condition:i},{path:["crtInfo","0","wxgame_video_card","thumb"],condition:l},{path:["appInfo","iconUrl"],condition:t},{path:["app","icon"],condition:!t},{path:["app_info","icon_url"],condition:t},{path:["crtInfo","0","card_info","thumb"],condition:n},{path:["crtInfo","card_info","thumb"],condition:n},{path:["game_info","head_img"],condition:r},{path:["biz_info","head_img"],condition:!t},{path:["crtInfo","0","card_info","thumb"]},{path:["crtInfo","card_info","thumb"]},{value:"https://wxa.wxs.qq.com/images/wxapp/dianshang.png",condition:a},{value:"https://wxa.wxs.qq.com/images/wxapp/pinpai.png"}])}function n4(e){let t=[19,12].includes(null==e?void 0:e.product_type),n=[51].includes(null==e?void 0:e.product_type),i=972===e.crt_size,a=[29,31].includes(null==e?void 0:e.product_type),r=[30,25].includes(null==e?void 0:e.product_type),l=[46].includes(null==e?void 0:e.product_type),s=[559,560,721,929].includes(null==e?void 0:e.crt_size);return nU(e,[{path:["extBackComm","nick_name"]},{path:["weapp_info","nick_name"],condition:i},{path:["crtInfo","0","wxgame_video_card","title"],condition:s},{path:["appInfo","appName"],condition:t},{path:["app","nickname"],condition:!t},{path:["game_info","nick_name"],condition:l},{path:["app_info","app_name"],condition:t},{path:["crtInfo","0","card_info","title"],condition:n},{path:["crtInfo","card_info","title"],condition:n},{path:["biz_info","nick_name"]},{path:["crtInfo","0","card_info","title"]},{path:["crtInfo","card_info","title"]},{value:"品牌推广",condition:a},{value:"电商推广",condition:r},{value:"活动推广"}])}var n6=tt(tl()),n8=tt(tl()),n9=tt(tl());var n7=((ed=n7||{}).HIDEAD="hideAd",ed.SETSTYLE="setStyle",ed.REPORTIDKEY="reportIdKey",ed.TRIGGEREVENT="triggerEvent",ed.SETEXPRVARS="setExprVar",ed.BATCHSETEXPRVARS="batchSetExprVar",ed.VIDEOTOGGLEMUTED="videoToggleMuted",ed.VIDEOPLAY="videoPlay",ed.VIDEOPAUSE="videoPause",ed.VIDEOSTOP="videoStop",ed.BUTTONSETTEXT="buttonSetText",ed.RELOADAD="reloadAd",ed.REPORTMMDATA="reportMMData",ed.ENDPAGEHIDE="endPageHide",ed.IMAGESETSRC="imageSetSrc",ed.TEXTSETTEXT="textSetText",ed.ANIMATE="animate",ed.TRIGGERBTNCLICK="triggerBtnClick",ed.CHANGEMULTISHOW="changeMultiShow",ed.LANDAD="landAd",ed.EMITCUSTOMMESSAGE="emitCustomMessage",ed.SPLASHSKIPAD="splashSkipAd",ed.ADDCHILDREN="addChildren",ed.REPORTADMONITORDATA="reportAdMonitorData",ed.REWARDEDVIDEODYNAMIC="rewardedVideoDynamic",ed.REPORTENDPAGE="reportEndPage",ed.SHOWANIMATEDONE="showAnimateDone",ed.HIDEANIMATEDONE="hideAnimateDone",ed.FORCEREBUILD="forceRebuild",ed.CHANGEIFRAMESIZE="changeIframeSize",ed.CHANGEADAREASTYLE="changeAdAreaStyle",ed.TRIGGERCOMPONENTEVENT="triggerComponentEvent",ed.ADDCLASS="addClass",ed.REMOVECLASS="removeClass",ed.SHOWPOPUP="showPopup",ed.HIDEPOPUP="hidePopup",ed.GETBOUNDINGCLIENTRECT="getBoundingClientRect",ed.CHANGECOUNTDOWNVALUE="changeCountdownValue",ed);var ie=((ec=ie||{}).TAP="tap",ec.TOUCHSTART="touchstart",ec.TOUCHEND="touchend",ec.TOUCHCANCEL="touchcancel",ec.LONGTAP="longTap",ec.ANIMATIONEND="animationend",ec.HOSTMESSAGE="hostMessage",ec.TEMPLATEWILLUPDATE="templateWillUpdate",ec),it=((eh=it||{}).APPENTERFOREGROUND="appEnterForeground",eh.APPENTERBACKGROUND="appEnterBackground",eh.CONTAINERTAP="containerTap",eh.ADBUTTONTAP="adButtonTap",eh.VIEWABLEEXPOSURE="viewableExposure",eh.EXPOSURE="exposure",eh.SHOWEDGE="showEdge",eh.HIDEEDGE="hideEdge",eh.SHOWHALF="showhalf",eh.HIDEHALF="hidehalf",eh.SHOWANIMATE="showAnimate",eh.HIDEANIMATE="hideAnimate",eh.SPLASHADFIRSTRENDER="splashAdFirstRender",eh.SPLASHADMENUVIEW="splashAdMenuView",eh.INITIALTEMPLATE="initialTemplate",eh),ii=((ef=ii||{}).BUTTONTAP="buttonTap",ef),ia=((ep=ia||{}).RELATIONFETCHED="relationFetched",ep.RELATIONTEXTCHANGED="relationTextChanged",ep),ir=((eE=ir||{}).VIDEOENDED="videoEnded",eE.VIDEOPAUSE="videoPause",eE.VIDEOPLAY="videoPlay",eE.VIDEOTIMEUPDATE="videoTimeUpdate",eE.MUTEDCHANGE="mutedChange",eE.VIDEOLOADEDMETADATA="videoLoadedMetaData",eE),il=((em=il||{}).FEEDBACKBTNTAP="feedbackBtnTap",em.TAGTAP="tagTap",em.DISLIKETAP="dislikeTap",em.DISLIKEREASONTAP="dislikeReasonTap",em),is=((e_=is||{}).REPLAY="replay",e_.SHOW="show",e_.HIDE="hide",e_.VIDEODISLIKE="videodislike",e_),io=((eg=io||{}).SHOWCHANGE="showChange",eg.UNITTAP="unitTap",eg),iu=((eT=iu||{}).REWARDEDVIDEOPLAY="rewardedVideoPlay",eT.REWARDEDVIDEOEND="rewardedVideoEnd",eT.REWARDEDVIDEOTIMEUPDATE="rewardedVideoTimeUpdate",eT.REWARDEDVIDEOSHOWFLOORINGPAGE="rewardedVideoShowFlooringPage",eT.REWARDEDVIDEOHANDLEDYNAMICDONE="rewardedVideoHandleDynamicDone",eT),id=((eC=id||{}).IMGERROR="imgError",eC.IMGLOAD="imgLoad",eC),ic=((eS=ic||{}).IMGERROR="imgError",eS.IMGLOAD="imgLoad",eS),ih=void(iz=0);var ip=((ey=ip||{}).FEEDBACKBTNTAP="feedbackBtnTap",ey.FEEDBACKREPORT="feedbackReport",ey.FEEDBACKSINGLETAGTAP="feedbackSingleTagTap",ey.SHOWFEEDBACK="showFeedback",ey.FEEDBACKFAVTAP="feedbackFavTap",ey.CONTAINERTAP="containerTap",ey.CHECKEXPOSURE="checkExposure",ey.APPENTERBACKGROUND="appEnterBackground",ey.APPENTERFOREGROUND="appEnterForeground",ey.CHANGEIFRAMESIZE="changeIframeSize",ey.CHANGEADAREASTYLE="changeAdAreaStyle",ey.STOPPROPAGATIONTAP="stopPropagationTap",ey.BTNTAP="btnTap",ey.REPORTVIDEOPLAY="reportVideoPlay",ey.ADVIDEOPAUSE="adVideoPause",ey.ADVIDEOPLAY="adVideoPlay",ey.ADVIDEOMUTECHANGE="adVideoMuteChange",ey.ADVIDEOLOADFAILED="adVideoLoadFailed",ey.ADVIDEOLOADEDMETADATA="adVideoLoadedMetaData",ey.ADVIDEORESUMEBTNCLICK="adVideoResumeBtnClick",ey.ADVIDEOTIMEUPDATE="adVideoTimeUpdate",ey.ADVIDEOENDED="adVideoEnded",ey.FETCHRELATIONTEXT="fetchRelationText",ey.RELATIONTEXTCHANGED="relationTextChanged",ey.ENDPAGEINIT="endPageInit",ey.ENDPAGEHIDE="endPageHide",ey.ENDPAGESHOW="endPageShow",ey.SHOWENDPAGE="showEndPage",ey.REPORTENDPAGE="reportEndPage",ey.IMGLOAD="imgLoad",ey.MULTIMGLOAD="multImgLoad",ey.IMGERROR="imgError",ey.HOSTTAGMOUNTED="hostTagMounted",ey.ITEMTAP="itemTap",ey.HIDEAD="hideAd",ey.REPORTTIMEPOINT="reportTimePoint",ey.FEEDBACKHIDE="feedbackHide",ey.PREDOWNLOADMATERIAL="predownloadMaterial",ey.TRIGGEREVENT="triggerEvent",ey.LANDAD="landAd",ey.CHANGEMULTISHOW="changeMultiShow",ey.REPORTMMDATA="reportMMData",ey.REPORTADMONITORDATA="reportAdMonitorData",ey.REWARDEDVIDEODYNAMIC="rewardedVideoDynamic",ey.TEMPLATEMESSAGE="templateMessage",ey.REPORTIDKEY="reportIdKey",ey.SHOWPOPUP="showPopup",ey.HIDEPOPUP="hidePopup",ey.CHANGEADBTNTEXT="changedAdBtnText",ey.CHANGERELATIONTEXT="changeRelationText",ey.ADSHOWEDGE="adShowEdge",ey.ADHIDEEDGE="adHideEdge",ey.ADSHOWHALF="adShowHalf",ey.ADHIDEHALF="adHideHalf",ey.SETSTYLE="setStyle",ey.ORIENTATIONCHANGE="orientationChange",ey.SETDARKMODE="setDarkMode",ey.OPERATEADVIDEO="operateAdVideo",ey.CHANGECOUNTDOWN="changeCountdown",ey.SPLASHSKIPAD="splashSkipAd",ey.EXPOSURE="exposure",ey.VIEWABLEEXPOSURE="viewableExposure",ey.HOSTMESSAGE="hostMessage",ey.UPDATECHANNELSTATUS="updateChannelStatus",ey.PREDOWNLOADMATERIALDONE="predownloadMaterialDone",ey.CHANGEVIDEOMUTE="changeVideoMute",ey);var iE=((eA=iE||{})[eA.COMMON=1]="COMMON",eA[eA.FULLSCREEN=2]="FULLSCREEN",eA),im=((ev=im||{})[ev.COMMON=1]="COMMON",ev[ev.ROW=2]="ROW",ev[ev.COLUMN=3]="COLUMN",ev[ev.PROPORTION=4]="PROPORTION",ev),i_=((eP=i_||{})[eP.COMMON=1]="COMMON",eP[eP.ICON=2]="ICON",eP[eP.AD=3]="AD",eP),ig=((eb=ig||{})[eb.COMMON=1]="COMMON",eb[eb.AD=2]="AD",eb),iT=((eI=iT||{})[eI.COMMON=1]="COMMON",eI[eI.NICKNAME=2]="NICKNAME",eI[eI.DESC=3]="DESC",eI[eI.RELATION=4]="RELATION",eI),iC=((eO=iC||{})[eO.COMMON=1]="COMMON",eO[eO.LOGICCOMMON=2]="LOGICCOMMON",eO),iS=((eR=iS||{})[eR.COMMON=1]="COMMON",eR[eR.AD=2]="AD",eR),iy=((eD=iy||{})[eD.COMMON=1]="COMMON",eD[eD.SIMPLE=2]="SIMPLE",eD[eD.SINGLETAG=3]="SINGLETAG",eD),iA=((ek=iA||{})[ek.COMMON=1]="COMMON",ek),iv=((ex=iv||{})[ex.COMMON=1]="COMMON",ex),iP=((eN=iP||{})[eN.NONE=0]="NONE",eN[eN.COMMON=1]="COMMON",eN[eN.ADS=2]="ADS",eN),ib=((eL=ib||{})[eL.NONE=0]="NONE",eL[eL.ROWLOOP=1]="ROWLOOP",eL[eL.COLUMNLOOP=2]="COLUMNLOOP",eL),iI=((eM=iI||{})[eM.NONE=0]="NONE",eM[eM.COMMOM=1]="COMMOM",eM),iO=((ew=iO||{})[ew.NONE=0]="NONE",ew[ew.COMMON=1]="COMMON",ew),iR=((eU=iR||{})[eU.NONE=0]="NONE",eU[eU.COMMON=1]="COMMON",eU),iD=((eW=iD||{})[eW.NONE=0]="NONE",eW[eW.COMMOM=1]="COMMOM",eW),ik=((eV=ik||{})[eV.NONE=0]="NONE",eV[eV.COMMOM=1]="COMMOM",eV),ix=((eG=ix||{})[eG.NONE=0]="NONE",eG[eG.COMMON=1]="COMMON",eG);var iN=((eF=iN||{}).BASE="base",eF.COMPONENT="component",eF.HOC="hoc",eF);var iL=["left","top","right","bottom","width","height","maxWidth","maxHeight","minWidth","minHeight","margin","marginLeft","marginRight","marginTop","marginBottom","padding","paddingLeft","paddingRight","paddingTop","paddingBottom","borderWidth","fontSize","lineHeight","borderRadius"],iM=((eH=iM||{}).INNERSTYLE="innerStyle",eH.STYLE="style",eH),iw={},iU=class extends n9.Component{constructor(e,t){super(e),this.style={},this.darkModeStyle={},this.templateStyle={},this.dynamicStyle={},this.adCustomListeners=new Set,this.mounted=!1,this.classListMap={},this.handleStyleType=["innerStyle"],this.componentBehavior="component",this.state={},this.handleAdCustomAction=e=>{var t,n;e.targetidReg?null!=(n=(t=e.targetidReg).test)&&n.call(t,this.id)&&this.doCommonAction(e):e.targetid&&e.targetid===this.id&&this.doCommonAction(e)},this.bindStyle=e=>{[this.templateStyle,this.dynamicStyle].forEach(t=>{e.forEach(({originId:e,originKey:n,destRules:i})=>{t[e]||(t[e]={}),Object.defineProperty(t[e],n,{configurable:!0,enumerable:!0,get:()=>t[e][`_${n}`],set(a){a!==t[e][`_${n}`]&&(t[e][`_${n}`]=a,i.forEach(i=>{var r;let{destClassName:l,handleFn:s}=i,{destKey:o}=i;o||(o=n);let u=s?s(a,t[e]):a;t[r=`.bind-${l}`]||(t[r]={}),t[`.bind-${l}`][o]=u}))}})})})},this.adCustomId=e.adCustomId,this.adData=e.adData,this.adDatas=e.adDatas,this.emitter=e.emitter,this.idPrefix=e.idPrefix||"",this.forType=e.forType||0,this.forItem=e.forItem,this.forIndex=e.forIndex,this.hostContext=e.hostContext,this.renderContext=e.renderContext,null!=t&&t.componentBehavior&&(this.componentBehavior=t.componentBehavior);let n=e.template||{};this.template=n,this.id=`${this.adCustomId}-${this.idPrefix}${this.template.id}`;let i=n.options||{};this.options=i,this.data=i.data||{},this.events=i.events||{},this.lifecycle=i.lifecycle||{},this.innerStyles=i.innerStyles||{},this.innerClassStyles=i.innerClassStyles||{},this.animations=i.animations||{},this.bubbleEvent=void 0===i.bubbleEvent||i.bubbleEvent,this.classStyles=e.classStyles||{},this.classListMap=this.getClassList(i.classList||{}),this.classNames=this.getDataValue("_classNames")||{};let a=this.getDataValue("_bubbleEvent");void 0!==a&&(this.bubbleEvent=a),this.bindCommonEvents()}componentDidMount(){this.mounted=!0,["component"].includes(this.componentBehavior)&&this.emitComponentLifeCycle("onInit")}componentWillUnmount(){["component"].includes(this.componentBehavior)&&this.emitComponentLifeCycle("onDestroy"),this.emitter.off("adCustomAction",this.handleAdCustomAction),this.removeAdCustomListener()}bindCommonEvents(){["component","base"].includes(this.componentBehavior)&&this.emitter.on("adCustomAction",this.handleAdCustomAction),"component"===this.componentBehavior&&this.addAdCustomListener(e=>{if("setStyle"===e.action){let{id:t,targetid:n,style:i}=e.value||{};n===this.id&&this.setStyle(t,i,{isDynamic:!0,force:!0})}else"hostMessage"===e.action&&this.emitComponentEvent("hostMessage",e.value)})}getCommonSubComponentProps(){return{template:this.template,adCustomId:this.adCustomId,emitter:this.emitter,adData:this.adData,adDatas:this.adDatas,systemInfo:this.props.systemInfo,isMulti:this.props.isMulti,darkMode:this.props.darkMode,hostContext:this.props.hostContext,renderContext:this.renderContext,idPrefix:this.idPrefix||"",forType:this.forType,forIndex:this.forIndex,forItem:this.forItem,extraScope:this.props.extraScope,innerFunctions:this.props.innerFunctions,classStyles:this.classStyles}}getDataValue(e,t,n){let i=t||this.data,a=i[`${e}Expr`];return a?this.run(a,null==n?void 0:n.args):i[e]}getDataValueWithDefault(e,t,n,i){let a=n?this.getDataValue(e,n,i):this.getDataValue(e);return void 0===a?t:a}getAdData(){return 2===this.forType&&null!=this.forIndex?this.getAdDatas()[this.forIndex]||{}:this.adData}getAdDatas(){return this.adDatas||[]}addAdCustomListener(e){let t=t=>{t.adCustomId===this.adCustomId&&(null==e||e(t))};this.adCustomListeners.add(t),this.emitter.on("adCustomEvent",t)}removeAdCustomListener(){for(let e of this.adCustomListeners)this.emitter.off("adCustomEvent",e),this.adCustomListeners.delete(e)}emitAdCustomEvent(e,t){this.emitter.emit("adCustomEvent",{adCustomId:this.adCustomId,action:e,value:t})}emitComponentEvent(e,t){let n=this.events[e];null!=n&&n.length&&this.doActions(n,t||{})}emitComponentLifeCycle(e,t){let n=this.lifecycle[e];null!=n&&n.length&&this.doActions(n,t)}doActions(e=[],t={}){for(let n of e)if(n.force&&!n.targetid)this.doCommonAction({args:t,payload:n});else{let e=this.getDataValue("targetid",n),i=this.getDataValue("targetidReg",n),a=null;try{i&&(a=new RegExp(i))}catch(e){a=null}this.emitter.emit("adCustomAction",{fromid:this.id,targetid:e?`${this.adCustomId}-${e}`:this.id,targetidReg:a,args:t,payload:n})}}doCommonAction(e){let{payload:t,args:n}=e,{delay:i=null,expr:a}=t,r=()=>{var i,r,l;if(!(a&&!this.run(a,n))){if("base"===this.componentBehavior){"doAction"in this&&"function"==typeof this.doAction&&this.doAction(e);return}switch(t.action){case"setStyle":{let{value:e}=t;if(e.styleExpr)for(let t of Object.keys(e.styleExpr))e.style[t]=this.run(e.styleExpr[t],n);let a=null==(i=e.force)||i;e.forceExpr&&(a=null==(r=this.run(e.forceExpr,n))||r),this.setStyle(e.id,e.style,{isDynamic:!0,force:a});break}case"setExprVar":{let{key:e}=t.value,i=this.getDataValue("value",t.value,{args:n});this.getExprInnerVars()[e]=i;break}case"reportIdKey":{let{id:e,key:n}=t.value;this.emitAdCustomEvent("reportIdKey",{id:e,key:n});break}case"batchSetExprVar":{let{list:e}=t.value;for(let t of e){let{key:e}=t,n=this.getDataValue("value",t);this.getExprInnerVars()[e]=n}break}case"triggerEvent":{let{detail:e={},eventName:i}=t.value;try{if(null!=n&&n.event&&(e.rawDetail=JSON.stringify(n.event)),t.value.detailExpr)for(let i of Object.keys(t.value.detailExpr)){let a=t.value.detailExpr[i];e[i]=this.run(a,n)}}catch(e){}this.emitAdCustomEvent("triggerEvent",{eventName:i,detail:e});break}case"reportMMData":{let{data:e,dataExpr:i,id:a}=t.value;if(i)for(let t of Object.keys(i)){let a=i[t];e[t]=this.run(a,n)}this.emitAdCustomEvent("reportMMData",{id:a,data:e});break}case"animate":{let{id:e,animation:n}=t.value;this.doAnimation({id:e,animationName:n});break}case"triggerBtnClick":{let e=null==n?void 0:n.event;e&&this.emitAdCustomEvent("btnTap",{event:e});break}case"landAd":{let e=null==n?void 0:n.event;e&&this.emitAdCustomEvent("landAd",{index:this.getDataValueWithDefault("index",0,t.value,{args:n}),event:e});break}case"splashSkipAd":{let e=null==n?void 0:n.event;e&&this.emitAdCustomEvent("splashSkipAd",{event:e});break}case"reportAdMonitorData":{let{id:e,key:n,extInfo:i}=t.value;this.emitAdCustomEvent("reportAdMonitorData",{id:e,key:n,extInfo:this.getObjExpr(i)});break}case"rewardedVideoDynamic":{let{list:e}=t.value;this.emitAdCustomEvent("rewardedVideoDynamic",{list:e});break}case"emitCustomMessage":{let{message:e,args:i}=t.value;e&&this.emitAdCustomEvent("templateMessage",{message:e,args:this.getObjExpr(i),actionArgs:n});break}case"reportEndPage":{let{data:e}=t.value;this.emitAdCustomEvent("reportEndPage",{data:e});break}case"changeIframeSize":"value"in t?this.emitAdCustomEvent("changeIframeSize",{data:null==(l=t.value)?void 0:l.data}):this.emitAdCustomEvent("changeIframeSize");break;case"changeAdAreaStyle":{let{data:e}=t.value;if(t.value.dataExpr)for(let i of Object.keys(t.value.dataExpr)){let a=t.value.dataExpr[i];e[i]=this.run(a,n)}this.emitAdCustomEvent("changeAdAreaStyle",{data:e});break}case"triggerComponentEvent":{let{eventName:e,args:i}=t.value;this.emitComponentEvent(e,e8(e8({},n),i));break}case"addClass":{let{id:e,className:n}=t.value;Object.keys(this.classListMap).includes(e)?this.classListMap[e].includes(n)||(this.classListMap[e]=this.classListMap[e].concat(n),this.forceUpdate()):(this.classListMap[e]=[n],this.forceUpdate());break}case"removeClass":{let{id:e,className:n}=t.value;if(Object.keys(this.classListMap).includes(e)){let t=this.classListMap[e];if(t.includes(n)){let i=t.indexOf(n);t.splice(i,1),this.classListMap[e]=t,this.forceUpdate()}}break}case"getBoundingClientRect":{let{id:e,eventName:i}=t.value,{getBoundingClientRect:a}=this.hostContext,r=this.getElementById(e),l=e=>{this.emitComponentEvent(i,e9(e8({},n),{rect:e}))};if(r){let e=a(r);e instanceof Promise?e.then(l):l(e)}break}default:"doAction"in this&&"function"==typeof this.doAction&&this.doAction(e)}}};null!=i?setTimeout(r,i):r()}getObjExpr(e){if(!e||"object"!=typeof e)return e;let t={};for(let n of Object.keys(e)){let i="Expr";if(n.indexOf(i)>-1&&n.indexOf(i)===n.length-i.length){let i=n.slice(0,-4);t[i]=this.getDataValue(i,e)}else t[n]=e[n]}return t}run(e,t){let n=e8(e8({},this.getInnerFunction()),this.props.innerFunctions||{}),i=e8(e8({},this.getScope()),n);return n1(e,t?e8({$args:t},i):i,n)}getElementById(e){var t;let{renderContext:{hostContainer:n,getRootElement:i,isFlattenLayer:a},hostContext:{getPropertyOfElement:r}}=this,l=i(this);if(!l)return null;if("$$"===e||!e&&a())return l;if(!e){let e=null!=(t=r(l,"parent"))?t:null;return e===n?l:e}return this.travelAndFindElementById(e,l)}isTextNode(e){return!1}travelAndFindElementById(e,t){let{getPropertyOfElement:n,getDatasetPrefix:i}=this.hostContext;if(this.isTextNode(t))return null;let a=nF(t,i);if(a.templateId!==this.id)return null;if(a.id===e)return t;for(let i of n(t,"children")){let t=this.travelAndFindElementById(e,i);if(t)return t}return null}travelAndFindElementByIdDeeply(e,t,n){if(!n||this.isTextNode(n))return null;let{getPropertyOfElement:i,getDatasetPrefix:a}=this.hostContext,r=nF(n,a);if(`${this.adCustomId}-${this.idPrefix}${e}`===r.templateId&&(!t||r.id===t))return n;for(let a of i(n,"children")){let n=this.travelAndFindElementByIdDeeply(e,t,a);if(n)return n}return null}doAnimation(e){return ti(this,arguments,function*({id:e,emitEvent:t=!0,animationName:n,animation:i}){let{animate:a}=this.hostContext,r=!i&&n?this.animations[n]:i,l=this.getElementById(e);if(!r||!l)return;let{keys:s,duration:o,easing:u}=r;if(2>((null==s?void 0:s.length)||0))return;let d=0;for(let t of Object.keys(s)){let n=s[+t],i=this.getDataValue("offset",n),r=this.getDataValue("opacity",n),c=this.getDataValue("left",n),h=this.getDataValue("top",n),f=this.getDataValue("rotate",n),p=this.getDataValue("scaleX",n),E=this.getDataValue("scaleY",n),m=this.getDataValue("translateX",n),_=this.getDataValue("translateY",n),g=this.getDataValue("background",n),T=this.getDataValue("color",n),C=this.getDataValue("width",n),S=this.getDataValue("height",n),y=o*i,A={opacity:r,left:c,top:h,rotate:f,scaleWidth:p,scaleHeight:E,background:g,color:T,width:C,height:S,translateX:m,translateY:_};if(y<=0)this.setStyle(e,A,{isDynamic:!0,force:!0});else{let t=e9(e8({},A),{easing:u,duration:y-d});t.left=A.left,t.top=A.top,yield null==a?void 0:a(l,t),d=y,this.setStyle(e,A,{isDynamic:!0,force:!0})}}n&&t&&(yield this.emitComponentEvent("animationend",{animation:n}))})}isDarkMode(){var e;return null!=(e=this.state.darkMode)?e:this.props.darkMode}updateInnerStyle(){if(this.innerStyles)for(let e of Object.keys(this.innerStyles)){let t=this.innerStyles[e];if(t.expr&&!this.run(t.expr))continue;let n=e8({},t.style||{});this.isDarkMode()&&Object.assign(n,t.darkModeStyle||{}),this.updateStyle(n,e)}}updateInnerClassStyle(){var e;if(this.innerClassStyles)for(let t of Object.keys(this.innerClassStyles)){let n=this.innerClassStyles[t];if(n.expr&&!this.run(n.expr))continue;let i=e8({},n.style||{});(null!=(e=this.state.darkMode)?e:this.props.darkMode)&&Object.assign(i,n.darkModeStyle||{}),this.updateStyle(i,`.${t}`)}}updateOuterStyle(){let e=e8({},this.template.style||{});this.isDarkMode()&&Object.assign(e,this.template.darkModeStyle||{}),this.updateStyle(e,"container")}updateClassStyle(){let{classStyles:e}=this;if(e)for(let t of Object.keys(e)){let n=e[t];if(n.expr&&!this.run(n.expr))continue;let i=e8({},n.style||{});this.isDarkMode()&&Object.assign(i,n.darkModeStyle||{}),this.updateStyle(i,`.${t}`)}}getClassList(e){let t={};return Object.keys(e).forEach(n=>{let i=e[n];Object.keys(i).forEach(e=>{t[n]=e.includes("Expr")?this.run(i.listExpr):i.list})}),t}setStyle(e,t,{isDynamic:n,force:i}={isDynamic:!1,force:!1}){let a=n?this.dynamicStyle:this.templateStyle,r=this.getDataValue("_useApx")?this.convertToApxRatio(t):t,{getRootElement:l}=this.renderContext,s=e,o=!1;if("$$"===s){let e=l(this);if(!e)return;s=nF(e,this.hostContext.getDatasetPrefix).id}if(r){for(let e of Object.keys(r))r[e]=this.convertApxStyle(r[e]);if(s)for(let e of(a[s]||(a[s]={}),Object.keys(r)))null!=r[e]?(r[e]!==a[s][e]&&(o=!0),a[s][e]=r[e]):delete a[s][e]}this.mounted&&o&&n&&i&&this.forceUpdate()}convertApxStyle(e){let{renderContext:{ratio:t}}=this;return tm(e,t)}updateStyle(e,t){if(e)for(let n of Object.keys(e)){let i=n,a=e[n];if(n.includes("Expr")){let e=n.split("Expr");2===e.length&&([i]=e,a=this.run(a))}this.setStyle(t,{[i]:a})}}getScope(){var e,t;let{ratio:n}=this.renderContext;return{$ad:this.getAdData(),$ads:this.getAdDatas(),$const:{true:!0,false:!1,isGame:!0,openParen:"(",closeParen:")",systemInfo:(null==(e=this.props)?void 0:e.systemInfo)||{},darkMode:(null==(t=this.props)?void 0:t.darkMode)||!1,ua:"",ratio:n,time:Date.now(),undefined:void 0},$for:{index:this.forIndex,item:this.forItem},$scope:this.props.extraScope||{}}}getExprInnerVars(){return void 0===iw[this.adCustomId]&&(iw[this.adCustomId]={}),iw[this.adCustomId]}getInnerFunction(){let e=this.getExprInnerVars(),{hostContext:t}=this;return{"#getVar":t=>e[t],"#measureText":(e,n)=>"function"==typeof t.getTextWidth?t.getTextWidth(e,n):0,"#getIconSrc":n3,"#getNickName":n4}}checkUseApx(){if(this.getDataValue("_useApx"))for(let e of Object.keys(this.style))this.style[e]=this.convertToApxRatio(this.style[e])}convertToApxRatio(e){let{ratio:t}=this.renderContext,n=/(\d+)px/g,i={};for(let a of Object.keys(e))if(null!=iL&&iL.includes(a)){let r=e[a],l=r;"number"==typeof r?l=r*t:"string"==typeof r?l=r.replace(n,(e,n)=>{let i=Number(n);return Number.isNaN(i)?e:`${i*t}px`}):Array.isArray(r)&&(null==r?void 0:r.every(e=>"number"==typeof e))&&(l=r.map(e=>e*t)),i[a]=l}else i[a]=e[a];return i}predownloadMaterial(e,t,n){this.emitAdCustomEvent("predownloadMaterial",{targetid:this.id,url:e,md5:t,key:n})}getTemplateStyle(e){return this.templateStyle[e]}},iW=tt(tl()),iV=e=>{let t={};for(let n of e)for(let e of Object.keys(n))t[e]||(t[e]={}),Object.assign(t[e],n[e]||{});return t},iG=(e,t,n)=>{var i,a,r,l,s,o,u,d;if(!t)return t;let c={},h=null;if(null!=(i=null==t?void 0:t.props)&&i.className)for(let n of t.props.className.split(/\s+/).filter(Boolean))e[`.bind-${n}`]&&Object.assign(c,e[`.bind-${n}`]),e[`.${n}`]&&Object.assign(c,e[`.${n}`]);if(Object.keys(n.classListMap).forEach(i=>{var a,r;i===((null==(a=null==t?void 0:t.props)?void 0:a.id)&&nW(null==(r=null==t?void 0:t.props)?void 0:r.id))&&n.classListMap[i].forEach(t=>{Object.assign(c,e[`.${t}`])})}),null!=(a=null==t?void 0:t.props)&&a.className)for(let n of t.props.className.split(/\s+/).filter(Boolean)){let t=nW(n);e[`.${t}`]&&Object.assign(c,e[`.${t}`])}if(null!=(r=null==t?void 0:t.props)&&r.style&&Object.assign(c,t.props.style),null!=(l=null==t?void 0:t.props)&&l.id){let n=nW(t.props.id);h=n,e[n]&&Object.assign(c,e[n])}let f=null!=(o=null==(s=t.props)?void 0:s.children)?o:null;null!=f&&"string"!=typeof f&&(f=iW.default.Children.map(f,t=>iG(e,t,n)));let p=e8({style:tR(c,n.styleOptions)},h?{id:h,uniqueId:`${n.templateInfo.templateId}${n.isItem?"__item":""}-${h}`}:{}),E=e9(e8({},n.templateInfo),{preventMask:String(null!=(d=null==(u=t.props)?void 0:u.preventMask)&&d),id:h||""});for(let e of Object.keys(E)){let t=null!=n&&n.getDatasetPrefix?null==n?void 0:n.getDatasetPrefix(e):e;p[`data-${nV(t)}`]=E[e]}n.useTextTag||"text"!==t.type||null==t.props.value||null!==f||(f=t.props.value),!1===n.useAttrId&&(p.id=null),!1===n.useAttrClass&&(p.className=null),n.classNames&&Object.keys(n.classNames).forEach(e=>{var i,a;e===((null==(i=null==t?void 0:t.props)?void 0:i.id)&&nW(null==(a=null==t?void 0:t.props)?void 0:a.id))&&(p.className=n.classNames[e])});let m=Array.isArray(f)?f:[f];return null==f?iW.default.cloneElement(t,p):iW.default.cloneElement(t,p,m)},iF=()=>e=>{if(e.prototype.render){let t=e.prototype.render;e.prototype.render=function(){this.emitComponentEvent("templateWillUpdate");let e=t.call(this);this.updateClassStyle(),this.updateInnerClassStyle(),this.handleStyleType.includes("style")&&this.updateOuterStyle(),this.handleStyleType.includes("innerStyle")&&this.updateInnerStyle();let{style:n,darkModeStyle:i,templateStyle:a,dynamicStyle:r,classNames:l,renderContext:{styleOptions:s,useTextTag:o,useAttrId:u,useAttrClass:d},hostContext:{getDatasetPrefix:c}}=this;return iG(iV([n,this.isDarkMode()?i:{},a,r]),e,{styleOptions:s,isItem:"base"===this.componentBehavior,useTextTag:o,useAttrId:u,useAttrClass:d,templateInfo:{templateId:this.id},classListMap:this.classListMap,classNames:l,getDatasetPrefix:c})}}return e},iH=tt(tl()),iY=(e,t)=>(...n)=>{null==e||e(...n),null==t||t(...n)},iB=()=>e=>{if(e.prototype.render){let t=e.prototype.render;e.prototype.render=function(){var e,n,i,a;this.emitComponentEvent("templateWillUpdate");let r=t.call(this),{props:{handleCommonClick:l,handleCommonTouchStart:s,handleCommonTouchEnd:o,handleCommonTouchCancel:u}}=this;return iH.default.cloneElement(r,nD({onClick:iY(null==(e=r.props)?void 0:e.onClick,l),onTouchStart:iY(null==(n=r.props)?void 0:n.onTouchStart,s),onTouchEnd:iY(null==(i=r.props)?void 0:i.onTouchEnd,o),onTouchCancel:iY(null==(a=r.props)?void 0:a.onTouchCancel,u)}))}}return e},iz,ij,iZ,i$=class extends iU{constructor(e){var t;if(super(e,{componentBehavior:"base"}),this.style={blank:{width:0,height:0},container:{position:"relative",WebkitTapHighlightColor:"transparent"},mask:{position:"absolute",top:0,left:0,width:"100%",height:"100%"},linearGradient:{position:"absolute",top:0,left:0,width:"100%",height:"100%"},".reverse":{rotate:180}},this.handleStyleType=["style"],this.isLinearGradient=!1,this.isLinearGradientReverse=!1,this.longTapThreshold=null,this.linearGradientSrc="https://wxa.wxs.qq.com/images/wxapp/linear_gradient.png",this.linearGradientRe=/^linear-gradient\(rgba\(0,0,0,([\d.]+)\),rgba\(0,0,0,([\d.]+)\)\)$/,this.state={showMask:!1,randomKey:"",darkMode:null!=(ij=this.props.darkMode)&&ij,prePropsDarkMode:null!=(iZ=this.props.darkMode)&&iZ},this.handleCommonClick=e=>{!1===this.bubbleEvent&&(this.emitAdCustomEvent("stopPropagationTap",{event:e}),null==e||e.stopPropagation()),this.doActions(this.events.tap,{event:e}),this.clickInfo&&this.emitAdCustomEvent("itemTap",{clickInfo:this.clickInfo})},this.isTouching=!1,this.longTapTimer=null,this.handleCommonTouchStart=e=>{this.isTouching=!0,!1===this.bubbleEvent&&(null==e||e.stopPropagation()),this.longTapThreshold&&(this.longTapTimer=setTimeout(()=>{this.isTouching&&this.doActions(this.events.longTap,{event:e})},this.longTapThreshold)),this.doActions(this.events.touchstart,{event:e})},this.handleCommonTouchEnd=e=>{this.isTouching=!1,!1===this.bubbleEvent&&(null==e||e.stopPropagation()),this.clearLongTapTimer(),this.doActions(this.events.touchend,{event:e})},this.handleCommonTouchCancel=e=>{this.isTouching=!1,!1===this.bubbleEvent&&(null==e||e.stopPropagation()),this.clearLongTapTimer(),this.doActions(this.events.touchcancel,{event:e})},this.clickInfo=this.getDataValueWithDefault("_clickInfo",null),this.isShow=this.getDataValueWithDefault("_isShow",!0),this.longTapThreshold=this.getDataValueWithDefault("_longTapThreshold",null),this.state.randomKey=Math.random().toString(16).slice(2,10),this.isShow&&null!=(t=this.template.style)&&t.background){let e=this.linearGradientRe.exec(this.template.style.background.replace(/ /g,""));if(e){let[t,n]=e.slice(1);this.isLinearGradient=!0,Number(t)>Number(n)&&(this.isLinearGradientReverse=!0)}}this.bindEvents()}static getDerivedStateFromProps(e,t){return e.darkMode!==t.prePropsDarkMode?{darkMode:e.darkMode,prePropsDarkMode:e.darkMode}:null}bindEvents(){this.addAdCustomListener(e=>{if("setStyle"===e.action){let{id:t,targetid:n,style:i}=e.value||{};n!==this.id||t||this.setStyle("container",i,{isDynamic:!0,force:!0})}else if("setDarkMode"===e.action&&this.props.isRootItem){let{darkMode:t}=e.value;this.state.darkMode!==t&&this.setState({darkMode:t})}})}getRandomKey(){return Math.random().toString(16).slice(2,10)}doAction(e){"forceRebuild"===e.payload.action&&this.setState({randomKey:this.getRandomKey()})}clearLongTapTimer(){this.longTapTimer&&(clearTimeout(this.longTapTimer),this.longTapTimer=null)}render(){let{renderContext:{isFlattenLayer:e}}=this,{template:t}=this.props,{randomKey:n,darkMode:i}=this.state,a=e9(e8({},this.getCommonSubComponentProps()),{darkMode:i});if(a.randomKey=n,"for"===t.type)return n8.default.createElement(aE,e8({},a));let r={layout:n8.default.createElement(i1,e8({},a)),image:n8.default.createElement(i9,e8({},a)),text:n8.default.createElement(i3,e8({},a)),feedback:n8.default.createElement(as,e8({},a)),button:n8.default.createElement(at,e8({},a)),video:n8.default.createElement(aa,e8({},a)),end:n8.default.createElement(af,e8({},a)),carousel:n8.default.createElement(ag,e8({},a)),countdown:n8.default.createElement(ac,e8({},a)),livetag:n8.default.createElement(aI,e8({},a)),popup:n8.default.createElement(aR,e8({},a)),feedbackDialog:n8.default.createElement(aN,e8({},a)),multimage:n8.default.createElement(aW,e8({},a)),score:n8.default.createElement(aB,e8({},a)),rank:n8.default.createElement(a$,e8({},a)),hosttag:n8.default.createElement(aX,e8({},a))}[t.type]||null;if(!this.isShow||null===r)return n8.default.createElement("div",{id:"blank"});let l=this.isLinearGradient?n8.default.createElement("img",{id:"linear-gradient",className:this.isLinearGradientReverse?"reverse":"",src:this.linearGradientSrc}):null;if(r&&e()){let e=Array.isArray(r.props.children)?r.props.children:[r.props.children];return(0,n8.cloneElement)(r,{handleCommonClick:this.handleCommonClick,handleCommonTouchStart:this.handleCommonTouchStart,handleCommonTouchEnd:this.handleCommonTouchEnd,handleCommonTouchCancel:this.handleCommonTouchCancel},...e)}return n8.default.createElement("div",{id:"container",onClick:this.handleCommonClick,onTouchStart:this.handleCommonTouchStart,onTouchEnd:this.handleCommonTouchEnd,onTouchCancel:this.handleCommonTouchCancel},l,r)}};i$=tn([iF()],i$);var iK=tt(tl());function iQ(e){class t extends iU{constructor(e){var t,n;super(e,{componentBehavior:"hoc"}),this.style={mask:{position:"absolute",top:0,left:0,width:"100%",height:"100%"}},this.state={showMask:!1},this.handleStyleType=[],this.containerRef=(0,iK.createRef)(),this.handleTouchStart=e=>{let{getPropertyOfElement:t,getDatasetPrefix:n}=this.hostContext;if(e._isHandleMaskClick||!this.useMask||!this.containerRef.current)return;this.hideMaskOnSlide&&e.touches&&(this.isTouching=!0,this.touchStartX=e.touches[0].pageX,this.touchStartY=e.touches[0].pageY);let i=e.target,a=this.containerRef.current;for(;i&&i!==a;){let{preventMask:e}=nF(i,n);if("true"===e)return;i=t(i,"parent")}this.setState({showMask:!0}),e._isHandleMaskClick=!0},this.handleTouchMove=e=>{if(!this.isTouching||!this.useMask||!this.containerRef.current)return;let t=Math.abs(e.touches[0].pageX-this.touchStartX),n=Math.abs(e.touches[0].pageY-this.touchStartY);(t>this.minSlideDistance||n>this.minSlideDistance)&&this.setState({showMask:!1})},this.handleTouchEnd=()=>{this.useMask&&this.containerRef.current&&(this.setState({showMask:!1}),this.isTouching=!1)},this.eventDelegator=this.renderContext.globalEventDelegator,this.hideMaskOnSlide=null!=(t=this.renderContext.hideMaskOnSlide)&&t,this.minSlideDistance=null!=(n=this.renderContext.minSlideDistance)?n:1,this.touchStartX=0,this.touchStartY=0,this.isTouching=!1,this.useMask=this.getDataValueWithDefault("_useMask",!1),this.bindEvents()}componentWillUnmount(){this.unbindEvents(),super.componentWillUnmount()}bindEvents(){this.addAdCustomListener(e=>{if("setStyle"===e.action){let{id:t,targetid:n,style:i}=e.value||{};n!==this.id||t||this.setStyle("container",i,{isDynamic:!0,force:!0})}}),this.eventDelegator.on("touchstart",`${this.id}-container`,this.handleTouchStart),this.hideMaskOnSlide&&this.useMask&&this.eventDelegator.on("touchmove",`${this.id}-container`,this.handleTouchMove),this.eventDelegator.onGlobal("touchend",this.handleTouchEnd),this.eventDelegator.onGlobal("touchcancel",this.handleTouchEnd)}unbindEvents(){this.eventDelegator.off("touchstart",`${this.id}-container`,this.handleTouchStart),this.eventDelegator.off("touchmove",`${this.id}-container`,this.handleTouchMove),this.eventDelegator.offGlobal("touchend",this.handleTouchEnd),this.eventDelegator.offGlobal("touchcancel",this.handleTouchEnd)}getMaskStyle(){let{showMask:e}=this.state,{darkMode:t}=this.props,{getMaskStyle:n}=this.hostContext,{maskStyle:i,darkModeMaskStyle:a}=(null==n?void 0:n())||{},r=e8({},this.getDataValueWithDefault("_maskStyle",{})),l=i||{};return t&&(r=Object.assign(r,this.getDataValueWithDefault("_darkModeMaskStyle",{})),l=Object.assign(l,a)),Object.assign(Object.keys(l).length?l:{position:"absolute",top:0,left:0,width:"100%",height:"100%",backgroundColor:"rgba(0, 0, 0, 0.05)"},r,{display:this.useMask&&e?"":"none"})}render(){let{props:t}=this,{forwardedRef:n}=t,i={};return this.useMask&&Object.assign(i,{maskContainerRef:this.containerRef,renderMask:()=>iK.default.createElement("div",{id:"mask",style:this.getMaskStyle()})}),iK.default.createElement(e,e9(e8(e8({},t),i),{ref:n}))}}return(0,iK.forwardRef)((e,n)=>iK.default.createElement(t,e9(e8({},e),{forwardedRef:n})))}var iX=class extends iU{constructor(e){var t,n,i,a,r;super(e),this.style={body:{zIndex:1,flexDirection:"column",alignItems:"center",justifyContent:"center"},".full-size":{width:"100%",height:"100%"},".full":{position:"absolute",left:0,right:0,top:0,bottom:0},".tip-container":{position:"absolute",top:0,left:0,width:"100%",height:"100%",justifyContent:"center",alignItems:"center",hidden:!0},".video-close-tip":{position:"relative",zIndex:1e3,height:"48px",width:"122px",lineHeight:"48px",backgroundColor:"#4C4C4C",borderRadius:"12px",fontSize:"17px",color:"#fff",textAlign:"center"},".empty":{width:"100%",height:"100%",justifyContent:"center",alignContent:"center",alignItems:"center",backgroundColor:"#dcdcdc",color:"#888",textAlign:"center",fontSize:"12px",minHeight:"50px"},".full-card-container":{zIndex:1000100,display:"flex",background:"#ffffff",borderRadius:"7px"},".full-card-container-row":{flexDirection:"row"},".full-card-container-column":{flexDirection:"column"},".no-interest-logo":{backgroundImage:"url(\"data:image/svg+xml,%3Csvg width='96' height='91' xmlns='http://www.w3.org/2000/svg'%3E %3Cg fill='none' fill-rule='evenodd'%3E %3Cpath d='M-327-540h750V666h-750z'/%3E %3Cpath d='M0-2h96v96H0z'/%3E %3Cpath d='M20 59v5l-8.89 5.927A2 2 0 0 1 8 68.263V18a6 6 0 0 1 6-6h56a6 6 0 0 1 6 6v2h-4v-2a2 2 0 0 0-2-2H14a2 2 0 0 0-2 2v46l8-5zm4 0h2.298v5H24v-5zm48-35h4v2.62h-4V24z' fill='%2307C160'/%3E %3Cpath d='M30 24h54a6 6 0 0 1 6 6v48.263a2 2 0 0 1-3.11 1.664L78 74H30a6 6 0 0 1-6-6V30a6 6 0 0 1 6-6zm0 4a2 2 0 0 0-2 2v38a2 2 0 0 0 2 2h50l6 4V30a2 2 0 0 0-2-2H30z' fill='%2307C160'/%3E %3Cpath d='M53.172 54.485l14.142-14.142 2.828 2.829L56 57.314l-2.828 2.828-11.314-11.314L44.686 46l8.486 8.485z' fill='%2307C160' fill-rule='nonzero'/%3E %3C/g%3E %3C/svg%3E\")",backgroundSize:"cover",width:"48px",height:"45.5px"},".no-interest-logo-row":{marginRight:"8px"},".no-interest-logo-column":{marginBottom:"24px"},".full-card-feedback-container-column":{display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center"},".full-card-feedback-title":{fontSize:"17px",lineHeight:"17px",color:"#2e2e2e"},".full-card-feedback-title-row":{marginBottom:"5px"},".full-card-feedback-title-column":{marginBottom:"14px"},".full-card-feedback-tips":{color:"#acacac",lineHeight:"12px",fontSize:"12px"}},this.state={hidden:!1,showFeedback:!1,darkMode:!1},this.handleStyleType=["style","innerStyle"],this.handleAdCustomEvent=e=>{switch(e.action){case"setDarkMode":{let{darkMode:t}=e.value;this.state.darkMode!==t&&this.setState({darkMode:t});break}case"hideAd":this.hide();break;case"adShowHalf":this.emitComponentEvent("showhalf");break;case"adHideHalf":this.emitComponentEvent("hidehalf");break;case"adShowEdge":this.emitComponentEvent("showEdge");break;case"adHideEdge":this.emitComponentEvent("hideEdge");break;case"btnTap":this.emitComponentEvent("adButtonTap");break;case"exposure":"value"in e?this.emitComponentEvent("exposure",e.value):this.emitComponentEvent("exposure");break;case"viewableExposure":"value"in e?this.emitComponentEvent("viewableExposure",e.value):this.emitComponentEvent("viewableExposure");break;case"showFeedback":this.showFeedback()}},this.show=e=>new Promise(t=>{this.useAnimate&&!e?this.setState({hidden:!1},()=>{this.emitComponentEvent("showAnimate"),t()}):this.useAnimate&&e?(this.emitAdCustomEvent("checkExposure"),t()):this.setState({hidden:!1},()=>{this.emitAdCustomEvent("checkExposure"),t()})}),this.hide=e=>new Promise(t=>{this.useAnimate&&!e?(this.emitComponentEvent("hideAnimate"),t()):this.setState({hidden:!0},()=>{this.emitAdCustomEvent("checkExposure"),t()})}),this.showFeedback=()=>{let{useDefaultFeedback:e,useFullAreaFeedBack:t}=this;e&&(this.setState({showFeedback:!0}),setTimeout(()=>{this.setState({showFeedback:!1})},3e3)),t&&this.setState({showFeedback:!0})},this.handleEnterForeground=()=>{this.emitComponentEvent("appEnterForeground"),this.emitAdCustomEvent("appEnterForeground")},this.handleEnterBackground=()=>{this.emitComponentEvent("appEnterBackground"),this.emitAdCustomEvent("appEnterBackground")},this.handleContainerClick=e=>{this.props.isMulti||this.disableContainerTap||(null==e||e.stopPropagation(),this.emitAdCustomEvent("stopPropagationTap",{event:e}),this.emitComponentEvent("containerTap"),this.emitAdCustomEvent("containerTap",{event:e,clickInfo:this.clickInfo}))},this.useDefaultFeedback=this.getDataValueWithDefault("useDefaultFeedback",!0),this.isFullScreen=this.getDataValueWithDefault("isFullScreen",!1),this.isWidthRequired=this.getDataValueWithDefault("isWidthRequired",!1),this.useStyleHidden=this.getDataValueWithDefault("useStyleHidden",!1),this.isMulti=null!=(n=null==(t=this.template.tplConfig)?void 0:t.isMulti)&&n,this.useAnimate=this.getDataValueWithDefault("useAnimate",!1),this.disableContainerTap=this.getDataValueWithDefault("disableContainerTap",!1),this.state.hidden=!this.getDataValueWithDefault("_isShow",!0),this.state.darkMode=null!=(i=this.props.darkMode)&&i,this.useFullAreaFeedBack=this.getDataValueWithDefault("useFullAreaFeedBack",!1),this.fullAreaFeedBackType=this.getDataValueWithDefault("fullAreaFeedBackType","column"),this.classStyles=(null==(r=null==(a=this.template)?void 0:a.options)?void 0:r.classStyles)||{},this.bindEvents(),this.emitComponentEvent("initialTemplate"),this.clickInfo=this.getDataValueWithDefault("_clickInfo",null)}componentWillUnmount(){super.componentWillUnmount(),this.unbindEvents()}bindEvents(){let{hostContext:e}=this.props;e.onAppEnterForeground(this.handleEnterForeground),e.onAppEnterBackground(this.handleEnterBackground),this.addAdCustomListener(this.handleAdCustomEvent)}unbindEvents(){let{hostContext:e}=this.props;e.offAppEnterForeground(this.handleEnterForeground),e.offAppEnterBackground(this.handleEnterBackground)}getRootData(e){switch(e){case"hidable":return this.getDataValueWithDefault("hidable",!0);case"refetchable":return this.getDataValueWithDefault("refetchable",!1);case"flattenLayer":return this.getDataValueWithDefault("flattenLayer",!1);case"exportAid":return this.getDataValueWithDefault("exportAid",!1);case"enableTimer":return this.getDataValueWithDefault("enableTimer",!0);case"exposureUntilVideoPlay":return this.getDataValueWithDefault("exposureUntilVideoPlay",!1);case"hideAfterLanding":return this.getDataValueWithDefault("hideAfterLanding",!1);case"multiShowList":return this.getDataValueWithDefault("multiShowList",[]);case"hasVideoElement":return this.getDataValueWithDefault("hasVideoElement",!1)}return null}doAction({payload:e,args:t}){var n,i;"hideAd"===e.action?(this.hide(),"value"in e&&e.value.preventClose):"changeMultiShow"===e.action?null!=(n=e.value)&&n.listExpr?this.emitAdCustomEvent("changeMultiShow",{list:this.run(e.value.listExpr,t)}):null!=(i=e.value)&&i.list?this.emitAdCustomEvent("changeMultiShow",{list:e.value.list}):null!=t&&t.list&&this.emitAdCustomEvent("changeMultiShow",{list:t.list}):"showAnimateDone"===e.action?this.show(!0):"hideAnimateDone"===e.action&&this.hide(!0)}isShow(){return!this.state.hidden}render(){let{props:{template:e,adPosition:t,systemInfo:n,renderMask:i,maskContainerRef:a},state:{hidden:r,showFeedback:l,darkMode:s}}=this,o={display:r?"none":""},u=["left","top"];return"miniprogram"===this.props.systemInfo.hostEnv&&u.push("fixed"),this.isWidthRequired&&u.push("width"),this.isFullScreen?Object.assign(o,{width:n.screenWidth,height:n.screenHeight}):Object.assign(o,nO(t||{},u)),r&&!this.useStyleHidden?n6.default.createElement("div",null):n6.default.createElement("div",{id:"body",style:o,className:this.isFullScreen?"full-size":""},e&&n6.default.createElement("div",{id:"container",className:`wx-ad-custom ${this.isFullScreen?"full full-size":""}`,onClick:this.handleContainerClick,ref:a},e.children.map(e=>n6.default.createElement(i$,e9(e8({key:e.id},this.getCommonSubComponentProps()),{template:e,darkMode:s})))),this.useFullAreaFeedBack&&l&&n6.default.createElement("div",{id:"full-card-container",className:`tip-container full-card-container ${"row"===this.fullAreaFeedBackType?"full-card-container-row":"full-card-container-column"}`},n6.default.createElement("div",{className:`no-interest-logo ${"row"===this.fullAreaFeedBackType?"no-interest-logo-row":"no-interest-logo-column"}`}),n6.default.createElement("div",{id:"full-card-feedback-container",className:`${"row"===this.fullAreaFeedBackType?"":"full-card-feedback-container-column"}`},n6.default.createElement("div",{id:"full-card-feedback-title",className:`full-card-feedback-title ${"row"===this.fullAreaFeedBackType?"full-card-feedback-title-row":"full-card-feedback-title-column"}`},"感谢你的反馈"),n6.default.createElement("div",{id:"full-card-feedback-tips",className:"full-card-feedback-tips"},"我们会努力为你推荐更优质的广告"))),this.useDefaultFeedback&&l&&n6.default.createElement("div",{id:"tip-container",className:"tip-container"},n6.default.createElement("text",{id:"close-tip",className:"video-close-tip",value:"感谢反馈"})),null==i?void 0:i())}},iq=iQ(iX=tn([iF()],iX)),iJ=tt(tl()),i0=class extends iU{constructor(e){super(e),this.style={".layout-row":{display:"flex",flexDirection:"row",alignItems:"center",justifyContent:"center"},".layout-column":{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},".layout-proportion":{display:"flex",position:"absolute",width:"100%",height:"100%"}},this.state={children:[]},this.classRow=!1,this.classColumn=!1,this.containerStyle={},this.baseType=this.options.baseType||1,2===this.baseType?this.classRow=!0:3===this.baseType?this.classColumn=!0:this.baseType,this.state.children=this.template.children||[]}doAction({payload:e}){if("addChildren"===e.action){let{value:t}=e,n=Array.isArray(t)?t:[t],i=[...this.state.children,...n];this.setState({children:i})}}render(){let{children:e}=this.state,{renderMask:t,maskContainerRef:n,randomKey:i}=this.props,a=["layout"];return this.classRow&&a.push("layout-row"),this.classColumn&&a.push("layout-column"),iJ.default.createElement("div",{id:"container",className:a.join(" "),ref:n,style:this.containerStyle,key:i||""},e.map(e=>iJ.default.createElement(i$,e9(e8({key:e.id},this.getCommonSubComponentProps()),{template:e}))),null==t?void 0:t())}},i1=iQ(i0=tn([iF(),iB()],i0)),i2=tt(tl()),i5=class extends iU{constructor(e){var t;super(e),this.style={".text":{color:"#000",fontSize:"14px",wordBreak:"break-all"},".text-ellipsis":{lineBreak:"ellipsis",overflow:"hidden"},".emoji-container":{position:"relative",width:"100%",overflow:"hidden",display:"flex",flexDirection:"row",flexWrap:"wrap",alignItems:"center",justifyContent:"flex-start"},".emoji-text":{display:"flex"}},this.state={text:"",renderType:"text",renderText:""},this.classEllipsis=!1,this.emojiContainerRef=i2.default.createRef(),this.emojiStyleStatus=!1,this.useTextAriaLabel=!1,this.searchReplaceNode=()=>ti(this,null,function*(){var e;let t=this.emojiContainerRef.current,n=t.childNodes,i=yield this.getBoundingClientRect(t),a=parseInt(null==(e=this.getTemplateStyle("text"))?void 0:e.fontSize,10),r=yield this.upperBound(n,i);if(r<n.length-1)for(let e=r;e>0;e--){let e=yield this.getBoundingClientRect(n[r]);if(i.width-(e.left-i.left)<a){r-=1;continue}break}else r=-1;return r}),this.upperBound=(e,t)=>ti(this,null,function*(){let n=0,i=e.length;for(;n<i;){let a=n+Math.floor((i-n)/2);(yield this.isHidden(e[a],t))?i=a:n=a+1}return n-1}),this.isHidden=(e,t)=>ti(this,null,function*(){let{top:n,left:i,width:a}=yield this.getBoundingClientRect(e),{top:r,height:l,left:s,width:o}=t;return!!(Math.round(n-r)>=Math.round(l)||i-s+a>=o+.1)});let n=this.options.baseType||2;1===n?this.initCommonComponent():2===n?this.initNickNameComponent():3===n?this.initDescComponent():4===n&&this.initRelationComponent(),this.classEllipsis=this.getDataValueWithDefault("isEllipsis",!0),this.maxLen=this.getDataValueWithDefault("maxLen",null),this.useTextAriaLabel=(null==(t=this.hostContext)?void 0:t.useTextAriaLabel)||!1,this.setText(this.state.text)}componentDidMount(){super.componentDidMount(),4===this.options.baseType&&this.emitAdCustomEvent("fetchRelationText",{targetid:this.id}),this.calcEmojiOverflow()}componentDidUpdate(e,t){super.componentDidUpdate&&super.componentDidUpdate(e,t),"emoji"===this.state.renderType&&t.text!==this.state.text&&this.calcEmojiOverflow()}initCommonComponent(){this.state.text=this.getDataValueWithDefault("text","")}initRelationComponent(){this.state.text="",this.addAdCustomListener(e=>{if("changeRelationText"===e.action){let{targetid:t,text:n}=e.value;t===this.id&&(this.setText(n),this.emitComponentEvent("relationTextChanged",{text:n}),this.emitAdCustomEvent("relationTextChanged",{text:n}))}})}initNickNameComponent(){let e=this.getAdData();this.state.text=n4(e)}initDescComponent(){let e=this.getAdData(),t=[19,12].includes(e.product_type),n=[559,560,721,929].includes(e.crt_size),i=46===e.product_type,a=23===e.product_type,r=[29,31].includes(e.product_type),l=[30,25].includes(e.product_type),s=[{path:["extBackComm","desc_1"]},{path:["crtInfo","0","wxgame_video_card","desc"],condition:n},{path:["wording"],condition:[927,841].includes(e.crt_size)}];this.getDataValueWithDefault("useDefault",!1)?s.push({path:["extBackComm","default_desc"]},{value:"今日活动推荐",condition:r},{value:"今日商品推荐",condition:l},{value:"今日应用推荐",condition:t},{value:"今日公众号推荐",condition:a},{value:"今日小游戏推荐",condition:i},{value:"今日活动推荐"}):s.push({value:""}),this.state.text=nU(e,s)}doAction({payload:e,args:t}){if("textSetText"===e.action){let n=this.getDataValue("text",e.value,{args:t});this.setText(n)}}setText(e){let{text:t,renderText:n,renderType:i}=this.prepareRenderText(e);this.mounted?this.setState({text:t,renderText:n,renderType:i}):(this.state.text=t,this.state.renderType=i,this.state.renderText=n)}hasEmoji(e){return/\[.*?\]/g.test(e)}prepareRenderText(e){let{renderContext:{transformLinefeed:t}}=this,n=this.hasEmoji(e),i=e;return t&&(i=this.transformLinefeed(e)),n&&(this.emojiStyleStatus||this.initEmojiTextStyle(),i=this.getEmojiTextJsx(i)),{text:e,renderType:n?"emoji":"text",renderText:i}}transformLinefeed(e){return e.replace(`
`," ")}getEmojiTextJsx(e){let t=function(e){let t,n;let i=(t=[],n=[],nH.forEach(e=>{t.push(e.img),n.push(e.text)}),{nameList:n,imgList:t}),a=/\[.*?\]/g,r=a.exec(e),l=0,s=[];for(;r;){let t=r[0],n=r.index,o=i.nameList.indexOf(t);n>l&&e.substr(l,n-l).split("").forEach(e=>{s.push({type:"text",props:{value:e}})}),o>-1&&i.imgList[o]?s.push({type:"img",props:{src:`https://wxa.wxs.qq.com/wxad-design/emojis/${i.imgList[o]}.png`}}):t.split("").forEach(e=>{s.push({type:"text",props:{value:e}})}),l=n+t.length,r=a.exec(e)}return e.substr(l).split("").forEach(e=>{s.push({type:"text",props:{value:e}})}),s}(e)||[];return t.length?i2.default.createElement(i2.default.Fragment,null,t.map((e,t)=>{var n,i;let a;return e.props||(e.props={}),"img"===e.type?(null!=(n=e.props)&&n.className?e.props.className+=" emoji-img":e.props.className="emoji-img",a={type:"div",props:{className:"emoji-img-wrap",key:`img_${t}`},children:i2.default.createElement(e.type,e.props,null)}):(null!=(i=(a=Object.assign({},e)).props)&&i.className?a.props.className+=" emoji-text":a.props.className="emoji-text",a.props.key=`text_${t}`),i2.default.createElement(a.type,a.props,a.children)})):""}initEmojiTextStyle(){var e,t,n;(null==(n=null==(t=null==(e=this.template)?void 0:e.options)?void 0:t.innerStyles)?void 0:n.text)&&(this.bindStyle([{originId:"text",originKey:"maxHeight",destRules:[{destClassName:"emoji-container"}]},{originId:"text",originKey:"maxWidth",destRules:[{destClassName:"emoji-container"}]},{originId:"text",originKey:"width",destRules:[{destClassName:"emoji-container"}]},{originId:"text",originKey:"textAlign",destRules:[{destClassName:"emoji-container",handleFn:e=>"left"===e?"flex-start":"right"===e?"flex-end":e,destKey:"justifyContent"}]},{originId:"text",originKey:"height",destRules:[{destClassName:"emoji-container"},{destClassName:"emoji-text",handleFn:(e,t)=>t.lineHeight||e}]},{originId:"text",originKey:"WebkitBoxOrient",destRules:[{destClassName:"emoji-container"}]},{originId:"text",originKey:"marginTop",destRules:[{destClassName:"emoji-container"}]},{originId:"text",originKey:"color",destRules:[{destClassName:"emoji-text"}]},{originId:"text",originKey:"fontSize",destRules:[{destClassName:"emoji-text"},{destClassName:"emoji-img",destKey:"width"},{destClassName:"emoji-img",destKey:"height"},{destClassName:"emoji-img-wrap",destKey:"width"},{destClassName:"emoji-img-wrap",destKey:"height"},{destClassName:"emoji-overflow",destKey:"width"}]},{originId:"text",originKey:"lineHeight",destRules:[{destClassName:"emoji-text"},{destClassName:"emoji-img"},{destClassName:"emoji-text",destKey:"height",handleFn:(e,t)=>e||t.height}]},{originId:"text",originKey:"fontWeight",destRules:[{destClassName:"emoji-text"}]},{originId:"text",originKey:"fontFamily",destRules:[{destClassName:"emoji-text"}]}]),this.emojiStyleStatus=!0)}getBoundingClientRect(e){let{getBoundingClientRect:t}=this.hostContext;return t?t(e):{}}calcEmojiOverflow(){return ti(this,null,function*(){let{renderType:e,renderText:t}=this.state;if("text"===e||!t)return;let n=[...t.props.children],i=yield this.searchReplaceNode();-1!==i&&("div"===n[i].type?n.splice(i,1,i2.default.createElement("text",{className:"emoji-text emoji-overflow",value:"..."})):(n[i].props.className+=" emoji-overflow",n[i].props.value="..."),n=n.slice(0,i+1),t.props.children=n,this.setState({renderText:Object.assign({},t)}))})}render(){let{classEllipsis:e,emojiContainerRef:t,useTextAriaLabel:n}=this,{renderMask:i,maskContainerRef:a,randomKey:r}=this.props,{text:l,renderType:s}=this.state,{renderText:o}=this.state;return"text"===s&&this.classEllipsis&&null!==this.maxLen&&o&&(o=nG(o,2*this.maxLen)),i2.default.createElement("div",{id:"container",ref:a,key:r||""},"emoji"===s?i2.default.createElement("div",e9(e8({ref:t},n?{role:"option","aria-label":l}:{}),{id:"emoji-container",className:"emoji-container"}),o):i2.default.createElement("text",{id:"text",className:`text ${e?"text-ellipsis":""}`,value:o}),null==i?void 0:i())}},i3=iQ(i5=tn([iF(),iB()],i5)),i4=tt(tl()),i6=(e,t)=>{if(!e)return"";if(!t)return e;let n=e.split("#"),i=n[0].indexOf("?")>-1?"&":"?",a="",r=Object.keys(t);if(r.length>0){for(let e of r)a=a.concat(`${e}=${t[e]}&`);return a.length>0&&(a=a.substring(0,a.length-1)),n[0]+i+a+(n[1]?`#${n[1]}`:"")}return e},i8=class extends iU{constructor(e){super(e),this.style={".image-img":{width:"100%",height:"100%"},".image-img-appicon":{borderRadius:"5px"},".image-img-icon":{borderRadius:"50%"}},this.state={imgUrl:"",localImgUrl:""},this.classAppIcon=!1,this.classIcon=!1,this.useBgMode=!1,this.usePredownload=!1,this.retry=0,this.exposureLoadRetry=0,this.loadError=!1,this.orginImgUrl="",this.currentImgUrl="",this.handleAdCustomEvent=e=>{if("adShowEdge"===e.action){if(this.loadError){let e=Number(this.getDataValueWithDefault("attachRetryTimes",5));if(this.exposureLoadRetry<e){this.exposureLoadRetry+=1;let e=i6(this.state.imgUrl,{exposureLoadRetry:this.exposureLoadRetry});this.usePredownload?this.setState({localImgUrl:e}):this.setState({imgUrl:e})}}}else"predownloadMaterialDone"===e.action&&e.value.targetid===this.id&&this.usePredownload&&e.value.url===this.currentImgUrl&&this.setState({localImgUrl:e.value.cachedUrl})},this.handleImgLoad=()=>{this.loadError=!1,this.emitComponentEvent("imgLoad"),this.emitAdCustomEvent("imgLoad",{baseType:this.baseType,src:this.getRenderUrl()})},this.handleImgError=e=>{this.loadError=!0;let t=Number(this.getDataValue("retryTimes"));if(t){if(this.orginImgUrl||(this.orginImgUrl=this.state.imgUrl),this.retry<t){this.retry+=1;let e=i6(this.orginImgUrl,{retry:this.retry});this.setState({imgUrl:e})}else this.emitComponentEvent("imgError"),this.emitAdCustomEvent("imgError",{baseType:this.baseType,src:this.getRenderUrl(),error:e})}else this.emitComponentEvent("imgError"),this.emitAdCustomEvent("imgError",{baseType:this.baseType,src:this.getRenderUrl(),error:e})},this.useBgMode=this.getDataValueWithDefault("useBgMode",!1),this.usePredownload=this.getDataValueWithDefault("usePredownload",!1);let t=this.options.baseType||2;switch(this.baseType=t,t){case 1:this.initCommonComponent();break;case 3:this.initADComponent();break;case 2:this.initIconComponent()}this.addAdCustomListener(this.handleAdCustomEvent),this.useBgMode}initCommonComponent(){this.setImageUrl(this.getDataValueWithDefault("imgUrl",""))}initIconComponent(){let e=this.getAdData();[19,12].includes(e.product_type)?this.classAppIcon=!0:this.classIcon=!0,this.setImageUrl(n3(e))}initADComponent(){let e=this.getAdData(),t=e.image_url;t.startsWith("http")||(t=nU(e,[{path:["crtInfo","0","image_url"]},{path:["crtInfo","image_url"]}])||t);let n=nU(e,[{path:["crtInfo","0","card_info","image_md5"]},{path:["crtInfo","card_info","image_md5"]}]);this.setImageUrl(this.getDataValue("imgUrl")||t,n)}setImageUrl(e,t){this.currentImgUrl=e,this.mounted?this.setState({imgUrl:e}):this.state.imgUrl=e,this.usePredownload&&this.predownloadMaterial(e,t)}doAction({payload:e}){"imageSetSrc"===e.action&&this.setImageUrl(this.getDataValue("imgUrl",e.value))}getRenderUrl(){let{imgUrl:e,localImgUrl:t}=this.state;return this.usePredownload?t:e}render(){let{classAppIcon:e,classIcon:t}=this,{renderMask:n,maskContainerRef:i,randomKey:a}=this.props,r=3===this.baseType,l=["image-img"];return e&&l.push("image-img-appicon"),t&&l.push("image-img-icon"),i4.default.createElement("div",{id:"container",className:"image",ref:i,key:a||""},i4.default.createElement("img",e9(e8({id:"img"},r?{"bubble-event":!1}:{}),{className:l.join(" "),src:this.getRenderUrl(),onLoad:this.handleImgLoad,onError:this.handleImgError})),null==n?void 0:n())}},i9=iQ(i8=tn([iF(),iB()],i8)),i7=tt(tl()),ae=class extends iU{constructor(e){super(e),this.style={".button":{position:"relative",display:"flex",flexDirection:"row",alignItems:"center",justifyContent:"center",backgroundColor:"#07c160",padding:"0 12px",borderRadius:"3px",overflow:"hidden"},".button-text":{textAlign:"center",color:"#fff",fontSize:"16px"},".button-icon":{width:"12px",height:"11px",marginRight:6,backgroundSize:"100% 100%",backgroundPosition:"0 0"}},this.state={buttonText:""},this.isShowIcon=!1,this.handleAdCustomEvent=e=>{"changedAdBtnText"===e.action&&e.value.text&&this.setState({buttonText:e.value.text})},this.handleButtonClick=e=>{null==e||e.stopPropagation(),this.emitAdCustomEvent("stopPropagationTap",{event:e}),this.emitAdCustomEvent("btnTap",{event:e}),this.emitComponentEvent("buttonTap",{event:e})};let t=this.options.baseType||2;this.isShowIcon=this.getDataValueWithDefault("isShowIcon",!1),1===t?this.initCommonComponent():2===t&&this.initLogicCommonComponent()}componentWillUnmount(){super.componentWillUnmount()}initCommonComponent(){this.state.buttonText=this.getDataValueWithDefault("buttonText","")}initLogicCommonComponent(){var e,t,n;let i=this.getAdData(),a=this.getDataValue("buttonText"),r=(null==(e=i.buttonAction)?void 0:e.button_text)||(null==(t=i.button_action)?void 0:t.button_text)||"查看";a?this.state.buttonText=a:23===i.product_type?this.state.buttonText=null!=(n=null==i?void 0:i.biz_info)&&n.is_subscribed?"查看":r:this.state.buttonText=r,this.addAdCustomListener(this.handleAdCustomEvent)}doAction({payload:e}){let t=this.getDataValue("value",e);"buttonSetText"===e.action&&this.setText(t)}setText(e){this.setState({buttonText:this.getDataValue("text",e)})}render(){let{renderMask:e,maskContainerRef:t,randomKey:n}=this.props;return i7.default.createElement("div",{id:"container",className:"button",ref:t,onClick:this.handleButtonClick,key:n||""},this.isShowIcon&&i7.default.createElement("div",{id:"button-icon",className:"button-icon"}),i7.default.createElement("text",{id:"button-text",className:"button-text",value:this.state.buttonText}),null==e?void 0:e())}},at=iQ(ae=tn([iF()],ae)),an=tt(tl()),ai=class extends iU{constructor(e){super(e),this.style={".video-context":{zIndex:1000008},".video-block":{display:"block",width:"100%",height:"100%"},".click-mask":{position:"absolute",zIndex:1000008,borderRadius:"6px",width:"100%",height:"100%"},".video-poster":{display:"flex",position:"absolute",zIndex:1000008,width:"100%",height:"100%"},".video-voice-box":{zIndex:1000009,position:"absolute",width:"24px",height:"24px"},".video-voice-box-topleft":{top:"10px",left:"10px"},".video-voice-box-topright":{top:"10px",right:"10px"},".video-voice-box-bottomleft":{bottom:"10px",left:"10px"},".video-voice-box-bottomright":{bottom:"10px",right:"10px"},".video-count-down":{zIndex:1000009,position:"absolute",color:"#fff",textAlign:"right",fontSize:"12px",width:"32px",height:"16px",lineHeight:"16px"},".video-count-down-topleft":{top:"10px",left:"10px"},".video-count-down-topright":{top:"10px",right:"10px"},".video-count-down-bottomleft":{bottom:"10px",left:"10px"},".video-count-down-bottomright":{bottom:"10px",right:"10px"},".video-voice-icon":{width:"100%",height:"100%"},".video-progress":{position:"absolute",zIndex:9,bottom:0,left:0,right:0,height:"3px",backgroundColor:"#f2f2f2"},".video-progress-played":{width:0,height:"3px",backgroundColor:"#07c160"},".video-resume-container":{position:"absolute",top:0,left:0,height:"100%",width:"100%",display:"flex",alignItems:"center",background:"rgba(0,0,0,0.5)",borderRadius:"8px 8px 0px 0px"},".video-resume-btn":{position:"absolute",left:"50%",top:"50%",zIndex:10,MsTransform:"translate(-50%, -50%)",transform:"translate(-50%, -50%)",WebkitTransform:"translate(-50%, -50%)",textAlign:"center",borderRadius:0,MozBorderRadius:0,WebkitBorderRadius:0,color:"#fff",WebkitTapHighlightColor:"rgba(0, 0, 0, 0)",zoom:"0.8"},".video-resume-icon":{display:"inline-block",fontSize:0,width:0,height:0,marginTop:"25px",marginLeft:"25px",marginBottom:"25px",borderWidth:"14px 25px",overflow:"hidden",borderColor:"transparent transparent transparent #fff",borderStyle:" dotted dotted dotted solid"},".hidden":{display:"none"}},this.state={src:"",poster:"",muted:!0,loop:!0,autoplay:!1,countDownNumber:null,isEndPageShowing:!1,isShowResumeBtn:!1,isPosterShow:!0},this.isShowVoice=!0,this.isShowProgress=!0,this.isShowCountDown=!1,this.isShowEndPage=!1,this.voicePlacement=null,this.countDownPlacement=null,this.voiceIconSrc="https://wxa.wxs.qq.com/images/wxapp/voice_icon.png",this.mutedIconSrc="https://wxa.wxs.qq.com/images/wxapp/muted_icon.png",this.currentTime=0,this.videoRef=an.default.createRef(),this.videoDuration=0,this.objectFit="contain",this.playsInline=!1,this.useResumeBtn=!1,this.preload="auto",this.usePredownload=!1,this.useImagePoster=!1,this.toggleMuted=e=>{null==e||e.stopPropagation();let t=!this.state.muted;this.setState({muted:t}),e&&this.emitAdCustomEvent("stopPropagationTap",{event:e}),this.emitComponentEvent("mutedChange",{muted:t}),this.emitAdCustomEvent("adVideoMuteChange",{muted:t})},this.handleAdCustomEvent=e=>{var t;switch(e.action){case"appEnterBackground":{let n=Math.min(this.currentTime,this.videoDuration);"value"in e&&(null==(t=e.value)?void 0:t.inAdDetail)?this.reportVideoPlay("partial","app_exit",n):this.reportVideoPlay("partial","exit",n);break}case"endPageInit":this.isShowEndPage=this.getDataValueWithDefault("isShowEndPage",!1),this.isShowEndPage&&this.setState({loop:!1});break;case"endPageHide":this.isShowEndPage&&(this.setState({isEndPageShowing:!1}),this.operateVideoPlay("play"));break;case"endPageShow":this.isShowEndPage&&this.setState({isEndPageShowing:!0});break;case"btnTap":this.reportVideoPlay(this.currentTime<this.videoDuration?"partial":"complete","land",this.videoDuration);break;case"operateAdVideo":{let{type:t}=e.value;["play","pause","stop"].includes(t)&&this.operateVideoPlay(t);break}case"predownloadMaterialDone":if(e.value.targetid===this.id&&this.usePredownload){for(let t of["src","poster"])e.value.url===this.state[t]&&this.setState({[t]:e.value.cachedUrl});e.value.url===this.state.src?this.setState({src:e.value.cachedUrl}):e.value.url===this.state.poster&&this.setState({poster:e.value.cachedUrl})}break;case"changeVideoMute":{let{muted:t}=e.value;this.setState({muted:t}),this.emitComponentEvent("mutedChange",{muted:t})}}},this.handleError=e=>{this.useResumeBtn&&this.setState({isShowResumeBtn:!0}),this.emitAdCustomEvent("adVideoLoadFailed",e)},this.handleTimeUpdate=e=>{var t,n;let{loop:i}=this.state,a=e.duration||(null==(t=e.currentTarget)?void 0:t.duration),r=e.position||(null==(n=e.currentTarget)?void 0:n.currentTime);if(this.setState({isPosterShow:!1}),null==a||null==r)return;let l=Math.min(1,r/a);this.setStyle("videoProgressPlayed",{width:`${100*l}%`}),this.isShowCountDown&&this.setState({countDownNumber:`${Math.ceil(a-r)}s`}),this.emitComponentEvent("videoTimeUpdate",{event:e,rate:l}),this.emitAdCustomEvent("adVideoTimeUpdate",{duration:a,position:r}),i&&this.currentTime>1e3*r&&(this.reportVideoPlay("complete","end",this.videoDuration),this.reportVideoPlay("partial","start",0)),this.currentTime=1e3*r,this.videoDuration=1e3*a},this.handleEnded=e=>{this.emitComponentEvent("videoEnded",{event:e}),this.emitAdCustomEvent("triggerEvent",{eventName:"videoended",detail:{baseType:this.baseType}}),this.emitAdCustomEvent("adVideoEnded"),this.reportVideoPlay("complete","end",this.videoDuration),this.currentTime=0,this.isShowEndPage&&this.emitAdCustomEvent("showEndPage",{videoId:this.id}),this.setState({countDownNumber:null})},this.handlePlay=e=>{this.emitComponentEvent("videoPlay",{event:e}),this.emitAdCustomEvent("adVideoPlay",{currentTime:this.currentTime,videoDuration:this.videoDuration}),this.reportVideoPlay("partial","start",Math.min(this.currentTime,this.videoDuration))},this.handlePause=e=>{this.emitComponentEvent("videoPause",{event:e}),this.emitAdCustomEvent("adVideoPause",{currentTime:this.currentTime,videoDuration:this.videoDuration}),this.reportVideoPlay("partial","close",Math.min(this.currentTime,this.videoDuration))},this.handleLoadedMetaData=e=>{this.emitAdCustomEvent("adVideoLoadedMetaData"),this.emitComponentEvent("videoLoadedMetaData",{event:e,time:Date.now()})},this.handleResume=e=>{null==e||e.stopPropagation(),this.emitAdCustomEvent("stopPropagationTap",{event:e}),this.emitAdCustomEvent("adVideoResumeBtnClick")},this.baseType=this.options.baseType||2,1===this.baseType?this.initCommonComponent():2===this.baseType&&this.initADComponent(),this.isShowVoice=this.getDataValueWithDefault("isShowVoice",!0),this.voicePlacement=this.getDataValueWithDefault("voicePosition","bottomleft").toLowerCase(),this.state.muted=this.getDataValueWithDefault("muted",!0),this.state.loop=this.getDataValueWithDefault("loop",!0),this.state.autoplay=this.getDataValueWithDefault("autoplay",!1),this.isShowProgress=this.getDataValueWithDefault("isShowProgress",!0),this.isShowCountDown=this.getDataValueWithDefault("isShowCountDown",!1),this.countDownPlacement=this.getDataValueWithDefault("countDownPosition","topright").toLowerCase(),this.objectFit=this.getDataValueWithDefault("objectFit","contain"),this.playsInline=this.getDataValueWithDefault("playsInline",!1),this.useResumeBtn=this.getDataValueWithDefault("useResumeBtn",!1),this.useClickMask=this.getDataValueWithDefault("useClickMask",!1),this.preload=this.getDataValueWithDefault("preload","auto"),this.usePredownload=this.getDataValueWithDefault("usePredownload",!1),this.useImagePoster=this.getDataValueWithDefault("useImagePoster",!1),setTimeout(()=>{this.emitComponentEvent("mutedChange",{muted:this.state.muted})},0)}initCommonComponent(){this.state.src=this.getDataValueWithDefault("src",""),this.state.poster=this.getDataValueWithDefault("poster",""),this.usePredownload&&this.predownloadMaterial(this.state.src),this.usePredownload&&this.predownloadMaterial(this.state.poster)}initADComponent(){let e=this.getAdData();this.state.src=this.getDataValue("src")||nU(e,[{path:["video_info","videoUrl"]},{path:["video_info","video_url"]}]),this.state.poster=this.getDataValue("poster")||nU(e,[{path:["video_info","thumbUrl"]},{path:["video_info","thumb_url"]}]),this.videoDuration=1e3*nU(e,[{path:["wx_game_video","short_video","video_duration"]},{path:["crtInfo","short_video","video_duration"]},{path:["crtInfo","0","short_video","video_duration"]}]);let t=this.getDataValue("src")?void 0:nU(e,[{path:["video_info","video_md5"]},{path:["wx_game_video","short_video","video_md5"]},{path:["crtInfo","short_video","video_md5"]},{path:["crtInfo","0","short_video","video_md5"]}]),n=this.getDataValue("poster")?void 0:nU(e,[{path:["video_info","thumb_md5"]},{path:["wx_game_video","short_video","thumb_md5"]},{path:["crtInfo","short_video","thumb_md5"]},{path:["crtInfo","0","short_video","thumb_md5"]}]);this.addAdCustomListener(this.handleAdCustomEvent),this.usePredownload&&this.predownloadMaterial(this.state.src,t),this.usePredownload&&this.predownloadMaterial(this.state.poster,n),this.emitAdCustomEvent("reportTimePoint",{key:"p8"})}doAction({payload:e}){switch(e.action){case"videoToggleMuted":this.toggleMuted();break;case"videoPlay":this.operateVideoPlay("play");break;case"videoPause":this.operateVideoPlay("pause");break;case"videoStop":this.operateVideoPlay("stop")}}operateVideoPlay(e){let{hostContext:{operateVideo:t}}=this;["play","pause","stop","seek"].includes(e)&&(this.setState({isShowResumeBtn:"pause"===e}),this.videoRef.current&&(null==t||t(this.videoRef.current,e)))}reportVideoPlay(e,t,n=0){2===this.baseType&&this.emitAdCustomEvent("reportVideoPlay",{playType:e,conditionType:t,videoInfo:{playDuration:n,videoDuration:this.videoDuration,isMuted:this.state.muted,isAutoPlay:this.state.autoplay}})}render(){let{isShowVoice:e,mutedIconSrc:t,voiceIconSrc:n,voicePlacement:i,countDownPlacement:a,isShowProgress:r,isShowCountDown:l,objectFit:s,playsInline:o,useResumeBtn:u,useClickMask:d,useImagePoster:c,preload:h}=this,{muted:f,loop:p,autoplay:E,countDownNumber:m,isEndPageShowing:_,isShowResumeBtn:g,src:T,poster:C,isPosterShow:S}=this.state,{renderMask:y,maskContainerRef:A,randomKey:v}=this.props,{renderContext:{videoUseBlock:P}}=this,b=["video-voice-box"],I=["video-count-down"];return b.push(`video-voice-box-${i}`),I.push(`video-count-down-${a}`),an.default.createElement("div",{id:"container",className:"video",ref:A,key:v||""},an.default.createElement("video",{id:"video",className:nI("video-context",P&&"video-block"),ref:this.videoRef,src:T,poster:this.useImagePoster?void 0:C,controls:!1,muted:f,loop:p,autoPlay:E,autoplay:E,autoInsert:!1,playsInline:o,underGameView:!1,showProgress:!1,showPlayBtn:!1,showCenterPlayBtn:!1,showFullscreenBtn:!1,enableProgressGesture:!1,objectFit:s,preload:h,onTimeUpdate:this.handleTimeUpdate,onPlay:this.handlePlay,onPause:this.handlePause,onEnded:this.handleEnded,onError:this.handleError,onLoadedMetadata:this.handleLoadedMetaData},d&&an.default.createElement("div",{className:"video-click-mask",id:"video-click-mask"})),c&&C&&an.default.createElement("img",{id:"video-poster",className:nI("video-poster",!S&&"hidden"),src:C}),u&&g&&an.default.createElement("div",{className:"video-resume-container",id:"video-resume-container"},an.default.createElement("a",{className:"video-resume-btn",id:"video-resume-btn",onClick:this.handleResume},an.default.createElement("i",{id:"video-resume-icon",className:"video-resume-icon"}," "))),!_&&e&&an.default.createElement("div",{id:"videovoicebox",className:b.join(" "),onClick:this.toggleMuted,preventMask:!0},an.default.createElement("img",{src:t,id:"video-muted-img",className:nI("video-voice-icon",!f&&"hidden")}),an.default.createElement("img",{src:n,id:"video-voice-img",className:nI("video-voice-icon",f&&"hidden")})),r&&an.default.createElement("div",{id:"video-progress",className:"video-progress"},an.default.createElement("div",{id:"video-progress-played",className:"video-progress-played"})),!_&&l&&m&&an.default.createElement("text",{id:"video-count-down",className:I.join(" "),value:m}),null==y?void 0:y())}},aa=iQ(ai=tn([iF(),iB()],ai)),ar=tt(tl()),al=class extends iU{constructor(e){super(e),this.style={".feedback-icon":{width:"6px",height:"4px",marginLeft:"2px",position:"relative"},".close-icon":{width:"6px",height:"6px",marginLeft:"2px",position:"relative"},".feedback":{position:"relative",zIndex:1000010,padding:6},".show":{display:""},".hide":{display:"none"},".feedback-hot-click":{display:"none"},".feedback-tag-container":{flexDirection:"row",alignItems:"center",justifyContent:"center",position:"relative",padding:"2px 4px",backgroundColor:"rgba(0, 0, 0, 0.2)",borderRadius:"2px"},".feedback-tag-text":{fontSize:"10px",height:"12px",lineHeight:"12px",color:"#fff"},".feedback-list":{position:"absolute",width:"134px",flexDirection:"column",alignItems:"center",justifyContent:"center",backgroundColor:"#fff",borderWidth:"1px",borderColor:"rgba(0, 0, 0, 0.03)",borderRadius:"4px",zIndex:1000011},".feedback-list-bottomleft":{top:"26px",left:"6px"},".feedback-list-bottomright":{top:"26px",right:"6px"},".feedback-list-topleft":{bottom:"26px",left:"6px"},".feedback-list-topright":{bottom:"26px",right:"6px"},".feedback-btn":{position:"relative",width:"132px",height:"32px",lineHeight:"32px",color:"#566a97",fontSize:"12px",opacity:.9,textAlign:"center",zIndex:1000012},".feedback-title":{color:"#000",zIndex:1000012},".feedback-line":{width:"100px",height:"1px",opacity:.03,backgroundColor:"#000",zIndex:1000012}},this.state={isShowFeedback:!1,isShowFeedbackMenu:!1,position:null},this.feedbackIconSrc="https://wxa.wxs.qq.com/images/wxapp/feedback_icon.png",this.closeIconSrc="https://wxa.wxs.qq.com/images/wxapp/close.png",this.handleCloseClick=e=>{null==e||e.stopPropagation(),this.emitAdCustomEvent("feedbackReport",{secondGradeId:2,secondGradeDesc:"不感兴趣",feedbackReason:6,event:e}),this.emitComponentEvent("tagTap",{event:e}),this.emitAdCustomEvent("hideAd",{animation:this.getDataValueWithDefault("hideWithAnimation",!1)})},this.handleTagClick=e=>{if(2===this.baseType)return this.handleSimpleTagClick(e);if(3===this.baseType)return this.handleSingleTagClick(e);null==e||e.stopPropagation();let{isShowFeedback:t,isShowFeedbackMenu:n}=this.state;t||n?(this.setState({isShowFeedback:!1,isShowFeedbackMenu:!1}),this.emitAdCustomEvent("feedbackHide")):(this.setState({isShowFeedback:!0}),this.updateMenuPosition(),this.updateInnerStyle(),this.emitAdCustomEvent("feedbackReport",{firstGradeId:1,firstGradeDesc:"广告角标",event:e}),this.emitComponentEvent("tagTap",{event:e}))},this.handleDislikeClick=e=>{null==e||e.stopPropagation(),this.setState({isShowFeedback:!1,isShowFeedbackMenu:!0}),this.updateMenuPosition(),this.updateInnerStyle(),this.emitComponentEvent("dislikeTap",{event:e}),this.emitAdCustomEvent("feedbackReport",{firstGradeId:1,firstGradeDesc:"不感兴趣",event:e})},this.handleFeedbackBtnClick=e=>{null==e||e.stopPropagation(),this.setState({isShowFeedback:!1,isShowFeedbackMenu:!1}),this.emitComponentEvent("feedbackBtnTap",{event:e}),this.emitAdCustomEvent("feedbackReport",{firstGradeId:1,firstGradeDesc:"投诉",event:e}),this.emitAdCustomEvent("feedbackBtnTap")},this.handleAdCustomEvent=e=>{"feedbackHide"===e.action&&this.setState({isShowFeedback:!1,isShowFeedbackMenu:!1})},this.checkUseApx(),this.baseType=this.options.baseType||1,this.isShowFeedbackTitle=this.getDataValueWithDefault("isShowFeedbackTitle",!0),this.isShowCloseTag=this.getDataValueWithDefault("isShowCloseTag",!1),this.useStyleHidden=this.getDataValueWithDefault("useStyleHidden",!1),this.tagContainerPreventMask=this.getDataValueWithDefault("tagContainerPreventMask",!0),this.addAdCustomListener(this.handleAdCustomEvent)}updateMenuPosition(){this.setState({position:this.getDataValueWithDefault("menuPosition","bottomleft").toLowerCase()})}handleSimpleTagClick(e){null==e||e.stopPropagation(),this.emitAdCustomEvent("feedbackReport",{secondGradeId:2,secondGradeDesc:"不感兴趣",feedbackReason:6,event:e}),this.getDataValue("preventHide")||this.emitAdCustomEvent("hideAd",{animation:this.getDataValueWithDefault("hideWithAnimation",!1)}),this.emitComponentEvent("tagTap",{event:e})}handleSingleTagClick(e){null==e||e.stopPropagation(),this.emitAdCustomEvent("stopPropagationTap",{event:e}),this.emitAdCustomEvent("feedbackSingleTagTap",{event:e,tagElement:this.getElementById("feedbackTagContainer"),feedbackMenuStyle:this.options.feedbackMenuStyle}),this.emitComponentEvent("tagTap",{event:e})}handleDislikeReasonClick(e,t){null==t||t.stopPropagation(),this.setState({isShowFeedback:!1,isShowFeedbackMenu:!1}),this.emitComponentEvent("dislikeReasonTap",{event:t}),this.emitAdCustomEvent("feedbackReport",{secondGradeId:2,secondGradeDesc:"不感兴趣",feedbackReason:e,event:t});let n=this.getDataValueWithDefault("afterDislike","showTip");"showTip"===n?this.emitAdCustomEvent("showFeedback"):"hide"===n&&this.emitAdCustomEvent("hideAd",{animation:this.getDataValueWithDefault("hideWithAnimation",!1)})}render(){let{isShowFeedback:e,isShowFeedbackMenu:t,position:n}=this.state,{useStyleHidden:i,tagContainerPreventMask:a}=this,{renderMask:r,maskContainerRef:l,randomKey:s}=this.props,o=["feedback-list"],u=["feedback-list"];n&&(o.push(`feedback-list-${n.toLowerCase()}`),u.push(`feedback-list-${n.toLowerCase()}`)),i&&(o.push(e?"show":"hide"),u.push(t?"show":"hide"));let d=this.getDataValue("feedbackIcon")||(2===this.baseType?this.closeIconSrc:this.feedbackIconSrc),c=2===this.baseType?"close-icon":"feedback-icon",h=this.getDataValueWithDefault("feedbackTagText","广告");return ar.default.createElement("div",{id:"container",className:"feedback","bubble-event":"false",ref:l,key:s||""},ar.default.createElement("div",{id:"feedback-tag-container",className:"feedback-tag-container",onClick:this.handleTagClick,preventMask:a},ar.default.createElement("div",{id:"feedback-hot-click",className:"feedback-hot-click"}),ar.default.createElement("text",{id:"feedback-tag",className:"feedback-tag-text",value:h}),ar.default.createElement("img",{id:"feedback-icon",className:c,src:d})),(e||i)&&ar.default.createElement("div",{id:"menu1",key:"menu1",className:o.join(" ")},this.isShowFeedbackTitle&&ar.default.createElement("text",{className:"feedback-btn feedback-title",value:"由赞助商提供的内容"}),this.isShowCloseTag&&ar.default.createElement(ar.default.Fragment,null,ar.default.createElement("div",{className:"feedback-line"}),ar.default.createElement("text",{id:"close-btn",className:"feedback-btn",onClick:this.handleCloseClick,value:"关闭此广告",preventMask:!0})),ar.default.createElement("div",{className:"feedback-line"}),ar.default.createElement("text",{id:"dislike",className:"feedback-btn",onClick:this.handleDislikeClick,value:"不感兴趣",preventMask:!0}),ar.default.createElement("div",{className:"feedback-line"}),ar.default.createElement("text",{id:"feedback-btn",className:"feedback-btn",onClick:this.handleFeedbackBtnClick,value:"投诉",preventMask:!0})),(t||i)&&ar.default.createElement("div",{id:"menu2",key:"menu2",className:u.join(" ")},ar.default.createElement("text",{id:"isdismatch",className:"feedback-btn",onClick:this.handleDislikeReasonClick.bind(this,1),value:"与我无关",preventMask:!0}),ar.default.createElement("div",{className:"feedback-line"}),ar.default.createElement("text",{id:"isduplicate",className:"feedback-btn",onClick:this.handleDislikeReasonClick.bind(this,5),value:"重复收到多次",preventMask:!0}),ar.default.createElement("div",{className:"feedback-line"}),ar.default.createElement("text",{id:"isbadad",className:"feedback-btn",onClick:this.handleDislikeReasonClick.bind(this,4),value:"内容太差",preventMask:!0})),null==r?void 0:r())}},as=iQ(al=tn([iF(),iB()],al)),ao=tt(tl()),au=["SPLITLINE","COUNTDOWN"],ad=class extends iU{constructor(e){super(e),this.style={".countdown":{position:"relative"},".backgroundContainer":{display:"flex",position:"relative",flexDirection:"row",height:"32px",alignItems:"center",paddingLeft:"12px",paddingRight:"12px",background:"rgba(0, 0, 0, 0.15)",borderColor:"rgba(255, 255, 255, 0.25)",borderWidth:"0.5px",borderRadius:"16px"},".countdown-container":{height:"20px",display:"flex",flexDirection:"row",alignItems:"center",justifyContent:"center",position:"relative"},".countdown-text":{fontSize:"14px",height:"20px",lineHeight:"20px",width:"14px",color:"#fff",alignSelf:"center",position:"relative"},".countdown-value":{fontSize:"14px",height:"20px",lineHeight:"20px",width:"11px",color:"#fff",alignSelf:"center",position:"relative"},".split-line":{width:"0.5px",height:"18.5px",marginRight:"9px",position:"relative",backgroundColor:"rgba(255, 255, 255, 0.25)",marginLeft:"50px"}},this.state={countdownValue:"5"},this.children=[],this.useCountdownText=!0,this.useFeedback=!0,this.useCountdownEvent=!0,this.startNumber=5,this.componentOrders=au,this.feedback={type:"feedback",id:"Countdown-Feedback",options:{innerStyles:{container:{style:{position:"absolute",height:"32px",display:"flex",zIndex:1111999,left:"12px",padding:"0px"}},feedbackTag:{style:{fontSize:"14px",width:"28px",height:"20px",lineHeight:"20px"}},feedbackTagContainer:{style:{backgroundColor:"",borderRadius:"0px",padding:"0px",height:"32px",top:"0px",left:"0px",display:"flex"}},feedbackIcon:{style:{marginLeft:"4.5px",width:"9px",height:"6px",marginTop:"4px"}},menu1:{style:{top:"40px",left:"0px"}},menu2:{style:{top:"40px",left:"0px"}}},data:{}}},this.defaultChildren={},this.handleAdCustomEvent=e=>{if("changeCountdown"===e.action&&this.useCountdownEvent){let t=2===e.value.value.toString().length?18:11;this.setStyle("countdownvalue",{width:t}),this.setState({countdownValue:e.value.value})}},this.defaultChildren.feedback=this.feedback,this.children=this.getChildren(),this.updateInnerStyle(),this.addAdCustomListener(this.handleAdCustomEvent),this.startNumber=this.getDataValueWithDefault("startNumber",5),this.state.countdownValue=String(this.startNumber),this.useCountdownText=this.getDataValueWithDefault("useCountdownText",!0),this.useCountdownEvent=this.getDataValueWithDefault("useCountdownEvent",!0),this.useFeedback=this.getDataValueWithDefault("useFeedback",!0),this.componentOrders=this.getDataValueWithDefault("componentOrders",au)}doAction({payload:e,args:t}){if("changeCountdownValue"===e.action){let n=this.getDataValue("text",e.value,{args:t});this.setState({countdownValue:n})}}getChildren(){let e=this.getDataValueWithDefault("childType",1);if(2===e)return this.template.children||[];let t=this.getDataValue("childrenConf");if(t)for(let e of Object.keys(t))this.defaultChildren[e]&&nx(this.defaultChildren[e],t[e]);let n=[];return n=[this.defaultChildren.feedBack],3===e?n.concat(this.template.children||[]):n}render(){let{children:e,state:{countdownValue:t},useCountdownText:n,componentOrders:i,useFeedback:a}=this,{renderMask:r,maskContainerRef:l,randomKey:s}=this.props,o=e=>ao.default.createElement("div",{id:"countdown-container",className:"countdown-container",key:e},ao.default.createElement("text",{className:"countdown-value",id:"countdownvalue",value:t}),n&&ao.default.createElement("text",{className:"countdown-text",value:"秒"})),u=e=>ao.default.createElement("div",{id:"split-line",className:"split-line",key:e}),d=t=>e.map(e=>ao.default.createElement(i$,e9(e8({},this.getCommonSubComponentProps()),{template:e,key:t}))),c=e=>({CHILDREN:d(e),SPLITLINE:u(e),COUNTDOWN:o(e)})[e];return ao.default.createElement("div",{id:"container",className:"countdown",ref:l,key:s||""},a&&ao.default.createElement(i$,e9(e8({},this.getCommonSubComponentProps()),{template:this.feedback})),ao.default.createElement("div",{className:"backgroundContainer"},i.map(e=>c(e))),null==r?void 0:r())}},ac=iQ(ad=tn([iF(),iB()],ad)),ah=tt(tl()),af=class extends iU{constructor(e){super(e),this.style={".end":{position:"absolute",top:0,right:0,bottom:0,left:0,zIndex:1000099,backgroundColor:"rgba(0, 0, 0, 0.8)",flexDirection:"column",justifyContent:"center",alignItems:"center",hidden:!0},".end-refresh":{position:"absolute",left:0,bottom:0,flexDirection:"row",alignItems:"center"},".end-refresh-text":{color:"#D8D8D8",opacity:.5},".refresh-play":{fontSize:"14px",height:"18px",lineHeight:"18px",display:"none",color:"#d8d8d8"}},this.state={isShowEndPage:!1},this.children=[],this.refreshIconSrc="https://wxa.wxs.qq.com/images/wxapp/refresh_icon.png",this.showTime=0,this.backgroundImage="",this.exposured=!1,this.viewableExposured=!1,this.viewableExposureTimer=null,this.useEndDefaultFeedback=!0,this.handleAdCustomListener=e=>ti(this,[e],function*({action:e}){switch(e){case"showEndPage":yield this.setState({isShowEndPage:!0}),this.emitAdCustomEvent("endPageShow"),this.showTime=Date.now(),this.exposured||this.reportEndPageExposure(),this.exposured=!0,this.viewableExposured||(this.viewableExposureTimer=setTimeout(()=>{this.viewableExposured||this.reportEndPageExposure(!0)},1e3)),this.emitComponentEvent("show");break;case"appEnterBackground":this.state.isShowEndPage&&this.reportEndPageDuration();break;case"appEnterForeground":this.state.isShowEndPage&&(this.showTime=Date.now());break;case"adShowHalf":this.state.isShowEndPage&&(this.showTime=Date.now(),this.viewableExposured||(this.viewableExposureTimer=setTimeout(()=>{this.viewableExposured||this.reportEndPageExposure(!0)},1e3)));break;case"adHideHalf":this.state.isShowEndPage&&(this.reportEndPageDuration(),this.viewableExposureTimer&&(clearTimeout(this.viewableExposureTimer),this.viewableExposureTimer=null))}}),this.handleReplay=e=>{null==e||e.stopPropagation(),this.setState({isShowEndPage:!1}),this.emitAdCustomEvent("endPageHide"),this.emitComponentEvent("hide"),this.reportEndPage({act_type:1003}),this.emitComponentEvent("replay")},this.handleContainerClick=()=>{this.reportEndPage({act_type:1002})};let t=this.getAdData();this.cardInfo=nN(t,["crtInfo",0,"card_info"],"")||nN(t,["crtInfo","card_info"],"")||{},this.showFreshIcon=this.getDataValueWithDefault("showFreshIcon",!0);let{end_page_type:n}=this.cardInfo;(n||this.getDataValueWithDefault("forceEnabled",!1))&&(this.backgroundImage=this.cardInfo.head_bg_img,this.refreshIconSrc=this.getDataValueWithDefault("refreshIconSrc","https://wxa.wxs.qq.com/images/wxapp/refresh_icon.png"),this.useEndDefaultFeedback=this.getDataValueWithDefault("useEndDefaultFeedback",!0),this.setStyle("refresh",{padding:"13apx 12apx"}),this.setStyle("refreshIcon",{height:"16apx",width:"16apx",marginRight:"4apx"}),this.setStyle("refreshText",{lineHeight:"18apx",fontSize:"14apx"}),this.children=this.getChildren(),this.bindEvent(),setTimeout(()=>{this.emitAdCustomEvent("endPageInit")},0))}bindEvent(){this.addAdCustomListener(this.handleAdCustomListener)}componentWillUnmount(){super.componentWillUnmount(),this.removeAdCustomListener()}reportEndPageDuration(){this.showTime&&(this.reportEndPage({act_type:1006,exposure_duration:Date.now()-this.showTime}),this.showTime=0)}reportEndPageExposure(e=!1){let t=0;e&&(t=1,this.viewableExposured=!0),this.reportEndPage({act_type:t})}reportEndPage(e){this.emitAdCustomEvent("reportEndPage",{data:e})}doAction({payload:e}){"endPageHide"===e.action&&(this.setState({isShowEndPage:!1}),this.emitAdCustomEvent("endPageHide"),this.emitComponentEvent("hide"))}getChildren(){let e=this.getDataValueWithDefault("childType",1);if(2===e)return this.template.children||[];let t=this.getAdData(),n={pos_type:t.pos_type,costtype:t.is_cpm?1:2,slotid:"4071202390577885",is_game:0},i={},a=this.template.id,{renderContext:{isFlattenLayer:r}}=this;i.feedBack={type:"feedback",id:`${a}_endPageFeedback`,style:{position:"absolute",zIndex:99,top:"2px",left:"2px"},options:{events:{tagTap:[{action:"reportMMData",value:{id:"19002",data:Object.assign({act_type:1004},n)}}],dislikeReasonTap:[{action:"triggerEvent",value:{eventName:"videodislike"}}]}}},i.iconBig={type:"image",id:`${a}_endPageIconBig`,style:{width:"40apx",height:"40apx",marginBottom:"7apx"},options:{baseType:2}},i.nickBig={type:"text",id:`${a}_endPageNickBig`,style:{marginBottom:"6apx"},options:{baseType:2,data:{},innerStyles:{text:{style:{color:"#fff",fontSize:"17apx",height:"24apx",lineHeight:"24apx"}}}}},i.descSmall={type:"text",id:`${a}_endPageDescSmall`,style:{marginBottom:"23apx"},options:{baseType:1,data:{text:this.cardInfo.end_page_desc,isEllipsis:!1},innerStyles:{text:{style:{color:"#fff",opacity:.5,fontSize:"14apx",lineHeight:"20apx"}}}}},i.button={type:"button",id:`${a}_endPageButton`,style:{},options:{innerStyles:{container:{style:{height:"32apx",backgroundColor:"#07C160",borderRadius:"4apx",padding:"0 12.5apx",paddingBottomExpr:'#like($const.systemInfo.osVersion, "Android") ? "1px" : "0"'}},buttonText:{style:{fontSize:"16apx",color:"#fff"}}},baseType:2,events:{buttonTap:[{action:"reportEndPage",value:{data:{act_type:1001}}}]}}},i.iconSmall={type:"image",id:`${a}_endPageIconSmall`,style:{width:"20apx",height:"20apx",marginRight:"8apx"},options:{baseType:2}},i.nickSmall={type:"text",id:`${a}_endPageNickSmall`,style:{},options:{baseType:2,data:{},innerStyles:{text:{style:{color:"#fff",fontSize:"15apx",height:"18apx",lineHeight:"18apx",opacity:".5"}}}}},i.layoutSmall={type:"layout",id:`${a}_endPageLayoutSmall`,style:{marginBottom:"16apx"},options:{baseType:2},children:[]},i.layoutSmall.children.push(i.iconSmall),i.layoutSmall.children.push(i.nickSmall),i.descBig={type:"text",id:`${a}_endPageDescBig`,style:{marginBottom:"32apx"},options:{baseType:1,data:{text:this.cardInfo.end_page_desc,isEllipsis:!1},innerStyles:{text:{style:{color:"#fff",fontSize:"17apx",lineHeight:"22apx"}}}}};let l=this.getDataValue("childrenConf");if(l)for(let e of Object.keys(l))i[e]&&nx(i[e],l[e]);let s=[];if(this.useEndDefaultFeedback&&s.push(i.feedBack),s=2===this.cardInfo.end_page_type?s.concat([i.layoutSmall,i.descBig,i.button]):s.concat([i.iconBig,i.nickBig,i.descSmall,i.button]),3===e)return s.concat(this.template.children||[]);if(r()&&s){let e=t=>{t.forEach(t=>{var n;t.style&&nL(t,"options.innerStyles.container.style",nx({},t.style,nN(t,"options.innerStyles.container.style",{}))),null!=(n=null==t?void 0:t.children)&&n.length&&e(t.children)})};e(s)}return s}render(){let{backgroundImage:e,children:t,refreshIconSrc:n,showFreshIcon:i,state:{isShowEndPage:a}}=this,{renderMask:r,maskContainerRef:l,randomKey:s}=this.props;return ah.default.createElement("div",{id:"container",className:"end",onClick:this.handleContainerClick,ref:l,key:s||"",style:{display:a?"flex":"none"}},e&&ah.default.createElement("img",{id:"backgroundImage",src:e}),t.map(e=>ah.default.createElement(i$,e9(e8({key:e.id},this.getCommonSubComponentProps()),{template:e}))),i&&ah.default.createElement("div",{id:"refresh",className:"end-refresh",onClick:this.handleReplay},ah.default.createElement("img",{id:"refresh-icon",className:"end-refresh-icon",src:n}),ah.default.createElement("div",{id:"refresh-play",className:"refresh-play"},"重播")),null==r?void 0:r())}};af=tn([iF(),iB()],af);var ap=tt(tl()),aE=class extends iU{constructor(e){super(e),this.forType=this.options.baseType||1,this.forArr=this.getDataValueWithDefault("forArr",[]),2===this.forType&&(this.forArr=this.getAdDatas())}render(){let{template:e}=this.props,t=this.forArr.map((t,n)=>e.children.map(i=>ap.default.createElement(i$,e9(e8({},this.getCommonSubComponentProps()),{key:`${i.id}-${n}`,idPrefix:this.idPrefix+(e.id||"")+n.toString(),forType:this.forType,forItem:t,template:i,forIndex:n})))).reduce((e,t)=>[...e,...t],[]);return ap.default.createElement(ap.default.Fragment,null,t)}};aE=tn([iF()],aE);var am=tt(tl()),a_=class extends iU{constructor(e){super(e),this.style={".carousel":{overflow:"hidden",position:"relative"},".carousel-list":{position:"relative",zIndex:1},".carousel-row":{flexDirection:"row",alignItems:"center",justifyContent:"center"},".carousel-col":{flexDirection:"column",alignItems:"center",justifyContent:"center"},".carousel-mask":{position:"absolute",width:"100%",height:"100%",zIndex:2000001,backgroundColor:"rgba(0, 0, 0, 0)"}},this.isViewable=!1,this.isPlaying=!1,this.animated=!1,this.timerSet=new Set,this.handleMaskClick=e=>{null==e||e.stopPropagation();let t=null!=this.moveTime?(Date.now()-this.moveTime)/this.duration*this.size:0,n;n=this.animated?-this.animateCount*this.size+t:-t;let i=Math.floor(((this.isCol?e.pageY:e.pageX)-n)/this.size);this.emitComponentEvent("unitTap",{index:i,event:e})},this.handleAdCustomEvent=({action:e})=>{["adShowHalf"].includes(e)?(this.isViewable=!0,this.autoplay()):["adHideHalf"].includes(e)&&(this.isViewable=!1,this.clearTimers())},this.moveTime=null,this.baseType=this.options.baseType||1,this.showCount=this.getDataValueWithDefault("showCount",0),this.allCount=this.getDataValueWithDefault("allCount",0),this.animateCount=this.allCount-this.showCount,this.size=Number(this.convertApxStyle(this.getDataValueWithDefault("size","0px")).replace("px",""))||0,this.duration=this.getDataValueWithDefault("duration",1500),this.disabled=this.getDataValueWithDefault("disabled",!1),this.allIndexList=Array.from({length:this.allCount}).map((e,t)=>t),this.isCol=2===this.baseType,1===this.baseType?this.initCarousel("width"):2===this.baseType&&this.initCarousel("height")}componentWillUnmount(){super.componentWillUnmount(),this.clearTimers(),this.isViewable=!1}initCarousel(e){this.setStyle("container",{[e]:this.showCount*this.size}),this.setStyle("list",{[e]:this.allCount*this.size}),this.addAdCustomListener(this.handleAdCustomEvent)}clearTimers(){for(let e of this.timerSet)clearTimeout(e)}autoplay(){return ti(this,null,function*(){if(!this.isViewable||this.isPlaying)return;let e=1===this.baseType?"left":"top";if(this.disabled)this.emitComponentEvent("showChange",{list:this.allIndexList.slice(0,this.showCount)||[]}),this.isPlaying=!1;else if(this.animateCount<=0)this.emitComponentEvent("showChange",{list:this.allIndexList||[]}),this.isPlaying=!1;else{this.isPlaying=!0;for(let e=0;e<this.animateCount;e+=1){let{animated:t}=this,n=setTimeout(()=>{this.timerSet.delete(n);let i=t?this.allIndexList.slice(this.allCount-e-this.showCount,this.allCount-e):this.allIndexList.slice(e,e+this.showCount);this.emitComponentEvent("showChange",{list:i})},e*this.duration);this.timerSet.add(n)}this.moveTime=Date.now();try{yield this.doAnimation({id:"list",emitEvent:!1,animation:{keys:[{offset:0,[e]:this.animated?-1*this.animateCount*this.size:0},{offset:1,[e]:this.animated?0:-1*this.animateCount*this.size}],duration:this.duration*this.animateCount,easing:"linear"}})}catch(e){}this.animated=!this.animated,this.isPlaying=!1,this.moveTime=null;let t=setTimeout(()=>{this.timerSet.delete(t),this.autoplay()},this.duration/4);this.timerSet.add(t)}})}render(){let{template:{children:e}}=this.props,{renderMask:t,maskContainerRef:n,randomKey:i}=this.props,{baseType:a,allCount:r,showCount:l}=this;return e&&e.length&&r&&l?am.default.createElement("div",{id:"container",className:"carousel",ref:n,key:i||""},am.default.createElement("div",{id:"mask",className:"carousel-mask",onClick:this.handleMaskClick}),am.default.createElement("div",{id:"list",className:["carousel-list",1===a?"carousel-row":"carousel-col"].join(" ")},e.map(e=>am.default.createElement(i$,e9(e8({key:e.id},this.getCommonSubComponentProps()),{template:e})))),null==t?void 0:t()):am.default.createElement("div",{id:"blank"})}},ag=iQ(a_=tn([iF(),iB()],a_)),aT=tt(tl()),aC=()=>aT.default.createElement("div",null),aS=tt(tl()),ay="none",aA="onlive",av="end",aP="reserve",ab=class extends iU{constructor(e){super(e),this.style={".livetag":{position:"relative",zIndex:1000010},".livetag-hidden":{display:"none"},".ad-channels-live":{display:"flex",backgroundColor:"#FF6146",flexDirection:"row",alignItems:"center",justifyContent:"flex-start",borderRadius:"9px",padding:"2px 6px 2px 5px"},".ad-channels-live-icon":{width:"10px",height:"10px",marginRight:"3px"},".ad-channels-live-text":{height:"14px",color:"#ffffff",fontSize:"10px",lineHeight:"15px"},".ad-channels-end":{display:"flex",backgroundColor:"#959FC1",flexDirection:"row",alignItems:"center",borderRadius:"9px",padding:"2px 6px 2px 5px"},".ad-channels-reserve":{display:"flex",backgroundColor:"#FF6146",flexDirection:"row",alignItems:"center",borderRadius:"9px",padding:"2px 6px 2px 6px"}},this.state={status:ay},this.handleAdCustomEvent=e=>{"updateChannelStatus"===e.action&&this.setState({status:e.value.status},()=>{this.updateInnerStyle()})},this.addAdCustomListener(this.handleAdCustomEvent)}render(){let{renderMask:e,maskContainerRef:t,randomKey:n}=this.props,{status:i}=this.state;return i===ay&&["livetag"].push("livetag-hidden"),aS.default.createElement("div",{id:"container",className:"livetag",ref:t,key:n||""},i===aA&&aS.default.createElement("div",{id:"ad-channels-live",className:"ad-channels-live"},aS.default.createElement("img",{id:"ad-channels-live-icon",className:"ad-channels-live-icon",src:"https://wxa.wxs.qq.com/images/wxapp/live_icon_1.png"}),aS.default.createElement("text",{id:"ad-channels-live-text",className:"ad-channels-live-text",value:"直播中"})),i===av&&aS.default.createElement("div",{id:"ad-channels-end",className:"ad-channels-end"},aS.default.createElement("img",{id:"ad-channels-live-icon",className:"ad-channels-live-icon",src:"https://wxa.wxs.qq.com/images/wxapp/live_icon_1.png"}),aS.default.createElement("text",{id:"ad-channels-live-text",className:"ad-channels-live-text",value:"直播结束"})),i===aP&&aS.default.createElement("div",{id:"ad-channels-reserve",className:"ad-channels-reserve"},aS.default.createElement("text",{id:"ad-channels-live-text",className:"ad-channels-live-text",value:"直播预约"})),null==e?void 0:e())}},aI=iQ(ab=tn([iF(),iB()],ab)),aO=class extends iU{constructor(e){super(e),this.style={".popup-blank":{display:"none"}},this.hidePopupFn=null,this.handleAdCustomEvent=e=>{let t=this.options.data;if("showPopup"===e.action&&e.value.popupName===t.popupName?this.showPopup():"hidePopup"===e.action&&e.value.popupName===t.popupName&&this.hidePopup(),"orientationChange"===e.action){let{hideOnOrientationChange:e}=this.getPopupData();e&&this.hidePopup()}},this.addAdCustomListener(this.handleAdCustomEvent)}componentWillUnmount(){super.componentWillUnmount(),this.hidePopup()}doAction(e){"showPopup"===e.payload.action?this.showPopup():"hidePopup"===e.payload.action&&this.hidePopup()}getPopupData(){return{hideOnOrientationChange:this.getDataValueWithDefault("hideOnOrientationChange",!1),mask:this.getDataValue("mask"),maskColor:this.getDataValue("maskColor"),maskOpacity:this.getDataValue("maskOpacity"),popupName:this.getDataValue("popupName"),popupWidth:this.getDataValue("popupWidth"),popupHeight:this.getDataValue("popupHeight"),popupLeft:this.getDataValue("popupLeft"),popupTop:this.getDataValue("popupTop")}}showPopup(){return ti(this,null,function*(){let{hostContext:e,template:t,systemInfo:n,adData:i,adDatas:a,renderContext:r,adCustomId:l,extraScope:s,darkMode:o,isMulti:u,emitter:d}=this.props;if(!e.showPopup)return;let c=e9(e8({},this.getPopupData()),{template:t.children[0]}),h=yield e.showPopup({popupOptions:c,renderOptions:{systemInfo:n,adData:i,adDatas:a,darkMode:o,adCustomId:l,isMulti:u,styleOptions:r.styleOptions,flattenLayer:r.isFlattenLayer(),extraScope:s},hostContext:{hidePopupInner:()=>this.hidePopup()},renderContext:r,emitter:d});this.hidePopupFn=h.hidePopup})}hidePopup(){return ti(this,null,function*(){this.hidePopupFn&&(yield this.hidePopupFn(),this.hidePopupFn=null)})}render(){return null}},aR=aO=tn([iF()],aO),aD=tt(tl()),ak={1:"与我无关",5:"重复收到多次",4:"内容太差"},ax=class extends iU{constructor(e){super(e),this.style={".feedback-container":{display:"block",width:"100%",height:"100%"},".feedback-content-warp":{display:"block",width:"100%",paddingTop:5},".feedback":{display:"flex",width:"300px",flexDirection:"column",justifyContent:"flex-start",alignItems:"flex-start",paddingTop:"4px",backgroundColor:"#fff",borderRadius:"8px"},".feedback-spot":{display:"block",position:"absolute",left:18,top:0,width:14,height:7,backgroundImage:"url(resources/angle.png)",backgroundSize:"100% 100%",backgroundPosition:"0 0"},".submit-button":{display:"flex",position:"absolute",top:0,right:16,height:32,backgroundColor:"#07C160",boxSizing:"border-box",borderRadius:3,alignItems:"center",paddingTop:6,paddingBottom:6,paddingLeft:12,paddingRight:12},".submit-button-text":{display:"block",color:"#fff",height:20,lineHeight:20,fontSize:14,fontWeight:600},".disable-btn":{display:"block",backgroundColor:"#f2f2f2"},".disable-btn-text":{display:"block",color:"#c6c6c6"},".feedback-content":{display:"flex",flexDirection:"column",justifyContent:"flex-start",alignItems:"flex-start",paddingLeft:16,paddingRight:16,paddingBottom:12,marginTop:12},".feedback-split-line":{height:1,backgroundColor:"rgba(0, 0, 0, 0.05)",marginLeft:16,marginRight:16,alignSelf:"stretch"},".feedback-bottom":{display:"flex",flexDirection:"row",alignSelf:"stretch",height:52,boxSizing:"border-box",paddingTop:16,paddingBottom:16,paddingLeft:16,paddingRight:16,alignItems:"center",justifyContent:"space-between"},".feedback-bottom:active":{display:"block",backgroundColor:"rgba(0, 0, 0, 0.05)",borderLeftBottomRadius:8,borderRightBottomRadius:8},".feedback-bottom-text":{display:"block",color:"rgba(0, 0, 0, 0.5)",fontSize:14,height:20,lineHeight:20},".feedback-bottom-entry-icon":{display:"block",width:5,height:10,backgroundImage:"url(resources/arrow_right.png)",backgroundSize:"100% 100%",backgroundPosition:"0 0",backgroundRepeat:"no-repeat"},".sponsor-text":{display:"block",color:"rgba(0, 0, 0, 0.5)",fontSize:14,height:20},".question-text":{display:"block",color:"rgba(0, 0, 0, 0.9)",fontSize:14,marginTop:8,height:20,lineHeight:20},".feedback-reason":{display:"flex",flexDirection:"row",marginTop:16},".feedback-option":{display:"flex",flexDirection:"row",justifyContent:"center",alignItems:"center",paddingLeft:12,paddingRight:12,paddingTop:8,paddingBottom:8,marginRight:8,backgroundColor:"rgba(0, 0, 0, 0.05)",borderRadius:4},".feedback-option:active":{display:"block",backgroundColor:"rgba(0, 0, 0, 0.1)"},".feedback-option-icon":{display:"block",width:15,height:15,marginRight:4,flex:"0 0 auto",backgroundSize:"100% 100%",backgroundPosition:"0 0"},".feedback-option-icon-like":{display:"block",backgroundImage:"url(resources/like.png)"},".feedback-option-icon-dontlike":{display:"block",backgroundImage:"url(resources/dontlike.png)"},".feedback-option-text":{display:"block",fontSize:14,height:20,lineHeight:20},".feedback-option-text:active":{},".no-interest-reason":{display:"flex",flexDirection:"row",justifyContent:"flex-start",alignItems:"center",marginTop:4,flexWrap:"wrap"},".no-interest-option":{display:"flex",flexDirection:"row",justifyContent:"flex-start",alignItems:"center",paddingLeft:12,paddingRight:12,paddingTop:8,paddingBottom:8,marginRight:8,marginTop:12,backgroundColor:"rgba(0, 0, 0, 0.05)",borderRadius:4},".no-interest-option:active":{display:"block",backgroundColor:"rgba(0, 0, 0, 0.1)"},".chosen-option":{backgroundColor:"rgba(7, 193, 96, 0.1)"},".chosen-option-text":{color:"#07C160"},".no-interest-option-icon":{display:"block",width:15,height:15,marginRight:4,flex:"0 0 auto",backgroundSize:"100% 100%",backgroundPosition:"0 0"},".no-interest-option-text":{display:"block",fontSize:14,height:20,lineHeight:20},".hidden":{display:"none"}},this.darkModeStyle={".feedback":{backgroundColor:"#232323"},".feedback-spot":{backgroundImage:"url(resources/angle_dark.png)"},".feedback-split-line":{backgroundColor:"rgba(255, 255, 255, 0.05)"},".feedback-bottom:active":{backgroundColor:"rgba(255, 255, 255, 0.05)"},".feedback-bottom-text":{color:"rgba(255, 255, 255, 0.6)"},".feedback-bottom-entry-icon":{backgroundImage:"url(resources/arrow_right_dark.png)"},".sponsor-text":{color:"rgba(255, 255, 255, 0.5)"},".question-text":{color:"rgba(255, 255, 255, 0.8)"},".feedback-option":{backgroundColor:"rgba(255, 255, 255, 0.05)"},".feedback-option:active":{backgroundColor:"rgba(255, 255, 255, 0.1)"},".feedback-option-icon-like":{backgroundImage:"url(resources/like_dark.png)"},".feedback-option-icon-dontlike":{backgroundImage:"url(resources/dontlike_dark.png)"},".feedback-option-text":{color:"rgba(255, 255, 255, 0.8)"},".disable-btn":{backgroundColor:"rgba(255, 255, 255, 0.05)"},".disable-btn-text":{color:"rgba(255, 255, 255, 0.3)"},".no-interest-option":{backgroundColor:"rgba(255, 255, 255, 0.05)"},".no-interest-option:active":{backgroundColor:"rgba(255, 255, 255, 0.1)"},".no-interest-option-text":{color:"rgba(255, 255, 255, 0.8)"},".chosen-option":{backgroundColor:"rgba(7, 193, 96, 0.1)"},".chosen-option-text":{color:"#07C160"}},this.state={chosenNoInterestOptions:[],isShowFeedbackMenu:!1,dislikeReason:null},this.containerRef=aD.default.createRef(),this.dislikeOrders=[1,5,4],this.handleFavClick=e=>{var t;null==(t=e.stopPropagation)||t.call(e),this.closePopup(),this.emitAdCustomEvent("feedbackReport",{firstGradeId:1,firstGradeDesc:"还不错",event:e}),this.emitAdCustomEvent("feedbackFavTap")},this.handleDislikeClick=e=>{var t;null==(t=e.stopPropagation)||t.call(e),this.setState({isShowFeedbackMenu:!0}),this.emitAdCustomEvent("feedbackReport",{firstGradeId:1,firstGradeDesc:"不感兴趣",event:e})},this.handleFeedbackBtnClick=e=>{var t;null==(t=e.stopPropagation)||t.call(e),this.setState({isShowFeedbackMenu:!1}),this.emitComponentEvent("feedbackBtnTap",{event:e}),this.emitAdCustomEvent("feedbackReport",{firstGradeId:1,firstGradeDesc:"投诉",event:e}),this.closePopup().then(()=>{this.emitAdCustomEvent("feedbackBtnTap")})},this.handleFeedbackContainerClick=e=>{e.target===this.containerRef.current&&this.closePopup()},this.handleSubmit=e=>{this.state.dislikeReason&&(this.emitComponentEvent("dislikeReasonTap",{event:e}),this.emitAdCustomEvent("feedbackReport",{secondGradeId:2,secondGradeDesc:"不感兴趣",feedbackReason:this.state.dislikeReason,event:e}),this.closePopup())},this.baseType=this.options.baseType||1,this.avoidAdText=this.getDataValueWithDefault("avoidAdText",!1)}closePopup(){return ti(this,null,function*(){var e,t;yield null==(t=(e=this.props.hostContext).hidePopupInner)?void 0:t.call(e)})}handleDislikeReasonClick(e,t){var n;null==(n=t.stopPropagation)||n.call(t),this.setState({dislikeReason:e})}render(){let{avoidAdText:e,dislikeOrders:t}=this,{isShowFeedbackMenu:n,dislikeReason:i}=this.state,a=e=>nI("no-interest-option",e===i&&"chosen-option"),r=e=>nI("no-interest-option-text",e===i&&"chosen-option-text");return aD.default.createElement("div",{id:"container",className:"feedback-container",ref:this.containerRef,onClick:this.handleFeedbackContainerClick},aD.default.createElement("div",{id:"contentWarp",className:"feedback-content-warp"},aD.default.createElement("div",{id:"feedbackSpot",className:"feedback-spot"}),aD.default.createElement("div",{id:"feedback",className:"feedback"},aD.default.createElement("div",{id:"feedbackContent",className:"feedback-content"},aD.default.createElement("div",{id:"submitButton",className:nI("submit-button",!i&&"disable-btn",!n&&"hidden")},aD.default.createElement("text",{id:"submitText",className:nI("submit-button-text",!i&&"disable-btn-text"),value:"确定",onClick:this.handleSubmit})),aD.default.createElement("text",{id:"sponsorText",className:"sponsor-text",value:"由赞助商提供的内容"}),aD.default.createElement("text",{id:"questionText",className:"question-text",value:e?"你觉得这条内容":"你觉得这条广告怎么样"}),aD.default.createElement("div",{id:"feedbackReason",className:nI("feedback-reason",n&&"hidden")},aD.default.createElement("div",{id:"feedbackFav",className:"feedback-option",onClick:this.handleFavClick},aD.default.createElement("div",{id:"feedbackFavIcon",className:"feedback-option-icon feedback-option-icon-like"}),aD.default.createElement("text",{id:"feedbackFavText",className:"feedback-option-text",value:"还不错"})),aD.default.createElement("div",{id:"feedbackDislike",className:"feedback-option",onClick:this.handleDislikeClick},aD.default.createElement("div",{id:"feedbackDislikeIcon",className:"feedback-option-icon feedback-option-icon-dontlike"}),aD.default.createElement("text",{id:"feedbackDislikeText",className:"feedback-option-text",value:"不感兴趣"}))),aD.default.createElement("div",{className:nI("no-interest-reason",!n&&"hidden")},t.map(e=>aD.default.createElement("div",{key:e,className:a(e),onClick:this.handleDislikeReasonClick.bind(this,e)},aD.default.createElement("text",{className:r(e),value:ak[e]}))))),aD.default.createElement("div",{className:"feedback-split-line"}),aD.default.createElement("div",{className:"feedback-bottom",onClick:this.handleFeedbackBtnClick},aD.default.createElement("text",{className:"feedback-bottom-text",value:e?"投诉":"投诉广告"}),aD.default.createElement("div",{className:"feedback-bottom-entry-icon"})))))}},aN=ax=tn([iF(),iB()],ax),aL=tt(tl());tt(tl());var aM=((eY=aM||{}).opacity="opacity",eY.left="left",eY.top="top",eY),aw=((eB=aw||{}).rotate="rotate",eB.scaleX="scaleX",eB.scaleY="scaleY",eB),aU=class extends iU{constructor(e){super(e),this.style={".mult-image":{display:"flex",flexDirection:"column",justifyContent:"center",overflow:"hidden"},".img-row-container":{width:"100%",display:"flex",flexDirection:"row",justifyContent:"center",overflow:"hidden",flex:"1 1 auto",alignItems:"stretch"},".img-container":{display:"flex",flex:"1",overflow:"hidden"},".img-item":{width:"100%",height:"100%",borderRadius:"4px"},".img-container-column-space-right":{marginRight:"2px"},".img-container-column-space-left":{marginLeft:"2px"},".img-container-row-space-bottom":{marginBottom:"2px"},".img-container-row-space-top":{marginTop:"2px"}},this.state={imgUrlArr:[],imgUrlPlacement:[],row:0,column:0},this.retry=[],this.exposureLoadRetry=[],this.loadError=[],this.initADComponent=()=>{let e=this.getAdData(),t=[];Array.isArray(e.crtInfo)&&e.crtInfo.length>1?t=e.crtInfo:Array.isArray(e.crtInfos)&&e.crtInfos.length>1&&(t=e.crtInfos),t=null==t?void 0:t.map(e=>e.image_url),this.setImgUrlPlacement(t)},this.initCommonComponent=()=>{let e=this.getDataValueWithDefault("imgUrlArr",[]);this.setImgUrlPlacement(e)},this.setImgUrlPlacement=e=>{this.state.imgUrlArr=e,this.state.imgUrlPlacement=this.getImgUrlPlacement(e)},this.getImgUrlPlacement=e=>{let{row:t,column:n}=this.state,i=[];for(let t=0;t<e.length;t++){let a=Math.floor(t/n),r=e[t];i[a]?i[a].push(r):i[a]=[r]}return i.splice(t,i.length-t),i},this.handleAdCustomEvent=e=>{if("adShowEdge"===e.action&&-1!==this.loadError.indexOf(!0)){let{imgUrlArr:e}=this.state,t=Number(this.getDataValueWithDefault("attachRetryTimes",5));this.loadError.forEach((n,i)=>{var a;if((a=this.exposureLoadRetry)[i]||(a[i]=0),n&&this.exposureLoadRetry[i]<t){this.exposureLoadRetry[i]+=1;let t=i6(e[i],{exposureLoadRetry:this.exposureLoadRetry[i]});e.splice(i,1,t)}}),this.setState({imgUrlArr:e,imgUrlPlacement:this.getImgUrlPlacement(e)})}},this.handleImgLoad=e=>()=>{this.loadError[e]=!1,this.emitComponentEvent("imgLoad",{index:e}),this.emitAdCustomEvent("multImgLoad",{index:e})},this.handleImgError=e=>()=>{var t;this.loadError[e]=!0;let n=Number(this.getDataValue("retryTimes"));if(n){if((t=this.retry)[e]||(t[e]=0),this.retry[e]<=n){let{imgUrlArr:t}=this.state,n=t[e];this.retry[e]+=1;let i=i6(n,{retry:this.retry[e]});t.splice(e,1,i),this.setState({imgUrlArr:t,imgUrlPlacement:this.getImgUrlPlacement(t)})}else this.emitComponentEvent("imgError",{index:e})}};let t=this.options.baseType||2,n=this.getDataValueWithDefault("row",0),i=this.getDataValueWithDefault("column",0);switch(this.baseType=t,this.state.row=+n,this.state.column=+i,t){case 1:this.initCommonComponent();break;case 2:this.initADComponent()}this.addAdCustomListener(this.handleAdCustomEvent)}doAction({payload:e}){if("imageSetSrc"===e.action){let t=this.getDataValue("imgUrlArr",e.value);this.setState({imgUrlArr:t,imgUrlPlacement:this.getImgUrlPlacement(t)})}}render(){let{renderMask:e,randomKey:t,maskContainerRef:n}=this.props,i=2===this.baseType,{row:a,column:r,imgUrlPlacement:l}=this.state;return aL.default.createElement("div",{id:"container",className:"mult-image",ref:n,key:t||""},l.map((e,t)=>aL.default.createElement("div",{className:nI("img-row-container",`img-row-container-${t}`,t<a-1&&"img-container-row-space-bottom",t>0&&"img-container-row-space-top"),key:`row_${t}`},null==e?void 0:e.map((e,n)=>aL.default.createElement("div",{className:nI("img-container",n<r-1&&"img-container-column-space-right",n>0&&"img-container-column-space-left"),key:`column_${n}`},this.loadError[t*r+n]?aL.default.createElement("div",{className:"img-item img-item-placeholder"}):aL.default.createElement("img",e9(e8({},i?{"bubble-event":!1}:{}),{className:"img-item",src:e,onLoad:this.handleImgLoad(t*a+n),onError:this.handleImgError(t*a+n)})))))),null==e?void 0:e())}},aW=iQ(aU=tn([iF(),iB()],aU)),aV=tt(tl()),aG=["https://wxa.wxs.qq.com/images/factory/star_full_lm.png","https://wxa.wxs.qq.com/images/factory/star_half_lm.png","https://wxa.wxs.qq.com/images/factory/star_empty_lm.png"],aF=["https://wxa.wxs.qq.com/images/factory/star_full_dm.png","https://wxa.wxs.qq.com/images/factory/star_half_dm.png","https://wxa.wxs.qq.com/images/factory/star_empty_dm.png"],aH=["TEXT","STARS","SCORE"],aY=class extends iU{constructor(e){super(e),this.isNormal=!1,this.textStyle={fontWeight:600,fontSize:"14px",lineHeight:"24px",height:"24px"},this.style={".score":{display:"flex",flexDirection:"row",alignItems:"center",justifyContent:"flex-start"},".score-title":e8({color:"#10AEFF"},this.textStyle),".score-img-wrapper":{marginLeft:"12px",display:"flex",flexDirection:"row",alignItems:"center",height:this.textStyle.height},".score-img":{height:"13px",width:"13px",marginLeft:"1px"},".score-num":e8({marginLeft:"8px",color:"#10AEFF"},this.textStyle),".split-line":{width:"1px",height:"10px",background:"rgba(0, 0, 0, 0.1)"},".split-line-left":{marginRight:"8px"},".split-line-right":{marginLeft:"8px"}},this.state={buttonText:""},this.useScoreText=!0,this.componentOrders=aH,this.starIcons=aG,this.starIconsDark=aF,this.useLeftSplitLine=!1,this.useRightSplitLine=!1,this.getScoreInfo=()=>{var e;let t=this.getAdData();return[19,12].includes(t.product_type)&&(null==(e=null==t?void 0:t.app_info)?void 0:e.app_rating)&&!isNaN(t.app_info.app_rating)&&t.app_info.app_rating>=3?Number(t.app_info.app_rating.toFixed(1)):0},this.scoreInfo=()=>{let e=this.getAdData(),[t,n,i]=this.props.darkMode?this.starIconsDark:this.starIcons,a=this.getScoreInfo.call(this),r=`${a}`,l=e.product_type;if(a){let e,s=[,,,,,].fill(null).map(()=>t);return 19===l?e="App Store评分":12===l&&(e="应用宝评分"),3===a?(s[3]=i,s[4]=i):a<=3.5?(s[3]=n,s[4]=i):a<=4?s[4]=i:a<=4.5&&(s[4]=n),/\./.test(r)||(r=`${a}.0`),{scoreTitle:e,scoreImage:s,score:`${r}${this.useScoreText?"分":""}`}}return null},this.useScoreText=!!this.getDataValueWithDefault("useScoreText",!0),this.componentOrders=this.getDataValueWithDefault("componentOrders",aH),this.starIcons=this.getDataValueWithDefault("starIcons",aG),this.starIconsDark=this.getDataValueWithDefault("starIconsDark",aF)||this.starIcons,this.useLeftSplitLine=this.getDataValueWithDefault("useLeftSplitLine",!1),this.useRightSplitLine=this.getDataValueWithDefault("useRightSplitLine",!1)}render(){let e=this.scoreInfo();if(!e)return null;let{scoreTitle:t,scoreImage:n,score:i}=e,{renderMask:a,maskContainerRef:r,randomKey:l}=this.props,{componentOrders:s,useLeftSplitLine:o,useRightSplitLine:u}=this,d=e=>aV.default.createElement("text",{key:e,id:"scoreTitle",className:"score-title score-text",value:t}),c=e=>aV.default.createElement("text",{key:e,id:"scoreNum",className:"score-num score-text",value:i}),h=e=>aV.default.createElement("div",{id:"scoreImgWrapper",className:"score-img-wrapper",key:e},n.map((e,t)=>aV.default.createElement("img",{key:t,id:`scoreImg${t+1}`,className:"score-img",src:e}))),f=e=>({TEXT:d(e),SCORE:c(e),STARS:h(e)})[e];return aV.default.createElement("div",{id:"container",className:"score",ref:r,key:l||""},o&&aV.default.createElement("div",{className:"split-line split-line-left"}),s.map(e=>f(e)),u&&aV.default.createElement("div",{className:"split-line split-line-right"}),null==a?void 0:a())}},aB=iQ(aY=tn([iF(),iB()],aY)),az=tt(tl()),aj="https://wxa.wxs.qq.com/images/factory/rankIcon.png",aZ=class extends iU{constructor(e){super(e),this.style={".ranking":{display:"flex",flexDirection:"row",alignItems:"center",justifyContent:"flex-start"},".ranking-img":{width:"16px",height:"16px"},".ranking-info":{marginLeft:"8px",color:"#FA9D3B",fontWeight:600,fontSize:"12px",lineHeight:"16px",height:"16px"},".split-line":{width:"1px",height:"10px",background:"rgba(0, 0, 0, 0.1)"},".split-line-left":{marginRight:"8px"},".split-line-right":{marginLeft:"8px"}},this.state={buttonText:""},this.iconUrl=aj,this.useIcon=!1,this.useLeftSplitLine=!1,this.useRightSplitLine=!1,this.useRankSpace=!1,this.getRankingType=()=>{var e;let t=this.getAdData();switch(null==(e=null==t?void 0:t.extBackComm)?void 0:e.wegame_ranking_type){case 2:return"人气榜";case 3:return"畅销榜";case 4:return"新游榜"}return""},this.getRankingInfo=()=>{var e;let t=this.getAdData(),n=null==(e=null==t?void 0:t.extBackComm)?void 0:e.wegame_ranking_no;if(!isNaN(n)){let e=+n+1;return e<=5?e:e<=30?e-e%5+5:50}return 0},this.getValue=()=>{let e=this.getRankingType.call(this);if(e){let t=this.getRankingInfo.call(this);return`\u5C0F\u6E38\u620F${e} TOP${this.useRankSpace?" ":""}${t}`}return null},this.iconUrl=this.getDataValueWithDefault("iconUrl",aj),this.useIcon=this.getDataValueWithDefault("useIcon",!0),this.useLeftSplitLine=this.getDataValueWithDefault("useLeftSplitLine",!1),this.useRightSplitLine=this.getDataValueWithDefault("useRightSplitLine",!1),this.useRankSpace=this.getDataValueWithDefault("useRankSpace",!1)}render(){let{renderMask:e,maskContainerRef:t,randomKey:n}=this.props,i=this.getValue();if(!i)return null;let{useIcon:a,iconUrl:r,useLeftSplitLine:l,useRightSplitLine:s}=this;return az.default.createElement("div",{id:"container",className:"ranking",ref:t,key:n||""},l&&az.default.createElement("div",{className:"split-line split-line-left"}),a&&az.default.createElement("img",{id:"rankingImg",className:"ranking-img",src:r}),az.default.createElement("text",{id:"rankingInfo",className:"ranking-info",value:i}),s&&az.default.createElement("div",{className:"split-line split-line-right"}),null==e?void 0:e())}},a$=iQ(aZ=tn([iF(),iB()],aZ)),aK=tt(tl()),aQ=class extends iU{constructor(e){super(e),this.state={children:[]},this.tag="",this.tagOptions={};let{tag:t,options:n}=this.getComputedData();this.tag=t,this.tagOptions=n,this.state.children=this.template.children||[]}getComputedData(){let e=this.getDataValue("hostStyles"),t={};if(e)for(let n of Object.keys(e)){let i=e[n];if(t[n]={},i.expr&&!this.run(i.expr))continue;let a=e8({},i.style||{});for(let e of(this.props.darkMode&&Object.assign(a,i.darkModeStyle||{}),Object.keys(a))){let i=e,r=a[e];if(e.includes("Expr")){let t=e.split("Expr");2===t.length&&([i]=t,r=this.run(r))}t[n][i]=r}}let n=this.getDataValue("tagConf"),i={};if(n)for(let e of Object.keys(n)){let t=e,a=n[e];if(e.includes("Expr")){let n=e.split("Expr");2===n.length&&([t]=n,a=this.run(a))}i[t]=a}return{tag:this.getDataValueWithDefault("tag",null),options:{computedStyles:t,computedConf:i,computedClassName:this.getDataValueWithDefault("tagClassName",null)}}}componentDidMount(){super.componentDidMount()}render(){let{children:e}=this.state,{randomKey:t,renderMask:n,maskContainerRef:i}=this.props;return aK.default.createElement("div",{id:"container",className:"hosttag",key:t||"",ref:i},aK.default.createElement("hostTag",{tagName:this.tag,tagOptions:this.tagOptions},e.map(e=>aK.default.createElement(i$,e9(e8({key:e.id},this.getCommonSubComponentProps()),{template:e})))),null==n?void 0:n())}},aX=iQ(aQ=tn([iF(),iB()],aQ)),aq=class{constructor(e){var t,n,i,a,r;this.adCustomIdInc=0,this.containerMap=new WeakMap,"renderer"in e?(nw(nM(e.renderer,["render","unmount","findDOMNode"]),"options.renderer achieved uncompletely"),this.renderer=e.renderer):this.renderer=new tp(e.hostConfig),this.hostContext=e.hostContext,this.useTextTag=null==(t=e.useTextTag)||t,this.useAttrId=null==(n=e.useAttrId)||n,this.useAttrClass=null==(i=e.useAttrClass)||i,this.hideMaskOnSlide=null!=(a=e.hideMaskOnSlide)&&a,this.minSlideDistance=null!=(r=e.minSlideDistance)?r:1,this.globalEventDelegator=new nY({onGlobalEvent:e.hostContext.onGlobalEvent,offGlobalEvent:e.hostContext.offGlobalEvent,getPropertyOfElement:e.hostContext.getPropertyOfElement,getDatasetPrefix:e.hostContext.getDatasetPrefix})}generateCustomId(){return this.adCustomIdInc+=1,`AD_CUSTOM_${this.adCustomIdInc}`}getConvertRatio(e,t=0,n){let{width:i}=n||{};null==i&&(i=t),i=Number(i);let a=e.minWidth||0,r=e.maxWidth||0,l=e.baseWidth||375,s=Math.min(t,Math.max(a,i));return r&&(s=Math.min(s,r)),0===s?{ratio:1,width:null}:{ratio:s/l,width:s}}createTemplateRef(e,{emitter:t,adCustomId:n,rootRef:i,itemRef:a}){let{hostContainer:r}=e,l=t=>!e.isPartial;return{emitEvent:(e,i)=>{t.emit("adCustomEvent",e8({adCustomId:n,action:e},i?{value:i}:{}))},onEvent:e=>(t.on("adCustomEvent",e),e),offEvent:e=>{t.off("adCustomEvent",e)},getCustomId:()=>n,getElementById:(e,t)=>i.current&&i.current.travelAndFindElementByIdDeeply(e,t,this.renderer.findDOMNode(i.current))||null,getRelativeTemplateInfo:(e,t)=>{if(!e||!t||!this.hostContext.getPropertyOfElement)return null;let n=e;for(;n;){let e=nF(n,this.hostContext.getDatasetPrefix);if(t(e))return e;n=this.hostContext.getPropertyOfElement(n,"parent")}return null},update:e=>ti(this,null,function*(){let t=nR(e,["adData","adDatas","position","systemInfo","isMulti","darkMode","extraScope"]);a.current&&e.isPartial?yield this.renderer.render(th.default.createElement(i$,e8({},e8(e8(e9(e8({},a.current.props),{ref:a}),t),e.template?{template:e.template}:{}))),r):i.current&&!e.isPartial&&(yield this.renderer.render(th.default.createElement(iq,e8({},e8(e8(e9(e8({},i.current.props),{ref:i}),t),e.template?{template:e.template}:{}))),r))}),getRootElement:()=>i.current?this.renderer.findDOMNode(i.current):a.current?this.renderer.findDOMNode(a.current):null,getRootData:e=>{var t;return l(i)&&(null==(t=i.current)?void 0:t.getRootData(e))},show:()=>ti(this,null,function*(){var e;l(i)&&(yield null==(e=i.current)?void 0:e.show())}),hide:()=>ti(this,null,function*(){var e;l(i)&&(yield null==(e=i.current)?void 0:e.hide())}),isShow:()=>{var e;return l(i)&&(null==(e=i.current)?void 0:e.isShow())||!1}}}render(e){return ti(this,null,function*(){var t;if("isEmpty"in e&&e.isEmpty)return this.renderer.render(th.default.createElement(aC,null),e.hostContainer);let{hostContainer:n,adData:i,template:a,position:r,systemInfo:l,isMulti:s,darkMode:o,adCustomId:u,styleOptions:d,flattenLayer:c,videoUseBlock:h,transformLinefeed:f,ref:p,extraScope:E,innerFunctions:m}=e,{adDatas:_}=e,{hostContext:g}=this,T=u||this.generateCustomId(),C=null!=(t=e.emitter)?t:new n5({maxListeners:1e3}),S=th.default.createRef(),y=th.default.createRef(),A=this.getConvertRatio(e.isPartial?{}:e.template.tplConfig||{},l.screenWidth,r),{width:v}=A,{ratio:P}=A;_||(_=[i]),null!=d&&d.ratio&&(P=d.ratio),null!=v&&(null==r?void 0:r.width)!=null&&(r.width=v);let b=this.createTemplateRef(e,{emitter:C,adCustomId:T,rootRef:S,itemRef:y});null==p||p(b),this.containerMap.set(n,{emitter:C,options:e});let I={adData:i,adDatas:_,isMulti:s||!1,systemInfo:l,adCustomId:T,darkMode:o,emitter:C,hostContext:g,extraScope:E,innerFunctions:m,renderContext:{hostContainer:n,ratio:P,useTextTag:this.useTextTag,useAttrId:this.useAttrId,useAttrClass:this.useAttrClass,hideMaskOnSlide:this.hideMaskOnSlide,minSlideDistance:this.minSlideDistance,isFlattenLayer:()=>{var e,t;return null!=c?c:"root"===a.type&&(null==(t=null==(e=null==a?void 0:a.options)?void 0:e.data)?void 0:t.flattenLayer)!=null&&a.options.data.flattenLayer},videoUseBlock:h,transformLinefeed:f,styleOptions:d||{},getRootElement:e=>this.renderer.findDOMNode(e),globalEventDelegator:this.globalEventDelegator}};e.isPartial?yield this.renderer.render(th.default.createElement(i$,e8({},e9(e8({},I),{ref:y,isRootItem:!0,template:e.template}))),n):yield this.renderer.render(th.default.createElement(iq,e8({},e9(e8({},I),{ref:S,template:e.template}))),n)})}unmount(e){let{hostContainer:t}=e;if(!this.containerMap.has(t))return;let{emitter:n,options:i}=this.containerMap.get(t);return(i.isEmpty||!i.emitter)&&n.removeAllListeners(),this.containerMap.delete(t),this.renderer.unmount(t)}destroy(){this.globalEventDelegator.destroy()}};var{unpack:aJ,pack:a0}=(ez=function(e){return"string"!=typeof e?e:e.replace(/[+ |^%]/g,e=>({" ":"+","+":"%2B","|":"%7C","^":"%5E","%":"%25"})[e])},ej=function(e){return"string"!=typeof e?e:e.replace(/\+|%2B|%7C|%5E|%25/g,e=>({"+":" ","%2B":"+","%7C":"|","%5E":"^","%25":"%"})[e])},eZ=function(e){return Number.prototype.toString.call(e,36).toUpperCase()},e$=function(e){return parseInt(e,36)},eK=Array.prototype.indexOf||function(e,t){for(let n=t||0,i=this.length;n<i;n++)if(this[n]===e)return n;return -1},{JSON,pack:function(e,t={}){let n=t.verbose||!1;n&&console.log("Normalize the JSON Object"),e="string"==typeof e?JSON.parse(e):e,n&&console.log("Creating a empty dictionary");let i={strings:[],integers:[],floats:[]};n&&console.log("Creating the AST");let a=function e(t){if(n&&console.log(`Calling recursiveAstBuilder with ${JSON.stringify(t)}`),null===t)return{type:"null",index:-3};if(void 0===t)return{type:"undefined",index:-5};if(t instanceof Array){let n=["@"];for(let i of t)n.push(e(i));return n}if("object"==typeof t){let n=["$"];for(let i of Object.keys(t))n.push(e(i)),n.push(e(t[i]));return n}if(""===t)return{type:"empty",index:-4};if("string"==typeof t){let e=eK.call(i.strings,t);return -1===e&&(i.strings.push(ez(t)),e=i.strings.length-1),{type:"strings",index:e}}if("number"==typeof t&&t%1==0){let e=eK.call(i.integers,t);return -1===e&&(i.integers.push(eZ(t)),e=i.integers.length-1),{type:"integers",index:e}}if("number"==typeof t){let e=eK.call(i.floats,t);return -1===e&&(i.floats.push(t),e=i.floats.length-1),{type:"floats",index:e}}if("boolean"==typeof t)return{type:"boolean",index:t?-1:-2};throw Error(`Unexpected argument of type ${typeof t}`)}(e),r=i.strings.length,l=i.integers.length;n&&console.log("Parsing the dictionary");let s=i.strings.join("|");return s+=`^${i.integers.join("|")}^${i.floats.join("|")}`,n&&console.log("Parsing the structure"),s+=`^${function e(t){if(n&&console.log(`Calling a recursiveParser with ${JSON.stringify(t)}`),t instanceof Array){let n=t.shift();for(let i of t)n+=`${e(i)}|`;return`${"|"===n[n.length-1]?n.slice(0,-1):n}]`}let{type:i}=t,{index:a}=t;if("strings"===i)return eZ(a);if("integers"===i)return eZ(r+a);if("floats"===i)return eZ(r+l+a);if("boolean"===i)return t.index;if("null"===i)return -3;if("undefined"===i)return -5;if("empty"===i)return -4;throw TypeError("The item is alien!")}(a)}`,n&&console.log("Ending parser"),t.debug?{dictionary:i,ast:a,packed:s}:s},unpack:function(e,t={}){t=t||{};let n=e.split("^");t.verbose&&console.log("Building dictionary");let i=[],a=n[0];if(""!==a){a=a.split("|"),t.verbose&&console.log("Parse the strings dictionary");for(let e=0,t=a.length;e<t;e++)i.push(ej(a[e]))}if(""!==(a=n[1])){a=a.split("|"),t.verbose&&console.log("Parse the integers dictionary");for(let e=0,t=a.length;e<t;e++)i.push(e$(a[e]))}if(""!==(a=n[2])){a=a.split("|"),t.verbose&&console.log("Parse the floats dictionary");for(let e=0,t=a.length;e<t;e++)i.push(parseFloat(a[e]))}a=null,t.verbose&&console.log("Tokenizing the structure");let r="",l=[],s=n[3].length;for(let e=0;e<s;e++){let t=n[3].charAt(e);"|"===t||"$"===t||"@"===t||"]"===t?(r&&(l.push(e$(r)),r=""),"|"!==t&&l.push(t)):r+=t}let o=l.length,u=0;return t.verbose&&console.log("Starting recursive parser"),function e(){let n=l[u];if(u+=1,t.verbose&&console.log(`Reading collection type ${"$"===n?"object":"Array"}`),"@"===n){let n=[];for(;u<o;u++){let a=l[u];if(t.verbose&&console.log(`Read ${a} symbol`),"]"===a)return n;if("@"===a||"$"===a)n.push(e());else switch(a){case -1:n.push(!0);break;case -2:n.push(!1);break;case -3:n.push(null);break;case -5:n.push(void 0);break;case -4:n.push("");break;default:n.push(i[a])}}return t.verbose&&console.log(`Parsed ${JSON.stringify(n)}`),n}if("$"===n){let n={};for(;u<o;u++){let t=l[u];if("]"===t)return n;t=-4===t?"":i[t];let a=l[u+=1];if("@"===a||"$"===a)n[t]=e();else switch(a){case -1:n[t]=!0;break;case -2:n[t]=!1;break;case -3:n[t]=null;break;case -5:n[t]=void 0;break;case -4:n[t]="";break;default:n[t]=i[a]}}return t.verbose&&console.log(`Parsed ${JSON.stringify(n)}`),n}throw TypeError(`Bad token ${n} isn't a type`)}()}})}}]);