"use strict";(self.webpackChunkweixin_search=self.webpackChunkweixin_search||[]).push([["91-download-app"],{764273:function(t,e,i){i.r(e);var n=i(97421),s=i(875742),o=i(551900),a=i(25838),c=(0,o.Z)(s.Z,n.s,n.x,!1,null,"f20ad78c",null);"function"==typeof a.Z&&(0,a.Z)(c),e.default=c.exports},381027:function(t,e,i){i.d(e,{Z:function(){return n}});function n(t){t.options.__wxs_id="6c1a502c"}},25838:function(t,e,i){var n=i(381027);e.Z=n.Z},875742:function(t,e,i){var n=i(360726);e.Z=n.Z},97421:function(t,e,i){i.d(e,{s:function(){return n},x:function(){return s}});var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{directives:[{name:"active",rawName:"v-active.stop",modifiers:{stop:!0}}],staticClass:"download-wrap active__item",class:{"no-sub-title":!t.source.subTitle},attrs:{role:"option","data-report-id":t.M_itemReportId(t.source),"data-cli":"","data-fcn-download-app":"","data-fr-1266f0177":""},on:{click:function(e){return e.stopPropagation(),t.onClickDownload(t.source)}}},[i("ui-image",{staticClass:"app-icon",attrs:{url:t.source.iconUrl,size:64,"data-fc-1b11da4cc":""}}),i("div",{staticClass:"app-main"},[i("p",{staticClass:"app-title",domProps:{innerHTML:t._s(t.xss(t.source.title))}}),t.source.subTitle?i("div",{staticClass:"sub-title-wrap"},[i("p",{staticClass:"app-sub-title"},[i("span",{domProps:{innerHTML:t._s(t.xss(t.source.subTitle))}})])]):t._e(),t.source.descList?i("ul",{staticClass:"desc-list"},t._l(t.source.descList,function(e){return i("li",{key:e,staticClass:"desc-item",domProps:{innerHTML:t._s(t.xss(e))}})}),0):t._e()]),t.source.button?i("ui-button",{directives:[{name:"active",rawName:"v-active.stop",modifiers:{stop:!0}}],staticClass:"download-button active__item",attrs:{title:t.source.button.title,type:0,"data-report-id":t.M_itemReportId(t.source.button),"data-fc-1b11da4ca":"","data-cli":""},nativeOn:{click:function(e){return e.stopPropagation(),t.onClickButton(t.source.button)}}}):t._e()],1)},s=[]},360726:function(t,e,i){var n=i(798509);e.Z={name:"DownloadApp",mixins:[n.jB,n.uW],props:{data:{type:Object,default:function(){return{}}},item:{type:Object,default:function(){return{}}},source:{type:Object,default:function(){return{}}},pos:{type:Number,default:0},typePos:{type:Number,default:0}},data:function(){return{}},methods:{onClickDownload:function(t){this.M_clickReport({clickContent:t.title},t),this.M_go(t)},onClickButton:function(t){this.M_clickReport({clickContent:t.title},t),this.M_go(t)}}}}}]);