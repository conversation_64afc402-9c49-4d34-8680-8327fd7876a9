"use strict";(self.webpackChunkweixin_search=self.webpackChunkweixin_search||[]).push([["vendors-node_modules_decimal_js_decimal_mjs"],{972867:function(n,i){var e,t,r="0123456789abcdef",s="2.3025850929940456840179914546843642076011014886287729760333279009675726096773524802359972050895982983419677840422862486334095254650828067566662873690987816894829072083255546808437998948262331985283935053089653777326288461633662222876982198867465436674744042432743651550489343149393914796194044002221051017141748003688084012647080685567743216228355220114804663715659121373450747856947683463616792101806445070648000277502684916746550586856935673420670581136429224554405758925724208241314695689016758940256776311356919292033376587141660230105703089634572075440370847469940168269282808481184289314848524948644871927809676271275775397027668605952496716674183485704422507197965004714951050492214776567636938662976979522110718264549734772662425709429322582798502585509785265383207606726317164309505995087807523710333101197857547331541421808427543863591778117054309827482385045648019095610299291824318237525357709750539565187697510374970888692180205189339507238539205144634197265287286965110862571492198849978748873771345686209167058",o="3.1415926535897932384626433832795028841971693993751058209749445923078164062862089986280348253421170679821480865132823066470938446095505822317253594081284811174502841027019385211055596446229489549303819644288109756659334461284756482337867831652712019091456485669234603486104543266482133936072602491412737245870066063155881748815209209628292540917153643678925903600113305305488204665213841469519415116094330572703657595919530921861173819326117931051185480744623799627495673518857527248912279381830119491298336733624406566430860213949463952247371907021798609437027705392171762931767523846748184676694051320005681271452635608277857713427577896091736371787214684409012249534301465495853710507922796892589235420199561121290219608640344181598136297747713099605187072113499999983729780499510597317328160963185950244594553469083026425223082533446850352619311881710100031378387528865875332083814206171776691473035982534904287554687311595628638823537875937519577818577805321712268066130019278766111959092164201989380952572010654858632789",u={precision:20,rounding:4,modulo:1,toExpNeg:-7,toExpPos:21,minE:-9e15,maxE:9e15,crypto:!1},c=!0,h="[DecimalError] ",f=h+"Invalid argument: ",a=h+"Precision limit exceeded",l=h+"crypto unavailable",d="[object Decimal]",g=Math.floor,p=Math.pow,w=/^0b([01]+(\.[01]*)?|\.[01]+)(p[+-]?\d+)?$/i,m=/^0x([0-9a-f]+(\.[0-9a-f]*)?|\.[0-9a-f]+)(p[+-]?\d+)?$/i,v=/^0o([0-7]+(\.[0-7]*)?|\.[0-7]+)(p[+-]?\d+)?$/i,N=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,b=s.length-1,E=o.length-1,x={toStringTag:d};function y(n){var i,e,t,r=n.length-1,s="",o=n[0];if(r>0){for(s+=o,i=1;i<r;i++)(e=7-(t=n[i]+"").length)&&(s+=P(e)),s+=t;(e=7-(t=(o=n[i])+"").length)&&(s+=P(e))}else if(0===o)return"0";for(;o%10==0;)o/=10;return s+o}function M(n,i,e){if(n!==~~n||n<i||n>e)throw Error(f+n)}function q(n,i,e,t){var r,s,o,u;for(s=n[0];s>=10;s/=10)--i;return--i<0?(i+=7,r=0):(r=Math.ceil((i+1)/7),i%=7),s=p(10,7-i),u=n[r]%s|0,null==t?i<3?(0==i?u=u/100|0:1==i&&(u=u/10|0),o=e<4&&99999==u||e>3&&49999==u||5e4==u||0==u):o=(e<4&&u+1==s||e>3&&u+1==s/2)&&(n[r+1]/s/100|0)==p(10,i-2)-1||(u==s/2||0==u)&&(n[r+1]/s/100|0)==0:i<4?(0==i?u=u/1e3|0:1==i?u=u/100|0:2==i&&(u=u/10|0),o=(t||e<4)&&9999==u||!t&&e>3&&4999==u):o=((t||e<4)&&u+1==s||!t&&e>3&&u+1==s/2)&&(n[r+1]/s/1e3|0)==p(10,i-3)-1,o}function O(n,i,e){for(var t,s,o=[0],u=0,c=n.length;u<c;){for(s=o.length;s--;)o[s]*=i;for(o[0]+=r.indexOf(n.charAt(u++)),t=0;t<o.length;t++)o[t]>e-1&&(void 0===o[t+1]&&(o[t+1]=0),o[t+1]+=o[t]/e|0,o[t]%=e)}return o.reverse()}x.absoluteValue=x.abs=function(){var n=new this.constructor(this);return n.s<0&&(n.s=1),_(n)},x.ceil=function(){return _(new this.constructor(this),this.e+1,2)},x.clampedTo=x.clamp=function(n,i){var e=this.constructor;if(n=new e(n),i=new e(i),!n.s||!i.s)return new e(NaN);if(n.gt(i))throw Error(f+i);return 0>this.cmp(n)?n:this.cmp(i)>0?i:new e(this)},x.comparedTo=x.cmp=function(n){var i,e,t,r,s=this.d,o=(n=new this.constructor(n)).d,u=this.s,c=n.s;if(!s||!o)return u&&c?u!==c?u:s===o?0:!s^u<0?1:-1:NaN;if(!s[0]||!o[0])return s[0]?u:o[0]?-c:0;if(u!==c)return u;if(this.e!==n.e)return this.e>n.e^u<0?1:-1;for(i=0,t=s.length,e=t<(r=o.length)?t:r;i<e;++i)if(s[i]!==o[i])return s[i]>o[i]^u<0?1:-1;return t===r?0:t>r^u<0?1:-1},x.cosine=x.cos=function(){var n,i,e=this,r=e.constructor;return e.d?e.d[0]?(n=r.precision,i=r.rounding,r.precision=n+Math.max(e.e,e.sd())+7,r.rounding=1,e=function(n,i){var e,t,r;if(i.isZero())return i;(t=i.d.length)<32?r=(1/j(4,e=Math.ceil(t/3))).toString():(e=16,r="2.3283064365386962890625e-10"),n.precision+=e,i=B(n,1,i.times(r),new n(1));for(var s=e;s--;){var o=i.times(i);i=o.times(o).minus(o).times(8).plus(1)}return n.precision-=e,i}(r,V(r,e)),r.precision=n,r.rounding=i,_(2==t||3==t?e.neg():e,n,i,!0)):new r(1):new r(NaN)},x.cubeRoot=x.cbrt=function(){var n,i,e,t,r,s,o,u,h,f,a=this.constructor;if(!this.isFinite()||this.isZero())return new a(this);for(c=!1,(s=this.s*p(this.s*this,1/3))&&Math.abs(s)!=1/0?t=new a(s.toString()):(e=y(this.d),(s=((n=this.e)-e.length+1)%3)&&(e+=1==s||-2==s?"0":"00"),s=p(e,1/3),n=g((n+1)/3)-(n%3==(n<0?-1:2)),(t=new a(e=s==1/0?"5e"+n:(e=s.toExponential()).slice(0,e.indexOf("e")+1)+n)).s=this.s),o=(n=a.precision)+3;;)if(t=F((f=(h=(u=t).times(u).times(u)).plus(this)).plus(this).times(u),f.plus(h),o+2,1),y(u.d).slice(0,o)===(e=y(t.d)).slice(0,o)){if("9999"!=(e=e.slice(o-3,o+1))&&(r||"4999"!=e)){(!+e||!+e.slice(1)&&"5"==e.charAt(0))&&(_(t,n+1,1),i=!t.times(t).times(t).eq(this));break}if(!r&&(_(u,n+1,0),u.times(u).times(u).eq(this))){t=u;break}o+=4,r=1}return c=!0,_(t,n,a.rounding,i)},x.decimalPlaces=x.dp=function(){var n,i=this.d,e=NaN;if(i){if(e=((n=i.length-1)-g(this.e/7))*7,n=i[n])for(;n%10==0;n/=10)e--;e<0&&(e=0)}return e},x.dividedBy=x.div=function(n){return F(this,new this.constructor(n))},x.dividedToIntegerBy=x.divToInt=function(n){var i=this.constructor;return _(F(this,new i(n),0,1,1),i.precision,i.rounding)},x.equals=x.eq=function(n){return 0===this.cmp(n)},x.floor=function(){return _(new this.constructor(this),this.e+1,3)},x.greaterThan=x.gt=function(n){return this.cmp(n)>0},x.greaterThanOrEqualTo=x.gte=function(n){var i=this.cmp(n);return 1==i||0===i},x.hyperbolicCosine=x.cosh=function(){var n,i,e,t,r,s=this,o=s.constructor,u=new o(1);if(!s.isFinite())return new o(s.s?1/0:NaN);if(s.isZero())return u;e=o.precision,t=o.rounding,o.precision=e+Math.max(s.e,s.sd())+4,o.rounding=1,(r=s.d.length)<32?i=(1/j(4,n=Math.ceil(r/3))).toString():(n=16,i="2.3283064365386962890625e-10"),s=B(o,1,s.times(i),new o(1),!0);for(var c,h=n,f=new o(8);h--;)c=s.times(s),s=u.minus(c.times(f.minus(c.times(f))));return _(s,o.precision=e,o.rounding=t,!0)},x.hyperbolicSine=x.sinh=function(){var n,i,e,t,r=this,s=r.constructor;if(!r.isFinite()||r.isZero())return new s(r);if(i=s.precision,e=s.rounding,s.precision=i+Math.max(r.e,r.sd())+4,s.rounding=1,(t=r.d.length)<3)r=B(s,2,r,r,!0);else{n=(n=1.4*Math.sqrt(t))>16?16:0|n,r=B(s,2,r=r.times(1/j(5,n)),r,!0);for(var o,u=new s(5),c=new s(16),h=new s(20);n--;)o=r.times(r),r=r.times(u.plus(o.times(c.times(o).plus(h))))}return s.precision=i,s.rounding=e,_(r,i,e,!0)},x.hyperbolicTangent=x.tanh=function(){var n,i,e=this.constructor;return this.isFinite()?this.isZero()?new e(this):(n=e.precision,i=e.rounding,e.precision=n+7,e.rounding=1,F(this.sinh(),this.cosh(),e.precision=n,e.rounding=i)):new e(this.s)},x.inverseCosine=x.acos=function(){var n,i=this,e=i.constructor,t=i.abs().cmp(1),r=e.precision,s=e.rounding;return -1!==t?0===t?i.isNeg()?S(e,r,s):new e(0):new e(NaN):i.isZero()?S(e,r+4,s).times(.5):(e.precision=r+6,e.rounding=1,i=i.asin(),n=S(e,r+4,s).times(.5),e.precision=r,e.rounding=s,n.minus(i))},x.inverseHyperbolicCosine=x.acosh=function(){var n,i,e=this,t=e.constructor;return e.lte(1)?new t(e.eq(1)?0:NaN):e.isFinite()?(n=t.precision,i=t.rounding,t.precision=n+Math.max(Math.abs(e.e),e.sd())+4,t.rounding=1,c=!1,e=e.times(e).minus(1).sqrt().plus(e),c=!0,t.precision=n,t.rounding=i,e.ln()):new t(e)},x.inverseHyperbolicSine=x.asinh=function(){var n,i,e=this,t=e.constructor;return!e.isFinite()||e.isZero()?new t(e):(n=t.precision,i=t.rounding,t.precision=n+2*Math.max(Math.abs(e.e),e.sd())+6,t.rounding=1,c=!1,e=e.times(e).plus(1).sqrt().plus(e),c=!0,t.precision=n,t.rounding=i,e.ln())},x.inverseHyperbolicTangent=x.atanh=function(){var n,i,e,t,r=this,s=r.constructor;return r.isFinite()?r.e>=0?new s(r.abs().eq(1)?r.s/0:r.isZero()?r:NaN):(n=s.precision,i=s.rounding,Math.max(t=r.sd(),n)<-(2*r.e)-1)?_(new s(r),n,i,!0):(s.precision=e=t-r.e,r=F(r.plus(1),new s(1).minus(r),e+n,1),s.precision=n+4,s.rounding=1,r=r.ln(),s.precision=n,s.rounding=i,r.times(.5)):new s(NaN)},x.inverseSine=x.asin=function(){var n,i,e,t,r=this,s=r.constructor;if(r.isZero())return new s(r);if(i=r.abs().cmp(1),e=s.precision,t=s.rounding,-1!==i)return 0===i?((n=S(s,e+4,t).times(.5)).s=r.s,n):new s(NaN);return s.precision=e+6,s.rounding=1,r=r.div(new s(1).minus(r.times(r)).sqrt().plus(1)).atan(),s.precision=e,s.rounding=t,r.times(2)},x.inverseTangent=x.atan=function(){var n,i,e,t,r,s,o,u,h,f=this,a=f.constructor,l=a.precision,d=a.rounding;if(f.isFinite()){if(f.isZero())return new a(f);else if(f.abs().eq(1)&&l+4<=E)return(o=S(a,l+4,d).times(.25)).s=f.s,o}else{if(!f.s)return new a(NaN);if(l+4<=E)return(o=S(a,l+4,d).times(.5)).s=f.s,o}for(a.precision=u=l+10,a.rounding=1,n=e=Math.min(28,u/7+2|0);n;--n)f=f.div(f.times(f).plus(1).sqrt().plus(1));for(c=!1,i=Math.ceil(u/7),t=1,h=f.times(f),o=new a(f),r=f;-1!==n;)if(r=r.times(h),s=o.minus(r.div(t+=2)),r=r.times(h),void 0!==(o=s.plus(r.div(t+=2))).d[i])for(n=i;o.d[n]===s.d[n]&&n--;);return e&&(o=o.times(2<<e-1)),c=!0,_(o,a.precision=l,a.rounding=d,!0)},x.isFinite=function(){return!!this.d},x.isInteger=x.isInt=function(){return!!this.d&&g(this.e/7)>this.d.length-2},x.isNaN=function(){return!this.s},x.isNegative=x.isNeg=function(){return this.s<0},x.isPositive=x.isPos=function(){return this.s>0},x.isZero=function(){return!!this.d&&0===this.d[0]},x.lessThan=x.lt=function(n){return 0>this.cmp(n)},x.lessThanOrEqualTo=x.lte=function(n){return 1>this.cmp(n)},x.logarithm=x.log=function(n){var i,e,t,r,s,o,u,h,f=this.constructor,a=f.precision,l=f.rounding;if(null==n)n=new f(10),i=!0;else{if(e=(n=new f(n)).d,n.s<0||!e||!e[0]||n.eq(1))return new f(NaN);i=n.eq(10)}if(e=this.d,this.s<0||!e||!e[0]||this.eq(1))return new f(e&&!e[0]?-Infinity:1!=this.s?NaN:e?0:1/0);if(i){if(e.length>1)s=!0;else{for(r=e[0];r%10==0;)r/=10;s=1!==r}}if(c=!1,o=C(this,u=a+5),q((h=F(o,t=i?D(f,u+10):C(n,u),u,1)).d,r=a,l))do if(u+=10,o=C(this,u),h=F(o,t=i?D(f,u+10):C(n,u),u,1),!s){+y(h.d).slice(r+1,r+15)+1==1e14&&(h=_(h,a+1,0));break}while(q(h.d,r+=10,l));return c=!0,_(h,a,l)},x.minus=x.sub=function(n){var i,e,t,r,s,o,u,h,f,a,l,d,p=this.constructor;if(n=new p(n),!this.d||!n.d)return this.s&&n.s?this.d?n.s=-n.s:n=new p(n.d||this.s!==n.s?this:NaN):n=new p(NaN),n;if(this.s!=n.s)return n.s=-n.s,this.plus(n);if(f=this.d,d=n.d,u=p.precision,h=p.rounding,!f[0]||!d[0]){if(d[0])n.s=-n.s;else{if(!f[0])return new p(3===h?-0:0);n=new p(this)}return c?_(n,u,h):n}if(e=g(n.e/7),a=g(this.e/7),f=f.slice(),s=a-e){for((l=s<0)?(i=f,s=-s,o=d.length):(i=d,e=a,o=f.length),s>(t=Math.max(Math.ceil(u/7),o)+2)&&(s=t,i.length=1),i.reverse(),t=s;t--;)i.push(0);i.reverse()}else{for(t=f.length,(l=t<(o=d.length))&&(o=t),t=0;t<o;t++)if(f[t]!=d[t]){l=f[t]<d[t];break}s=0}for(l&&(i=f,f=d,d=i,n.s=-n.s),o=f.length,t=d.length-o;t>0;--t)f[o++]=0;for(t=d.length;t>s;){if(f[--t]<d[t]){for(r=t;r&&0===f[--r];)f[r]=1e7-1;--f[r],f[t]+=1e7}f[t]-=d[t]}for(;0===f[--o];)f.pop();for(;0===f[0];f.shift())--e;return f[0]?(n.d=f,n.e=A(f,e),c?_(n,u,h):n):new p(3===h?-0:0)},x.modulo=x.mod=function(n){var i,e=this.constructor;return(n=new e(n),this.d&&n.s&&(!n.d||n.d[0]))?n.d&&(!this.d||this.d[0])?(c=!1,9==e.modulo?(i=F(this,n.abs(),0,3,1),i.s*=n.s):i=F(this,n,0,e.modulo,1),i=i.times(n),c=!0,this.minus(i)):_(new e(this),e.precision,e.rounding):new e(NaN)},x.naturalExponential=x.exp=function(){return U(this)},x.naturalLogarithm=x.ln=function(){return C(this)},x.negated=x.neg=function(){var n=new this.constructor(this);return n.s=-n.s,_(n)},x.plus=x.add=function(n){var i,e,t,r,s,o,u,h,f,a,l=this.constructor;if(n=new l(n),!this.d||!n.d)return this.s&&n.s?!this.d&&(n=new l(n.d||this.s===n.s?this:NaN)):n=new l(NaN),n;if(this.s!=n.s)return n.s=-n.s,this.minus(n);if(f=this.d,a=n.d,u=l.precision,h=l.rounding,!f[0]||!a[0])return!a[0]&&(n=new l(this)),c?_(n,u,h):n;if(s=g(this.e/7),t=g(n.e/7),f=f.slice(),r=s-t){for(r<0?(e=f,r=-r,o=a.length):(e=a,t=s,o=f.length),r>(o=(s=Math.ceil(u/7))>o?s+1:o+1)&&(r=o,e.length=1),e.reverse();r--;)e.push(0);e.reverse()}for(o=f.length,o-(r=a.length)<0&&(r=o,e=a,a=f,f=e),i=0;r;)i=(f[--r]=f[r]+a[r]+i)/1e7|0,f[r]%=1e7;for(i&&(f.unshift(i),++t),o=f.length;0==f[--o];)f.pop();return n.d=f,n.e=A(f,t),c?_(n,u,h):n},x.precision=x.sd=function(n){var i;if(void 0!==n&&!!n!==n&&1!==n&&0!==n)throw Error(f+n);return this.d?(i=k(this.d),n&&this.e+1>i&&(i=this.e+1)):i=NaN,i},x.round=function(){var n=this.constructor;return _(new n(this),this.e+1,n.rounding)},x.sine=x.sin=function(){var n,i,e=this,r=e.constructor;return e.isFinite()?e.isZero()?new r(e):(n=r.precision,i=r.rounding,r.precision=n+Math.max(e.e,e.sd())+7,r.rounding=1,e=function(n,i){var e,t=i.d.length;if(t<3)return i.isZero()?i:B(n,2,i,i);e=(e=1.4*Math.sqrt(t))>16?16:0|e,i=B(n,2,i=i.times(1/j(5,e)),i);for(var r,s=new n(5),o=new n(16),u=new n(20);e--;)r=i.times(i),i=i.times(s.plus(r.times(o.times(r).minus(u))));return i}(r,V(r,e)),r.precision=n,r.rounding=i,_(t>2?e.neg():e,n,i,!0)):new r(NaN)},x.squareRoot=x.sqrt=function(){var n,i,e,t,r,s,o=this.d,u=this.e,h=this.s,f=this.constructor;if(1!==h||!o||!o[0])return new f(!h||h<0&&(!o||o[0])?NaN:o?this:1/0);for(c=!1,0==(h=Math.sqrt(+this))||h==1/0?(((i=y(o)).length+u)%2==0&&(i+="0"),h=Math.sqrt(i),u=g((u+1)/2)-(u<0||u%2),t=new f(i=h==1/0?"5e"+u:(i=h.toExponential()).slice(0,i.indexOf("e")+1)+u)):t=new f(h.toString()),e=(u=f.precision)+3;;)if(t=(s=t).plus(F(this,s,e+2,1)).times(.5),y(s.d).slice(0,e)===(i=y(t.d)).slice(0,e)){if("9999"!=(i=i.slice(e-3,e+1))&&(r||"4999"!=i)){(!+i||!+i.slice(1)&&"5"==i.charAt(0))&&(_(t,u+1,1),n=!t.times(t).eq(this));break}if(!r&&(_(s,u+1,0),s.times(s).eq(this))){t=s;break}e+=4,r=1}return c=!0,_(t,u,f.rounding,n)},x.tangent=x.tan=function(){var n,i,e=this,r=e.constructor;return e.isFinite()?e.isZero()?new r(e):(n=r.precision,i=r.rounding,r.precision=n+10,r.rounding=1,(e=e.sin()).s=1,e=F(e,new r(1).minus(e.times(e)).sqrt(),n+10,0),r.precision=n,r.rounding=i,_(2==t||4==t?e.neg():e,n,i,!0)):new r(NaN)},x.times=x.mul=function(n){var i,e,t,r,s,o,u,h,f,a=this.constructor,l=this.d,d=(n=new a(n)).d;if(n.s*=this.s,!l||!l[0]||!d||!d[0])return new a(n.s&&(!l||l[0]||d)&&(!d||d[0]||l)?l&&d?0*n.s:n.s/0:NaN);for(e=g(this.e/7)+g(n.e/7),h=l.length,h<(f=d.length)&&(s=l,l=d,d=s,o=h,h=f,f=o),s=[],t=o=h+f;t--;)s.push(0);for(t=f;--t>=0;){for(i=0,r=h+t;r>t;)u=s[r]+d[t]*l[r-t-1]+i,s[r--]=u%1e7|0,i=u/1e7|0;s[r]=(s[r]+i)%1e7|0}for(;!s[--o];)s.pop();return i?++e:s.shift(),n.d=s,n.e=A(s,e),c?_(n,a.precision,a.rounding):n},x.toBinary=function(n,i){return $(this,2,n,i)},x.toDecimalPlaces=x.toDP=function(n,i){var e=this,t=e.constructor;return(e=new t(e),void 0===n)?e:(M(n,0,1e9),void 0===i?i=t.rounding:M(i,0,8),_(e,n+e.e+1,i))},x.toExponential=function(n,i){var e,t=this,r=t.constructor;return void 0===n?e=Z(t,!0):(M(n,0,1e9),void 0===i?i=r.rounding:M(i,0,8),e=Z(t=_(new r(t),n+1,i),!0,n+1)),t.isNeg()&&!t.isZero()?"-"+e:e},x.toFixed=function(n,i){var e,t,r=this.constructor;return void 0===n?e=Z(this):(M(n,0,1e9),void 0===i?i=r.rounding:M(i,0,8),e=Z(t=_(new r(this),n+this.e+1,i),!1,n+t.e+1)),this.isNeg()&&!this.isZero()?"-"+e:e},x.toFraction=function(n){var i,e,t,r,s,o,u,h,a,l,d,g,w=this.d,m=this.constructor;if(!w)return new m(this);if(a=e=new m(1),t=h=new m(0),o=(s=(i=new m(t)).e=k(w)-this.e-1)%7,i.d[0]=p(10,o<0?7+o:o),null==n)n=s>0?i:a;else{if(!(u=new m(n)).isInt()||u.lt(a))throw Error(f+u);n=u.gt(i)?s>0?i:a:u}for(c=!1,u=new m(y(w)),l=m.precision,m.precision=s=14*w.length;d=F(u,i,0,1,1),1!=(r=e.plus(d.times(t))).cmp(n);){;e=t,t=r,r=a,a=h.plus(d.times(r)),h=r,r=i,i=u.minus(d.times(r)),u=r}return r=F(n.minus(e),t,0,1,1),h=h.plus(r.times(a)),e=e.plus(r.times(t)),h.s=a.s=this.s,g=1>F(a,t,s,1).minus(this).abs().cmp(F(h,e,s,1).minus(this).abs())?[a,t]:[h,e],m.precision=l,c=!0,g},x.toHexadecimal=x.toHex=function(n,i){return $(this,16,n,i)},x.toNearest=function(n,i){var e=this,t=e.constructor;if(e=new t(e),null==n){if(!e.d)return e;n=new t(1),i=t.rounding}else{if(n=new t(n),void 0===i?i=t.rounding:M(i,0,8),!e.d)return n.s?e:n;if(!n.d)return n.s&&(n.s=e.s),n}return n.d[0]?(c=!1,e=F(e,n,0,i,1).times(n),c=!0,_(e)):(n.s=e.s,e=n),e},x.toNumber=function(){return+this},x.toOctal=function(n,i){return $(this,8,n,i)},x.toPower=x.pow=function(n){var i,e,t,r,s,o,u=this,h=u.constructor,f=+(n=new h(n));if(!u.d||!n.d||!u.d[0]||!n.d[0])return new h(p(+u,f));if((u=new h(u)).eq(1))return u;if(t=h.precision,s=h.rounding,n.eq(1))return _(u,t,s);if((i=g(n.e/7))>=n.d.length-1&&(e=f<0?-f:f)<=9007199254740991)return r=R(h,u,e,t),n.s<0?new h(1).div(r):_(r,t,s);if((o=u.s)<0){if(i<n.d.length-1)return new h(NaN);if((1&n.d[i])==0&&(o=1),0==u.e&&1==u.d[0]&&1==u.d.length)return u.s=o,u}return(i=0!=(e=p(+u,f))&&isFinite(e)?new h(e+"").e:g(f*(Math.log("0."+y(u.d))/Math.LN10+u.e+1)))>h.maxE+1||i<h.minE-1?new h(i>0?o/0:0):(c=!1,h.rounding=u.s=1,e=Math.min(12,(i+"").length),(r=U(n.times(C(u,t+e)),t)).d&&q((r=_(r,t+5,1)).d,t,s)&&(i=t+10,+y((r=_(U(n.times(C(u,i+e)),i),i+5,1)).d).slice(t+1,t+15)+1==1e14&&(r=_(r,t+1,0))),r.s=o,c=!0,h.rounding=s,_(r,t,s))},x.toPrecision=function(n,i){var e,t=this,r=t.constructor;return void 0===n?e=Z(t,t.e<=r.toExpNeg||t.e>=r.toExpPos):(M(n,1,1e9),void 0===i?i=r.rounding:M(i,0,8),e=Z(t=_(new r(t),n,i),n<=t.e||t.e<=r.toExpNeg,n)),t.isNeg()&&!t.isZero()?"-"+e:e},x.toSignificantDigits=x.toSD=function(n,i){var e=this.constructor;return void 0===n?(n=e.precision,i=e.rounding):(M(n,1,1e9),void 0===i?i=e.rounding:M(i,0,8)),_(new e(this),n,i)},x.toString=function(){var n=this.constructor,i=Z(this,this.e<=n.toExpNeg||this.e>=n.toExpPos);return this.isNeg()&&!this.isZero()?"-"+i:i},x.truncated=x.trunc=function(){return _(new this.constructor(this),this.e+1,1)},x.valueOf=x.toJSON=function(){var n=this.constructor,i=Z(this,this.e<=n.toExpNeg||this.e>=n.toExpPos);return this.isNeg()?"-"+i:i};var F=function(){function n(n,i,e){var t,r=0,s=n.length;for(n=n.slice();s--;)t=n[s]*i+r,n[s]=t%e|0,r=t/e|0;return r&&n.unshift(r),n}function i(n,i,e,t){var r,s;if(e!=t)s=e>t?1:-1;else for(r=s=0;r<e;r++)if(n[r]!=i[r]){s=n[r]>i[r]?1:-1;break}return s}function t(n,i,e,t){for(var r=0;e--;)n[e]-=r,r=n[e]<i[e]?1:0,n[e]=r*t+n[e]-i[e];for(;!n[0]&&n.length>1;)n.shift()}return function(r,s,o,u,c,h){var f,a,l,d,p,w,m,v,N,b,E,x,y,M,q,O,F,Z,A,D,S=r.constructor,k=r.s==s.s?1:-1,P=r.d,R=s.d;if(!P||!P[0]||!R||!R[0])return new S(r.s&&s.s&&(P?!R||P[0]!=R[0]:R)?P&&0==P[0]||!R?0*k:k/0:NaN);for(h?(p=1,a=r.e-s.e):(h=1e7,p=7,a=g(r.e/p)-g(s.e/p)),A=R.length,F=P.length,b=(N=new S(k)).d=[],l=0;R[l]==(P[l]||0);l++);if(R[l]>(P[l]||0)&&a--,null==o?(M=o=S.precision,u=S.rounding):M=c?o+(r.e-s.e)+1:o,M<0)b.push(1),w=!0;else{if(M=M/p+2|0,l=0,1==A){for(d=0,R=R[0],M++;(l<F||d)&&M--;l++)q=d*h+(P[l]||0),b[l]=q/R|0,d=q%R|0;w=d||l<F}else{for((d=h/(R[0]+1)|0)>1&&(R=n(R,d,h),P=n(P,d,h),A=R.length,F=P.length),O=A,x=(E=P.slice(0,A)).length;x<A;)E[x++]=0;(D=R.slice()).unshift(0),Z=R[0],R[1]>=h/2&&++Z;do d=0,(f=i(R,E,A,x))<0?(y=E[0],A!=x&&(y=y*h+(E[1]||0)),(d=y/Z|0)>1?(d>=h&&(d=h-1),v=(m=n(R,d,h)).length,x=E.length,1==(f=i(m,E,v,x))&&(d--,t(m,A<v?D:R,v,h))):(0==d&&(f=d=1),m=R.slice()),(v=m.length)<x&&m.unshift(0),t(E,m,x,h),-1==f&&(x=E.length,(f=i(R,E,A,x))<1&&(d++,t(E,A<x?D:R,x,h))),x=E.length):0===f&&(d++,E=[0]),b[l++]=d,f&&E[0]?E[x++]=P[O]||0:(E=[P[O]],x=1);while((O++<F||void 0!==E[0])&&M--);w=void 0!==E[0]}!b[0]&&b.shift()}if(1==p)N.e=a,e=w;else{for(l=1,d=b[0];d>=10;d/=10)l++;N.e=l+a*p-1,_(N,c?o+N.e+1:o,u,w)}return N}}();function _(n,i,e,t){var r,s,o,u,h,f,a,l,d,g=n.constructor;n:if(null!=i){if(!(l=n.d))return n;for(r=1,u=l[0];u>=10;u/=10)r++;if((s=i-r)<0)s+=7,o=i,h=(a=l[d=0])/p(10,r-o-1)%10|0;else if((d=Math.ceil((s+1)/7))>=(u=l.length)){if(t){for(;u++<=d;)l.push(0);a=h=0,r=1,s%=7,o=s-7+1}else break n}else{for(r=1,a=u=l[d];u>=10;u/=10)r++;s%=7,h=(o=s-7+r)<0?0:a/p(10,r-o-1)%10|0}if(t=t||i<0||void 0!==l[d+1]||(o<0?a:a%p(10,r-o-1)),f=e<4?(h||t)&&(0==e||e==(n.s<0?3:2)):h>5||5==h&&(4==e||t||6==e&&(s>0?o>0?a/p(10,r-o):0:l[d-1])%10&1||e==(n.s<0?8:7)),i<1||!l[0])return l.length=0,f?(i-=n.e+1,l[0]=p(10,(7-i%7)%7),n.e=-i||0):l[0]=n.e=0,n;if(0==s?(l.length=d,u=1,d--):(l.length=d+1,u=p(10,7-s),l[d]=o>0?(a/p(10,r-o)%p(10,o)|0)*u:0),f)for(;;){if(0==d){for(s=1,o=l[0];o>=10;o/=10)s++;for(o=l[0]+=u,u=1;o>=10;o/=10)u++;s!=u&&(n.e++,1e7==l[0]&&(l[0]=1));break}if(l[d]+=u,1e7!=l[d])break;l[d--]=0,u=1}for(s=l.length;0===l[--s];)l.pop()}return c&&(n.e>g.maxE?(n.d=null,n.e=NaN):n.e<g.minE&&(n.e=0,n.d=[0])),n}function Z(n,i,e){if(!n.isFinite())return I(n);var t,r=n.e,s=y(n.d),o=s.length;return i?(e&&(t=e-o)>0?s=s.charAt(0)+"."+s.slice(1)+P(t):o>1&&(s=s.charAt(0)+"."+s.slice(1)),s=s+(n.e<0?"e":"e+")+n.e):r<0?(s="0."+P(-r-1)+s,e&&(t=e-o)>0&&(s+=P(t))):r>=o?(s+=P(r+1-o),e&&(t=e-r-1)>0&&(s=s+"."+P(t))):((t=r+1)<o&&(s=s.slice(0,t)+"."+s.slice(t)),e&&(t=e-o)>0&&(r+1===o&&(s+="."),s+=P(t))),s}function A(n,i){var e=n[0];for(i*=7;e>=10;e/=10)i++;return i}function D(n,i,e){if(i>b)throw c=!0,e&&(n.precision=e),Error(a);return _(new n(s),i,1,!0)}function S(n,i,e){if(i>E)throw Error(a);return _(new n(o),i,e,!0)}function k(n){var i=n.length-1,e=7*i+1;if(i=n[i]){for(;i%10==0;i/=10)e--;for(i=n[0];i>=10;i/=10)e++}return e}function P(n){for(var i="";n--;)i+="0";return i}function R(n,i,e,t){var r,s=new n(1),o=Math.ceil(t/7+4);for(c=!1;;){if(e%2&&W((s=s.times(i)).d,o)&&(r=!0),0===(e=g(e/2))){e=s.d.length-1,r&&0===s.d[e]&&++s.d[e];break}W((i=i.times(i)).d,o)}return c=!0,s}function T(n){return 1&n.d[n.d.length-1]}function L(n,i,e){for(var t,r=new n(i[0]),s=0;++s<i.length;)if((t=new n(i[s])).s)r[e](t)&&(r=t);else{r=t;break}return r}function U(n,i){var e,t,r,s,o,u,h,f=0,a=0,l=0,d=n.constructor,g=d.rounding,w=d.precision;if(!n.d||!n.d[0]||n.e>17)return new d(n.d?n.d[0]?n.s<0?0:1/0:1:n.s?n.s<0?0:n:0/0);for(null==i?(c=!1,h=w):h=i,u=new d(.03125);n.e>-2;)n=n.times(u),l+=5;for(h+=t=Math.log(p(2,l))/Math.LN10*2+5|0,e=s=o=new d(1),d.precision=h;;){if(s=_(s.times(n),h,1),e=e.times(++a),y((u=o.plus(F(s,e,h,1))).d).slice(0,h)===y(o.d).slice(0,h)){for(r=l;r--;)o=_(o.times(o),h,1);if(null!=i)return d.precision=w,o;if(!(f<3&&q(o.d,h-t,g,f)))return _(o,d.precision=w,g,c=!0);d.precision=h+=10,e=s=u=new d(1),a=0,f++}o=u}}function C(n,i){var e,t,r,s,o,u,h,f,a,l,d,g=1,p=n,w=p.d,m=p.constructor,v=m.rounding,N=m.precision;if(p.s<0||!w||!w[0]||!p.e&&1==w[0]&&1==w.length)return new m(w&&!w[0]?-Infinity:1!=p.s?NaN:w?0:p);if(null==i?(c=!1,a=N):a=i,m.precision=a+=10,t=(e=y(w)).charAt(0),!(15e14>Math.abs(s=p.e)))return f=D(m,a+2,N).times(s+""),p=C(new m(t+"."+e.slice(1)),a-10).plus(f),m.precision=N,null==i?_(p,N,v,c=!0):p;for(;t<7&&1!=t||1==t&&e.charAt(1)>3;)t=(e=y((p=p.times(n)).d)).charAt(0),g++;s=p.e,t>1?(p=new m("0."+e),s++):p=new m(t+"."+e.slice(1));for(l=p,h=o=p=F(p.minus(1),p.plus(1),a,1),d=_(p.times(p),a,1),r=3;;){if(o=_(o.times(d),a,1),y((f=h.plus(F(o,new m(r),a,1))).d).slice(0,a)===y(h.d).slice(0,a)){if(h=h.times(2),0!==s&&(h=h.plus(D(m,a+2,N).times(s+""))),h=F(h,new m(g),a,1),null!=i)return m.precision=N,h;if(!q(h.d,a-10,v,u))return _(h,m.precision=N,v,c=!0);m.precision=a+=10,f=o=p=F(l.minus(1),l.plus(1),a,1),d=_(p.times(p),a,1),r=u=1}h=f,r+=2}}function I(n){return String(n.s*n.s/0)}function H(n,i){var e,t,r;for((e=i.indexOf("."))>-1&&(i=i.replace(".","")),(t=i.search(/e/i))>0?(e<0&&(e=t),e+=+i.slice(t+1),i=i.substring(0,t)):e<0&&(e=i.length),t=0;48===i.charCodeAt(t);t++);for(r=i.length;48===i.charCodeAt(r-1);--r);if(i=i.slice(t,r)){if(r-=t,n.e=e=e-t-1,n.d=[],t=(e+1)%7,e<0&&(t+=7),t<r){for(t&&n.d.push(+i.slice(0,t)),r-=7;t<r;)n.d.push(+i.slice(t,t+=7));t=7-(i=i.slice(t)).length}else t-=r;for(;t--;)i+="0";n.d.push(+i),c&&(n.e>n.constructor.maxE?(n.d=null,n.e=NaN):n.e<n.constructor.minE&&(n.e=0,n.d=[0]))}else n.e=0,n.d=[0];return n}function B(n,i,e,t,r){var s,o,u,h,f=1,a=n.precision,l=Math.ceil(a/7);for(c=!1,h=e.times(e),u=new n(t);;){if(o=F(u.times(h),new n(i++*i++),a,1),u=r?t.plus(o):t.minus(o),t=F(o.times(h),new n(i++*i++),a,1),void 0!==(o=u.plus(t)).d[l]){for(s=l;o.d[s]===u.d[s]&&s--;);if(-1==s)break}s=u,u=t,t=o,o=s}return c=!0,o.d.length=l+1,o}function j(n,i){for(var e=n;--i;)e*=n;return e}function V(n,i){var e,r=i.s<0,s=S(n,n.precision,1),o=s.times(.5);if((i=i.abs()).lte(o))return t=r?4:1,i;if((e=i.divToInt(s)).isZero())t=r?3:2;else{if((i=i.minus(e.times(s))).lte(o))return t=T(e)?r?2:3:r?4:1,i;t=T(e)?r?1:4:r?3:2}return i.minus(s).abs()}function $(n,i,t,s){var o,u,c,h,f,a,l,d,g,p=n.constructor,w=void 0!==t;if(w?(M(t,1,1e9),void 0===s?s=p.rounding:M(s,0,8)):(t=p.precision,s=p.rounding),n.isFinite()){for(c=(l=Z(n)).indexOf("."),w?(o=2,16==i?t=4*t-3:8==i&&(t=3*t-2)):o=i,c>=0&&(l=l.replace(".",""),(g=new p(1)).e=l.length-c,g.d=O(Z(g),10,o),g.e=g.d.length),u=f=(d=O(l,10,o)).length;0==d[--f];)d.pop();if(d[0]){if(c<0?u--:((n=new p(n)).d=d,n.e=u,d=(n=F(n,g,t,s,0,o)).d,u=n.e,a=e),c=d[t],h=o/2,a=a||void 0!==d[t+1],a=s<4?(void 0!==c||a)&&(0===s||s===(n.s<0?3:2)):c>h||c===h&&(4===s||a||6===s&&1&d[t-1]||s===(n.s<0?8:7)),d.length=t,a)for(;++d[--t]>o-1;)d[t]=0,!t&&(++u,d.unshift(1));for(f=d.length;!d[f-1];--f);for(c=0,l="";c<f;c++)l+=r.charAt(d[c]);if(w){if(f>1){if(16==i||8==i){for(c=16==i?4:3,--f;f%c;f++)l+="0";for(f=(d=O(l,o,i)).length;!d[f-1];--f);for(c=1,l="1.";c<f;c++)l+=r.charAt(d[c])}else l=l.charAt(0)+"."+l.slice(1)}l=l+(u<0?"p":"p+")+u}else if(u<0){for(;++u;)l="0"+l;l="0."+l}else if(++u>f)for(u-=f;u--;)l+="0";else u<f&&(l=l.slice(0,u)+"."+l.slice(u))}else l=w?"0p+0":"0";l=(16==i?"0x":2==i?"0b":8==i?"0o":"")+l}else l=I(n);return n.s<0?"-"+l:l}function W(n,i){if(n.length>i)return n.length=i,!0}function J(n){return new this(n).abs()}function z(n){return new this(n).acos()}function G(n){return new this(n).acosh()}function K(n,i){return new this(n).plus(i)}function Q(n){return new this(n).asin()}function X(n){return new this(n).asinh()}function Y(n){return new this(n).atan()}function nn(n){return new this(n).atanh()}function ni(n,i){n=new this(n),i=new this(i);var e,t=this.precision,r=this.rounding,s=t+4;return n.s&&i.s?n.d||i.d?!i.d||n.isZero()?(e=i.s<0?S(this,t,r):new this(0)).s=n.s:!n.d||i.isZero()?(e=S(this,s,1).times(.5)).s=n.s:i.s<0?(this.precision=s,this.rounding=1,e=this.atan(F(n,i,s,1)),i=S(this,s,1),this.precision=t,this.rounding=r,e=n.s<0?e.minus(i):e.plus(i)):e=this.atan(F(n,i,s,1)):(e=S(this,s,1).times(i.s>0?.25:.75)).s=n.s:e=new this(NaN),e}function ne(n){return new this(n).cbrt()}function nt(n){return _(n=new this(n),n.e+1,2)}function nr(n,i,e){return new this(n).clamp(i,e)}function ns(n){if(!n||"object"!=typeof n)throw Error(h+"Object expected");var i,e,t,r=!0===n.defaults,s=["precision",1,1e9,"rounding",0,8,"toExpNeg",-9e15,0,"toExpPos",0,9e15,"maxE",0,9e15,"minE",-9e15,0,"modulo",0,9];for(i=0;i<s.length;i+=3)if(e=s[i],r&&(this[e]=u[e]),void 0!==(t=n[e])){if(g(t)===t&&t>=s[i+1]&&t<=s[i+2])this[e]=t;else throw Error(f+e+": "+t)}if(e="crypto",r&&(this[e]=u[e]),void 0!==(t=n[e])){if(!0===t||!1===t||0===t||1===t){if(t){if("undefined"!=typeof crypto&&crypto&&(crypto.getRandomValues||crypto.randomBytes))this[e]=!0;else throw Error(l)}else this[e]=!1}else throw Error(f+e+": "+t)}return this}function no(n){return new this(n).cos()}function nu(n){return new this(n).cosh()}function nc(n,i){return new this(n).div(i)}function nh(n){return new this(n).exp()}function nf(n){return _(n=new this(n),n.e+1,3)}function na(){var n,i,e=new this(0);for(n=0,c=!1;n<arguments.length;)if(i=new this(arguments[n++]),i.d)e.d&&(e=e.plus(i.times(i)));else{if(i.s)return c=!0,new this(1/0);e=i}return c=!0,e.sqrt()}function nl(n){return n instanceof nk||n&&n.toStringTag===d||!1}function nd(n){return new this(n).ln()}function ng(n,i){return new this(n).log(i)}function np(n){return new this(n).log(2)}function nw(n){return new this(n).log(10)}function nm(){return L(this,arguments,"lt")}function nv(){return L(this,arguments,"gt")}function nN(n,i){return new this(n).mod(i)}function nb(n,i){return new this(n).mul(i)}function nE(n,i){return new this(n).pow(i)}function nx(n){var i,e,t,r,s=0,o=new this(1),u=[];if(void 0===n?n=this.precision:M(n,1,1e9),t=Math.ceil(n/7),this.crypto){if(crypto.getRandomValues)for(i=crypto.getRandomValues(new Uint32Array(t));s<t;)(r=i[s])>=429e7?i[s]=crypto.getRandomValues(new Uint32Array(1))[0]:u[s++]=r%1e7;else if(crypto.randomBytes){for(i=crypto.randomBytes(t*=4);s<t;)(r=i[s]+(i[s+1]<<8)+(i[s+2]<<16)+((127&i[s+3])<<24))>=214e7?crypto.randomBytes(4).copy(i,s):(u.push(r%1e7),s+=4);s=t/4}else throw Error(l)}else for(;s<t;)u[s++]=1e7*Math.random()|0;for(t=u[--s],n%=7,t&&n&&(r=p(10,7-n),u[s]=(t/r|0)*r);0===u[s];s--)u.pop();if(s<0)e=0,u=[0];else{for(e=-1;0===u[0];e-=7)u.shift();for(t=1,r=u[0];r>=10;r/=10)t++;t<7&&(e-=7-t)}return o.e=e,o.d=u,o}function ny(n){return _(n=new this(n),n.e+1,this.rounding)}function nM(n){return(n=new this(n)).d?n.d[0]?n.s:0*n.s:n.s||NaN}function nq(n){return new this(n).sin()}function nO(n){return new this(n).sinh()}function nF(n){return new this(n).sqrt()}function n_(n,i){return new this(n).sub(i)}function nZ(){var n=0,i=arguments,e=new this(i[0]);for(c=!1;e.s&&++n<i.length;)e=e.plus(i[n]);return c=!0,_(e,this.precision,this.rounding)}function nA(n){return new this(n).tan()}function nD(n){return new this(n).tanh()}function nS(n){return _(n=new this(n),n.e+1,1)}x[Symbol.for("nodejs.util.inspect.custom")]=x.toString,x[Symbol.toStringTag]="Decimal";var nk=x.constructor=function n(i){var e,t,r;function s(n){var i,e,t;if(!(this instanceof s))return new s(n);if(this.constructor=s,nl(n)){this.s=n.s,c?!n.d||n.e>s.maxE?(this.e=NaN,this.d=null):n.e<s.minE?(this.e=0,this.d=[0]):(this.e=n.e,this.d=n.d.slice()):(this.e=n.e,this.d=n.d?n.d.slice():n.d);return}if("number"==(t=typeof n)){if(0===n){this.s=1/n<0?-1:1,this.e=0,this.d=[0];return}if(n<0?(n=-n,this.s=-1):this.s=1,n===~~n&&n<1e7){for(i=0,e=n;e>=10;e/=10)i++;c?i>s.maxE?(this.e=NaN,this.d=null):i<s.minE?(this.e=0,this.d=[0]):(this.e=i,this.d=[n]):(this.e=i,this.d=[n]);return}if(0*n!=0){!n&&(this.s=NaN),this.e=NaN,this.d=null;return}return H(this,n.toString())}if("string"!==t)throw Error(f+n);return 45===(e=n.charCodeAt(0))?(n=n.slice(1),this.s=-1):(43===e&&(n=n.slice(1)),this.s=1),N.test(n)?H(this,n):function(n,i){var e,t,r,s,o,u,h,a,l;if(i.indexOf("_")>-1){if(i=i.replace(/(\d)_(?=\d)/g,"$1"),N.test(i))return H(n,i)}else if("Infinity"===i||"NaN"===i)return!+i&&(n.s=NaN),n.e=NaN,n.d=null,n;if(m.test(i))e=16,i=i.toLowerCase();else if(w.test(i))e=2;else if(v.test(i))e=8;else throw Error(f+i);for((s=i.search(/p/i))>0?(h=+i.slice(s+1),i=i.substring(2,s)):i=i.slice(2),o=(s=i.indexOf("."))>=0,t=n.constructor,o&&(s=(u=(i=i.replace(".","")).length)-s,r=R(t,new t(e),s,2*s)),s=l=(a=O(i,e,1e7)).length-1;0===a[s];--s)a.pop();return s<0?new t(0*n.s):(n.e=A(a,l),n.d=a,c=!1,o&&(n=F(n,r,4*u)),h&&(n=n.times(54>Math.abs(h)?p(2,h):nk.pow(2,h))),c=!0,n)}(this,n)}if(s.prototype=x,s.ROUND_UP=0,s.ROUND_DOWN=1,s.ROUND_CEIL=2,s.ROUND_FLOOR=3,s.ROUND_HALF_UP=4,s.ROUND_HALF_DOWN=5,s.ROUND_HALF_EVEN=6,s.ROUND_HALF_CEIL=7,s.ROUND_HALF_FLOOR=8,s.EUCLID=9,s.config=s.set=ns,s.clone=n,s.isDecimal=nl,s.abs=J,s.acos=z,s.acosh=G,s.add=K,s.asin=Q,s.asinh=X,s.atan=Y,s.atanh=nn,s.atan2=ni,s.cbrt=ne,s.ceil=nt,s.clamp=nr,s.cos=no,s.cosh=nu,s.div=nc,s.exp=nh,s.floor=nf,s.hypot=na,s.ln=nd,s.log=ng,s.log10=nw,s.log2=np,s.max=nm,s.min=nv,s.mod=nN,s.mul=nb,s.pow=nE,s.random=nx,s.round=ny,s.sign=nM,s.sin=nq,s.sinh=nO,s.sqrt=nF,s.sub=n_,s.sum=nZ,s.tan=nA,s.tanh=nD,s.trunc=nS,void 0===i&&(i={}),i&&!0!==i.defaults)for(e=0,r=["precision","rounding","toExpNeg","toExpPos","maxE","minE","modulo","crypto"];e<r.length;)!i.hasOwnProperty(t=r[e++])&&(i[t]=this[t]);return s.config(i),s}(u);s=new nk(s),o=new nk(o),i.Z=nk}}]);