"use strict";(self.webpackChunkweixin_search=self.webpackChunkweixin_search||[]).push([["src_components_general_source-panel_source-panel_vue"],{42971:function(t,e,n){n.r(e);var i=n(704909),r=n(420584),o=n(551900),c=n(700182),s=(0,o.Z)(r.Z,i.s,i.x,!1,null,"1401109b",null);"function"==typeof c.Z&&(0,c.Z)(s),e.default=s.exports},344645:function(t,e,n){n.d(e,{Z:function(){return i}});function i(t){t.options.__wxs_id="f41daaf3"}},191311:function(t,e,n){n.r(e);var i=n(950690),r=n(173443),o=n(551900),c=n(356939),s=(0,o.Z)(r.Z,i.s,i.x,!1,null,"cf6af3fa",null);"function"==typeof c.Z&&(0,c.Z)(s),e.default=s.exports},584918:function(t,e,n){n.d(e,{Z:function(){return i}});function i(t){t.options.__wxs_id="d2954635"}},15779:function(t,e,n){n.r(e);var i=n(419065),r=n(621467),o=n(551900),c=n(7440),s=(0,o.Z)(r.Z,i.s,i.x,!1,null,"0429b6ab",null);"function"==typeof c.Z&&(0,c.Z)(s),e.default=s.exports},994808:function(t,e,n){n.d(e,{Z:function(){return i}});function i(t){t.options.__wxs_id="f294bdc2"}},597950:function(t,e,n){n.r(e);var i=n(148439),r=n(708699),o=n(551900),c=n(601403),s=(0,o.Z)(r.Z,i.s,i.x,!1,null,"01dcfc15",null);"function"==typeof c.Z&&(0,c.Z)(s),e.default=s.exports},8514:function(t,e,n){n.d(e,{Z:function(){return i}});function i(t){t.options.__wxs_id="3b2de3ed"}},739199:function(t,e,n){n.r(e);var i=n(117149),r=n(206596),o=n(551900),c=n(455522),s=(0,o.Z)(r.Z,i.s,i.x,!1,null,"f65e7382",null);"function"==typeof c.Z&&(0,c.Z)(s),e.default=s.exports},405176:function(t,e,n){n.d(e,{Z:function(){return i}});function i(t){t.options.__wxs_id="2190cfd7"}},896831:function(t,e,n){n.r(e);var i=n(590664),r=n(122339),o=n(551900),c=n(477597),s=(0,o.Z)(r.Z,i.s,i.x,!1,null,"4c7e3929",null);"function"==typeof c.Z&&(0,c.Z)(s),e.default=s.exports},532272:function(t,e,n){n.d(e,{Z:function(){return i}});function i(t){t.options.__wxs_id="79d8f361"}},700182:function(t,e,n){var i=n(344645);e.Z=i.Z},356939:function(t,e,n){var i=n(584918);e.Z=i.Z},7440:function(t,e,n){var i=n(994808);e.Z=i.Z},601403:function(t,e,n){var i=n(8514);e.Z=i.Z},455522:function(t,e,n){var i=n(405176);e.Z=i.Z},477597:function(t,e,n){var i=n(532272);e.Z=i.Z},420584:function(t,e,n){var i=n(375185);e.Z=i.Z},173443:function(t,e,n){var i=n(676998);e.Z=i.Z},621467:function(t,e,n){var i=n(164260);e.Z=i.Z},708699:function(t,e,n){var i=n(344762);e.Z=i.Z},206596:function(t,e,n){var i=n(641297);e.Z=i.Z},122339:function(t,e,n){var i=n(460285);e.Z=i.Z},704909:function(t,e,n){n.d(e,{s:function(){return i},x:function(){return r}});var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"cell-service",attrs:{"data-fcn-cell-service":"","data-fr-1e5279792":""}},[t.itemInfo.title?n("p",{staticClass:"service-title",domProps:{innerHTML:t._s(t.xss(t.itemInfo.title))}}):t._e(),t.itemInfo.descList?n("ul",{staticClass:"service-desc-list"},t._l(t.itemInfo.descList,function(e){return n("li",{key:e,staticClass:"desc-item",domProps:{innerHTML:t._s(t.xss(e))}})}),0):t._e(),t._l(t.itemInfo.cellList,function(e){return n("div",{directives:[{name:"active",rawName:"v-active.stop",modifiers:{stop:!0}}],key:e.title,staticClass:"cell-item",class:{active__absolute:e.jumpInfo},attrs:{"data-report-id":t.M_itemReportId(e),"data-cli":""},on:{click:function(n){return n.stopPropagation(),t.onClickCellItem(e)}}},[n("ui-image",{staticClass:"cell-item__icon",attrs:{size:40,url:e.iconUrl,"allow-empty":t.allowIconEmpty,mode:"avatar","data-fc-1201a52da":""}}),n("div",{staticClass:"cell-item__middle"},[n("p",{staticClass:"cell-title",domProps:{innerHTML:t._s(t.xss(e.title))}}),n("p",{staticClass:"cell-desc",domProps:{innerHTML:t._s(t.xss(e.desc))}})]),e.button?n("ui-button",{directives:[{name:"active",rawName:"v-active.stop",modifiers:{stop:!0}}],staticClass:"cell-item__btn active__item",attrs:{title:e.button.title,type:0,"data-report-id":t.M_itemReportId(e.button),"data-fc-1201a52db":"","data-cli":""},nativeOn:{click:function(n){return n.stopPropagation(),t.onClickButton(e.button)}}}):t._e()],1)}),t.itemInfo.link?n("div",{directives:[{name:"active",rawName:"v-active.stop",modifiers:{stop:!0}}],staticClass:"service-link active__mask",attrs:{role:"link","data-report-id":t.M_itemReportId(t.itemInfo.link),"data-cli":""},on:{click:function(e){return e.stopPropagation(),t.onTapLink(t.itemInfo.link)}}},[t._v("\n      "+t._s(t.itemInfo.link.title)+"\n    ")]):t._e()],2)},r=[]},950690:function(t,e,n){n.d(e,{s:function(){return i},x:function(){return r}});var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"source-panel",attrs:{"data-fcn-source-panel":"","data-fr-171695137":""}},[n(t.subItemTypes[t.currentSource.itemInfo.type]||t.globalType[t.currentSource.itemInfo.type],t._b({tag:"component",attrs:{"data-fc-1887e544c":""},on:{"replace-renderItem":t.onReplaceRenderItem,"refresh:itemInfo":t.onRefreshItemInfo,"tap:refresh":t.onTapRefresh}},"component",Object.assign({},t.$props,{source:t.currentSource.itemInfo,parentItemPos:t.currentSource.source&&t.currentSource.source.reportId?t.M_parentItemPos(t.currentIndex+1+":"+t.M_getItemType(t.currentSource.source.reportId)):""}),!1)),t.currentSource.source?n("blockSource",{staticClass:"item-source",class:{"safe-spacer":t.currentSource.hasSafeSpacer},attrs:{source:t.currentSource.source,"data-report-id":t.M_itemReportId(t.currentSource.source.reportId,t.currentIndex+1),"data-fc-1887e544a":""},nativeOn:{click:function(e){return t.onTapSource(e)}},scopedSlots:t._u([t.canSourceChange||t.currentSource.source.endText?{key:"extra-info",fn:function(){return[t.canSourceChange?n("ui-arrow",{attrs:{direction:"down",align:"flex",gap:"","data-fc-1887e5448":""}}):t._e(),t.currentSource.source.endText?n("div",{staticClass:"end-text"},[t._v("\n          "+t._s(t.currentSource.source.endText)+"\n        ")]):t._e()]},proxy:!0}:null],null,!0)}):t._e()],1)},r=[]},419065:function(t,e,n){n.d(e,{s:function(){return i},x:function(){return r}});var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"container",attrs:{"data-fcn-square-division":"","data-fr-10fa39e1a":""}},[t._l(t.itemInfo.list,function(e,i){return n("div",{key:(e.title||"")+i,staticClass:"row"},[e.title?n("p",{staticClass:"title",domProps:{innerHTML:t._s(t.xss(e.title))}}):t._e(),n("ul",{staticClass:"cells",class:{cells__wrap:t.source.col&&e.cells.length>t.source.col}},t._l(e.cells,function(e,r){return n("li",{directives:[{name:"active",rawName:"v-active.stop",modifiers:{stop:!0}}],key:e.desc+r,ref:"cell",refInFor:!0,staticClass:"cell-item",class:{disabled:e.disabled,active__item:!e.disabled,last:t.source.col&&(r+1)%t.source.col==0},style:t.cellWidth,attrs:{"data-report-id":t.M_itemReportId(e.reportId,i+1+":list|"+(r+1)+":"+t.M_getItemType(e.reportId)),"data-cli":""},on:{click:function(n){return n.stopPropagation(),t.onTapCell(e)}}},[n("span",{staticClass:"ellipsis_2"},[t._v(t._s(e.desc))])])}),0)])}),t.source.action?n("div",{staticClass:"action"},[n("span",{directives:[{name:"active",rawName:"v-active.stop",modifiers:{stop:!0}}],staticClass:"active__mask expand-hotspot",attrs:{"data-report-id":t.M_itemReportId(t.source.action),"data-cli":""},on:{click:function(e){return e.stopPropagation(),t.onTapAction(e)}}},[t._v(t._s(t.source.action.title))])]):t._e()],2)},r=[]},148439:function(t,e,n){n.d(e,{s:function(){return i},x:function(){return r}});var i=function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"lazy-loading",attrs:{"data-fcn-lazy-loading":"","data-fr-10a99629e":""}},[e("span",{staticClass:"circle-loading lazy-loading-circle"}),e("p",{staticClass:"loading-text",attrs:{role:"option"}},[this._v(this._s(this.source.title))])])},r=[]},117149:function(t,e,n){n.d(e,{s:function(){return i},x:function(){return r}});var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"refresh-wrap",style:{height:t.innerHeight+"px"},attrs:{"data-report-id":t.M_itemReportId(t.source),"data-fcn-refresh":"","data-fr-10e52047d":""},on:{click:function(e){return t.onTapBody(t.source.jumpInfo)}}},[t.isLoading?n("lazy-loading",t._b({attrs:{"data-fc-18822e958":""}},"lazy-loading",Object.assign({},t.$props,{source:{title:"加载中"}}),!1)):n("div",{ref:"inner",staticClass:"inner"},[n("div",{staticClass:"title"},[t._v("\n        "+t._s(t.source.title)+"\n      ")]),n("div",{staticClass:"button"},[n("ui-button",{attrs:{type:t.source.richButton&&t.source.richButton.type||1,title:t.source.richButton&&t.source.richButton.title||t.source.button||"重新加载","data-report-id":t.M_itemReportId(t.source.richButton),"data-fc-18822e956":""},on:{tap:function(e){return e.stopPropagation(),t.tapButton(e)}}})],1)])],1)},r=[]},590664:function(t,e,n){n.d(e,{s:function(){return i},x:function(){return r}});var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"link-list",attrs:{"data-fcn-link-list":"","data-fr-1293a4194":""}},[t.source.title?n("p",{staticClass:"link-title",domProps:{innerHTML:t._s(t.xss(t.source.title))}}):t._e(),t._l(t.options,function(e,i){return n("div",{key:e.title,staticClass:"option",attrs:{role:"listbox"}},[e.title?n("div",{staticClass:"title ellipsis_1"},[t._v(t._s(e.title))]):t._e(),n("div",{staticClass:"spacer"}),t._l(e.actions,function(e,r){return n("a",{directives:[{name:"active",rawName:"v-active.stop",modifiers:{stop:!0}}],key:e.title,staticClass:"link-list-action",class:{"link-can-jump active__link":t.canJump(e)},attrs:{role:"button",tabindex:"-1","aria-disabled":!t.canJump(e),"data-report-id":t.M_itemReportId(e.reportId,r+1),"data-cli":""},on:{click:function(n){return n.stopPropagation(),t.onTapAction(e,r,i)}}},[t._v("\n        "+t._s(e.title)+"\n      ")])})],2)}),t.source.link&&t.showLink?n("div",{staticClass:"link-line"},[n("span",{directives:[{name:"active",rawName:"v-active.stop",modifiers:{stop:!0}}],staticClass:"active__mask",attrs:{"data-report-id":t.M_itemReportId(t.source.link.reportId),"data-cli":""},domProps:{innerHTML:t._s(t.xss(t.source.link.title))},on:{click:function(e){return e.stopPropagation(),t.onTapLink(t.source.link)}}})]):t._e()],2)},r=[]},375185:function(t,e,n){var i=n(798509);e.Z={name:"CellService",mixins:[i.jB,i.uW,i.Sx],data:function(){return{}},computed:{allowIconEmpty:function(){return 20971520===this.data.real_type&&10021===this.data.subType}},methods:{onClickCellItem:function(t){t.jumpInfo&&(this.M_go(t),this.M_clickReport({clickContent:t.title},t))},onClickButton:function(t){t.jumpInfo&&(this.M_go(t),this.M_clickReport({clickContent:t.title},t))},onTapLink:function(t){t.jumpInfo&&(this.M_go(t),this.M_clickReport({clickContent:t.title},t))}}}},676998:function(t,e,n){var i=n(798509),r=n(992826),o=n(896831),c=n(740115),s=n(15779),a=n(825601),u=n(739199),l=n(603043),f=n(136525),p=n(417655),d=n(984928),m=n(42971),h=n(689291);function _(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=Array(e);n<e;n++)i[n]=t[n];return i}function v(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter(function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable}))),i.forEach(function(e){var i,r,o;i=t,r=e,o=n[e],r in i?Object.defineProperty(i,r,{value:o,enumerable:!0,configurable:!0,writable:!0}):i[r]=o})}return t}function y(t,e){return e=null!=e?e:{},Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):(function(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);n.push.apply(n,i)}return n})(Object(e)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(e,n))}),t}function b(t){return function(t){if(Array.isArray(t))return _(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return _(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);if("Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return _(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}e.Z={name:"SourcePanel",components:{tabPanel:function(){var t=h.Z.startImport("tabPanel");return Promise.all([n.e("src_components_general_competition_schedule_vue"),n.e("src_views_result_block_service-search_service-search-item_sub-item_single-service_single-serv-78031f"),n.e("src_components_general_traffic_traffic-info_vue"),n.e("src_components_general_comm-widget_commodity_vue"),n.e("src_views_result_block_service-search_service-search-item_sub-item_basic-item_basic-item_vue"),n.e("src_views_result_block_service-search_service-search-item_sub-item_activity_single-module-ser-8828b7"),n.e("tabPanel")]).then(n.bind(n,531711)).then(function(e){return h.Z.afterImport(t,"tabPanel"),e})},searchBarPanel:function(){var t=h.Z.startImport("searchBarPanel");return n.e("searchBarPanel").then(n.bind(n,955588)).then(function(e){return h.Z.afterImport(t,"searchBarPanel"),e})},CellService:m.default,blockSource:r.default,linkList:o.default,gridTable:c.default,smartVolunteer:l.default,packageInfo:a.default,refresh:u.default,squareDivision:s.default},mixins:[i.jB,i.uW],props:{source:{type:Object,default:function(){return{}}},data:{type:Object,default:function(){return{}}},item:{type:Object,default:function(){return{}}},pos:{type:Number,default:0},typePos:{type:Number,default:0}},data:function(){return{subItemTypes:p._t,globalType:f.jJ,currentIndex:0,refreshItemInfo:null}},computed:{itemInfo:function(){return this.refreshItemInfo?this.refreshItemInfo:i.Zr.isObjectEmpty(this.source)?this.item:this.source},currentSource:function(){return(this.itemInfo.list||[])[this.currentIndex]||{}},canSourceChange:function(){return(this.itemInfo.list||[]).length>1},defaultItem:function(){var t=(this.itemInfo.list||[]).findIndex(function(t){return!!t.default});return t>=0?{index:t,key:this.itemInfo.list[t].default}:null}},watch:{item:{handler:function(){this.refreshItemInfo=null},immediate:!0},defaultItem:{handler:function(t,e){t&&(!e||e.key!==t.key)&&(this.currentIndex=t.index)},immediate:!0,deep:!0}},created:function(){var t;this.currentIndex=(null===(t=this.defaultItem)||void 0===t?void 0:t.index)||0},methods:{onRefreshItemInfo:function(t){console.log("itemInfo",t),this.refreshItemInfo=t,this.$nextTick(function(){i.Gc.$emit(i.U3.exposeAnalysis)})},onTapSource:function(){var t=this;if(!!this.canSourceChange)this.M_clickReport({clickContent:this.currentSource.source.title,actionType:d.At.CLICK_CP_CODE},this.currentSource.source),this.$store.commit({ctx:this,type:"updateActionSheet",title:this.currentSource.changeTitle||"选择服务提供方",desc:this.currentSource.changeDesc||"",style:1,sheets:this.itemInfo.list.map(function(t){return y(v({},t.source),{tag:t.tags||t.source&&t.source.tags})}),className:"service-search-item-action-sheet",clickHideCallback:function(e){t.M_clickReport(y(v({},e),{actionType:d.At.CLICK_CLOSE_DIALOG}),t.currentSource.source)},clickCallback:function(e){t.M_clickReport({clickContent:e.title,actionType:d.At.CONFIRM_CHANGE_CODE,cardExtInfo:{fromCode:t.currentSource.source.title,toCode:e.title}},t.currentSource.source),t.currentIndex=e.pos-1;var n=t.currentSource.source.cgi;n&&n.name&&i.hi.commonCgi({cgiName:n.name,data:n.params})}})},onTapRefresh:function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];this.$emit.apply(this,["tap:refresh"].concat(b(e)))},onReplaceRenderItem:function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];this.$emit.apply(this,["replace-renderItem"].concat(b(e)))}}}},164260:function(t,e,n){var i=n(798509);e.Z={name:"SquareDivision",mixins:[i.jB,i.uW],props:{source:{type:Object,default:function(){return{}}},data:{type:Object,default:function(){return{}}},item:{type:Object,default:function(){return{}}},pos:{type:Number,default:0},typePos:{type:Number,default:0}},computed:{itemInfo:function(){return i.Zr.isObjectEmpty(this.source)?this.item:this.source},cellWidth:function(){var t=this.source.col;return t&&t>1?{width:"calc((100% - ".concat((t-1)*8,"px) / ").concat(t,")")}:{flex:1}}},methods:{onTapCell:function(t){t.toast?i.Gc.$emit(i.U3.showToast,t.toast):!t.disabled&&this.M_serviceSearchGo(t),this.M_clickReport({clickContent:t.desc},t)},onTapAction:function(){this.source.action&&(this.M_go(this.source.action),this.M_clickReport({},this.source.action))}}}},344762:function(t,e){e.Z={name:"LazyLoading",props:{source:{type:Object,default:function(){return{}}},data:{type:Object,default:function(){return{}}},item:{type:Object,default:function(){return{}}},pos:{type:Number,default:0},typePos:{type:Number,default:0}}}},641297:function(t,e,n){var i=n(798509),r=n(984928),o=n(597950);e.Z={name:"Refresh",components:{LazyLoading:o.default},mixins:[i.jB,i.uW],props:{source:{type:Object,default:function(){return{}}},data:{type:Object,default:function(){return{}}},item:{type:Object,default:function(){return{}}},pos:{type:Number,default:0},typePos:{type:Number,default:0}},data:function(){return{innerHeight:-1,isLoading:!1}},watch:{source:{handler:function(){var t=this;this.$nextTick(function(){t.$refs.inner&&(t.innerHeight=t.$refs.inner.clientHeight),t.isLoading=!1})},immediate:!0}},methods:{onTapBody:function(t){t&&(this.M_serviceSearchGo(t),this.M_clickReport({clickZone:"".concat(this.M_getClickZonePath()),expand:this.M_getJumpSubScene(t)},this.source))},tapButton:function(){var t=this,e="".concat(this.M_getClickZonePath(),".button");this.M_storeClickInfo(),this.M_clickReport({clickZone:e,expand:this.M_getJumpSubScene(this.source),clickContent:this.source.button,actionType:r.At.CLICK_SEARCH_REFRESH},this.source.richButton),this.isLoading=!0,this.$emit("tap:refresh",this.source.richButton&&this.source.richButton.cgi,function(){t.isLoading=!1})}}}},460285:function(t,e,n){var i=n(798509),r=n(984928),o=n(462474);e.Z={name:"LinkList",mixins:[i.jB,i.uW],props:{source:{type:Object,default:function(){return{}}},data:{type:Object,default:function(){return{}}},item:{type:Object,default:function(){return{}}},pos:{type:Number,default:0},typePos:{type:Number,default:0}},data:function(){return{showLink:!0,showCount:0}},computed:{options:function(){return this.source.options&&this.source.options.slice(0,this.showCount)}},watch:{source:{handler:function(t){var e=t&&t.link||{showCount:0};e.showCount?this.showCount=e.showCount:this.showCount=this.source.options.length,this.showLink=!0},immediate:!0}},methods:{onTapAction:function(t,e,n){if(!this.canJump(t)){t.emptyWording&&o.Z.$emit(o.U.showToast,{text:t.emptyWording});return}var i="".concat(this.M_getClickZonePath(),".options[").concat(n,"].actions[").concat(e,"]");this.M_serviceSearchGo(t,i),this.M_clickReport({clickZone:i,clickContent:t.title||""},t)},canJump:function(t){return!!(t.jumpInfo||t.jumpType)},onTapLink:function(t){t.showCount?(this.showCount=this.source.options.length,this.showLink=!1,t.actionType=r.At.CLICK_UNFOLD):this.M_serviceSearchGo(t),this.M_clickReport({clickContent:t.title||""},t)}}}}}]);