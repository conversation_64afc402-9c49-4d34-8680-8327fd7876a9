"use strict";(self.webpackChunkweixin_search=self.webpackChunkweixin_search||[]).push([["vendors-node_modules_echarts_lib_chart_line_install_js"],{374771:function(t,e,i){var n=i(518299),o=i(19750),a=i(202953),s=i(707498),r=i(276868),l=i(623815),u=i(910904),h=i(807028),c=i(931918),p=i(694923),d=function(t){function e(e,i,n,o){var a=t.call(this)||this;return a.updateData(e,i,n,o),a}return(0,n.ZT)(e,t),e.prototype._createSymbol=function(t,e,i,n,a){this.removeAll();var s=(0,o.th)(t,-1,-1,2,2,null,a);s.attr({z2:100,culling:!0,scaleX:n[0]/2,scaleY:n[1]/2}),s.drift=g,this._symbolType=t,this.add(s)},e.prototype.stopSymbolAnimation=function(t){this.childAt(0).stopAnimation(null,t)},e.prototype.getSymbolType=function(){return this._symbolType},e.prototype.getSymbolPath=function(){return this.childAt(0)},e.prototype.highlight=function(){(0,l.fD)(this.childAt(0))},e.prototype.downplay=function(){(0,l.Mh)(this.childAt(0))},e.prototype.setZ=function(t,e){var i=this.childAt(0);i.zlevel=t,i.z=e},e.prototype.setDraggable=function(t,e){var i=this.childAt(0);i.draggable=t,i.cursor=!e&&t?"move":i.cursor},e.prototype.updateData=function(t,i,n,o){this.silent=!1;var s=t.getItemVisual(i,"symbol")||"circle",r=t.hostModel,l=e.getSymbolSize(t,i),u=s!==this._symbolType,h=o&&o.disableAnimation;if(u){var c=t.getItemVisual(i,"symbolKeepAspect");this._createSymbol(s,t,i,l,c)}else{var p=this.childAt(0);p.silent=!1;var d={scaleX:l[0]/2,scaleY:l[1]/2};h?p.attr(d):a.D(p,d,r,i),(0,a.Zi)(p)}if(this._updateCommon(t,i,l,n,o),u){var p=this.childAt(0);if(!h){var d={scaleX:this._sizeX,scaleY:this._sizeY,style:{opacity:p.style.opacity}};p.scaleX=p.scaleY=0,p.style.opacity=0,a.KZ(p,d,r,i)}}h&&this.childAt(0).stopAnimation("leave")},e.prototype._updateCommon=function(t,e,i,n,a){var s,r,d,g,f,m,y,v,b,S=this.childAt(0),_=t.hostModel;if(n&&(s=n.emphasisItemStyle,r=n.blurItemStyle,d=n.selectItemStyle,g=n.focus,f=n.blurScope,y=n.labelStatesModels,v=n.hoverScale,b=n.cursorStyle,m=n.emphasisDisabled),!n||t.hasItemOption){var x=n&&n.itemModel?n.itemModel:t.getItemModel(e),I=x.getModel("emphasis");s=I.getModel("itemStyle").getItemStyle(),d=x.getModel(["select","itemStyle"]).getItemStyle(),r=x.getModel(["blur","itemStyle"]).getItemStyle(),g=I.get("focus"),f=I.get("blurScope"),m=I.get("disabled"),y=(0,c.k3)(x),v=I.getShallow("scale"),b=x.getShallow("cursor")}var D=t.getItemVisual(e,"symbolRotate");S.attr("rotation",(D||0)*Math.PI/180||0);var M=(0,o.Cq)(t.getItemVisual(e,"symbolOffset"),i);M&&(S.x=M[0],S.y=M[1]),b&&S.attr("cursor",b);var k=t.getItemVisual(e,"style"),w=k.fill;if(S instanceof p.ZP){var A=S.style;S.useStyle((0,h.l7)({image:A.image,x:A.x,y:A.y,width:A.width,height:A.height},k))}else S.__isEmptyBrush?S.useStyle((0,h.l7)({},k)):S.useStyle(k),S.style.decal=null,S.setColor(w,a&&a.symbolInnerColor),S.style.strokeNoScale=!0;var P=t.getItemVisual(e,"liftZ"),N=this._z2;null!=P?null==N&&(this._z2=S.z2,S.z2+=P):null!=N&&(S.z2=N,this._z2=null);var L=a&&a.useNameLabel;(0,c.ni)(S,y,{labelFetcher:_,labelDataIndex:e,defaultText:function(e){return L?t.getName(e):(0,u.H)(t,e)},inheritColor:w,defaultOpacity:k.opacity});this._sizeX=i[0]/2,this._sizeY=i[1]/2;var O=S.ensureState("emphasis");O.style=s,S.ensureState("select").style=d,S.ensureState("blur").style=r;var T=null==v||!0===v?Math.max(1.1,3/this._sizeY):isFinite(v)&&v>0?+v:1;O.scaleX=this._sizeX*T,O.scaleY=this._sizeY*T,this.setSymbolScale(1),(0,l.k5)(this,g,f,m)},e.prototype.setSymbolScale=function(t){this.scaleX=this.scaleY=t},e.prototype.fadeOut=function(t,e,i){var n=this.childAt(0),o=(0,r.A)(this).dataIndex,s=i&&i.animation;if(this.silent=n.silent=!0,i&&i.fadeLabel){var l=n.getTextContent();l&&a.bX(l,{style:{opacity:0}},e,{dataIndex:o,removeOpt:s,cb:function(){n.removeTextContent()}})}else n.removeTextContent();a.bX(n,{style:{opacity:0},scaleX:0,scaleY:0},e,{dataIndex:o,cb:t,removeOpt:s})},e.getSymbolSize=function(t,e){return(0,o.zp)(t.getItemVisual(e,"symbolSize"))},e}(s.Z);function g(t,e){this.parent.drift(t,e)}e.Z=d},330488:function(t,e,i){var n=i(707498),o=i(202953),a=i(339738),s=i(374771),r=i(807028),l=i(931918);function u(t,e,i,n){return e&&!isNaN(e[0])&&!isNaN(e[1])&&!(n.isIgnore&&n.isIgnore(i))&&!(n.clipShape&&!n.clipShape.contain(e[0],e[1]))&&"none"!==t.getItemVisual(i,"symbol")}function h(t){return null!=t&&!(0,r.Kn)(t)&&(t={isIgnore:t}),t||{}}function c(t){var e=t.hostModel,i=e.getModel("emphasis");return{emphasisItemStyle:i.getModel("itemStyle").getItemStyle(),blurItemStyle:e.getModel(["blur","itemStyle"]).getItemStyle(),selectItemStyle:e.getModel(["select","itemStyle"]).getItemStyle(),focus:i.get("focus"),blurScope:i.get("blurScope"),emphasisDisabled:i.get("disabled"),hoverScale:i.get("scale"),labelStatesModels:(0,l.k3)(e),cursorStyle:e.get("cursor")}}var p=function(){function t(t){this.group=new n.Z,this._SymbolCtor=t||s.Z}return t.prototype.updateData=function(t,e){this._progressiveEls=null,e=h(e);var i=this.group,n=t.hostModel,a=this._data,s=this._SymbolCtor,r=e.disableAnimation,l=c(t),p={disableAnimation:r},d=e.getSymbolPoint||function(e){return t.getItemLayout(e)};!a&&i.removeAll(),t.diff(a).add(function(n){var o=d(n);if(u(t,o,n,e)){var a=new s(t,n,l,p);a.setPosition(o),t.setItemGraphicEl(n,a),i.add(a)}}).update(function(h,c){var g=a.getItemGraphicEl(c),f=d(h);if(!u(t,f,h,e)){i.remove(g);return}var m=t.getItemVisual(h,"symbol")||"circle",y=g&&g.getSymbolType&&g.getSymbolType();if(!g||y&&y!==m)i.remove(g),(g=new s(t,h,l,p)).setPosition(f);else{g.updateData(t,h,l,p);var v={x:f[0],y:f[1]};r?g.attr(v):o.D(g,v,n)}i.add(g),t.setItemGraphicEl(h,g)}).remove(function(t){var e=a.getItemGraphicEl(t);e&&e.fadeOut(function(){i.remove(e)},n)}).execute(),this._getSymbolPoint=d,this._data=t},t.prototype.updateLayout=function(){var t=this,e=this._data;e&&e.eachItemGraphicEl(function(e,i){var n=t._getSymbolPoint(i);e.setPosition(n),e.markRedraw()})},t.prototype.incrementalPrepareUpdate=function(t){this._seriesScope=c(t),this._data=null,this.group.removeAll()},t.prototype.incrementalUpdate=function(t,e,i){function n(t){!t.isGroup&&(t.incremental=!0,t.ensureState("emphasis").hoverLayer=!0)}this._progressiveEls=[],i=h(i);for(var o=t.start;o<t.end;o++){var a=e.getItemLayout(o);if(u(e,a,o,i)){var s=new this._SymbolCtor(e,o,this._seriesScope);s.traverse(n),s.setPosition(a),this.group.add(s),e.setItemGraphicEl(o,s),this._progressiveEls.push(s)}}},t.prototype.eachRendered=function(t){a.traverseElements(this._progressiveEls||this.group,t)},t.prototype.remove=function(t){var e=this.group,i=this._data;i&&t?i.eachItemGraphicEl(function(t){t.fadeOut(function(){e.remove(t)},i.hostModel)}):e.removeAll()},t}();e.Z=p},415389:function(t,e,i){var n=i(518299),o=i(878004),a=i(970597),s=i(19750),r=i(707498),l=function(t){function e(){var i=null!==t&&t.apply(this,arguments)||this;return i.type=e.type,i.hasSymbolVisual=!0,i}return(0,n.ZT)(e,t),e.prototype.getInitialData=function(t){return(0,o.Z)(null,this,{useEncodeDefaulter:!0})},e.prototype.getLegendIcon=function(t){var e=new r.Z,i=(0,s.th)("line",0,t.itemHeight/2,t.itemWidth,0,t.lineStyle.stroke,!1);e.add(i),i.setStyle(t.lineStyle);var n=this.getData().getVisual("symbol"),o=this.getData().getVisual("symbolRotate"),a="none"===n?"circle":n,l=.8*t.itemHeight,u=(0,s.th)(a,(t.itemWidth-l)/2,(t.itemHeight-l)/2,l,l,t.itemStyle.fill);e.add(u),u.setStyle(t.itemStyle);var h="inherit"===t.iconRotate?o:t.iconRotate||0;return u.rotation=h*Math.PI/180,u.setOrigin([t.itemWidth/2,t.itemHeight/2]),a.indexOf("empty")>-1&&(u.style.stroke=u.style.fill,u.style.fill="#fff",u.style.lineWidth=2),e},e.type="series.line",e.dependencies=["grid","polar"],e.defaultOption={z:3,coordinateSystem:"cartesian2d",legendHoverLink:!0,clip:!0,label:{position:"top"},endLabel:{show:!1,valueAnimation:!0,distance:8},lineStyle:{width:2,type:"solid"},emphasis:{scale:!0},step:!1,smooth:!1,smoothMonotone:null,symbol:"emptyCircle",symbolSize:4,symbolRotate:null,showSymbol:!0,showAllSymbol:"auto",connectNulls:!1,sampling:"none",animationEasing:"linear",progressive:0,hoverLayerThreshold:1/0,universalTransition:{divideShape:"clone"},triggerLineEvent:!1},e}(a.Z);e.Z=l},799950:function(t,e,i){var n=i(518299),o=i(807028),a=i(330488),s=i(374771),r=i(979312),l=i(550025),u=i(707498),h=i(202953),c=i(345262),p=i(133141),d=i(799522),g=i(804575),f=i(942908),m=i(434337),y=i(567622),v=i(623815),b=i(931918),S=i(910904),_=i(276868),x=i(901843),I=i(84164),D=i(529134);function M(t,e){if(t.length===e.length){for(var i=0;i<t.length;i++)if(t[i]!==e[i])return;return!0}}function k(t){for(var e=1/0,i=1/0,n=-1/0,o=-1/0,a=0;a<t.length;){var s=t[a++],r=t[a++];!isNaN(s)&&(e=Math.min(s,e),n=Math.max(s,n)),!isNaN(r)&&(i=Math.min(r,i),o=Math.max(r,o))}return[[e,i],[n,o]]}function w(t,e){var i=k(t),n=i[0],o=i[1],a=k(e),s=a[0],r=a[1];return Math.max(Math.abs(n[0]-s[0]),Math.abs(n[1]-s[1]),Math.abs(o[0]-r[0]),Math.abs(o[1]-r[1]))}function A(t){return o.hj(t)?t:t?.5:0}function P(t,e,i,n){var o=e.getBaseAxis(),a="x"===o.dim||"radius"===o.dim?0:1,s=[],r=0,l=[],u=[],h=[],c=[];if(n){for(r=0;r<t.length;r+=2)!isNaN(t[r])&&!isNaN(t[r+1])&&c.push(t[r],t[r+1]);t=c}for(r=0;r<t.length-2;r+=2)switch(h[0]=t[r+2],h[1]=t[r+3],u[0]=t[r],u[1]=t[r+1],s.push(u[0],u[1]),i){case"end":l[a]=h[a],l[1-a]=u[1-a],s.push(l[0],l[1]);break;case"middle":var p=(u[a]+h[a])/2,d=[];l[a]=d[a]=p,l[1-a]=u[1-a],d[1-a]=h[1-a],s.push(l[0],l[1]),s.push(d[0],d[1]);break;default:l[a]=u[a],l[1-a]=h[1-a],s.push(l[0],l[1])}return s.push(t[r++],t[r++]),s}function N(t,e){return[t[2*e],t[2*e+1]]}function L(t){if(t.get(["endLabel","show"]))return!0;for(var e=0;e<v.L1.length;e++)if(t.get([v.L1[e],"endLabel","show"]))return!0;return!1}function O(t,e,i,n){if(!(0,y.H)(e,"cartesian2d"))return(0,m.X0)(e,i,n);var o=n.getModel("endLabel"),a=o.get("valueAnimation"),s=n.getData(),r={lastFrameIndex:0},l=L(n)?function(i,n){t._endLabelOnDuring(i,n,s,r,a,o,e)}:null,u=e.getBaseAxis().isHorizontal(),h=(0,m.ID)(e,i,n,function(){var e=t._endLabel;e&&i&&null!=r.originalX&&e.attr({x:r.originalX,y:r.originalY})},l);if(!n.get("clip",!0)){var c=h.shape,p=Math.max(c.width,c.height);u?(c.y-=p,c.height+=2*p):(c.x-=p,c.width+=2*p)}return l&&l(1,h),h}var T=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return(0,n.ZT)(e,t),e.prototype.init=function(){var t=new u.Z,e=new a.Z;this.group.add(e.group),this._symbolDraw=e,this._lineGroup=t},e.prototype.render=function(t,e,i){var n,a=this,r=t.coordinateSystem,u=this.group,c=t.getData(),p=t.getModel("lineStyle"),d=t.getModel("areaStyle"),g=c.getLayout("points")||[],m="polar"===r.type,y=this._coordSys,b=this._symbolDraw,S=this._polyline,k=this._polygon,w=this._lineGroup,N=!e.ssr&&t.get("animation"),L=!d.isEmpty(),T=d.get("origin"),C=(0,f.s)(r,c,T),Z=L&&function(t,e,i){if(!i.valueDim)return[];for(var n=e.count(),o=(0,x.o)(2*n),a=0;a<n;a++){var s=(0,f.q)(i,t,e,a);o[2*a]=s[0],o[2*a+1]=s[1]}return o}(r,c,C),z=t.get("showSymbol"),E=t.get("connectNulls"),G=z&&!m&&function(t,e,i){var n=t.get("showAllSymbol"),a="auto"===n;if(n&&!a)return;var r=i.getAxesByScale("ordinal")[0];if(!(!r||a&&function(t,e){var i=t.getExtent(),n=Math.abs(i[1]-i[0])/t.scale.count();isNaN(n)&&(n=0);for(var o=e.count(),a=Math.max(1,Math.round(o/5)),r=0;r<o;r+=a)if(1.5*s.Z.getSymbolSize(e,r)[t.isHorizontal()?1:0]>n)return!1;return!0}(r,e))){var l=e.mapDimension(r.dim),u={};return o.S6(r.getViewLabels(),function(t){u[r.scale.getRawOrdinalNumber(t.tickValue)]=1}),function(t){return!u.hasOwnProperty(e.get(l,t))}}}(t,c,r),V=this._data;V&&V.eachItemGraphicEl(function(t,e){t.__temp&&(u.remove(t),V.setItemGraphicEl(e,null))}),!z&&b.remove(),u.add(w);var R=!m&&t.get("step");r&&r.getArea&&t.get("clip",!0)&&(null!=(n=r.getArea()).width?(n.x-=.1,n.y-=.1,n.width+=.2,n.height+=.2):n.r0&&(n.r0-=.5,n.r+=.5)),this._clipShapeForSymbol=n;var X=function(t,e,i){var n,a,s=t.getVisual("visualMeta");if(!s||!s.length||!t.count()||"cartesian2d"!==e.type)return;for(var r=s.length-1;r>=0;r--){var u=t.getDimensionInfo(s[r].dimension);if("x"===(n=u&&u.coordDim)||"y"===n){a=s[r];break}}if(!!a){var h=e.getAxis(n),c=o.UI(a.stops,function(t){return{coord:h.toGlobalCoord(h.dataToCoord(t.value)),color:t.color}}),p=c.length,d=a.outerColors.slice();p&&c[0].coord>c[p-1].coord&&(c.reverse(),d.reverse());var g=function(t,e){var i,n,o=[],a=t.length;function s(t,e,i){var n=t.coord,o=(i-n)/(e.coord-n);return{coord:i,color:(0,D.t7)(o,[t.color,e.color])}}for(var r=0;r<a;r++){var l=t[r],u=l.coord;if(u<0)i=l;else if(u>e){n?o.push(s(n,l,e)):i&&o.push(s(i,l,0),s(i,l,e));break}else i&&(o.push(s(i,l,0)),i=null),o.push(l),n=l}return o}(c,"x"===n?i.getWidth():i.getHeight()),f=g.length;if(!f&&p)return c[0].coord<0?d[1]?d[1]:c[p-1].color:d[0]?d[0]:c[0].color;var m=g[0].coord-10,y=g[f-1].coord+10,v=y-m;if(v<.001)return"transparent";o.S6(g,function(t){t.offset=(t.coord-m)/v}),g.push({offset:f?g[f-1].offset:.5,color:d[1]||"transparent"}),g.unshift({offset:f?g[0].offset:.5,color:d[0]||"transparent"});var b=new l.Z(0,0,0,0,g,!0);return b[n]=m,b[n+"2"]=y,b}}(c,r,i)||c.getVisual("style")[c.getVisual("drawType")];if(S&&y.type===r.type&&R===this._step){L&&!k?k=this._newPolygon(g,Z):k&&!L&&(w.remove(k),k=this._polygon=null),!m&&this._initOrUpdateEndLabel(t,r,(0,I.Lz)(X));var H=w.getClipPath();if(H){var Y=O(this,r,!1,t);h.KZ(H,{shape:Y.shape},t)}else w.setClipPath(O(this,r,!0,t));z&&b.updateData(c,{isIgnore:G,clipShape:n,disableAnimation:!0,getSymbolPoint:function(t){return[g[2*t],g[2*t+1]]}}),(!M(this._stackedOnPoints,Z)||!M(this._points,g))&&(N?this._doUpdateAnimation(c,Z,r,i,R,T,E):(R&&(g=P(g,r,R,E),Z&&(Z=P(Z,r,R,E))),S.setShape({points:g}),k&&k.setShape({points:g,stackedOnPoints:Z})))}else z&&b.updateData(c,{isIgnore:G,clipShape:n,disableAnimation:!0,getSymbolPoint:function(t){return[g[2*t],g[2*t+1]]}}),N&&this._initSymbolLabelAnimation(c,r,n),R&&(g=P(g,r,R,E),Z&&(Z=P(Z,r,R,E))),S=this._newPolyline(g),L?k=this._newPolygon(g,Z):k&&(w.remove(k),k=this._polygon=null),!m&&this._initOrUpdateEndLabel(t,r,(0,I.Lz)(X)),w.setClipPath(O(this,r,!0,t));var F=t.getModel("emphasis"),U=F.get("focus"),W=F.get("blurScope"),B=F.get("disabled");S.useStyle(o.ce(p.getLineStyle(),{fill:"none",stroke:X,lineJoin:"bevel"})),(0,v.WO)(S,t,"lineStyle"),S.style.lineWidth>0&&"bolder"===t.get(["emphasis","lineStyle","width"])&&(S.getState("emphasis").style.lineWidth=+S.style.lineWidth+1),(0,_.A)(S).seriesIndex=t.seriesIndex,(0,v.k5)(S,U,W,B);var q=A(t.get("smooth")),K=t.get("smoothMonotone");if(S.setShape({smooth:q,smoothMonotone:K,connectNulls:E}),k){var j=c.getCalculationInfo("stackedOnSeries"),J=0;k.useStyle(o.ce(d.getAreaStyle(),{fill:X,opacity:.7,lineJoin:"bevel",decal:c.getVisual("style").decal})),j&&(J=A(j.get("smooth"))),k.setShape({smooth:q,stackedOnSmooth:J,smoothMonotone:K,connectNulls:E}),(0,v.WO)(k,t,"areaStyle"),(0,_.A)(k).seriesIndex=t.seriesIndex,(0,v.k5)(k,U,W,B)}var Q=function(t){a._changePolyState(t)};c.eachItemGraphicEl(function(t){t&&(t.onHoverStateChange=Q)}),this._polyline.onHoverStateChange=Q,this._data=c,this._coordSys=r,this._stackedOnPoints=Z,this._points=g,this._step=R,this._valueOrigin=T,t.get("triggerLineEvent")&&(this.packEventData(t,S),k&&this.packEventData(t,k))},e.prototype.packEventData=function(t,e){(0,_.A)(e).eventData={componentType:"series",componentSubType:"line",componentIndex:t.componentIndex,seriesIndex:t.seriesIndex,seriesName:t.name,seriesType:"line"}},e.prototype.highlight=function(t,e,i,n){var o=t.getData(),a=p.gO(o,n);if(this._changePolyState("emphasis"),a instanceof Array||null==a||!(a>=0))g.Z.prototype.highlight.call(this,t,e,i,n);else{var r=o.getLayout("points"),l=o.getItemGraphicEl(a);if(!l){var u=r[2*a],h=r[2*a+1];if(isNaN(u)||isNaN(h)||this._clipShapeForSymbol&&!this._clipShapeForSymbol.contain(u,h))return;var c=t.get("zlevel")||0,d=t.get("z")||0;(l=new s.Z(o,a)).x=u,l.y=h,l.setZ(c,d);var f=l.getSymbolPath().getTextContent();f&&(f.zlevel=c,f.z=d,f.z2=this._polyline.z2+1),l.__temp=!0,o.setItemGraphicEl(a,l),l.stopSymbolAnimation(!0),this.group.add(l)}l.highlight()}},e.prototype.downplay=function(t,e,i,n){var o=t.getData(),a=p.gO(o,n);if(this._changePolyState("normal"),null!=a&&a>=0){var s=o.getItemGraphicEl(a);s&&(s.__temp?(o.setItemGraphicEl(a,null),this.group.remove(s)):s.downplay())}else g.Z.prototype.downplay.call(this,t,e,i,n)},e.prototype._changePolyState=function(t){var e=this._polygon;(0,v.Gl)(this._polyline,t),e&&(0,v.Gl)(e,t)},e.prototype._newPolyline=function(t){var e=this._polyline;return e&&this._lineGroup.remove(e),e=new d.X({shape:{points:t},segmentIgnoreThreshold:2,z2:10}),this._lineGroup.add(e),this._polyline=e,e},e.prototype._newPolygon=function(t,e){var i=this._polygon;return i&&this._lineGroup.remove(i),i=new d.K({shape:{points:t,stackedOnPoints:e},segmentIgnoreThreshold:2}),this._lineGroup.add(i),this._polygon=i,i},e.prototype._initSymbolLabelAnimation=function(t,e,i){var n,a,s=e.getBaseAxis(),r=s.inverse;"cartesian2d"===e.type?(n=s.isHorizontal(),a=!1):"polar"===e.type&&(n="angle"===s.dim,a=!0);var l=t.hostModel,u=l.get("animationDuration");o.mf(u)&&(u=u(null));var h=l.get("animationDelay")||0,c=o.mf(h)?h(null):h;t.eachItemGraphicEl(function(t,s){if(t){var l=[t.x,t.y],p=void 0,d=void 0,g=void 0;if(i){if(a){var f=e.pointToCoord(l);n?(p=i.startAngle,d=i.endAngle,g=-f[1]/180*Math.PI):(p=i.r0,d=i.r,g=f[0])}else n?(p=i.x,d=i.x+i.width,g=t.x):(p=i.y+i.height,d=i.y,g=t.y)}var m=d===p?0:(g-p)/(d-p);r&&(m=1-m);var y=o.mf(h)?h(s):u*m+c,v=t.getSymbolPath(),b=v.getTextContent();t.attr({scaleX:0,scaleY:0}),t.animateTo({scaleX:1,scaleY:1},{duration:200,setToFinal:!0,delay:y}),b&&b.animateFrom({style:{opacity:0}},{duration:300,delay:y}),v.disableLabelAnimation=!0}})},e.prototype._initOrUpdateEndLabel=function(t,e,i){var n=t.getModel("endLabel");if(L(t)){var o,a,s,r,l=t.getData(),u=this._polyline,h=l.getLayout("points");if(!h){u.removeTextContent(),this._endLabel=null;return}var p=this._endLabel;!p&&((p=this._endLabel=new c.ZP({z2:200})).ignoreClip=!0,u.setTextContent(this._endLabel),u.disableLabelAnimation=!0);var d=function(t){for(var e,i,n=t.length/2;n>0;n--){;if(e=t[2*n-2],i=t[2*n-1],!(isNaN(e)||isNaN(i)))break}return n-1}(h);if(d>=0){;(0,b.ni)(u,(0,b.k3)(t,"endLabel"),{inheritColor:i,labelFetcher:t,labelDataIndex:d,defaultText:function(t,e,i){return null!=i?(0,S.O)(l,i):(0,S.H)(l,t)},enableTextSetter:!0},(o=n,s=(a=e.getBaseAxis()).isHorizontal(),r=a.inverse,{normal:{align:o.get("align")||(s?r?"right":"left":"center"),verticalAlign:o.get("verticalAlign")||(s?"middle":r?"top":"bottom")}})),u.textConfig.position=null}}else this._endLabel&&(this._polyline.removeTextContent(),this._endLabel=null)},e.prototype._endLabelOnDuring=function(t,e,i,n,o,a,s){var r=this._endLabel,l=this._polyline;if(r){t<1&&null==n.originalX&&(n.originalX=r.x,n.originalY=r.y);var u=i.getLayout("points"),h=i.hostModel,c=h.get("connectNulls"),d=a.get("precision"),g=a.get("distance")||0,f=s.getBaseAxis(),m=f.isHorizontal(),y=f.inverse,v=e.shape,S=y?m?v.x:v.y+v.height:m?v.x+v.width:v.y,_=(m?g:0)*(y?-1:1),x=(m?0:-g)*(y?-1:1),I=m?"x":"y",D=function(t,e,i){for(var n,o,a=t.length/2,s="x"===i?0:1,r=0,l=-1,u=0;u<a;u++){if(!(isNaN(o=t[2*u+s])||isNaN(t[2*u+1-s]))){if(0===u){n=o;continue}if(n<=e&&o>=e||n>=e&&o<=e){l=u;break}r=u,n=o}}return{range:[r,l],t:(e-n)/(o-n)}}(u,S,I),M=D.range,k=M[1]-M[0],w=void 0;if(k>=1){if(k>1&&!c){var A=N(u,M[0]);r.attr({x:A[0]+_,y:A[1]+x}),o&&(w=h.getRawValue(M[0]))}else{var A=l.getPointOn(S,I);A&&r.attr({x:A[0]+_,y:A[1]+x});var P=h.getRawValue(M[0]),L=h.getRawValue(M[1]);o&&(w=p.pk(i,d,P,L,D.t))}n.lastFrameIndex=M[0]}else{var O=1===t||n.lastFrameIndex>0?M[0]:0,A=N(u,O);o&&(w=h.getRawValue(O)),r.attr({x:A[0]+_,y:A[1]+x})}if(o){var T=(0,b.qA)(r);"function"==typeof T.setLabelText&&T.setLabelText(w)}}},e.prototype._doUpdateAnimation=function(t,e,i,n,o,a,s){var l=this._polyline,u=this._polygon,c=t.hostModel,p=(0,r.Z)(this._data,t,this._stackedOnPoints,e,this._coordSys,i,this._valueOrigin,a),d=p.current,g=p.stackedOnCurrent,f=p.next,m=p.stackedOnNext;if(o&&(d=P(p.current,i,o,s),g=P(p.stackedOnCurrent,i,o,s),f=P(p.next,i,o,s),m=P(p.stackedOnNext,i,o,s)),w(d,f)>3e3||u&&w(g,m)>3e3){l.stopAnimation(),l.setShape({points:f}),u&&(u.stopAnimation(),u.setShape({points:f,stackedOnPoints:m}));return}l.shape.__points=p.current,l.shape.points=d;var y={shape:{points:f}};p.current!==d&&(y.shape.__points=p.next),l.stopAnimation(),h.D(l,y,c),u&&(u.setShape({points:d,stackedOnPoints:g}),u.stopAnimation(),h.D(u,{shape:{stackedOnPoints:m}},c),l.shape.points!==u.shape.points&&(u.shape.points=l.shape.points));for(var v=[],b=p.status,S=0;S<b.length;S++)if("="===b[S].cmd){var _=t.getItemGraphicEl(b[S].idx1);_&&v.push({el:_,ptIdx:S})}l.animators&&l.animators.length&&l.animators[0].during(function(){u&&u.dirtyShape();for(var t=l.shape.__points,e=0;e<v.length;e++){var i=v[e].el,n=2*v[e].ptIdx;i.x=t[n],i.y=t[n+1],i.markRedraw()}})},e.prototype.remove=function(t){var e=this.group,i=this._data;this._lineGroup.removeAll(),this._symbolDraw.remove(!0),i&&i.eachItemGraphicEl(function(t,n){t.__temp&&(e.remove(t),i.setItemGraphicEl(n,null))}),this._polyline=this._polygon=this._coordSys=this._points=this._stackedOnPoints=this._endLabel=this._data=null},e.type="line",e}(g.Z);e.Z=T},942908:function(t,e,i){i.d(e,{q:function(){return s},s:function(){return a}});var n=i(254308),o=i(807028);function a(t,e,i){var a=t.getBaseAxis(),s=t.getOtherAxis(a),r=function(t,e){var i=0,n=t.scale.getExtent();return"start"===e?i=n[0]:"end"===e?i=n[1]:(0,o.hj)(e)&&!isNaN(e)?i=e:n[0]>0?i=n[0]:n[1]<0&&(i=n[1]),i}(s,i),l=a.dim,u=s.dim,h=e.mapDimension(u),c=e.mapDimension(l),p="x"===u||"radius"===u?1:0,d=(0,o.UI)(t.dimensions,function(t){return e.mapDimension(t)}),g=!1,f=e.getCalculationInfo("stackResultDimension");return(0,n.M)(e,d[0])&&(g=!0,d[0]=f),(0,n.M)(e,d[1])&&(g=!0,d[1]=f),{dataDimsForPoint:d,valueStart:r,valueAxisDim:u,baseAxisDim:l,stacked:!!g,valueDim:h,baseDim:c,baseDataOffset:p,stackedOverDimension:e.getCalculationInfo("stackedOverDimension")}}function s(t,e,i,n){var o=NaN;t.stacked&&(o=i.get(i.getCalculationInfo("stackedOverDimension"),n)),isNaN(o)&&(o=t.valueStart);var a=t.baseDataOffset,s=[];return s[a]=i.get(t.baseDim,n),s[1-a]=o,e.dataToPoint(s)}},346348:function(t,e,i){i.d(e,{N:function(){return r}});var n=i(415389),o=i(799950),a=i(646291),s=i(951092);function r(t){t.registerChartView(o.Z),t.registerSeriesModel(n.Z),t.registerLayout((0,a.Z)("line",!0)),t.registerVisual({seriesType:"line",reset:function(t){var e=t.getData(),i=t.getModel("lineStyle").getLineStyle();i&&!i.stroke&&(i.stroke=e.getVisual("style").fill),e.setVisual("legendLineStyle",i)}}),t.registerProcessor(t.PRIORITY.PROCESSOR.STATISTIC,(0,s.Z)("line"))}},979312:function(t,e,i){i.d(e,{Z:function(){return a}});var n=i(942908),o=i(901843);function a(t,e,i,a,s,r,l,u){for(var h,c,p=(h=t,c=[],e.diff(h).add(function(t){c.push({cmd:"+",idx:t})}).update(function(t,e){c.push({cmd:"=",idx:e,idx1:t})}).remove(function(t){c.push({cmd:"-",idx:t})}).execute(),c),d=[],g=[],f=[],m=[],y=[],v=[],b=[],S=(0,n.s)(s,e,l),_=t.getLayout("points")||[],x=e.getLayout("points")||[],I=0;I<p.length;I++){var D=p[I],M=!0,k=void 0,w=void 0;switch(D.cmd){case"=":k=2*D.idx,w=2*D.idx1;var A=_[k],P=_[k+1],N=x[w],L=x[w+1];(isNaN(A)||isNaN(P))&&(A=N,P=L),d.push(A,P),g.push(N,L),f.push(i[k],i[k+1]),m.push(a[w],a[w+1]),b.push(e.getRawIndex(D.idx1));break;case"+":var O=D.idx,T=S.dataDimsForPoint,C=s.dataToPoint([e.get(T[0],O),e.get(T[1],O)]);w=2*O,d.push(C[0],C[1]),g.push(x[w],x[w+1]);var Z=(0,n.q)(S,s,e,O);f.push(Z[0],Z[1]),m.push(a[w],a[w+1]),b.push(e.getRawIndex(O));break;case"-":M=!1}M&&(y.push(D),v.push(v.length))}v.sort(function(t,e){return b[t]-b[e]});for(var z=d.length,E=(0,o.o)(z),G=(0,o.o)(z),V=(0,o.o)(z),R=(0,o.o)(z),X=[],I=0;I<v.length;I++){var H=v[I],Y=2*I,F=2*H;E[Y]=d[F],E[Y+1]=d[F+1],G[Y]=g[F],G[Y+1]=g[F+1],V[Y]=f[F],V[Y+1]=f[F+1],R[Y]=m[F],R[Y+1]=m[F+1],X[I]=y[H]}return{current:E,next:G,stackedOnCurrent:V,stackedOnNext:R,status:X}}},799522:function(t,e,i){i.d(e,{K:function(){return g},X:function(){return p}});var n=i(518299),o=i(894641),a=i(351415),s=i(343556),r=Math.min,l=Math.max;function u(t,e){return isNaN(t)||isNaN(e)}function h(t,e,i,n,o,a,s,h,c){for(var p,d,g,f,m,y,v=i,b=0;b<n;b++){var S=e[2*v],_=e[2*v+1];if(v>=o||v<0)break;if(u(S,_)){if(c){v+=a;continue}break}if(v===i)t[a>0?"moveTo":"lineTo"](S,_),g=S,f=_;else{var x=S-p,I=_-d;if(x*x+I*I<.5){v+=a;continue}if(s>0){for(var D=v+a,M=e[2*D],k=e[2*D+1];M===S&&k===_&&b<n;)b++,D+=a,v+=a,M=e[2*D],k=e[2*D+1],S=e[2*v],_=e[2*v+1],x=S-p,I=_-d;var w=b+1;if(c)for(;u(M,k)&&w<n;)w++,D+=a,M=e[2*D],k=e[2*D+1];var A=.5,P=0,N=0,L=void 0,O=void 0;if(w>=n||u(M,k))m=S,y=_;else{P=M-p,N=k-d;var T=S-p,C=M-S,Z=_-d,z=k-_,E=void 0,G=void 0;if("x"===h){E=Math.abs(T),G=Math.abs(C);var V=P>0?1:-1;m=S-V*E*s,y=_,L=S+V*G*s,O=_}else if("y"===h){E=Math.abs(Z),G=Math.abs(z);var R=N>0?1:-1;m=S,y=_-R*E*s,L=S,O=_+R*G*s}else E=Math.sqrt(T*T+Z*Z),m=S-P*s*(1-(A=(G=Math.sqrt(C*C+z*z))/(G+E))),y=_-N*s*(1-A),L=S+P*s*A,O=_+N*s*A,L=r(L,l(M,S)),O=r(O,l(k,_)),L=l(L,r(M,S)),O=l(O,r(k,_)),P=L-S,N=O-_,m=S-P*E/G,y=_-N*E/G,m=r(m,l(p,S)),y=r(y,l(d,_)),m=l(m,r(p,S)),y=l(y,r(d,_)),P=S-m,N=_-y,L=S+P*G/E,O=_+N*G/E}t.bezierCurveTo(g,f,m,y,S,_),g=L,f=O}else t.lineTo(S,_)}p=S,d=_,v+=a}return b}var c=function(){this.smooth=0,this.smoothConstraint=!0},p=function(t){function e(e){var i=t.call(this,e)||this;return i.type="ec-polyline",i}return(0,n.ZT)(e,t),e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new c},e.prototype.buildPath=function(t,e){var i=e.points,n=0,o=i.length/2;if(e.connectNulls){for(;o>0&&u(i[2*o-2],i[2*o-1]);o--);for(;n<o&&u(i[2*n],i[2*n+1]);n++);}for(;n<o;)n+=h(t,i,n,o,o,1,e.smooth,e.smoothMonotone,e.connectNulls)+1},e.prototype.getPointOn=function(t,e){!this.path&&(this.createPathProxy(),this.buildPath(this.path,this.shape));for(var i,n,o=this.path.data,r=a.Z.CMD,l="x"===e,u=[],h=0;h<o.length;){var c=o[h++],p=void 0,d=void 0,g=void 0,f=void 0,m=void 0,y=void 0,v=void 0;switch(c){case r.M:i=o[h++],n=o[h++];break;case r.L:if(p=o[h++],d=o[h++],(v=l?(t-i)/(p-i):(t-n)/(d-n))<=1&&v>=0){var b=l?(d-n)*v+n:(p-i)*v+i;return l?[t,b]:[b,t]}i=p,n=d;break;case r.C:p=o[h++],d=o[h++],g=o[h++],f=o[h++],m=o[h++],y=o[h++];var S=l?(0,s.kD)(i,p,g,m,t,u):(0,s.kD)(n,d,f,y,t,u);if(S>0)for(var _=0;_<S;_++){var x=u[_];if(x<=1&&x>=0){var b=l?(0,s.af)(n,d,f,y,x):(0,s.af)(i,p,g,m,x);return l?[t,b]:[b,t]}}i=m,n=y}}},e}(o.ZP),d=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return(0,n.ZT)(e,t),e}(c),g=function(t){function e(e){var i=t.call(this,e)||this;return i.type="ec-polygon",i}return(0,n.ZT)(e,t),e.prototype.getDefaultShape=function(){return new d},e.prototype.buildPath=function(t,e){var i=e.points,n=e.stackedOnPoints,o=0,a=i.length/2,s=e.smoothMonotone;if(e.connectNulls){for(;a>0&&u(i[2*a-2],i[2*a-1]);a--);for(;o<a&&u(i[2*o],i[2*o+1]);o++);}for(;o<a;){var r=h(t,i,o,a,a,1,e.smooth,s,e.connectNulls);h(t,n,o+r-1,r,a,-1,e.stackedOnSmooth,s,e.connectNulls),o+=r+1,t.closePath()}},e}(o.ZP)},646291:function(t,e,i){i.d(e,{Z:function(){return r}});var n=i(807028),o=i(685043),a=i(254308),s=i(901843);function r(t,e){return{seriesType:t,plan:(0,o.Z)(),reset:function(t){var i=t.getData(),o=t.coordinateSystem,r=t.pipelineContext,l=e||r.large;if(!!o){var u=(0,n.UI)(o.dimensions,function(t){return i.mapDimension(t)}).slice(0,2),h=u.length,c=i.getCalculationInfo("stackResultDimension");(0,a.M)(i,u[0])&&(u[0]=c),(0,a.M)(i,u[1])&&(u[1]=c);var p=i.getStore(),d=i.getDimensionIndex(u[0]),g=i.getDimensionIndex(u[1]);return h&&{progress:function(t,e){for(var i=t.end-t.start,n=l&&(0,s.o)(i*h),a=[],r=[],u=t.start,c=0;u<t.end;u++){var f=void 0;if(1===h){var m=p.get(d,u);f=o.dataToPoint(m,null,r)}else a[0]=p.get(d,u),a[1]=p.get(g,u),f=o.dataToPoint(a,null,r);l?(n[c++]=f[0],n[c++]=f[1]):e.setItemLayout(u,f.slice())}l&&e.setLayout("points",n)}}}}}}}}]);