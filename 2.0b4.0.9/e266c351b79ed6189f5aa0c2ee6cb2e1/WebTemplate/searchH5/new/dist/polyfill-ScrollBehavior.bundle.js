(self.webpackChunkweixin_search=self.webpackChunkweixin_search||[]).push([["polyfill-ScrollBehavior"],{8662:function(){!function(){"use strict";var t="undefined"==typeof window,e=!t&&"scrollBehavior"in document.documentElement.style,o=function(){return(o=Object.assign||function(t){for(var e,o=1,l=arguments.length;o<l;o++)for(var r in e=arguments[o],e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}).apply(this,arguments)};function l(t,e){var o="function"==typeof Symbol&&t[Symbol.iterator];if(!o)return t;var l,r,n=o.call(t),i=[];try{for(;(void 0===e||e-- >0)&&!(l=n.next()).done;)i.push(l.value)}catch(t){r={error:t}}finally{try{l&&!l.done&&(o=n.return)&&o.call(n)}finally{if(r)throw r.error}}return i}function r(){return null!=document.scrollingElement?document.scrollingElement:document.documentElement}var n="scroll-behavior",i=RegExp(n+":\\s*([^;]*)");function s(t,e){var o=n+":"+e,l=t.getAttribute("style");if(null==l||""===l){t.setAttribute("style",o);return}var r=c(t);if(null!=r){var i=n+":"+r;l=(l=l.replace(i+";","")).replace(i,"")}t.setAttribute("style",l.endsWith(";")?""+l+o:";"+l+o)}function c(t){var e=t.getAttribute("style");if(null!=e&&e.includes(n)){var o=e.match(i);if(null!=o){var r=l(o,2)[1];if(null!=r&&""!==r)return r}}}function a(t,e){if(null!=e&&"smooth"===e.behavior)return"smooth";var o,l="style"in t?t:r();if("style"in l){var n=l.style.scrollBehavior;null!=n&&""!==n&&(o=n)}if(null==o){var i=l.getAttribute("scroll-behavior");null!=i&&""!==i&&(o=i)}if(null==o&&(o=c(l)),null==o){var s=getComputedStyle(l).getPropertyValue("scrollBehavior");null!=s&&""!==s&&(o=s)}return o}function u(t){return .5*(1-Math.cos(Math.PI*t))}var f={reset:function(){}},d="undefined"==typeof WeakMap?void 0:new WeakMap,p=t?void 0:Element.prototype.scroll,h=t?void 0:window.scroll,y=t?void 0:Element.prototype.scrollBy,v=t?void 0:window.scrollBy,w=t?void 0:Element.prototype.scrollTo,m=t?void 0:window.scrollTo;function g(t,e){this.__adjustingScrollPosition=!0,this.scrollLeft=t,this.scrollTop=e,delete this.__adjustingScrollPosition}function b(t,e){return g.call(this,t,e)}function S(t,e){this.__adjustingScrollPosition=!0,this.scrollLeft+=t,this.scrollTop+=e,delete this.__adjustingScrollPosition}function T(t,e){switch(t){case"scroll":if(!(e instanceof Element))return h;if(null!=p)return p;return g;case"scrollBy":if(!(e instanceof Element))return v;if(null!=y)return y;return S;case"scrollTo":if(!(e instanceof Element))return m;if(null!=w)return w;return b}}function E(t){if(null==t)return 0;if("number"==typeof t)return t;if("string"==typeof t)return parseFloat(t);else return 0}function M(t){return null!=t&&"object"==typeof t}function B(t,l,n,i){(function(t,o,l){var n,i,p,h,y,v,w,m,g,b,S,E,M,B=a(o,t);if(null==B||"auto"===B)T(l,o).call(o,t.left,t.top);else{;i=(n=function(t,e,o,l){var n="performance"in window?performance.now():Date.now();if(t instanceof Element){var i=t.scrollLeft,s=t.scrollTop,c=i,a=s;return{startTime:n,startX:c,startY:a,endX:Math.floor("scrollBy"===l?c+e:e),endY:Math.floor("scrollBy"===l?a+o:o),method:T("scrollTo",t).bind(t),scroller:t}}var u=window.scrollX,f=window.pageXOffset,d=window.scrollY,p=window.pageYOffset,c=null==u||0===u?f:u,a=null==d||0===d?p:d;return{startTime:n,startX:c,startY:a,endX:Math.floor("scrollBy"===l?c+e:e),endY:Math.floor("scrollBy"===l?a+o:o),method:T("scrollTo",window).bind(window),scroller:r()}}(o,t.left,t.top,l)).startTime,p=n.startX,h=n.startY,y=n.endX,v=n.endY,w=n.method,m=n.scroller,g=0,E=Math.max(Math.abs((b=y-p)/1e3*15e3),Math.abs((S=v-h)/1e3*15e3)),M=function(t){if(e||null==d)return f;var o,l,n,i,a,u=r(),p=d.get(t);if(null!=p)o=p.cachedScrollSnapValue,l=p.cachedScrollBehaviorStyleAttributeValue,n=p.secondaryScroller,i=p.secondaryScrollerCachedScrollSnapValue,a=p.secondaryScrollerCachedScrollBehaviorStyleAttributeValue,p.release();else{o=""===t.style.scrollSnapType?null:t.style.scrollSnapType,l=c(t),i=null==(n=t===u&&u!==document.body?document.body:void 0)?void 0:""===n.style.scrollSnapType?null:n.style.scrollSnapType,a=null==n?void 0:c(n);var h=getComputedStyle(t).getPropertyValue("scroll-snap-type"),y=null==n?void 0:getComputedStyle(n).getPropertyValue("scroll-snap-type");if("none"===h&&"none"===y)return f}t.style.scrollSnapType="none",void 0!==n&&(n.style.scrollSnapType="none"),void 0!==l&&s(t,l),void 0!==n&&void 0!==a&&s(n,a);var v=!1,w=t===u?window:t;function m(){w.removeEventListener("scroll",g),null!=d&&d.delete(t),v=!0}function g(){t.style.scrollSnapType=o,null!=n&&void 0!==i&&(n.style.scrollSnapType=i),void 0!==l&&s(t,l),void 0!==n&&void 0!==a&&s(n,a),m()}return d.set(t,{release:m,cachedScrollSnapValue:o,cachedScrollBehaviorStyleAttributeValue:l,secondaryScroller:n,secondaryScrollerCachedScrollSnapValue:i,secondaryScrollerCachedScrollBehaviorStyleAttributeValue:a}),{reset:function(){setTimeout(function(){!v&&w.addEventListener("scroll",g)})}}}(m),requestAnimationFrame(function t(e){g+=e-i;var o=Math.max(0,Math.min(1,0===E?0:g/E)),l=Math.floor(p+b*u(o)),r=Math.floor(h+S*u(o));w(l,r),l!==y||r!==v?requestAnimationFrame(t):null!=M&&(M.reset(),M=void 0)})}})(function(t,e){if(void 0===e&&!M(t))throw TypeError("Failed to execute 'scroll' on 'Element': parameter 1 ('options') is not an object.");return M(t)?o(o({},V(t.left,t.top)),{behavior:null==t.behavior?"auto":t.behavior}):o(o({},V(t,e)),{behavior:"auto"})}(n,i),t,l)}function V(t,e){return{left:E(t),top:E(e)}}function I(t){if("nodeType"in t&&1===t.nodeType)return t.parentNode;if("ShadowRoot"in window&&t instanceof window.ShadowRoot)return t.host;if(t===document)return window;if(t instanceof Node)return t.parentNode;return null}function L(t){return"visible"!==t&&"clip"!==t}function P(t){if(t.clientHeight<t.scrollHeight||t.clientWidth<t.scrollWidth){var e=getComputedStyle(t,null);return L(e.overflowY)||L(e.overflowX)}return!1}function j(t){for(var e=t,o=r();null!=e;){var l=a(e);if(null!=l&&(e===o||P(e)))return[e,l];e=I(e)}for(e=t;null!=e;){if(e===o||P(e))return[e,"auto"];e=I(e)}return[o,"auto"]}function W(t){if(void 0===t&&(t=location),"origin"in t&&null!=t.origin)return t.origin;var e=null!=t.port&&t.port.length>0?":"+t.port:"";return"http:"===t.protocol&&":80"===e?e="":"https:"===t.protocol&&":443"===e&&(e=""),t.protocol+"//"+t.hostname+e}var _=/^#\d/,C=t?void 0:Element.prototype.scrollIntoView;function O(t,e,o,l,r,n,i,s){return n<t&&i>e||n>t&&i<e?0:n<=t&&s<=o||i>=e&&s>=o?n-t-l:i>e&&s<o||n<t&&s>o?i-e+r:0}function A(t,e,o){var l=o.block,n=o.inline,i=r(),s=null!=window.visualViewport?visualViewport.width:innerWidth,c=null!=window.visualViewport?visualViewport.height:innerHeight,a=null!=window.scrollX?window.scrollX:window.pageXOffset,u=null!=window.scrollY?window.scrollY:window.pageYOffset,f=t.getBoundingClientRect(),d=f.height,p=f.width,h=f.top,y=f.right,v=f.bottom,w=f.left,m="start"===l||"nearest"===l?h:"end"===l?v:h+d/2,g="center"===n?w+p/2:"end"===n?y:w,b=e.getBoundingClientRect(),S=b.height,T=b.width,E=b.top,M=b.right,B=b.bottom,V=b.left,I=getComputedStyle(e),L=parseInt(I.borderLeftWidth,10),P=parseInt(I.borderTopWidth,10),j=parseInt(I.borderRightWidth,10),W=parseInt(I.borderBottomWidth,10),_=0,C=0,A="offsetWidth"in e?e.offsetWidth-e.clientWidth-L-j:0,X="offsetHeight"in e?e.offsetHeight-e.clientHeight-P-W:0;if(i===e)_="start"===l?m:"end"===l?m-c:"nearest"===l?O(u,u+c,c,P,W,u+m,u+m+d,d):m-c/2,C="start"===n?g:"center"===n?g-s/2:"end"===n?g-s:O(a,a+s,s,L,j,a+g,a+g+p,p),_=Math.max(0,_+u),C=Math.max(0,C+a);else{_="start"===l?m-E-P:"end"===l?m-B+W+X:"nearest"===l?O(E,B,S,P,W+X,m,m+d,d):m-(E+S/2)+X/2,C="start"===n?g-V-L:"center"===n?g-(V+T/2)+A/2:"end"===n?g-M+j+A:O(V,M,T,L,j+A,g,g+p,p);var Y=e.scrollLeft;_=Math.max(0,Math.min(e.scrollTop+_,e.scrollHeight-S+X)),C=Math.max(0,Math.min(Y+C,e.scrollWidth-T+A))}return{top:_,left:C}}var X=t?void 0:Object.getOwnPropertyDescriptor(Element.prototype,"scrollTop").set,Y=t?void 0:Object.getOwnPropertyDescriptor(Element.prototype,"scrollLeft").set,k=!t&&"scroll"in Element.prototype&&"scrollTo"in Element.prototype&&"scrollBy"in Element.prototype&&"scrollIntoView"in Element.prototype;!t&&(!e||!k)&&(Element.prototype.scroll=function(t,e){B(this,"scroll",t,e)},Element.prototype.scrollBy=function(t,e){B(this,"scrollBy",t,e)},Element.prototype.scrollTo=function(t,e){B(this,"scrollTo",t,e)},Element.prototype.scrollIntoView=function(t){var e=null==t||!0===t?{block:"start",inline:"nearest"}:!1===t?{block:"end",inline:"nearest"}:t,r=l(j(this),2),n=r[0],i=r[1],s=null!=e.behavior?e.behavior:i;if("smooth"!==s){if(null!=C)C.call(this,e);else{var c=A(this,n,e),a=c.top,u=c.left;T("scrollTo",this).call(this,u,a)}return}n.scrollTo(o({behavior:s},A(this,n,e)))},null!=HTMLElement.prototype.scrollIntoView&&HTMLElement.prototype.scrollIntoView!==Element.prototype.scrollIntoView&&(HTMLElement.prototype.scrollIntoView=Element.prototype.scrollIntoView),Object.defineProperty(Element.prototype,"scrollLeft",{set:function(t){return this.__adjustingScrollPosition?Y.call(this,t):(B(this,"scrollTo",t,this.scrollTop),t)}}),Object.defineProperty(Element.prototype,"scrollTop",{set:function(t){return this.__adjustingScrollPosition?X.call(this,t):(B(this,"scrollTo",this.scrollLeft,t),t)}}),window.scroll=function(t,e){B(this,"scroll",t,e)},window.scrollBy=function(t,e){B(this,"scrollBy",t,e)},window.scrollTo=function(t,e){B(this,"scrollTo",t,e)},window.addEventListener("click",function(t){if(!t.isTrusted||!(t.target instanceof HTMLAnchorElement))return;var e=t.target,o=e.pathname,r=e.search,n=e.hash;if(!(W(t.target)===W(location)&&o===location.pathname&&r===location.search)||null==n||n.length<1)return;var i=function(t){for(var e=t;null!=e;){if("ShadowRoot"in window&&e instanceof window.ShadowRoot)return e;var o=I(e);if(o===e)break;e=o}return document}(t.target),s=null!=n.match(_)?i.getElementById(n.slice(1)):i.querySelector(n);if(null!=s){var c=l(j(s),2)[1];"smooth"===c&&(t.preventDefault(),s.scrollIntoView({behavior:c}))}}))}()}}]);