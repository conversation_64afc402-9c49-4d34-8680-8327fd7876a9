"use strict";(self.webpackChunkweixin_search=self.webpackChunkweixin_search||[]).push([["vendors-node_modules_echarts_lib_component_visualMap_install_js-node_modules_echarts_lib_rend-8e4122"],{773630:function(t,e,i){function n(t,e,i,n,a,s){t=t||0;var l,u=i[1]-i[0];if(null!=a&&(a=o(a,[0,u])),null!=s&&(s=Math.max(s,null!=a?a:0)),"all"===n){var h=Math.abs(e[1]-e[0]);h=o(h,[0,u]),a=s=o(h,[a,s]),n=0}e[0]=o(e[0],i),e[1]=o(e[1],i);var p=r(e,n);e[n]+=t;var c=a||0,d=i.slice();return p.sign<0?d[0]+=c:d[1]-=c,e[n]=o(e[n],d),l=r(e,n),null!=a&&(l.sign!==p.sign||l.span<a)&&(e[1-n]=e[n]+p.sign*a),l=r(e,n),null!=s&&l.span>s&&(e[1-n]=e[n]+l.sign*s),e}function r(t,e){var i=t[e]-t[1-e];return{span:Math.abs(i),sign:i>0?-1:i<0?1:e?-1:1}}function o(t,e){return Math.min(null!=e[1]?e[1]:1/0,Math.max(null!=e[0]?e[0]:-1/0,t))}i.d(e,{Z:function(){return n}})},746051:function(t,e,i){var n=i(518299),r=i(807028),o=i(382495),a=i(31674),s=i(115856),l=[20,140],u=function(t){function e(){var i=null!==t&&t.apply(this,arguments)||this;return i.type=e.type,i}return(0,n.ZT)(e,t),e.prototype.optionUpdated=function(e,i){t.prototype.optionUpdated.apply(this,arguments),this.resetExtent(),this.resetVisual(function(t){t.mappingMethod="linear",t.dataExtent=this.getExtent()}),this._resetRange()},e.prototype.resetItemSize=function(){t.prototype.resetItemSize.apply(this,arguments);var e=this.itemSize;(null==e[0]||isNaN(e[0]))&&(e[0]=20),(null==e[1]||isNaN(e[1]))&&(e[1]=140)},e.prototype._resetRange=function(){var t=this.getExtent(),e=this.option.range;!e||e.auto?(t.auto=1,this.option.range=t):r.kJ(e)&&(e[0]>e[1]&&e.reverse(),e[0]=Math.max(e[0],t[0]),e[1]=Math.min(e[1],t[1]))},e.prototype.completeVisualOption=function(){t.prototype.completeVisualOption.apply(this,arguments),r.S6(this.stateList,function(t){var e=this.option.controller[t].symbolSize;e&&e[0]!==e[1]&&(e[0]=e[1]/3)},this)},e.prototype.setSelected=function(t){this.option.range=t.slice(),this._resetRange()},e.prototype.getSelected=function(){var t=this.getExtent(),e=a.dt((this.get("range")||[]).slice());return e[0]>t[1]&&(e[0]=t[1]),e[1]>t[1]&&(e[1]=t[1]),e[0]<t[0]&&(e[0]=t[0]),e[1]<t[0]&&(e[1]=t[0]),e},e.prototype.getValueState=function(t){var e=this.option.range,i=this.getExtent();return(e[0]<=i[0]||e[0]<=t)&&(e[1]>=i[1]||t<=e[1])?"inRange":"outOfRange"},e.prototype.findTargetDataIndices=function(t){var e=[];return this.eachTargetSeries(function(i){var n=[],r=i.getData();r.each(this.getDataDimensionIndex(r),function(e,i){t[0]<=e&&e<=t[1]&&n.push(i)},this),e.push({seriesId:i.id,dataIndex:n})},this),e},e.prototype.getVisualMeta=function(t){var e=h(this,"outOfRange",this.getExtent()),i=h(this,"inRange",this.option.range.slice()),n=[];function r(e,i){n.push({value:e,color:t(e,i)})}for(var o=0,a=0,s=i.length,l=e.length;a<l&&(!i.length||e[a]<=i[0]);a++)e[a]<i[o]&&r(e[a],"outOfRange");for(var u=1;o<s;o++,u=0)u&&n.length&&r(i[o],"outOfRange"),r(i[o],"inRange");for(var u=1;a<l;a++)(!i.length||i[i.length-1]<e[a])&&(u&&(n.length&&r(n[n.length-1].value,"outOfRange"),u=0),r(e[a],"outOfRange"));var p=n.length;return{stops:n,outerColors:[p?n[0].color:"transparent",p?n[p-1].color:"transparent"]}},e.type="visualMap.continuous",e.defaultOption=(0,s.ZL)(o.Z.defaultOption,{align:"auto",calculable:!1,hoverLink:!0,realtime:!0,handleIcon:"path://M-11.39,9.77h0a3.5,3.5,0,0,1-3.5,3.5h-22a3.5,3.5,0,0,1-3.5-3.5h0a3.5,3.5,0,0,1,3.5-3.5h22A3.5,3.5,0,0,1-11.39,9.77Z",handleSize:"120%",handleStyle:{borderColor:"#fff",borderWidth:1},indicatorIcon:"circle",indicatorSize:"50%",indicatorStyle:{borderColor:"#fff",borderWidth:2,shadowBlur:2,shadowOffsetX:1,shadowOffsetY:1,shadowColor:"rgba(0,0,0,0.2)"}}),e}(o.Z);function h(t,e,i){if(i[0]===i[1])return i.slice();for(var n=(i[1]-i[0])/200,r=i[0],o=[],a=0;a<=200&&r<i[1];a++)o.push(r),r+=n;return o.push(i[1]),o}e.Z=u},958678:function(t,e,i){var n=i(518299),r=i(807028),o=i(550025),a=i(660847),s=i(775345),l=i(345262),u=i(707498),h=i(406822),p=i(339738),c=i(638144),d=i(31674),f=i(773630),v=i(728739),g=i(133141),_=i(310123),y=i(623815),m=i(19750),x=i(694923),S=i(276868),w=i(931918),M=i(191743),I=d.NU,b=r.S6,L=Math.min,V=Math.max,T=function(t){function e(){var i=null!==t&&t.apply(this,arguments)||this;return i.type=e.type,i._shapes={},i._dataInterval=[],i._handleEnds=[],i._hoverLinkDataIndices=[],i}return(0,n.ZT)(e,t),e.prototype.init=function(e,i){t.prototype.init.call(this,e,i),this._hoverLinkFromSeriesMouseOver=r.ak(this._hoverLinkFromSeriesMouseOver,this),this._hideIndicator=r.ak(this._hideIndicator,this)},e.prototype.doRender=function(t,e,i,n){(!n||"selectDataRange"!==n.type||n.from!==this.uid)&&this._buildView()},e.prototype._buildView=function(){this.group.removeAll();var t=this.visualMapModel,e=this.group;this._orient=t.get("orient"),this._useHandle=t.get("calculable"),this._resetInterval(),this._renderBar(e);var i=t.get("text");this._renderEndsText(e,i,0),this._renderEndsText(e,i,1),this._updateView(!0),this.renderBackground(e),this._updateView(),this._enableHoverLinkToSeries(),this._enableHoverLinkFromSeries(),this.positionGroup(e)},e.prototype._renderEndsText=function(t,e,i){if(!!e){var n=e[1-i];n=null!=n?n+"":"";var r=this.visualMapModel,o=r.get("textGap"),a=r.itemSize,s=this._shapes.mainGroup,u=this._applyTransform([a[0]/2,0===i?-o:a[1]+o],s),h=this._applyTransform(0===i?"bottom":"top",s),p=this._orient,c=this.visualMapModel.textStyleModel;this.group.add(new l.ZP({style:(0,w.Lr)(c,{x:u[0],y:u[1],verticalAlign:"horizontal"===p?"middle":h,align:"horizontal"===p?h:"center",text:n})}))}},e.prototype._renderBar=function(t){var e=this.visualMapModel,i=this._shapes,n=e.itemSize,o=this._orient,a=this._useHandle,s=v.X(e,this.api,n),l=i.mainGroup=this._createBarGroup(s),p=new u.Z;l.add(p),p.add(i.outOfRange=C()),p.add(i.inRange=C(null,a?z(this._orient):null,r.ak(this._dragHandle,this,"all",!1),r.ak(this._dragHandle,this,"all",!0))),p.setClipPath(new h.Z({shape:{x:0,y:0,width:n[0],height:n[1],r:3}}));var c=e.textStyleModel.getTextRect("国"),d=V(c.width,c.height);a&&(i.handleThumbs=[],i.handleLabels=[],i.handleLabelPoints=[],this._createHandle(e,l,0,n,d,o),this._createHandle(e,l,1,n,d,o)),this._createIndicator(e,l,n,d,o),t.add(l)},e.prototype._createHandle=function(t,e,i,n,o,s){var u=r.ak(this._dragHandle,this,i,!1),h=r.ak(this._dragHandle,this,i,!0),p=(0,_.GM)(t.get("handleSize"),n[0]),c=(0,m.th)(t.get("handleIcon"),-p/2,-p/2,p,p,null,!0),d=z(this._orient);c.attr({cursor:d,draggable:!0,drift:u,ondragend:h,onmousemove:function(t){a.sT(t.event)}}),c.x=n[0]/2,c.useStyle(t.getModel("handleStyle").getItemStyle()),c.setStyle({strokeNoScale:!0,strokeFirst:!0}),c.style.lineWidth*=2,c.ensureState("emphasis").style=t.getModel(["emphasis","handleStyle"]).getItemStyle(),(0,y.Nj)(c,!0),e.add(c);var f=this.visualMapModel.textStyleModel,v=new l.ZP({cursor:d,draggable:!0,drift:u,onmousemove:function(t){a.sT(t.event)},ondragend:h,style:(0,w.Lr)(f,{x:0,y:0,text:""})});v.ensureState("blur").style={opacity:.1},v.stateTransition={duration:200},this.group.add(v);var g=[p,0],x=this._shapes;x.handleThumbs[i]=c,x.handleLabelPoints[i]=g,x.handleLabels[i]=v},e.prototype._createIndicator=function(t,e,i,n,o){var a=(0,_.GM)(t.get("indicatorSize"),i[0]),s=(0,m.th)(t.get("indicatorIcon"),-a/2,-a/2,a,a,null,!0);s.attr({cursor:"move",invisible:!0,silent:!0,x:i[0]/2});var u=t.getModel("indicatorStyle").getItemStyle();if(s instanceof x.ZP){var h=s.style;s.useStyle(r.l7({image:h.image,x:h.x,y:h.y,width:h.width,height:h.height},u))}else s.useStyle(u);e.add(s);var p=this.visualMapModel.textStyleModel,c=new l.ZP({silent:!0,invisible:!0,style:(0,w.Lr)(p,{x:0,y:0,text:""})});this.group.add(c);var d=[("horizontal"===o?n/2:6)+i[0]/2,0],f=this._shapes;f.indicator=s,f.indicatorLabel=c,f.indicatorLabelPoint=d,this._firstShowIndicator=!0},e.prototype._dragHandle=function(t,e,i,n){if(!!this._useHandle){if(this._dragging=!e,!e){var r=this._applyTransform([i,n],this._shapes.mainGroup,!0);this._updateInterval(t,r[1]),this._hideIndicator(),this._updateView()}!this.visualMapModel.get("realtime")===e&&this.api.dispatchAction({type:"selectDataRange",from:this.uid,visualMapId:this.visualMapModel.id,selected:this._dataInterval.slice()}),e?this._hovering||this._clearHoverLinkToSeries():R(this.visualMapModel)&&this._doHoverLinkToSeries(this._handleEnds[t],!1)}},e.prototype._resetInterval=function(){var t=this.visualMapModel,e=this._dataInterval=t.getSelected(),i=t.getExtent(),n=[0,t.itemSize[1]];this._handleEnds=[I(e[0],i,n,!0),I(e[1],i,n,!0)]},e.prototype._updateInterval=function(t,e){e=e||0;var i=this.visualMapModel,n=this._handleEnds,r=[0,i.itemSize[1]];(0,f.Z)(e,n,r,t,0);var o=i.getExtent();this._dataInterval=[I(n[0],r,o,!0),I(n[1],r,o,!0)]},e.prototype._updateView=function(t){var e=this.visualMapModel,i=e.getExtent(),n=this._shapes,r=[0,e.itemSize[1]],o=t?r:this._handleEnds,a=this._createBarVisual(this._dataInterval,i,o,"inRange"),s=this._createBarVisual(i,i,r,"outOfRange");n.inRange.setStyle({fill:a.barColor}).setShape("points",a.barPoints),n.outOfRange.setStyle({fill:s.barColor}).setShape("points",s.barPoints),this._updateHandle(o,a)},e.prototype._createBarVisual=function(t,e,i,n){var r={forceState:n,convertOpacityToAlpha:!0},a=this._makeColorGradient(t,r),s=[this.getControllerVisual(t[0],"symbolSize",r),this.getControllerVisual(t[1],"symbolSize",r)],l=this._createBarPoints(i,s);return{barColor:new o.Z(0,0,0,1,a),barPoints:l,handlesColor:[a[0].color,a[a.length-1].color]}},e.prototype._makeColorGradient=function(t,e){var i=[],n=(t[1]-t[0])/100;i.push({color:this.getControllerVisual(t[0],"color",e),offset:0});for(var r=1;r<100;r++){var o=t[0]+n*r;if(o>t[1])break;i.push({color:this.getControllerVisual(o,"color",e),offset:r/100})}return i.push({color:this.getControllerVisual(t[1],"color",e),offset:1}),i},e.prototype._createBarPoints=function(t,e){var i=this.visualMapModel.itemSize;return[[i[0]-e[0],t[0]],[i[0],t[0]],[i[0],t[1]],[i[0]-e[1],t[1]]]},e.prototype._createBarGroup=function(t){var e=this._orient,i=this.visualMapModel.get("inverse");return new u.Z("horizontal"!==e||i?"horizontal"===e&&i?{scaleX:"bottom"===t?-1:1,rotation:-Math.PI/2}:"vertical"!==e||i?{scaleX:"left"===t?1:-1}:{scaleX:"left"===t?1:-1,scaleY:-1}:{scaleX:"bottom"===t?1:-1,rotation:Math.PI/2})},e.prototype._updateHandle=function(t,e){if(!!this._useHandle){var i=this._shapes,n=this.visualMapModel,r=i.handleThumbs,o=i.handleLabels,a=n.itemSize,s=n.getExtent();b([0,1],function(l){var u=r[l];u.setStyle("fill",e.handlesColor[l]),u.y=t[l];var h=I(t[l],[0,a[1]],s,!0),c=this.getControllerVisual(h,"symbolSize");u.scaleX=u.scaleY=c/a[0],u.x=a[0]-c/2;var d=p.applyTransform(i.handleLabelPoints[l],p.getTransform(u,this.group));o[l].setStyle({x:d[0],y:d[1],text:n.formatValueText(this._dataInterval[l]),verticalAlign:"middle",align:"vertical"===this._orient?this._applyTransform("left",i.mainGroup):"center"})},this)}},e.prototype._showIndicator=function(t,e,i,n){var r=this.visualMapModel,o=r.getExtent(),a=r.itemSize,s=[0,a[1]],l=this._shapes,u=l.indicator;if(!!u){u.attr("invisible",!1);var h=this.getControllerVisual(t,"color",{convertOpacityToAlpha:!0}),c=this.getControllerVisual(t,"symbolSize"),d=I(t,o,s,!0),f=a[0]-c/2,v={x:u.x,y:u.y};u.y=d,u.x=f;var g=p.applyTransform(l.indicatorLabelPoint,p.getTransform(u,this.group)),_=l.indicatorLabel;_.attr("invisible",!1);var y=this._applyTransform("left",l.mainGroup),m="horizontal"===this._orient;_.setStyle({text:(i||"")+r.formatValueText(e),verticalAlign:m?y:"middle",align:m?"center":y});var x={x:f,y:d,style:{fill:h}},S={style:{x:g[0],y:g[1]}};if(r.ecModel.isAnimationEnabled()&&!this._firstShowIndicator){var w={duration:100,easing:"cubicInOut",additive:!0};u.x=v.x,u.y=v.y,u.animateTo(x,w),_.animateTo(S,w)}else u.attr(x),_.attr(S);this._firstShowIndicator=!1;var M=this._shapes.handleLabels;if(M)for(var b=0;b<M.length;b++)this.api.enterBlur(M[b])}},e.prototype._enableHoverLinkToSeries=function(){var t=this;this._shapes.mainGroup.on("mousemove",function(e){if(t._hovering=!0,!t._dragging){var i=t.visualMapModel.itemSize,n=t._applyTransform([e.offsetX,e.offsetY],t._shapes.mainGroup,!0,!0);n[1]=L(V(0,n[1]),i[1]),t._doHoverLinkToSeries(n[1],0<=n[0]&&n[0]<=i[0])}}).on("mouseout",function(){t._hovering=!1,t._dragging||t._clearHoverLinkToSeries()})},e.prototype._enableHoverLinkFromSeries=function(){var t=this.api.getZr();this.visualMapModel.option.hoverLink?(t.on("mouseover",this._hoverLinkFromSeriesMouseOver,this),t.on("mouseout",this._hideIndicator,this)):this._clearHoverLinkFromSeries()},e.prototype._doHoverLinkToSeries=function(t,e){var i=this.visualMapModel,n=i.itemSize;if(!!i.option.hoverLink){var r=[0,n[1]],o=i.getExtent();t=L(V(r[0],t),r[1]);var a=function(t,e,i){var n=6,r=t.get("hoverLinkDataSize");return r&&(n=I(r,e,i,!0)/2),n}(i,o,r),s=[t-a,t+a],l=I(t,r,o,!0),u=[I(s[0],r,o,!0),I(s[1],r,o,!0)];s[0]<r[0]&&(u[0]=-1/0),s[1]>r[1]&&(u[1]=1/0),e&&(u[0]===-1/0?this._showIndicator(l,u[1],"< ",a):u[1]===1/0?this._showIndicator(l,u[0],"> ",a):this._showIndicator(l,l,"≈ ",a));var h=this._hoverLinkDataIndices,p=[];(e||R(i))&&(p=this._hoverLinkDataIndices=i.findTargetDataIndices(u));var c=g.XI(h,p);this._dispatchHighDown("downplay",v.H(c[0],i)),this._dispatchHighDown("highlight",v.H(c[1],i))}},e.prototype._hoverLinkFromSeriesMouseOver=function(t){if((0,M.o)(t.target,function(t){var i=(0,S.A)(t);if(null!=i.dataIndex)return e=i,!0},!0),!e)return;var e,i=this.ecModel.getSeriesByIndex(e.seriesIndex),n=this.visualMapModel;if(!!n.isTargetSeries(i)){var r=i.getData(e.dataType),o=r.getStore().get(n.getDataDimensionIndex(r),e.dataIndex);!isNaN(o)&&this._showIndicator(o,o)}},e.prototype._hideIndicator=function(){var t=this._shapes;t.indicator&&t.indicator.attr("invisible",!0),t.indicatorLabel&&t.indicatorLabel.attr("invisible",!0);var e=this._shapes.handleLabels;if(e)for(var i=0;i<e.length;i++)this.api.leaveBlur(e[i])},e.prototype._clearHoverLinkToSeries=function(){this._hideIndicator();var t=this._hoverLinkDataIndices;this._dispatchHighDown("downplay",v.H(t,this.visualMapModel)),t.length=0},e.prototype._clearHoverLinkFromSeries=function(){this._hideIndicator();var t=this.api.getZr();t.off("mouseover",this._hoverLinkFromSeriesMouseOver),t.off("mouseout",this._hideIndicator)},e.prototype._applyTransform=function(t,e,i,n){var o=p.getTransform(e,n?null:this.group);return r.kJ(t)?p.applyTransform(t,o,i):p.transformDirection(t,o,i)},e.prototype._dispatchHighDown=function(t,e){e&&e.length&&this.api.dispatchAction({type:t,batch:e})},e.prototype.dispose=function(){this._clearHoverLinkFromSeries(),this._clearHoverLinkToSeries()},e.type="visualMap.continuous",e}(s.Z);function C(t,e,i,n){return new c.Z({shape:{points:t},draggable:!!i,cursor:e,drift:i,onmousemove:function(t){a.sT(t.event)},ondragend:n})}function R(t){var e=t.get("hoverLinkOnHandle");return!!(null==e?t.get("realtime"):e)}function z(t){return"vertical"===t?"ns-resize":"ew-resize"}e.Z=T},486167:function(t,e,i){var n=i(518299),r=i(807028),o=i(382495),a=i(794711),s=i(731958),l=i(31674),u=i(115856),h=function(t){function e(){var i=null!==t&&t.apply(this,arguments)||this;return i.type=e.type,i._pieceList=[],i}return(0,n.ZT)(e,t),e.prototype.optionUpdated=function(e,i){t.prototype.optionUpdated.apply(this,arguments),this.resetExtent();var n=this._mode=this._determineMode();this._pieceList=[],p[this._mode].call(this,this._pieceList),this._resetSelected(e,i);var o=this.option.categories;this.resetVisual(function(t,e){"categories"===n?(t.mappingMethod="category",t.categories=r.d9(o)):(t.dataExtent=this.getExtent(),t.mappingMethod="piecewise",t.pieceList=r.UI(this._pieceList,function(t){return t=r.d9(t),"inRange"!==e&&(t.visual=null),t}))})},e.prototype.completeVisualOption=function(){var e=this.option,i={},n=a.Z.listVisualTypes(),o=this.isCategory();function l(t,e,i){return t&&t[e]&&t[e].hasOwnProperty(i)}r.S6(e.pieces,function(t){r.S6(n,function(e){t.hasOwnProperty(e)&&(i[e]=1)})}),r.S6(i,function(t,i){var n=!1;r.S6(this.stateList,function(t){n=n||l(e,t,i)||l(e.target,t,i)},this),n||r.S6(this.stateList,function(t){(e[t]||(e[t]={}))[i]=s.Z.get(i,"inRange"===t?"active":"inactive",o)})},this),t.prototype.completeVisualOption.apply(this,arguments)},e.prototype._resetSelected=function(t,e){var i=this.option,n=this._pieceList,o=(e?i:t).selected||{};if(i.selected=o,r.S6(n,function(t,e){var i=this.getSelectedMapKey(t);!o.hasOwnProperty(i)&&(o[i]=!0)},this),"single"===i.selectedMode){var a=!1;r.S6(n,function(t,e){var i=this.getSelectedMapKey(t);o[i]&&(a?o[i]=!1:a=!0)},this)}},e.prototype.getItemSymbol=function(){return this.get("itemSymbol")},e.prototype.getSelectedMapKey=function(t){return"categories"===this._mode?t.value+"":t.index+""},e.prototype.getPieceList=function(){return this._pieceList},e.prototype._determineMode=function(){var t=this.option;return t.pieces&&t.pieces.length>0?"pieces":this.option.categories?"categories":"splitNumber"},e.prototype.setSelected=function(t){this.option.selected=r.d9(t)},e.prototype.getValueState=function(t){var e=a.Z.findPieceIndex(t,this._pieceList);return null!=e&&this.option.selected[this.getSelectedMapKey(this._pieceList[e])]?"inRange":"outOfRange"},e.prototype.findTargetDataIndices=function(t){var e=[],i=this._pieceList;return this.eachTargetSeries(function(n){var r=[],o=n.getData();o.each(this.getDataDimensionIndex(o),function(e,n){a.Z.findPieceIndex(e,i)===t&&r.push(n)},this),e.push({seriesId:n.id,dataIndex:r})},this),e},e.prototype.getRepresentValue=function(t){var e;if(this.isCategory())e=t.value;else if(null!=t.value)e=t.value;else{var i=t.interval||[];e=i[0]===-1/0&&i[1]===1/0?0:(i[0]+i[1])/2}return e},e.prototype.getVisualMeta=function(t){if(!this.isCategory()){var e=[],i=["",""],n=this;var o=this._pieceList.slice();if(o.length){var a=o[0].interval[0];a!==-1/0&&o.unshift({interval:[-1/0,a]}),(a=o[o.length-1].interval[1])!==1/0&&o.push({interval:[a,1/0]})}else o.push({interval:[-1/0,1/0]});var s=-1/0;return r.S6(o,function(t){var e=t.interval;e&&(e[0]>s&&l([s,e[0]],"outOfRange"),l(e.slice()),s=e[1])},this),{stops:e,outerColors:i}}function l(r,o){var a=n.getRepresentValue({interval:r});!o&&(o=n.getValueState(a));var s=t(a,o);r[0]===-1/0?i[0]=s:r[1]===1/0?i[1]=s:e.push({value:r[0],color:s},{value:r[1],color:s})}},e.type="visualMap.piecewise",e.defaultOption=(0,u.ZL)(o.Z.defaultOption,{selected:null,minOpen:!1,maxOpen:!1,align:"auto",itemWidth:20,itemHeight:14,itemSymbol:"roundRect",pieces:null,categories:null,splitNumber:5,selectedMode:"multiple",itemGap:10,hoverLink:!0}),e}(o.Z),p={splitNumber:function(t){var e=this.option,i=Math.min(e.precision,20),n=this.getExtent(),o=e.splitNumber;o=Math.max(parseInt(o,10),1),e.splitNumber=o;for(var a=(n[1]-n[0])/o;+a.toFixed(i)!==a&&i<5;)i++;e.precision=i,a=+a.toFixed(i),e.minOpen&&t.push({interval:[-1/0,n[0]],close:[0,0]});for(var s=0,u=n[0];s<o;u+=a,s++){var h=s===o-1?n[1]:u+a;t.push({interval:[u,h],close:[1,1]})}e.maxOpen&&t.push({interval:[n[1],1/0],close:[0,0]}),(0,l.nR)(t),r.S6(t,function(t,e){t.index=e,t.text=this.formatValueText(t.interval)},this)},categories:function(t){var e=this.option;r.S6(e.categories,function(e){t.push({text:this.formatValueText(e,!0),value:e})},this),c(e,t)},pieces:function(t){var e=this.option;r.S6(e.pieces,function(e,i){!r.Kn(e)&&(e={value:e});var n={text:"",index:i};if(null!=e.label&&(n.text=e.label),e.hasOwnProperty("value")){var o=n.value=e.value;n.interval=[o,o],n.close=[1,1]}else{for(var s=n.interval=[],l=n.close=[0,0],u=[1,0,1],h=[-1/0,1/0],p=[],c=0;c<2;c++){for(var d=[["gte","gt","min"],["lte","lt","max"]][c],f=0;f<3&&null==s[c];f++)s[c]=e[d[f]],l[c]=u[f],p[c]=2===f;null==s[c]&&(s[c]=h[c])}p[0]&&s[1]===1/0&&(l[0]=0),p[1]&&s[0]===-1/0&&(l[1]=0);s[0]===s[1]&&l[0]&&l[1]&&(n.value=s[0])}n.visual=a.Z.retrieveVisuals(e),t.push(n)},this),c(e,t),(0,l.nR)(t),r.S6(t,function(t){var e=t.close,i=[["<","≤"][e[1]],[">","≥"][e[0]]];t.text=t.text||this.formatValueText(null!=t.value?t.value:t.interval,!1,i)},this)}};function c(t,e){var i=t.inverse;("vertical"===t.orient?!i:i)&&e.reverse()}e.Z=h},39864:function(t,e,i){var n=i(518299),r=i(807028),o=i(775345),a=i(707498),s=i(345262),l=i(19750),u=i(918712),h=i(728739),p=i(931918),c=function(t){function e(){var i=null!==t&&t.apply(this,arguments)||this;return i.type=e.type,i}return(0,n.ZT)(e,t),e.prototype.doRender=function(){var t=this.group;t.removeAll();var e=this.visualMapModel,i=e.get("textGap"),n=e.textStyleModel,o=n.getFont(),l=n.getTextColor(),h=this._getItemAlign(),p=e.itemSize,c=this._getViewData(),d=c.endsText,f=r.Jv(e.get("showLabel",!0),!d);d&&this._renderEndsText(t,d[0],p,f,h),r.S6(c.viewPieceList,function(n){var u=n.piece,c=new a.Z;c.onclick=r.ak(this._onItemClick,this,u),this._enableHoverLink(c,n.indexInModelPieceList);var d=e.getRepresentValue(u);if(this._createItemSymbol(c,d,[0,0,p[0],p[1]]),f){var v=this.visualMapModel.getValueState(d);c.add(new s.ZP({style:{x:"right"===h?-i:p[0]+i,y:p[1]/2,text:u.text,verticalAlign:"middle",align:h,font:o,fill:l,opacity:"outOfRange"===v?.5:1}}))}t.add(c)},this),d&&this._renderEndsText(t,d[1],p,f,h),u.BZ(e.get("orient"),t,e.get("itemGap")),this.renderBackground(t),this.positionGroup(t)},e.prototype._enableHoverLink=function(t,e){var i=this;t.on("mouseover",function(){return n("highlight")}).on("mouseout",function(){return n("downplay")});var n=function(t){var n=i.visualMapModel;n.option.hoverLink&&i.api.dispatchAction({type:t,batch:h.H(n.findTargetDataIndices(e),n)})}},e.prototype._getItemAlign=function(){var t=this.visualMapModel,e=t.option;if("vertical"===e.orient)return h.X(t,this.api,t.itemSize);var i=e.align;return(!i||"auto"===i)&&(i="left"),i},e.prototype._renderEndsText=function(t,e,i,n,r){if(!!e){var o=new a.Z,l=this.visualMapModel.textStyleModel;o.add(new s.ZP({style:(0,p.Lr)(l,{x:n?"right"===r?i[0]:0:i[0]/2,y:i[1]/2,verticalAlign:"middle",align:n?r:"center",text:e})})),t.add(o)}},e.prototype._getViewData=function(){var t=this.visualMapModel,e=r.UI(t.getPieceList(),function(t,e){return{piece:t,indexInModelPieceList:e}}),i=t.get("text"),n=t.get("orient"),o=t.get("inverse");return("horizontal"===n?o:!o)?e.reverse():i&&(i=i.slice().reverse()),{viewPieceList:e,endsText:i}},e.prototype._createItemSymbol=function(t,e,i){t.add((0,l.th)(this.getControllerVisual(e,"symbol"),i[0],i[1],i[2],i[3],this.getControllerVisual(e,"color")))},e.prototype._onItemClick=function(t){var e=this.visualMapModel,i=e.option,n=i.selectedMode;if(!!n){var o=r.d9(i.selected),a=e.getSelectedMapKey(t);"single"===n||!0===n?(o[a]=!0,r.S6(o,function(t,e){o[e]=e===a})):o[a]=!o[a],this.api.dispatchAction({type:"selectDataRange",from:this.uid,visualMapId:this.visualMapModel.id,selected:o})}},e.type="visualMap.piecewise",e}(o.Z);e.Z=c},382495:function(t,e,i){var n=i(518299),r=i(807028),o=i(731958),a=i(794711),s=i(964199),l=i(133141),u=i(31674),h=i(882425),p=a.Z.mapVisual,c=a.Z.eachVisual,d=r.kJ,f=r.S6,v=u.dt,g=u.NU,_=function(t){function e(){var i=null!==t&&t.apply(this,arguments)||this;return i.type=e.type,i.stateList=["inRange","outOfRange"],i.replacableOptionKeys=["inRange","outOfRange","target","controller","color"],i.layoutMode={type:"box",ignoreSize:!0},i.dataBound=[-1/0,1/0],i.targetVisuals={},i.controllerVisuals={},i}return(0,n.ZT)(e,t),e.prototype.init=function(t,e,i){this.mergeDefaultAndTheme(t,i)},e.prototype.optionUpdated=function(t,e){var i=this.option;e||s.jO(i,t,this.replacableOptionKeys),this.textStyleModel=this.getModel("textStyle"),this.resetItemSize(),this.completeVisualOption()},e.prototype.resetVisual=function(t){var e=this.stateList;t=r.ak(t,this),this.controllerVisuals=s.qD(this.option.controller,e,t),this.targetVisuals=s.qD(this.option.target,e,t)},e.prototype.getItemSymbol=function(){return null},e.prototype.getTargetSeriesIndices=function(){var t=this.option.seriesIndex,e=[];return null==t||"all"===t?this.ecModel.eachSeries(function(t,i){e.push(i)}):e=l.kF(t),e},e.prototype.eachTargetSeries=function(t,e){r.S6(this.getTargetSeriesIndices(),function(i){var n=this.ecModel.getSeriesByIndex(i);n&&t.call(e,n)},this)},e.prototype.isTargetSeries=function(t){var e=!1;return this.eachTargetSeries(function(i){i===t&&(e=!0)}),e},e.prototype.formatValueText=function(t,e,i){var n,o=this.option,a=o.precision,s=this.dataBound,l=o.formatter;i=i||["<",">"],r.kJ(t)&&(t=t.slice(),n=!0);var u=e?t:n?[h(t[0]),h(t[1])]:h(t);if(r.HD(l))return l.replace("{value}",n?u[0]:u).replace("{value2}",n?u[1]:u);if(r.mf(l))return n?l(t[0],t[1]):l(t);if(!n)return u;return t[0]===s[0]?i[0]+" "+u[1]:t[1]===s[1]?i[1]+" "+u[0]:u[0]+" - "+u[1];function h(t){return t===s[0]?"min":t===s[1]?"max":(+t).toFixed(Math.min(a,20))}},e.prototype.resetExtent=function(){var t=this.option,e=v([t.min,t.max]);this._dataExtent=e},e.prototype.getDataDimensionIndex=function(t){var e=this.option.dimension;if(null!=e)return t.getDimensionIndex(e);for(var i=t.dimensions,n=i.length-1;n>=0;n--){var r=i[n],o=t.getDimensionInfo(r);if(!o.isCalculationCoord)return o.storeDimIndex}},e.prototype.getExtent=function(){return this._dataExtent.slice()},e.prototype.completeVisualOption=function(){var t=this.ecModel,e=this.option,i={inRange:e.inRange,outOfRange:e.outOfRange},n=e.target||(e.target={}),s=e.controller||(e.controller={});r.TS(n,i),r.TS(s,i);var l=this.isCategory();function u(i){d(e.color)&&!i.inRange&&(i.inRange={color:e.color.slice().reverse()}),i.inRange=i.inRange||{color:t.get("gradientColor")}}u.call(this,n),u.call(this,s),(function(t,e,i){var n=t[e],r=t[i];n&&!r&&(r=t[i]={},f(n,function(t,e){if(!!a.Z.isValidType(e)){var i=o.Z.get(e,"inactive",l);null!=i&&(r[e]=i,"color"===e&&!r.hasOwnProperty("opacity")&&!r.hasOwnProperty("colorAlpha")&&(r.opacity=[0,0]))}}))}).call(this,n,"inRange","outOfRange"),(function(t){var e=(t.inRange||{}).symbol||(t.outOfRange||{}).symbol,i=(t.inRange||{}).symbolSize||(t.outOfRange||{}).symbolSize,n=this.get("inactiveColor"),o=this.getItemSymbol()||"roundRect";f(this.stateList,function(a){var s=this.itemSize,u=t[a];!u&&(u=t[a]={color:l?n:[n]}),null==u.symbol&&(u.symbol=e&&r.d9(e)||(l?o:[o])),null==u.symbolSize&&(u.symbolSize=i&&r.d9(i)||(l?s[0]:[s[0],s[0]])),u.symbol=p(u.symbol,function(t){return"none"===t?o:t});var h=u.symbolSize;if(null!=h){var d=-1/0;c(h,function(t){t>d&&(d=t)}),u.symbolSize=p(h,function(t){return g(t,[0,d],[0,s[0]],!0)})}},this)}).call(this,s)},e.prototype.resetItemSize=function(){this.itemSize=[parseFloat(this.get("itemWidth")),parseFloat(this.get("itemHeight"))]},e.prototype.isCategory=function(){return!!this.option.categories},e.prototype.setSelected=function(t){},e.prototype.getSelected=function(){return null},e.prototype.getValueState=function(t){return null},e.prototype.getVisualMeta=function(t){return null},e.type="visualMap",e.dependencies=["series"],e.defaultOption={show:!0,z:4,seriesIndex:"all",min:0,max:200,left:0,right:null,top:null,bottom:0,itemWidth:null,itemHeight:null,inverse:!1,orient:"vertical",backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",contentColor:"#5793f3",inactiveColor:"#aaa",borderWidth:0,padding:5,textGap:10,precision:0,textStyle:{color:"#333"}},e}(h.Z);e.Z=_},775345:function(t,e,i){var n=i(518299),r=i(807028),o=i(406822),a=i(84164),s=i(918712),l=i(794711),u=function(t){function e(){var i=null!==t&&t.apply(this,arguments)||this;return i.type=e.type,i.autoPositionValues={left:1,right:1,top:1,bottom:1},i}return(0,n.ZT)(e,t),e.prototype.init=function(t,e){this.ecModel=t,this.api=e},e.prototype.render=function(t,e,i,n){if(this.visualMapModel=t,!1===t.get("show")){this.group.removeAll();return}this.doRender(t,e,i,n)},e.prototype.renderBackground=function(t){var e=this.visualMapModel,i=a.MY(e.get("padding")||0),n=t.getBoundingRect();t.add(new o.Z({z2:-1,silent:!0,shape:{x:n.x-i[3],y:n.y-i[0],width:n.width+i[3]+i[1],height:n.height+i[0]+i[2]},style:{fill:e.get("backgroundColor"),stroke:e.get("borderColor"),lineWidth:e.get("borderWidth")}}))},e.prototype.getControllerVisual=function(t,e,i){var n=(i=i||{}).forceState,o=this.visualMapModel,a={};if("color"===e){var s=o.get("contentColor");a.color=s}function u(t){return a[t]}function h(t,e){a[t]=e}var p=o.controllerVisuals[n||o.getValueState(t)],c=l.Z.prepareVisualTypes(p);return r.S6(c,function(n){var r=p[n];i.convertOpacityToAlpha&&"opacity"===n&&(n="colorAlpha",r=p.__alphaForOpacity),l.Z.dependsOn(n,e)&&r&&r.applyVisual(t,u,h)}),a[e]},e.prototype.positionGroup=function(t){var e=this.visualMapModel,i=this.api;s.p$(t,e.getBoxLayoutParams(),{width:i.getWidth(),height:i.getHeight()})},e.prototype.doRender=function(t,e,i,n){},e.type="visualMap",e}(i(860736).Z);e.Z=u},728739:function(t,e,i){i.d(e,{H:function(){return s},X:function(){return a}});var n=i(807028),r=i(918712),o=[["left","right","width"],["top","bottom","height"]];function a(t,e,i){var n=t.option,a=n.align;if(null!=a&&"auto"!==a)return a;for(var s={width:e.getWidth(),height:e.getHeight()},l="horizontal"===n.orient?1:0,u=o[l],h=[0,null,10],p={},c=0;c<3;c++)p[o[1-l][c]]=h[c],p[u[c]]=2===c?i[0]:n[u[c]];var d=[["x","width",3],["y","height",0]][l],f=(0,r.ME)(p,s,n.padding);return u[(f.margin[d[2]]||0)+f[d[0]]+.5*f[d[1]]<.5*s[d[1]]?0:1]}function s(t,e){return n.S6(t||[],function(t){null!=t.dataIndex&&(t.dataIndexInside=t.dataIndex,t.dataIndex=null),t.highlightKey="visualMap"+(e?e.componentIndex:"")}),t}},8401:function(t,e,i){i.d(e,{N:function(){return a}});var n=i(574138),r=i(408248),o=i(361476);function a(t){(0,n.D)(r.N),(0,n.D)(o.N)}},775877:function(t,e,i){i.d(e,{Z:function(){return l}});var n=i(963950),r=i(134828),o=i(807028),a=i(10368),s=!1;function l(t){if(!s)s=!0,t.registerSubTypeDefaulter("visualMap",function(t){return t.categories||(t.pieces?t.pieces.length>0:t.splitNumber>0)&&!t.calculable?"piecewise":"continuous"}),t.registerAction(n.B,n.p),(0,o.S6)(r.A,function(e){t.registerVisual(t.PRIORITY.VISUAL.COMPONENT,e)}),t.registerPreprocessor(a.Z)}},408248:function(t,e,i){i.d(e,{N:function(){return a}});var n=i(746051),r=i(958678),o=i(775877);function a(t){t.registerComponentModel(n.Z),t.registerComponentView(r.Z),(0,o.Z)(t)}},361476:function(t,e,i){i.d(e,{N:function(){return a}});var n=i(486167),r=i(39864),o=i(775877);function a(t){t.registerComponentModel(n.Z),t.registerComponentView(r.Z),(0,o.Z)(t)}},10368:function(t,e,i){i.d(e,{Z:function(){return o}});var n=i(807028),r=n.S6;function o(t){var e=t&&t.visualMap;!n.kJ(e)&&(e=e?[e]:[]),r(e,function(t){if(!!t){a(t,"splitList")&&!a(t,"pieces")&&(t.pieces=t.splitList,delete t.splitList);var e=t.pieces;e&&n.kJ(e)&&r(e,function(t){n.Kn(t)&&(a(t,"start")&&!a(t,"min")&&(t.min=t.start),a(t,"end")&&!a(t,"max")&&(t.max=t.end))})}})}function a(t,e){return t&&t.hasOwnProperty&&t.hasOwnProperty(e)}},134828:function(t,e,i){i.d(e,{A:function(){return s}});var n=i(807028),r=i(964199),o=i(794711),a=i(816691),s=[{createOnAllSeries:!0,reset:function(t,e){var i=[];return e.eachComponent("visualMap",function(e){var o=t.pipelineContext;if(!!e.isTargetSeries(t)&&(!o||!o.large))i.push(r.Z7(e.stateList,e.targetVisuals,n.ak(e.getValueState,e),e.getDataDimensionIndex(t.getData())))}),i}},{createOnAllSeries:!0,reset:function(t,e){var i=t.getData(),r=[];e.eachComponent("visualMap",function(e){if(e.isTargetSeries(t)){var o=e.getVisualMeta(n.ak(l,null,t,e))||{stops:[],outerColors:[]},a=e.getDataDimensionIndex(i);a>=0&&(o.dimension=a,r.push(o))}}),t.getData().setVisual("visualMeta",r)}}];function l(t,e,i,n){for(var r=e.targetVisuals[n],s=o.Z.prepareVisualTypes(r),l={color:(0,a.UL)(t.getData(),"color")},u=0,h=s.length;u<h;u++){var p=s[u],c=r["opacity"===p?"__alphaForOpacity":p];c&&c.applyVisual(i,d,f)}return l.color;function d(t){return l[t]}function f(t,e){l[t]=e}}},963950:function(t,e,i){i.d(e,{B:function(){return n},p:function(){return r}});var n={type:"selectDataRange",event:"dataRangeSelected",update:"update"},r=function(t,e){e.eachComponent({mainType:"visualMap",query:t},function(e){e.setSelected(t.selected)})}},29933:function(t,e,i){i.d(e,{N:function(){return r}});var n=i(919669);function r(t){t.registerPainter("canvas",n.Z)}},794711:function(t,e,i){var n=i(807028),r=i(529134),o=i(31674),a=n.S6,s=n.Kn,l=function(){function t(e){var i=e.mappingMethod,r=e.type,o=this.option=n.d9(e);this.type=r,this.mappingMethod=i,this._normalizeData=y[i];var s=t.visualHandlers[r];this.applyVisual=s.applyVisual,this.getColorMapper=s.getColorMapper,this._normalizedToVisual=s._normalizedToVisual[i],"piecewise"===i?(u(o),function(t){var e=t.pieceList;t.hasSpecialVisual=!1,n.S6(e,function(e,i){e.originIndex=i,null!=e.visual&&(t.hasSpecialVisual=!0)})}(o)):"category"===i?o.categories?function(t){var e=t.categories,i=t.categoryMap={},r=t.visual;if(a(e,function(t,e){i[t]=e}),!n.kJ(r)){var o=[];n.Kn(r)?a(r,function(t,e){var n=i[e];o[null!=n?n:-1]=t}):o[-1]=r,r=_(t,o)}for(var s=e.length-1;s>=0;s--)null==r[s]&&(delete i[e[s]],e.pop())}(o):u(o,!0):(n.hu("linear"!==i||o.dataExtent),u(o))}return t.prototype.mapValueToVisual=function(t){var e=this._normalizeData(t);return this._normalizedToVisual(e,t)},t.prototype.getNormalizer=function(){return n.ak(this._normalizeData,this)},t.listVisualTypes=function(){return n.XP(t.visualHandlers)},t.isValidType=function(e){return t.visualHandlers.hasOwnProperty(e)},t.eachVisual=function(t,e,i){n.Kn(t)?n.S6(t,e,i):e.call(i,t)},t.mapVisual=function(e,i,r){var o,a=n.kJ(e)?[]:n.Kn(e)?{}:(o=!0,null);return t.eachVisual(e,function(t,e){var n=i.call(r,t,e);o?a=n:a[e]=n}),a},t.retrieveVisuals=function(e){var i,n={};return e&&a(t.visualHandlers,function(t,r){e.hasOwnProperty(r)&&(n[r]=e[r],i=!0)}),i?n:null},t.prepareVisualTypes=function(t){if(n.kJ(t))t=t.slice();else{if(!s(t))return[];var e=[];a(t,function(t,i){e.push(i)}),t=e}return t.sort(function(t,e){return"color"===e&&"color"!==t&&0===t.indexOf("color")?1:-1}),t},t.dependsOn=function(t,e){return"color"===e?!!(t&&0===t.indexOf(e)):t===e},t.findPieceIndex=function(t,e,i){for(var r,o=1/0,a=0,s=e.length;a<s;a++){var l=e[a].value;if(null!=l){if(l===t||n.HD(l)&&l===t+"")return a;i&&c(l,a)}}for(var a=0,s=e.length;a<s;a++){var u=e[a],h=u.interval,p=u.close;if(h){if(h[0]===-1/0){if(function(t,e,i){return t?e<=i:e<i}(p[1],t,h[1]))return a}else if(h[1]===1/0){if(function(t,e,i){return t?e<=i:e<i}(p[0],h[0],t))return a}else if(function(t,e,i){return t?e<=i:e<i}(p[0],h[0],t)&&function(t,e,i){return t?e<=i:e<i}(p[1],t,h[1]))return a;i&&c(h[0],a),i&&c(h[1],a)}}if(i)return t===1/0?e.length-1:t===-1/0?0:r;function c(e,i){var n=Math.abs(e-t);n<o&&(o=n,r=i)}},t.visualHandlers={color:{applyVisual:c("color"),getColorMapper:function(){var t=this.option;return n.ak("category"===t.mappingMethod?function(t,e){return e||(t=this._normalizeData(t)),d.call(this,t)}:function(e,i,n){var o=!!n;return i||(e=this._normalizeData(e)),n=r.Uu(e,t.parsedVisual,n),o?n:r.Pz(n,"rgba")},this)},_normalizedToVisual:{linear:function(t){return r.Pz(r.Uu(t,this.option.parsedVisual),"rgba")},category:d,piecewise:function(t,e){var i=g.call(this,e);return null==i&&(i=r.Pz(r.Uu(t,this.option.parsedVisual),"rgba")),i},fixed:f}},colorHue:h(function(t,e){return r.ox(t,e)}),colorSaturation:h(function(t,e){return r.ox(t,null,e)}),colorLightness:h(function(t,e){return r.ox(t,null,null,e)}),colorAlpha:h(function(t,e){return r.m8(t,e)}),decal:{applyVisual:c("decal"),_normalizedToVisual:{linear:null,category:d,piecewise:null,fixed:null}},opacity:{applyVisual:c("opacity"),_normalizedToVisual:v([0,1])},liftZ:{applyVisual:c("liftZ"),_normalizedToVisual:{linear:f,category:f,piecewise:f,fixed:f}},symbol:{applyVisual:function(t,e,i){i("symbol",this.mapValueToVisual(t))},_normalizedToVisual:{linear:p,category:d,piecewise:function(t,e){var i=g.call(this,e);return null==i&&(i=p.call(this,t)),i},fixed:f}},symbolSize:{applyVisual:c("symbolSize"),_normalizedToVisual:v([0,1])}},t}();function u(t,e){var i=t.visual,r=[];n.Kn(i)?a(i,function(t){r.push(t)}):null!=i&&r.push(i);!e&&1===r.length&&!({color:1,symbol:1}).hasOwnProperty(t.type)&&(r[1]=r[0]),_(t,r)}function h(t){return{applyVisual:function(e,i,n){var r=this.mapValueToVisual(e);n("color",t(i("color"),r))},_normalizedToVisual:v([0,1])}}function p(t){var e=this.option.visual;return e[Math.round((0,o.NU)(t,[0,1],[0,e.length-1],!0))]||{}}function c(t){return function(e,i,n){n(t,this.mapValueToVisual(e))}}function d(t){var e=this.option.visual;return e[this.option.loop&&-1!==t?t%e.length:t]}function f(){return this.option.visual[0]}function v(t){return{linear:function(e){return(0,o.NU)(e,t,this.option.visual,!0)},category:d,piecewise:function(e,i){var n=g.call(this,i);return null==n&&(n=(0,o.NU)(e,t,this.option.visual,!0)),n},fixed:f}}function g(t){var e=this.option,i=e.pieceList;if(e.hasSpecialVisual){var n=l.findPieceIndex(t,i),r=i[n];if(r&&r.visual)return r.visual[this.type]}}function _(t,e){return t.visual=e,"color"===t.type&&(t.parsedVisual=n.UI(e,function(t){var e=r.Qc(t);return e||[0,0,0,1]})),e}var y={linear:function(t){return(0,o.NU)(t,this.option.dataExtent,[0,1],!0)},piecewise:function(t){var e=this.option.pieceList,i=l.findPieceIndex(t,e,!0);if(null!=i)return(0,o.NU)(i,[0,e.length-1],[0,1],!0)},category:function(t){var e=this.option.categories?this.option.categoryMap[t]:t;return null==e?-1:e},fixed:n.ZT};function m(t,e,i){return t?e<=i:e<i}e.Z=l},731958:function(t,e,i){var n=i(807028),r={color:{active:["#006edd","#e0ffff"],inactive:["rgba(0,0,0,0)"]},colorHue:{active:[0,360],inactive:[0,0]},colorSaturation:{active:[.3,1],inactive:[0,0]},colorLightness:{active:[.9,.5],inactive:[0,0]},colorAlpha:{active:[.3,1],inactive:[0,0]},opacity:{active:[.3,1],inactive:[0,0]},symbol:{active:["circle","roundRect","diamond"],inactive:["none"]},symbolSize:{active:[10,50],inactive:[0,0]}};e.Z={get:function(t,e,i){var o=n.d9((r[t]||{})[e]);return i&&n.kJ(o)?o[o.length-1]:o}}},964199:function(t,e,i){i.d(e,{Z7:function(){return h},jO:function(){return u},qD:function(){return l}});var n=i(807028),r=i(794711),o=i(816691),a=n.S6;function s(t){if(t){for(var e in t)if(t.hasOwnProperty(e))return!0}}function l(t,e,i){var o={};return a(e,function(e){var s=o[e]=function(){var t=function(){};return t.prototype.__hidden=t.prototype,new t}();a(t[e],function(t,o){if(!!r.Z.isValidType(o)){var a={type:o,visual:t};i&&i(a,e),s[o]=new r.Z(a),"opacity"===o&&((a=n.d9(a)).type="colorAlpha",s.__hidden.__alphaForOpacity=new r.Z(a))}})}),o}function u(t,e,i){var r;n.S6(i,function(t){e.hasOwnProperty(t)&&s(e[t])&&(r=!0)}),r&&n.S6(i,function(i){e.hasOwnProperty(i)&&s(e[i])?t[i]=n.d9(e[i]):delete t[i]})}function h(t,e,i,a){var s={};return n.S6(t,function(t){var i=r.Z.prepareVisualTypes(e[t]);s[t]=i}),{progress:function(t,n){function r(t){return(0,o.Or)(n,h,t)}function l(t,e){(0,o.LZ)(n,h,t,e)}null!=a&&(u=n.getDimensionIndex(a));for(var u,h,p=n.getStore();null!=(h=t.next());){var c=n.getRawDataItem(h);if(!c||!1!==c.visualMap)for(var d=null!=a?p.get(u,h):h,f=i(d),v=e[f],g=s[f],_=0,y=g.length;_<y;_++){var m=g[_];v[m]&&v[m].applyVisual(d,r,l)}}}}}},532841:function(t,e,i){var n=i(904311),r=i(807028),o=i(113525),a=i(367582),s=i(638687),l=i(157301),u=i(339296),h=i(360577),p=i(252919);function c(t,e,i){var n=p.qW.createCanvas(),r=e.getWidth(),o=e.getHeight(),a=n.style;return a&&(a.position="absolute",a.left="0",a.top="0",a.width=r+"px",a.height=o+"px",n.setAttribute("data-zr-dom-id",t)),n.width=r*i,n.height=o*i,n}var d=function(t){function e(e,i,n){var a,s=t.call(this)||this;s.motionBlur=!1,s.lastFrameAlpha=.7,s.dpr=1,s.virtual=!1,s.config={},s.incremental=!1,s.zlevel=0,s.maxRepaintRectCount=5,s.__dirty=!0,s.__firstTimePaint=!0,s.__used=!1,s.__drawIndex=0,s.__startIndex=0,s.__endIndex=0,s.__prevStartIndex=null,s.__prevEndIndex=null,n=n||o.KL,"string"==typeof e?a=c(e,i,n):r.Kn(e)&&(e=(a=e).id),s.id=e,s.dom=a;var l=a.style;return l&&(r.$j(a),a.onselectstart=function(){return!1},l.padding="0",l.margin="0",l.borderWidth="0"),s.painter=i,s.dpr=n,s}return(0,n.ZT)(e,t),e.prototype.getElementCount=function(){return this.__endIndex-this.__startIndex},e.prototype.afterBrush=function(){this.__prevStartIndex=this.__startIndex,this.__prevEndIndex=this.__endIndex},e.prototype.initContext=function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},e.prototype.setUnpainted=function(){this.__firstTimePaint=!0},e.prototype.createBackBuffer=function(){var t=this.dpr;this.domBack=c("back-"+this.id,this.painter,t),this.ctxBack=this.domBack.getContext("2d"),1!==t&&this.ctxBack.scale(t,t)},e.prototype.createRepaintRects=function(t,e,i,n){if(this.__firstTimePaint)return this.__firstTimePaint=!1,null;var r,o=[],a=this.maxRepaintRectCount,s=!1,l=new u.Z(0,0,0,0);function p(t){if(!(!t.isFinite()||t.isZero()))if(0===o.length){var e=new u.Z(0,0,0,0);e.copy(t),o.push(e)}else{for(var i=!1,n=1/0,r=0,h=0;h<o.length;++h){var p=o[h];if(p.intersect(t)){var c=new u.Z(0,0,0,0);c.copy(p),c.union(t),o[h]=c,i=!0;break}if(s){l.copy(t),l.union(p);var d=t.width*t.height,f=p.width*p.height,v=l.width*l.height-d-f;v<n&&(n=v,r=h)}}if(s&&(o[r].union(t),i=!0),!i){var e=new u.Z(0,0,0,0);e.copy(t),o.push(e)}!s&&(s=o.length>=a)}}for(var c=this.__startIndex;c<this.__endIndex;++c){var d=t[c];if(d){var f=d.shouldBePainted(i,n,!0,!0),v=d.__isRendered&&(d.__dirty&h.YV||!f)?d.getPrevPaintRect():null;v&&p(v);var g=f&&(d.__dirty&h.YV||!d.__isRendered)?d.getPaintRect():null;g&&p(g)}}for(var c=this.__prevStartIndex;c<this.__prevEndIndex;++c){var d=e[c],f=d&&d.shouldBePainted(i,n,!0,!0);if(d&&(!f||!d.__zr)&&d.__isRendered){var v=d.getPrevPaintRect();v&&p(v)}}do{r=!1;for(var c=0;c<o.length;){if(o[c].isZero()){o.splice(c,1);continue}for(var _=c+1;_<o.length;)o[c].intersect(o[_])?(r=!0,o[c].union(o[_]),o.splice(_,1)):_++;c++}}while(r);return this._paintRects=o,o},e.prototype.debugGetPaintRects=function(){return(this._paintRects||[]).slice()},e.prototype.resize=function(t,e){var i=this.dpr,n=this.dom,r=n.style,o=this.domBack;r&&(r.width=t+"px",r.height=e+"px"),n.width=t*i,n.height=e*i,o&&(o.width=t*i,o.height=e*i,1!==i&&this.ctxBack.scale(i,i))},e.prototype.clear=function(t,e,i){var n=this.dom,o=this.ctx,a=n.width,u=n.height;e=e||this.clearColor;var h=this.motionBlur&&!t,p=this.lastFrameAlpha,c=this.dpr,d=this;h&&(!this.domBack&&this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(n,0,0,a/c,u/c));var f=this.domBack;function v(t,i,n,a){if(o.clearRect(t,i,n,a),e&&"transparent"!==e){var u=void 0;r.Qq(e)?(u=(e.global||e.__width===n&&e.__height===a)&&e.__canvasGradient||(0,s.ZF)(o,e,{x:0,y:0,width:n,height:a}),e.__canvasGradient=u,e.__width=n,e.__height=a):r.dL(e)&&(e.scaleX=e.scaleX||c,e.scaleY=e.scaleY||c,u=(0,l.RZ)(o,e,{dirty:function(){d.setUnpainted(),d.painter.refresh()}})),o.save(),o.fillStyle=u||e,o.fillRect(t,i,n,a),o.restore()}h&&(o.save(),o.globalAlpha=p,o.drawImage(f,t,i,n,a),o.restore())}!i||h?v(0,0,a,u):i.length&&r.S6(i,function(t){v(t.x*c,t.y*c,t.width*c,t.height*c)})},e}(a.Z);e.Z=d},919669:function(t,e,i){var n=i(113525),r=i(807028),o=i(532841),a=i(980254),s=i(939828),l=i(157301),u=i(360577),h=i(638687),p=function(){function t(t,e,i,a){this.type="canvas",this._zlevelList=[],this._prevDisplayList=[],this._layers={},this._layerConfig={},this._needsManuallyCompositing=!1,this.type="canvas";var s=!t.nodeName||"CANVAS"===t.nodeName.toUpperCase();this._opts=i=r.l7({},i||{}),this.dpr=i.devicePixelRatio||n.KL,this._singleCanvas=s,this.root=t,t.style&&(r.$j(t),t.innerHTML=""),this.storage=e;var l=this._zlevelList;this._prevDisplayList=[];var u=this._layers;if(s){var p=t.width,c=t.height;null!=i.width&&(p=i.width),null!=i.height&&(c=i.height),this.dpr=i.devicePixelRatio||1,t.width=p*this.dpr,t.height=c*this.dpr,this._width=p,this._height=c;var d=new o.Z(t,this,this.dpr);d.__builtin__=!0,d.initContext(),u[314159]=d,d.zlevel=314159,l.push(314159),this._domRoot=t}else{this._width=(0,h.ap)(t,0,i),this._height=(0,h.ap)(t,1,i);var f,v,g,_=this._domRoot=(f=this._width,v=this._height,(g=document.createElement("div")).style.cssText=["position:relative","width:"+f+"px","height:"+v+"px","padding:0","margin:0","border-width:0"].join(";")+";",g);t.appendChild(_)}}return t.prototype.getType=function(){return"canvas"},t.prototype.isSingleCanvas=function(){return this._singleCanvas},t.prototype.getViewportRoot=function(){return this._domRoot},t.prototype.getViewportRootOffset=function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},t.prototype.refresh=function(t){var e=this.storage.getDisplayList(!0),i=this._prevDisplayList,n=this._zlevelList;this._redrawId=Math.random(),this._paintList(e,i,t,this._redrawId);for(var r=0;r<n.length;r++){var o=n[r],a=this._layers[o];if(!a.__builtin__&&a.refresh){var s=0===r?this._backgroundColor:null;a.refresh(s)}}return this._opts.useDirtyRect&&(this._prevDisplayList=e.slice()),this},t.prototype.refreshHover=function(){this._paintHoverList(this.storage.getDisplayList(!1))},t.prototype._paintHoverList=function(t){var e,i=t.length,n=this._hoverlayer;if(n&&n.clear(),!!i){for(var r={inHover:!0,viewWidth:this._width,viewHeight:this._height},o=0;o<i;o++){var a=t[o];a.__inHover&&(!n&&(n=this._hoverlayer=this.getLayer(1e5)),!e&&(e=n.ctx).save(),(0,l.Dm)(e,a,r,o===i-1))}e&&e.restore()}},t.prototype.getHoverLayer=function(){return this.getLayer(1e5)},t.prototype.paintOne=function(t,e){(0,l.RV)(t,e)},t.prototype._paintList=function(t,e,i,n){if(this._redrawId===n){i=i||!1,this._updateLayerStatus(t);var r=this._doPaintList(t,e,i),o=r.finished,s=r.needsRefreshHover;if(this._needsManuallyCompositing&&this._compositeManually(),s&&this._paintHoverList(t),o)this.eachLayer(function(t){t.afterBrush&&t.afterBrush()});else{var l=this;(0,a.Z)(function(){l._paintList(t,e,i,n)})}}},t.prototype._compositeManually=function(){var t=this.getLayer(314159).ctx,e=this._domRoot.width,i=this._domRoot.height;t.clearRect(0,0,e,i),this.eachBuiltinLayer(function(n){n.virtual&&t.drawImage(n.dom,0,0,e,i)})},t.prototype._doPaintList=function(t,e,i){for(var n=this,o=[],a=this._opts.useDirtyRect,l=0;l<this._zlevelList.length;l++){var u=this._zlevelList[l],h=this._layers[u];h.__builtin__&&h!==this._hoverlayer&&(h.__dirty||i)&&o.push(h)}for(var p=!0,c=!1,d=this,f=0;f<o.length;f++)!function(r){var s,l=o[r],u=l.ctx,h=a&&l.createRepaintRects(t,e,d._width,d._height),f=i?l.__startIndex:l.__drawIndex,v=!i&&l.incremental&&Date.now,g=v&&Date.now(),_=l.zlevel===d._zlevelList[0]?d._backgroundColor:null;if(l.__startIndex===l.__endIndex)l.clear(!1,_,h);else if(f===l.__startIndex){var y=t[f];(!y.incremental||!y.notClear||i)&&l.clear(!1,_,h)}-1===f&&(console.error("For some unknown reason. drawIndex is -1"),f=l.__startIndex);var m=function(e){var i={inHover:!1,allClipped:!1,prevEl:null,viewWidth:n._width,viewHeight:n._height};for(s=f;s<l.__endIndex;s++){var r=t[s];if(r.__inHover&&(c=!0),n._doPaintEl(r,l,a,e,i,s===l.__endIndex-1),v&&Date.now()-g>15)break}i.prevElClipPaths&&u.restore()};if(h){if(0===h.length)s=l.__endIndex;else{for(var x=d.dpr,S=0;S<h.length;++S){var w=h[S];u.save(),u.beginPath(),u.rect(w.x*x,w.y*x,w.width*x,w.height*x),u.clip(),m(w),u.restore()}}}else u.save(),m(),u.restore();l.__drawIndex=s,l.__drawIndex<l.__endIndex&&(p=!1)}(f);return s.Z.wxa&&r.S6(this._layers,function(t){t&&t.ctx&&t.ctx.draw&&t.ctx.draw()}),{finished:p,needsRefreshHover:c}},t.prototype._doPaintEl=function(t,e,i,n,r,o){var a=e.ctx;if(i){var s=t.getPaintRect();(!n||s&&s.intersect(n))&&((0,l.Dm)(a,t,r,o),t.setPrevPaintRect(s))}else(0,l.Dm)(a,t,r,o)},t.prototype.getLayer=function(t,e){this._singleCanvas&&!this._needsManuallyCompositing&&(t=314159);var i=this._layers[t];return!i&&((i=new o.Z("zr_"+t,this,this.dpr)).zlevel=t,i.__builtin__=!0,this._layerConfig[t]?r.TS(i,this._layerConfig[t],!0):this._layerConfig[t-.01]&&r.TS(i,this._layerConfig[t-.01],!0),e&&(i.virtual=e),this.insertLayer(t,i),i.initContext()),i},t.prototype.insertLayer=function(t,e){var i,n=this._layers,r=this._zlevelList,o=r.length,a=this._domRoot,s=null,l=-1;if(n[t])return;if(!!((i=e)&&(i.__builtin__||"function"==typeof i.resize&&"function"==typeof i.refresh))){if(o>0&&t>r[0]){for(l=0;l<o-1&&(!(r[l]<t)||!(r[l+1]>t));l++);s=n[r[l]]}if(r.splice(l+1,0,t),n[t]=e,!e.virtual){if(s){var u=s.dom;u.nextSibling?a.insertBefore(e.dom,u.nextSibling):a.appendChild(e.dom)}else a.firstChild?a.insertBefore(e.dom,a.firstChild):a.appendChild(e.dom)}e.painter||(e.painter=this)}},t.prototype.eachLayer=function(t,e){for(var i=this._zlevelList,n=0;n<i.length;n++){var r=i[n];t.call(e,this._layers[r],r)}},t.prototype.eachBuiltinLayer=function(t,e){for(var i=this._zlevelList,n=0;n<i.length;n++){var r=i[n],o=this._layers[r];o.__builtin__&&t.call(e,o,r)}},t.prototype.eachOtherLayer=function(t,e){for(var i=this._zlevelList,n=0;n<i.length;n++){var r=i[n],o=this._layers[r];!o.__builtin__&&t.call(e,o,r)}},t.prototype.getLayers=function(){return this._layers},t.prototype._updateLayerStatus=function(t){function e(t){s&&(s.__endIndex!==t&&(s.__dirty=!0),s.__endIndex=t)}if(this.eachBuiltinLayer(function(t,e){t.__dirty=t.__used=!1}),this._singleCanvas)for(var i,n,o=1;o<t.length;o++){var a=t[o];if(a.zlevel!==t[o-1].zlevel||a.incremental){this._needsManuallyCompositing=!0;break}}var s=null,l=0;for(n=0;n<t.length;n++){var a=t[n],h=a.zlevel,p=void 0;i!==h&&(i=h,l=0),a.incremental?((p=this.getLayer(h+.001,this._needsManuallyCompositing)).incremental=!0,l=1):p=this.getLayer(h+(l>0?.01:0),this._needsManuallyCompositing),!p.__builtin__&&r.H("ZLevel "+h+" has been used by unkown layer "+p.id),p!==s&&(p.__used=!0,p.__startIndex!==n&&(p.__dirty=!0),p.__startIndex=n,p.incremental?p.__drawIndex=-1:p.__drawIndex=n,e(n),s=p),a.__dirty&u.YV&&!a.__inHover&&(p.__dirty=!0,p.incremental&&p.__drawIndex<0&&(p.__drawIndex=n))}e(n),this.eachBuiltinLayer(function(t,e){!t.__used&&t.getElementCount()>0&&(t.__dirty=!0,t.__startIndex=t.__endIndex=t.__drawIndex=0),t.__dirty&&t.__drawIndex<0&&(t.__drawIndex=t.__startIndex)})},t.prototype.clear=function(){return this.eachBuiltinLayer(this._clearLayer),this},t.prototype._clearLayer=function(t){t.clear()},t.prototype.setBackgroundColor=function(t){this._backgroundColor=t,r.S6(this._layers,function(t){t.setUnpainted()})},t.prototype.configLayer=function(t,e){if(e){var i=this._layerConfig;i[t]?r.TS(i[t],e,!0):i[t]=e;for(var n=0;n<this._zlevelList.length;n++){var o=this._zlevelList[n];if(o===t||o===t+.01){var a=this._layers[o];r.TS(a,i[t],!0)}}}},t.prototype.delLayer=function(t){var e=this._layers,i=this._zlevelList,n=e[t];if(!!n)n.dom.parentNode.removeChild(n.dom),delete e[t],i.splice(r.cq(i,t),1)},t.prototype.resize=function(t,e){if(this._domRoot.style){var i=this._domRoot;i.style.display="none";var n=this._opts,r=this.root;if(null!=t&&(n.width=t),null!=e&&(n.height=e),t=(0,h.ap)(r,0,n),e=(0,h.ap)(r,1,n),i.style.display="",this._width!==t||e!==this._height){for(var o in i.style.width=t+"px",i.style.height=e+"px",this._layers)this._layers.hasOwnProperty(o)&&this._layers[o].resize(t,e);this.refresh(!0)}this._width=t,this._height=e}else{if(null==t||null==e)return;this._width=t,this._height=e,this.getLayer(314159).resize(t,e)}return this},t.prototype.clearLayer=function(t){var e=this._layers[t];e&&e.clear()},t.prototype.dispose=function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},t.prototype.getRenderedCanvas=function(t){if(t=t||{},this._singleCanvas&&!this._compositeManually)return this._layers[314159].dom;var e=new o.Z("image",this,t.pixelRatio||this.dpr);e.initContext(),e.clear(!1,t.backgroundColor||this._backgroundColor);var i=e.ctx;if(t.pixelRatio<=this.dpr){this.refresh();var n=e.dom.width,r=e.dom.height;this.eachLayer(function(t){t.__builtin__?i.drawImage(t.dom,0,0,n,r):t.renderToCanvas&&(i.save(),t.renderToCanvas(i),i.restore())})}else{for(var a={inHover:!1,viewWidth:this._width,viewHeight:this._height},s=this.storage.getDisplayList(!0),u=0,h=s.length;u<h;u++){var p=s[u];(0,l.Dm)(i,p,a,u===h-1)}}return e.dom},t.prototype.getWidth=function(){return this._width},t.prototype.getHeight=function(){return this._height},t}();e.Z=p}}]);