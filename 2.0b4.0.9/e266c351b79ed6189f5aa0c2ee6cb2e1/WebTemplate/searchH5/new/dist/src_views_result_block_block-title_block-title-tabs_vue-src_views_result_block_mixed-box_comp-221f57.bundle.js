"use strict";(self.webpackChunkweixin_search=self.webpackChunkweixin_search||[]).push([["src_views_result_block_block-title_block-title-tabs_vue-src_views_result_block_mixed-box_comp-221f57"],{368795:function(t,e,i){var n=i(826539),r=i(757257),o=i(551900),s=i(25677),c=(0,o.Z)(r.Z,n.s,n.x,!1,null,"68ea6371",null);"function"==typeof s.Z&&(0,s.Z)(c),e.Z=c.exports},390852:function(t,e,i){i.d(e,{Z:function(){return n}});function n(t){t.options.__wxs_id="a19973f1"}},196379:function(t,e,i){var n=i(52577),r=i(112282),o=i(551900),s=i(974572),c=(0,o.Z)(r.Z,n.s,n.x,!1,null,"3fe974ca",null);"function"==typeof s.Z&&(0,s.Z)(c),e.Z=c.exports},875878:function(t,e,i){i.d(e,{Z:function(){return n}});function n(t){t.options.__wxs_id="fdb21ce1"}},91543:function(t,e,i){var n=i(548041),r=i(409287),o=i(551900),s=i(69205),c=i(587639),a=(0,o.Z)(r.Z,n.s,n.x,!1,null,"770cad02",null);"function"==typeof s.Z&&(0,s.Z)(a),"function"==typeof c.Z&&(0,c.Z)(a),e.Z=a.exports},366065:function(t,e,i){i.d(e,{Z:function(){return n}});function n(t){t.options.__ruleConfig={version:2,componentPath:"11308c518",componentType:1,rules:[{model:"cnProps.items[$index].socialTags[$index].filter",path:".rich-media__social-tag",isReportIdFromJson:!0,aliasValue:"视频标签点击位",isInformal:!1,event:1,type:2}]}}},89566:function(t,e,i){i.d(e,{Z:function(){return n}});function n(t){t.options.__wxs_id="842d1dc3"}},25677:function(t,e,i){var n=i(390852);e.Z=n.Z},974572:function(t,e,i){var n=i(875878);e.Z=n.Z},69205:function(t,e,i){var n=i(366065);e.Z=n.Z},587639:function(t,e,i){var n=i(89566);e.Z=n.Z},757257:function(t,e,i){var n=i(866225);e.Z=n.Z},112282:function(t,e,i){var n=i(768698);e.Z=n.Z},409287:function(t,e,i){var n=i(81317);e.Z=n.Z},826539:function(t,e,i){i.d(e,{s:function(){return n},x:function(){return r}});var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"block-title-tabs",attrs:{"data-fcn-block-title-tabs":"","data-fr-11c715205":""}},[t._l(t.tabs,function(e,n){return i("div",{directives:[{name:"active",rawName:"v-active"}],key:e.title,staticClass:"block-title-tab",class:{selected:e.selected},attrs:{"data-cli":""},on:{click:function(i){return t.onTapTab(e,n)}}},[i("div",{staticClass:"block-title-tab-inner"},[t._v(t._s(e.title))])])}),t.filterOptions.length?i("div",{staticClass:"block-title-filter"},[i("pop-over",{ref:"negativePop",staticClass:"negative_wrap",attrs:{"mask-style":{backgroundColor:"transparent"},"class-name":"negative_wrap_prop","spacing-v":8,"spacing-h":16,"data-fc-171b8eecc":""},scopedSlots:t._u([{key:"trigger",fn:function(){return[i("div",{directives:[{name:"active",rawName:"v-active"}],staticClass:"block-title-filter-title active__mask",attrs:{"data-cli":""}},[i("div",{staticClass:"block-title-filter-title-text",class:{selected:t.currentFilterOption!=t.filterOptions[0]}},[t._v(t._s(t.currentFilterOption.title))]),i("ui-arrow",{staticClass:"block-title-filter-arrow",attrs:{direction:t.isMaskShow?"up":"down",align:"flex",size:"big","data-fc-171b8eecd":""}})],1)]},proxy:!0},{key:"content",fn:function(){return[i("div",t._l(t.filterOptions,function(e,n){return i("div",{directives:[{name:"active",rawName:"v-active.stop",modifiers:{stop:!0}}],key:e.paramValue,staticClass:"negative_wrap_content active__absolute",attrs:{"data-cli":""},on:{click:function(i){return i.stopPropagation(),t.changeFilterOption(e,n)},touchmove:function(t){t.stopPropagation(),t.preventDefault()}}},[t._v(t._s(e.title))])}),0)]},proxy:!0}],null,!1,3696839686)})],1):t._e()],2)},r=[]},52577:function(t,e,i){i.d(e,{s:function(){return n},x:function(){return r}});var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("transition",{attrs:{name:"finder-hint-fade","data-fc-149d97a14":"","data-fcn-finder-hint":"","data-fr-144725eca":""}},[t.finderHint&&t.finderHint.items.length?i("div",{directives:[{name:"show",rawName:"v-show",value:t.show,expression:"show"}],staticClass:"finder-hint"},[i("div",{staticClass:"mask"}),i("div",{ref:"main",staticClass:"finder-hint_main"},[i("transition",{attrs:{name:"finder-hint-slide","data-fc-149d97a12":""}},[i("div",{directives:[{name:"show",rawName:"v-show",value:t.show,expression:"show"}]},[i("div",{staticClass:"finder-hint_title"},[i("span",{staticClass:"finder-hint_title-text"},[t._v(t._s(t.finderHint.title))]),i("svg-icon",{staticClass:"delete-icon",attrs:{name:"cross","data-fc-149d97a10":""},nativeOn:{click:function(e){return e.stopPropagation(),t.onHide(e)}}})],1),i("div",{staticClass:"finder-hint_items"},t._l(t.finderHint.items,function(e,n){return i("div",{directives:[{name:"active",rawName:"v-active"}],key:e.word,staticClass:"finder-hint_item",attrs:{"data-cli":""},on:{click:function(i){return i.stopPropagation(),t.onTapHint(e,n)}}},[t._v("\n                "+t._s(e.word)+"\n                "),i("div",{ref:"calcHint",refInFor:!0,staticClass:"finder-hint_item-calc"},[t._v(t._s(e.word))])])}),0)])])],1)]):t._e()])},r=[]},548041:function(t,e,i){i.d(e,{s:function(){return n},x:function(){return r}});var n=function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"rich-media-box",attrs:{"data-back-report-id":this.M_docReportId(),"data-fcn-rich-media-box":"","data-fr-11308c518":""}},[e("rich-media",{attrs:{data:this.d,item:this.item,"type-pos":this.typePos,pos:this.pos,"series-key":""+this.typePos,"group-key":this.d.boxRowIndex?this.typePos+"_"+this.d.boxRowIndex:"","aspect-ratio":this.aspectRatio,"intersection-threshold":this.isFinderInnerSearch?.99:.7,disabled:this.showFinderHint,"trans-bg":this.transBg,"round-corner-size":this.roundCornerSize>0?this.roundCornerSize:this.isFinderInnerSearch?8:4,"data-fc-14e0fdd86":""},on:{tap:this.onTap}}),this.showFinderHint?e("finder-hint",{staticClass:"finder-hint",attrs:{d:this.d,"type-pos":this.typePos,item:this.item,"data-fc-14e0fdd84":""}}):this._e()],1)},r=[]},866225:function(t,e,i){var n=i(720144),r=i(798509),o=i(984928);function s(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=t.getBoundingClientRect(),n=window.pageYOffset||document.documentElement.scrollTop,r=Math.floor(i.top+n+e),o=window.innerHeight||document.documentElement.clientHeight;return r>n?(document.body.style.minHeight="".concat(n+o,"px"),Promise.resolve()):(document.body.style.minHeight="".concat(r+o,"px"),window.scrollTo({top:r,behavior:"instant"}),new Promise(function(e,i){var n=null,o=null,s=function(){return 1>Math.abs((window.pageYOffset||document.documentElement.scrollTop)-r)},c=function(){n&&window.removeEventListener("scroll",n)},a=function(){clearTimeout(o),c(),e()},d=function(){c(),i()},u=function(){clearTimeout(o),o=setTimeout(d,1e3)};s()?a():(u(),n=function(){u(),s()&&a()},window.addEventListener("scroll",n),t.getBoundingClientRect())}))}e.Z={mixins:[r.uW,r.jB],props:{d:{type:Object,default:function(){return{}}},typePos:{type:Number,default:0},tabs:{type:Array,required:!0},filters:{type:Array,default:function(){return[]}}},computed:{filterOptions:function(){return this.filters&&this.filters.length?this.filters[0].options:[]},currentFilterOption:function(){return this.filterOptions.find(function(t){return t.selected})||{}},isMaskShow:function(){return n.Z.$mask.isShow}},watch:{isMaskShow:function(t){if(t){var e="".concat(this.currentFilterOption.title,":filter_option:").concat(r.Zr.getRandom(6));this.M_clickReport({clickZone:0,clickContent:e,skipDocInfo:!0},{actionType:o.At.CLICK_SHOW_DIALOG,reportId:e,itemPos:"0:filter_option"})}else this.hidePopover()},$route:function(t){"result"!==t.name&&this.hidePopover()}},beforeDestroy:function(){this.hidePopover()},methods:{onTapTab:function(t,e){var i=this;this.hidePopover(),this.tabs.find(function(t){return t.selected})!=t&&s(this.$el.parentElement).then(function(){var n="".concat(t.title,":tab:").concat(r.Zr.getRandom(6));i.M_clickReport({clickZone:0,clickContent:n,skipDocInfo:!0},{actionType:o.At.CLICK_COMMON,reportId:n,itemPos:"".concat(e+1,":tab")});var s=i.M_composeParentSid({t:o.X$.SWITCH_TAB,s:r.xB.searchId,did:t.title,rid:i.$store.state.result.previousRid});i.$store.dispatch("result/tabs/switchTab",{selectedTab:t,selectedOption:i.currentFilterOption,block:i.d,parentSearchID:s}),i.$emit("tap:tab",t,e)})},changeFilterOption:function(t,e){var i=this;this.hidePopover();var n=this.tabs.find(function(t){return t.selected});t!=this.currentFilterOption&&s(this.$el.parentElement).then(function(){var s=t.reportId;i.M_clickReport({clickZone:0,clickContent:s,skipDocInfo:!0},{actionType:o.At.VERTICAL,reportId:s,itemPos:"".concat(e+1,":filter_option")});var c=i.M_composeParentSid({t:o.X$.FILTER,s:r.xB.searchId,did:t.title,rid:i.$store.state.result.previousRid});i.$store.dispatch("result/tabs/switchTab",{selectedTab:n,selectedOption:t,block:i.d,parentSearchID:c}),i.$emit("tap:filter",t,e)})},hidePopover:function(){this.$refs.negativePop&&this.$refs.negativePop.hide()}}}},768698:function(t,e,i){var n=i(420629),r=i(984928),o=i(798509),s=i(142862),c=i(745223),a=i(783466),d=i(959018),u=i(151285);function l(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=Array(e);i<e;i++)n[i]=t[i];return n}function f(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{},n=Object.keys(i);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(i).filter(function(t){return Object.getOwnPropertyDescriptor(i,t).enumerable}))),n.forEach(function(e){var n,r,o;n=t,r=e,o=i[e],r in n?Object.defineProperty(n,r,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[r]=o})}return t}function p(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var i,n,r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var o=[],s=!0,c=!1;try{for(r=r.call(t);!(s=(i=r.next()).done)&&(o.push(i.value),!e||o.length!==e);s=!0);}catch(t){c=!0,n=t}finally{try{!s&&null!=r.return&&r.return()}finally{if(c)throw n}}return o}}(t,e)||m(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function h(t){return function(t){if(Array.isArray(t))return l(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||m(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function m(t,e){if(t){if("string"==typeof t)return l(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);if("Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i)return Array.from(i);if("Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return l(t,e)}}e.Z={mixins:[o.jB],props:{d:{type:Object,default:function(){}},typePos:{type:Number,default:0},item:{type:Object,default:function(){}}},data:function(){return{show:!1}},computed:f({},(0,n.rn)({finderHint:function(t){return t.result.finderHint}}),(0,n.Se)({experiment:"result/self/getExperiment"})),mounted:function(){var t=this;this.show=!0,this.$nextTick(function(){t.$refs.main&&t.registerLeaveObserver()}),this.reportArrBase=this.getReportArrBase()},beforeDestroy:function(){this.observer&&this.observer.disconnect(),this.isToBeDestoried=!0,this.unregisterLeaveObserver()},methods:{onTapHint:function(t,e){var i=this;this.makeReport25032({actionType:r.At.VERTICAL,itemInfos:[t.reportId],itemPoses:["".concat(e+1,":").concat(this.M_getItemType(t.reportId))]}),this.reportOld(2,"".concat(t.word,"|").concat(e));var n=i.M_composeParentSid({t:t.parentType||r.X$.RECOMMEND_HINT_AFTER_CLICKING,s:o.xB.searchId,did:t.docID||t.word,rid:i.$store.state.result.previousRid});i.M_checkSpecialSearchBeforeLaunchNewSearch({query:t.word,callback:function(){o.hi.setInputBar({query:t.word,isInputChange:!1}),o.xB.setValue({searchId:""}),i.M_setQuery({page:r.kO.RESULT,query:t.word,tagInfo:{},searchId:"",extReqParams:{key:"parentSearchID",textValue:n}})},extParams:{crossExtReqParams:[{key:"parentSearchID",textValue:n}]}},{newSearchType:r.NK.HINT_AFTER_CLICK,docId:t.word})},onHide:function(){this.makeReport25032({actionType:r.At.CLICK_CLOSE_DIALOG,itemInfos:["term_close:clkdoc_rel_term:".concat(String(Math.random()).substr(2,6))],itemPoses:[]}),this.reportOld(5),this.$store.commit("result/resetFinderHint",{keepCtx:!0})},makeReport25032:function(t,e){var i,n,r=p((this.d.mixedPos||"").split("-"),2),c=r[0],a=r[1];(0,s.Z)(f((i=f({},o.xB.getBase()),n=(n={reqBusinessType:o.xB.type,resultSubType:1,businessType:this.d.real_type,extInfo:JSON.stringify(f({currentTab:o.xB.type},e)),boxInfo:(0,u.To)(this.d,this.typePos),docInfos:["".concat(this.item.docID,":").concat((c?+c-1:0)*2+ +a)],requestId:this.$store.state.result.doneRequestId,showType:this.d.type},n),Object.getOwnPropertyDescriptors?Object.defineProperties(i,Object.getOwnPropertyDescriptors(n)):(function(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);i.push.apply(i,n)}return i})(Object(n)).forEach(function(t){Object.defineProperty(i,t,Object.getOwnPropertyDescriptor(n,t))}),i),t))},getReportArrBase:function(){return[o.xB.searchId,o.xB.sessionId,o.xB.parentSearchID,o.yG.filterReportQuery(o.xB.query),o.xB.scene,this.item.docID,0,"",this.d.real_type,this.d.subType,0]},reportOld:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",i=this.reportArrBase||this.getReportArrBase();o.yG.reportCommonKv(c.t.hintAfterClicking,h(i.slice(0,6)).concat([t,e],h(i.slice(8,10)),[Date.now(),this.finderHint.requestId]))},registerLeaveObserver:function(){var t=this;if(!this.isToBeDestoried)this.observer=new IntersectionObserver(function(e){p(e,1)[0].isIntersecting?t.reportExpose():t.reportLeave()},{threshold:0,rootMargin:"-39px 0px 0px 0px"}),this.observer.observe(this.$refs.main),a.N.onWebviewPause(this.reportLeave),this.$once("hook:beforeDestroy",this.reportLeave)},unregisterLeaveObserver:function(){a.N.offWebviewPause(this.reportLeave)},reportLeave:function(){if(this.unregisterLeaveObserver(),!this.leaveReported)this.reportOld(4),this.leaveReported=!0},reportExpose:function(){var t=this;if(!this.exposeReported)this.makeReport25032({actionType:r.At.EXPOSE_ITEM,itemInfos:this.finderHint.items.map(function(t){return t.reportId}),itemPoses:this.finderHint.items.map(function(e,i){return"".concat(i+1,":").concat(t.M_getItemType(e.reportId))})}),![1,3,5].includes(+this.experiment.mmsearch_finderclickhint_abtest)&&(0,d.vV)(o.AX.finderHint.expose),this.$nextTick(function(){t.reportOld(1,t.finderHint.items.map(function(e,i){var n=t.$refs.calcHint[i].getBoundingClientRect().width,r=t.$refs.calcHint[i].parentElement.getBoundingClientRect().width;return"".concat(e.word,"|").concat(i,"|").concat(n+24-r>=1?0:1)}).join(";"))}),this.exposeReported=!0}}}},81317:function(t,e,i){var n=i(420629),r=i(798509),o=i(136525),s=i(617765),c=i(196379),a=i(462474);function d(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=Array(e);i<e;i++)n[i]=t[i];return n}function u(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{},n=Object.keys(i);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(i).filter(function(t){return Object.getOwnPropertyDescriptor(i,t).enumerable}))),n.forEach(function(e){var n,r,o;n=t,r=e,o=i[e],r in n?Object.defineProperty(n,r,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[r]=o})}return t}function l(t,e){return e=null!=e?e:{},Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):(function(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);i.push.apply(i,n)}return i})(Object(e)).forEach(function(i){Object.defineProperty(t,i,Object.getOwnPropertyDescriptor(e,i))}),t}e.Z={components:{RichMedia:s.Z,FinderHint:c.Z},mixins:[r.jB],props:{d:{type:Object,default:function(){}},typePos:{type:Number,default:0},pos:{type:Number,default:1},transBg:{type:Boolean,default:!1},roundCornerSize:{type:Number,default:0}},computed:l(u({},(0,n.rn)({finderHint:function(t){return t.result.finderHint},searchId:function(t){return t.result.searchId}})),{item:function(){return this.d.items[0]},aspectRatio:function(){var t=this.item.imageData;return t&&t.height&&t.width&&this.d.type==o.jJ.WATERFALL_CHANNEL_ACTIVITY?(t.width/t.height>=1?t.width=t.height:t.width=.75*t.height,t.height/t.width):this.item.videoAspectRatio?Math.max(1,Math.min(4/3,this.item.videoAspectRatio)):4/3},isFinderInnerSearch:function(){return r.xB.isFinderSearch||7==r.xB.type},showFinderHint:function(){return this.finderHint&&this.finderHint.sid==this.searchId&&this.finderHint.feedId&&[this.item.exportId,this.item.feedId,this.item.docID].includes(this.finderHint.feedId)},isLive:function(){return this.item&&!!this.item.exportId&&!!this.item.nonceId}}),created:function(){this.item&&(this.$store.state.result.clientIdToDocIDMap[this.item.docID]=this.item.docID,this.item.exportId&&(this.$store.state.result.clientIdToDocIDMap[this.item.exportId]=this.item.docID)),a.Z.$on(a.U.onFinderItemRefresh,this.onItemRefresh)},beforeDestroy:function(){a.Z.$off(a.U.onFinderItemRefresh,this.onItemRefresh)},methods:{onTap:function(){var t,e;a.Z.$emit(a.U.FINDER_READ_CLICK,{start_play_time_ms:Date.now(),stay_time_ms:0,feed_id:this.isLive?this.item.feedId:this.item.docID,boxID:this.d.boxID,docID:this.item.docID,feedId:this.isLive?this.item.feedId:this.item.docID,exportId:this.item.exportId,title:this.item.title,collectionId:null===(t=this.item.collectionInfo)||void 0===t?void 0:t.collectionId,boxInfo:[this.d.boxID,this.d.boxPos,this.d.boxPosMerge].join(":"),pageNum:this.d.vuePage,realType:this.d.real_type,subType:this.d.subType,showType:this.d.type,resultType:this.d.resultType}),(null===(e=this.item.collectionInfo)||void 0===e?void 0:e.collectionId)&&this.item.docID&&a.Z.$emit(a.U.onFinderFeedFocusChange,{currentTid:this.item.docID,forceRefresh:!0})},onItemRefresh:function(t,e){var i=this;if(this.d.boxID===t){var n=l(u({},this.item,e),{jumpInfo:function(){var t=u({},i.item.jumpInfo||{},e.jumpInfo||{});try{var n,r,o,s=JSON.parse(null===(r=i.item.jumpInfo)||void 0===r?void 0:r.extInfo),c=JSON.parse(null===(o=e.jumpInfo)||void 0===o?void 0:o.extInfo);t.extInfo=JSON.stringify(l(u({},s,c),{sessionId:c.sessionId||s.sessionId||"",behavior:(n=c.behavior||[],function(t){if(Array.isArray(t))return d(t)}(n)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(n)||function(t,e){if(t){if("string"==typeof t)return d(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);if("Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i)return Array.from(i);if("Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return d(t,e)}}(n)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()).concat(["allow_infinite_top_pull"])}))}catch(t){}return t}(),title:this.item.collectionDesc&&e.title||this.item.title,source:this.item.source,prefixIcon:this.item.prefixIcon,exportId:e.exportId||""});this.d.items.splice(0,1,n);var r=n.docID!==this.item.docID;this.$nextTick(function(){i.$emit("relayout"),r&&setTimeout(function(){a.Z.$emit(a.U.exposeByList,["".concat(i.item.docID,"&").concat(i.d.type)])})})}}}}}}]);