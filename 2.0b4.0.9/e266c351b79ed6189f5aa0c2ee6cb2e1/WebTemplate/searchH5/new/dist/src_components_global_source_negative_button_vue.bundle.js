"use strict";(self.webpackChunkweixin_search=self.webpackChunkweixin_search||[]).push([["src_components_global_source_negative_button_vue"],{948927:function(t,e,n){var i=n(431780),a=n(518820),r=n(551900),o=n(795932),s=(0,r.Z)(a.Z,i.s,i.x,!1,null,null,null);"function"==typeof o.Z&&(0,o.Z)(s),e.Z=s.exports},417938:function(t,e,n){n.d(e,{Z:function(){return i}});function i(t){t.options.__wxs_id="104b8623"}},289151:function(t,e,n){var i=n(323680),a=n(848073),r=n(551900),o=n(555259),s=(0,r.Z)(a.Z,i.s,i.x,!1,null,"63fe4076",null);"function"==typeof o.Z&&(0,o.Z)(s),e.Z=s.exports},241058:function(t,e,n){n.d(e,{Z:function(){return i}});function i(t){t.options.__wxs_id="2bc00004"}},721832:function(t,e,n){var i=n(831440),a=n(408844),r=n(551900),o=n(674187),s=(0,r.Z)(a.Z,i.s,i.x,!1,null,null,null);"function"==typeof o.Z&&(0,o.Z)(s),e.Z=s.exports},134693:function(t,e,n){n.d(e,{Z:function(){return i}});function i(t){t.options.__wxs_id="02741037"}},73738:function(t,e,n){var i=n(779314),a=n(496026),r=n(551900),o=n(572376),s=(0,r.Z)(a.Z,i.s,i.x,!1,null,"58ee5de1",null);"function"==typeof o.Z&&(0,o.Z)(s),e.Z=s.exports},967488:function(t,e,n){n.d(e,{Z:function(){return i}});function i(t){t.options.__wxs_id="6d94657d"}},795932:function(t,e,n){var i=n(417938);e.Z=i.Z},555259:function(t,e,n){var i=n(241058);e.Z=i.Z},674187:function(t,e,n){var i=n(134693);e.Z=i.Z},572376:function(t,e,n){var i=n(967488);e.Z=i.Z},518820:function(t,e,n){var i=n(393211);e.Z=i.Z},848073:function(t,e,n){var i=n(609817);e.Z=i.Z},408844:function(t,e,n){var i=n(756483);e.Z=i.Z},496026:function(t,e,n){var i=n(137857);e.Z=i.Z},431780:function(t,e,n){n.d(e,{s:function(){return i},x:function(){return a}});var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return t.popData?n("PopOver",{ref:"popOver",staticClass:"negative__wrap expand-hotspot",attrs:{"class-name":"negative__outter","data-fc-1dae687c4":"","data-fcn-button":"","data-fr-19bb4db1a":""},on:{hide:t.onHide},nativeOn:{click:function(t){t.stopPropagation()},touchstart:function(t){t.stopPropagation()},touchmove:function(t){t.stopPropagation()}},scopedSlots:t._u([{key:"trigger",fn:function(){return[n("ui-image",{directives:[{name:"active",rawName:"v-active.stop",modifiers:{stop:!0}}],staticClass:"source__negative active__mask",attrs:{size:20,url:"https://search.wxqcloud.qq.com/t/fed_upload/37edd6087d3381f2b5c117dc3464a53b/negative-button.svg","aria-hidden":"true","data-fc-1dae687c2":"","data-cli":""}})]},proxy:!0},{key:"content",fn:function(){return[n("pop",{attrs:{data:t.popData,"data-fc-1dae687c0":""},on:{tapJump:t.tapJump,tapConfirm:t.tapConfirm}})]},proxy:!0}],null,!1,3293523770)}):t._e()},a=[]},323680:function(t,e,n){n.d(e,{s:function(){return i},x:function(){return a}});var i=function(){var t=this.$createElement;return(this._self._c||t)("div",{directives:[{name:"active",rawName:"v-active"}],staticClass:"n-plain active__item",attrs:{"data-cli":"","data-fcn-plain":"","data-fr-161c69c99":""},on:{click:this.tap}},[this._v("\n    "+this._s(this.data.complaint.title)+"\n  ")])},a=[]},831440:function(t,e,n){n.d(e,{s:function(){return i},x:function(){return a}});var i=function(){var t=this.$createElement,e=this._self._c||t;return e("div",{attrs:{"data-fcn-pop":"","data-fr-15a688940":""}},[e(this.types[this.data.type],{tag:"component",attrs:{data:this.data,"data-fc-1f2a89e5e":""},on:{tapJump:this.tapJump,tapConfirm:this.tapConfirm}})],1)},a=[]},779314:function(t,e,n){n.d(e,{s:function(){return i},x:function(){return a}});var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"wrap",attrs:{"data-fcn-withReason":"","data-fr-1a704152a":""}},[n("div",{staticClass:"header line"},[n("div",{staticClass:"title line__item"},[n("p",{staticClass:"title__word"},[t._v("\n          "+t._s(t.data.title)+"\n        ")])]),n("div",{staticClass:"button line__item primary no-wrap"},[t.showButton?n("div",{staticClass:"weui-btn weui-btn_mini weui-btn_primary",on:{click:t.tapConfirm}},[t._v("\n          "+t._s(t.data.confirm.title)+"\n        ")]):t._e()])]),n("div",{staticClass:"reasons"},t._l(t.renderReasons,function(e,i){return n("div",{key:e.wording,staticClass:"reason",class:{reason__checked:e.checked},on:{click:function(n){return t.tapReason(e,i)}}},[t._v("\n        "+t._s(e.wording)+"\n      ")])}),0),n("div",{staticClass:"jump",on:{click:t.tapJump}},[n("div",{staticClass:"jump-in"},[t._v("\n        "+t._s(t.data.complaint.title)+"\n      ")])])])},a=[]},393211:function(t,e,n){var i=n(721832),a=n(151285),r=n(783466),o=n(448188),s=n(462474),c=n(798509);e.Z={components:{pop:i.Z},mixins:[a.ZP],props:{data:{type:Object,default:function(){return{}}},item:{type:Object,default:function(){return{}}},pos:{type:Number,default:0},typePos:{type:Number,default:0},needWords:{type:Boolean,default:!1}},computed:{feedback:function(){return this.item.feedback},popData:function(){if(!this.feedback)return!1;var t=(this.$store.state.result.self.resultObj.feedback||{})[this.feedback.id];return!!t&&Object.assign({},t,this.feedback)}},created:function(){s.Z.$on(s.U.viewChange,this._hide)},destroyed:function(){s.Z.$off(s.U.viewChange,this._hide)},methods:{_hide:function(){this.$refs&&this.$refs.popOver&&this.$refs.popOver.hide()},_makeUrlPamrams:function(){var t,e;return t=function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter(function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable}))),i.forEach(function(e){var i,a,r;i=t,a=e,r=n[e],a in i?Object.defineProperty(i,a,{value:r,enumerable:!0,configurable:!0,writable:!0}):i[a]=r})}return t}({},this.base),e=(e={clientType:this.data.real_type||this.data.type,subType:this.data.subType||0,docID:this.item.docID||"",boxID:this.data.boxID||"",boxPos:this.typePos||0,docPos:this.pos||0,docUrl:this.item.jumpUrl||this.item.doc_url||this.item.weappUrl||""},e),Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):(function(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);n.push.apply(n,i)}return n})(Object(e)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(e,n))}),t},onHide:function(){s.Z.$emit(s.U.negativeHide)},tap:function(){this._hide()},tapJump:function(t){if(this.needWords&&this.data.items.length&&t.jumpUrl&&!t.jumpUrl.includes("relatedWords")){var e=this.data.items.map(function(t){return t.word||""}).join("|");t.jumpUrl=this.M_composeUrl(t.jumpUrl,{relatedWords:e,query:c.xB.query})}this.M_go({jumpUrl:this.M_composeUrl(t.jumpUrl,this._makeUrlPamrams())}),this._hide()},tapConfirm:function(t){var e=Object.assign(this._makeUrlPamrams(),{clientTimeStamp:"".concat(+new Date),feedback:t.feedback});r.h.commonCgi({cgiName:t.confirm.cgiName||o.yh.negativeFeedBack,data:e}),this.$refs.popOver.hide(),s.Z.$emit(s.U.showToast,{text:"已反馈"})}}}},609817:function(t,e){e.Z={props:{data:{type:Object,default:function(){return{}}}},methods:{tap:function(){this.$emit("tapJump")}}}},756483:function(t,e,n){var i=n(73738),a=n(289151),r=new(n(116746)).J({withReason:1,plain:2});e.Z={components:{withReason:i.Z,plain:a.Z},props:{data:{type:Object,default:function(){return{}}}},data:function(){return{types:r}},methods:{tapJump:function(){var t=this.data.complaint;this.$emit("tapJump",t)},tapConfirm:function(t){this.$emit("tapConfirm",{confirm:this.data.confirm,feedback:{reasons:t,title:this.data.title}})}}}},137857:function(t,e,n){var i=n(151285),a=n(462474);e.Z={mixins:[i.ZP],props:{data:{type:Object,default:function(){return{}}}},data:function(){return{reasons:[]}},computed:{renderReasons:{set:function(t){this.reasons=t},get:function(){return this.reasons.length?this.reasons:this.data.reasons}},showButton:function(){return this.renderReasons.some(function(t){return t.checked})}},created:function(){var t=this;a.Z.$on(a.U.negativeHide,function(){t.renderReasons=[]})},methods:{tapJump:function(){this.$emit("tapJump")},tapConfirm:function(){this.$emit("tapConfirm",this.renderReasons)},tapReason:function(t,e){this.renderReasons=this.renderReasons.map(function(t,n){return e==n?Object.assign({},t,{checked:t.checked?0:1}):t})}}}}}]);