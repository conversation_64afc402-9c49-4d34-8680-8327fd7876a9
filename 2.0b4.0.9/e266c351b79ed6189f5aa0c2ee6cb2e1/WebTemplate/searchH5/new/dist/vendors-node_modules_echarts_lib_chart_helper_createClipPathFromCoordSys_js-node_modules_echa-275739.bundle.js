"use strict";(self.webpackChunkweixin_search=self.webpackChunkweixin_search||[]).push([["vendors-node_modules_echarts_lib_chart_helper_createClipPathFromCoordSys_js-node_modules_echa-275739"],{518299:function(t,e,n){n.d(e,{ZT:function(){return i}});var r=function(t,e){return(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)};function i(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}},904311:function(t,e,n){n.d(e,{ZT:function(){return i}});var r=function(t,e){return(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)};function i(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}},202953:function(t,e,n){n.d(e,{D:function(){return a},KZ:function(){return s},XD:function(){return h},Zi:function(){return f},bX:function(){return l},eq:function(){return u}});var r=n(807028),i=(0,n(133141).Yf)();function o(t,e,n,i,o,a,s){var u,l=!1;(0,r.mf)(o)?(s=a,a=o,o=null):(0,r.Kn)(o)&&(a=o.cb,s=o.during,l=o.isFrom,u=o.removeOpt,o=o.dataIndex);var c="leave"===t;!c&&e.stopAnimation("leave");var h=function(t,e,n,i,o){if(e&&e.ecModel){var a,s=e.ecModel.getUpdatePayload();a=s&&s.animation}var u=e&&e.isAnimationEnabled(),l="update"===t;if(!u)return null;var c=void 0,h=void 0,f=void 0;return i?(c=(0,r.pD)(i.duration,200),h=(0,r.pD)(i.easing,"cubicOut"),f=0):(c=e.getShallow(l?"animationDurationUpdate":"animationDuration"),h=e.getShallow(l?"animationEasingUpdate":"animationEasing"),f=e.getShallow(l?"animationDelayUpdate":"animationDelay")),a&&(null!=a.duration&&(c=a.duration),null!=a.easing&&(h=a.easing),null!=a.delay&&(f=a.delay)),(0,r.mf)(f)&&(f=f(n,o)),(0,r.mf)(c)&&(c=c(n)),{duration:c||0,delay:f,easing:h}}(t,i,o,c?u||{}:null,i&&i.getAnimationDelayParams?i.getAnimationDelayParams(e,o):null);if(h&&h.duration>0){var f={duration:h.duration,delay:h.delay||0,easing:h.easing,done:a,force:!!a||!!s,setToFinal:!c,scope:t,during:s};l?e.animateFrom(n,f):e.animateTo(n,f)}else e.stopAnimation(),l||e.attr(n),s&&s(1),a&&a()}function a(t,e,n,r,i,a){o("update",t,e,n,r,i,a)}function s(t,e,n,r,i,a){o("enter",t,e,n,r,i,a)}function u(t){if(!t.__zr)return!0;for(var e=0;e<t.animators.length;e++)if("leave"===t.animators[e].scope)return!0;return!1}function l(t,e,n,r,i,a){if(!u(t))o("leave",t,e,n,r,i,a)}function c(t,e,n,r){t.removeTextContent(),t.removeTextGuideLine(),l(t,{style:{opacity:0}},e,n,r)}function h(t,e,n){function r(){t.parent&&t.parent.remove(t)}t.isGroup?t.traverse(function(t){!t.isGroup&&c(t,e,n,r)}):c(t,e,n,r)}function f(t){i(t).oldStyle=t.style}},434337:function(t,e,n){n.d(e,{ID:function(){return u},X0:function(){return l},lQ:function(){return c}});var r=n(406822),i=n(202953),o=n(171093),a=n(31674),s=n(807028);function u(t,e,n,o,a){var u=t.getArea(),l=u.x,c=u.y,h=u.width,f=u.height,p=n.get(["lineStyle","width"])||2;l-=p/2,c-=p/2,h+=p,f+=p,h=Math.ceil(h),l!==Math.floor(l)&&(l=Math.floor(l),h++);var d=new r.Z({shape:{x:l,y:c,width:h,height:f}});if(e){var g=t.getBaseAxis(),v=g.isHorizontal(),y=g.inverse;v?(y&&(d.shape.x+=h),d.shape.width=0):(!y&&(d.shape.y+=f),d.shape.height=0);var m=(0,s.mf)(a)?function(t){a(t,d)}:null;i.KZ(d,{shape:{width:h,height:f,x:l,y:c}},n,null,o,m)}return d}function l(t,e,n){var r=t.getArea(),s=(0,a.NM)(r.r0,1),u=(0,a.NM)(r.r,1),l=new o.C({shape:{cx:(0,a.NM)(t.cx,1),cy:(0,a.NM)(t.cy,1),r0:s,r:u,startAngle:r.startAngle,endAngle:r.endAngle,clockwise:r.clockwise}});return e&&("angle"===t.getBaseAxis().dim?l.shape.endAngle=r.startAngle:l.shape.r=s,i.KZ(l,{shape:{endAngle:r.endAngle,r:u}},n)),l}function c(t,e,n,r,i){if(t){if("polar"===t.type)return l(t,e,n);else if("cartesian2d"===t.type)return u(t,e,n,r,i)}else;return null}},685043:function(t,e,n){n.d(e,{Z:function(){return i}});var r=n(133141);function i(){var t=(0,r.Yf)();return function(e){var n=t(e),r=e.pipelineContext,i=!!n.large,o=!!n.progressiveRender,a=n.large=!!(r&&r.large),s=n.progressiveRender=!!(r&&r.progressiveRender);return!!(i!==a||o!==s)&&"reset"}}},878004:function(t,e,n){var r=n(807028),i=n(670943),o=n(298322),a=n(220016),s=n(133141),u=n(796603),l=n(386953),c=n(429590),h=n(254308),f=n(457714),p=n(894870);e.Z=function(t,e,n){n=n||{};var d,g,v,y,m,_,x,w,S,b,M,T=e.getSourceManager(),k=!1;t?(k=!0,M=(0,c.nx)(t)):k=(M=T.getSource()).sourceFormat===p.cy;var D=(0,l.b)(e);var C=(d=e,g=D,y=d.get("coordinateSystem"),m=u.Z.get(y),g&&g.coordSysDims&&(v=r.UI(g.coordSysDims,function(t){var e={name:t},n=g.axisMap.get(t);if(n){var r=n.get("type");e.type=(0,a.T)(r)}return e})),!v&&(v=m&&(m.getDimensionsInfo?m.getDimensionsInfo():m.dimensions.slice())||["x","y"]),v),I=n.useEncodeDefaulter,A=r.mf(I)?I:I?r.WA(f.pY,C,e):null,P={coordDimensions:C,generateCoord:n.generateCoord,encodeDefine:e.getEncode(),encodeDefaulter:A,canOmitUnusedDimensions:!k},Z=(0,o.Z)(M,P);var L=(_=Z.dimensions,x=n.createInvertedIndices,(w=D)&&r.S6(_,function(t,e){var n=t.coordDim,r=w.categoryAxisMap.get(n);r&&(null==S&&(S=e),t.ordinalMeta=r.getOrdinalMeta(),x&&(t.createInvertedIndices=!0)),null!=t.otherDims.itemName&&(b=!0)}),!b&&null!=S&&(_[S].otherDims.itemName=0),S),O=k?null:T.getSharedDataStore(Z),R=(0,h.BM)(e,{schema:Z,store:O}),N=new i.Z(Z,e);N.setCalculationInfo(R);var E=null!=L&&function(t){if(t.sourceFormat===p.cy){var e=function(t){for(var e=0;e<t.length&&null==t[e];)e++;return t[e]}(t.data||[]);return!r.kJ((0,s.C4)(e))}}(M)?function(t,e,n,r){return r===L?n:this.defaultDimValueGetter(t,e,n,r)}:null;return N.hasItemOption=!1,N.initData(k?M:O,null,E),N}},910904:function(t,e,n){n.d(e,{H:function(){return o},O:function(){return a}});var r=n(279681),i=n(807028);function o(t,e){var n=t.mapDimensionsAll("defaultedLabel"),i=n.length;if(1===i){var o=(0,r.hk)(t,e,n[0]);return null!=o?o+"":null}if(i){for(var a=[],s=0;s<n.length;s++)a.push((0,r.hk)(t,e,n[s]));return a.join(" ")}}function a(t,e){var n=t.mapDimensionsAll("defaultedLabel");if(!(0,i.kJ)(e))return e+"";for(var r=[],o=0;o<n.length;o++){var a=t.getDimensionIndex(n[o]);a>=0&&r.push(e[a])}return r.join(" ")}},220442:function(t,e,n){var r=n(807028),i=n(707498),o=n(798383),a=n(339738),s=n(345262),u=n(276868),l=n(931918),c=n(954069),h=n(31674),f=n(19750),p=n(921715),d=n(217961),g=n(307143),v=n(289186),y=Math.PI,m=function(){function t(t,e){this.group=new i.Z,this.opt=e,this.axisModel=t,(0,r.ce)(e,{labelOffset:0,nameDirection:1,tickDirection:1,labelDirection:1,silent:!0,handleAutoShown:function(){return!0}});var n=new i.Z({x:e.position[0],y:e.position[1],rotation:e.rotation});n.updateTransform(),this._transformGroup=n}return t.prototype.hasBuilder=function(t){return!!_[t]},t.prototype.add=function(t){_[t](this.opt,this.axisModel,this.group,this._transformGroup)},t.prototype.getGroup=function(){return this.group},t.innerTextLayout=function(t,e,n){var r,i,o=(0,h.wW)(e-t);return(0,h.mW)(o)?(i=n>0?"top":"bottom",r="center"):(0,h.mW)(o-y)?(i=n>0?"bottom":"top",r="center"):(i="middle",r=o>0&&o<y?n>0?"right":"left":n>0?"left":"right"),{rotation:o,textAlign:r,textVerticalAlign:i}},t.makeAxisEventDataBase=function(t){var e={componentType:t.mainType,componentIndex:t.componentIndex};return e[t.mainType+"Index"]=t.componentIndex,e},t.isLabelSilent=function(t){var e=t.get("tooltip");return t.get("silent")||!(t.get("triggerEvent")||e&&e.show)},t}(),_={axisLine:function(t,e,n,i){var s=e.get(["axisLine","show"]);if("auto"===s&&t.handleAutoShown&&(s=t.handleAutoShown("axisLine")),!!s){var u=e.axis.getExtent(),l=i.transform,c=[u[0],0],h=[u[1],0],p=c[0]>h[0];l&&((0,d.Ne)(c,c,l),(0,d.Ne)(h,h,l));var g=(0,r.l7)({lineCap:"round"},e.getModel(["axisLine","lineStyle"]).getLineStyle()),v=new o.Z({shape:{x1:c[0],y1:c[1],x2:h[0],y2:h[1]},style:g,strokeContainThreshold:t.strokeContainThreshold||5,silent:!0,z2:1});a.subPixelOptimizeLine(v.shape,v.style.lineWidth),v.anid="line",n.add(v);var y=e.get(["axisLine","symbol"]);if(null!=y){var m=e.get(["axisLine","symbolSize"]);(0,r.HD)(y)&&(y=[y,y]),((0,r.HD)(m)||(0,r.hj)(m))&&(m=[m,m]);var _=(0,f.Cq)(e.get(["axisLine","symbolOffset"])||0,m),x=m[0],w=m[1];(0,r.S6)([{rotate:t.rotation+Math.PI/2,offset:_[0],r:0},{rotate:t.rotation-Math.PI/2,offset:_[1],r:Math.sqrt((c[0]-h[0])*(c[0]-h[0])+(c[1]-h[1])*(c[1]-h[1]))}],function(e,r){if("none"!==y[r]&&null!=y[r]){var i=(0,f.th)(y[r],-x/2,-w/2,x,w,g.stroke,!0),o=e.r+e.offset,a=p?h:c;i.attr({rotation:e.rotate,x:a[0]+o*Math.cos(t.rotation),y:a[1]-o*Math.sin(t.rotation),silent:!0,z2:11}),n.add(i)}})}}},axisTickLabel:function(t,e,n,i){var o=function(t,e,n,i){var o=n.axis,a=n.getModel("axisTick"),s=a.get("show");if("auto"===s&&i.handleAutoShown&&(s=i.handleAutoShown("axisTick")),!(!s||o.scale.isBlank())){for(var u=a.getModel("lineStyle"),l=i.tickDirection*a.get("length"),c=b(o.getTicksCoords(),e.transform,l,(0,r.ce)(u.getLineStyle(),{stroke:n.get(["axisLine","lineStyle","color"])}),"ticks"),h=0;h<c.length;h++)t.add(c[h]);return c}}(n,i,e,t),a=function(t,e,n,i){var o=n.axis;if(!(!(0,r.Jv)(i.axisLabelShow,n.get(["axisLabel","show"]))||o.scale.isBlank())){var a=n.getModel("axisLabel"),h=a.get("margin"),f=o.getViewLabels(),p=((0,r.Jv)(i.labelRotate,a.get("rotate"))||0)*y/180,d=m.innerTextLayout(i.rotation,p,i.labelDirection),g=n.getCategories&&n.getCategories(!0),v=[],_=m.isLabelSilent(n),x=n.get("triggerEvent");return(0,r.S6)(f,function(p,y){var w="ordinal"===o.scale.type?o.scale.getRawOrdinalNumber(p.tickValue):p.tickValue,S=p.formattedLabel,b=p.rawLabel,M=a;if(g&&g[w]){var T=g[w];(0,r.Kn)(T)&&T.textStyle&&(M=new c.Z(T.textStyle,a,n.ecModel))}var k=M.getTextColor()||n.get(["axisLine","lineStyle","color"]),D=o.dataToCoord(w),C=M.getShallow("align",!0)||d.textAlign,I=(0,r.pD)(M.getShallow("alignMinLabel",!0),C),A=(0,r.pD)(M.getShallow("alignMaxLabel",!0),C),P=M.getShallow("verticalAlign",!0)||M.getShallow("baseline",!0)||d.textVerticalAlign,Z=(0,r.pD)(M.getShallow("verticalAlignMinLabel",!0),P),L=(0,r.pD)(M.getShallow("verticalAlignMaxLabel",!0),P),O=new s.ZP({x:D,y:i.labelOffset+i.labelDirection*h,rotation:d.rotation,silent:_,z2:10+(p.level||0),style:(0,l.Lr)(M,{text:S,align:0===y?I:y===f.length-1?A:C,verticalAlign:0===y?Z:y===f.length-1?L:P,fill:(0,r.mf)(k)?k("category"===o.type?b:"value"===o.type?w+"":w,y):k})});if(O.anid="label_"+w,x){var R=m.makeAxisEventDataBase(n);R.targetType="axisLabel",R.value=b,R.tickIndex=y,"category"===o.type&&(R.dataIndex=w),(0,u.A)(O).eventData=R}e.add(O),O.updateTransform(),v.push(O),t.add(O),O.decomposeTransform()}),v}}(n,i,e,t);if(function(t,e,n){if(!(0,g.WY)(t.axis)){var r=t.get(["axisLabel","showMinLabel"]),i=t.get(["axisLabel","showMaxLabel"]);n=n||[];var o=(e=e||[])[0],a=e[1],s=e[e.length-1],u=e[e.length-2],l=n[0],c=n[1],h=n[n.length-1],f=n[n.length-2];!1===r?(x(o),x(l)):w(o,a)&&(r?(x(a),x(c)):(x(o),x(l))),!1===i?(x(s),x(h)):w(u,s)&&(i?(x(u),x(f)):(x(s),x(h)))}}(e,a,o),function(t,e,n,i){var o=n.axis,a=n.getModel("minorTick");if(!a.get("show")||o.scale.isBlank())return;var s=o.getMinorTicksCoords();if(!!s.length)for(var u=a.getModel("lineStyle"),l=i*a.get("length"),c=(0,r.ce)(u.getLineStyle(),(0,r.ce)(n.getModel("axisTick").getLineStyle(),{stroke:n.get(["axisLine","lineStyle","color"])})),h=0;h<s.length;h++){for(var f=b(s[h],e.transform,l,c,"minorticks_"+h),p=0;p<f.length;p++)t.add(f[p])}}(n,i,e,t.tickDirection),e.get(["axisLabel","hideOverlap"])){var h=(0,v.VT)((0,r.UI)(a,function(t){return{label:t,priority:t.z2,defaultAttr:{ignore:t.ignore}}}));(0,v.yl)(h)}},axisName:function(t,e,n,i){var o,c,f=(0,r.Jv)(t.axisName,e.get("name"));if(!!f){var p=e.get("nameLocation"),d=t.nameDirection,g=e.getModel("nameTextStyle"),v=e.get("nameGap")||0,_=e.axis.getExtent(),x=_[0]>_[1]?-1:1,w=["start"===p?_[0]-x*v:"end"===p?_[1]+x*v:(_[0]+_[1])/2,S(p)?t.labelOffset+d*v:0],b=e.get("nameRotate");null!=b&&(b=b*y/180),S(p)?o=m.innerTextLayout(t.rotation,null!=b?b:t.rotation,d):(o=function(t,e,n,r){var i,o,a=(0,h.wW)(n-t),s=r[0]>r[1],u="start"===e&&!s||"start"!==e&&s;return(0,h.mW)(a-y/2)?(o=u?"bottom":"top",i="center"):(0,h.mW)(a-1.5*y)?(o=u?"top":"bottom",i="center"):(o="middle",i=a<1.5*y&&a>y/2?u?"left":"right":u?"right":"left"),{rotation:a,textAlign:i,textVerticalAlign:o}}(t.rotation,p,b||0,_),null!=(c=t.axisNameAvailableWidth)&&(isFinite(c=Math.abs(c/Math.sin(o.rotation)))||(c=null)));var M=g.getFont(),T=e.get("nameTruncate",!0)||{},k=T.ellipsis,D=(0,r.Jv)(t.nameTruncateMaxWidth,T.maxWidth,c),C=new s.ZP({x:w[0],y:w[1],rotation:o.rotation,silent:m.isLabelSilent(e),style:(0,l.Lr)(g,{text:f,font:M,overflow:"truncate",width:D,ellipsis:k,fill:g.getTextColor()||e.get(["axisLine","lineStyle","color"]),align:g.get("align")||o.textAlign,verticalAlign:g.get("verticalAlign")||o.textVerticalAlign}),z2:1});if(a.setTooltipConfig({el:C,componentModel:e,itemName:f}),C.__fullText=f,C.anid="name",e.get("triggerEvent")){var I=m.makeAxisEventDataBase(e);I.targetType="axisName",I.name=f,(0,u.A)(C).eventData=I}i.add(C),C.updateTransform(),n.add(C),C.decomposeTransform()}}};function x(t){t&&(t.ignore=!0)}function w(t,e){var n=t&&t.getBoundingRect().clone(),r=e&&e.getBoundingRect().clone();if(!!n&&!!r){var i=p.yR([]);return p.U1(i,i,-t.rotation),n.applyTransform(p.dC([],i,t.getLocalTransform())),r.applyTransform(p.dC([],i,e.getLocalTransform())),n.intersect(r)}}function S(t){return"middle"===t||"center"===t}function b(t,e,n,r,i){for(var s=[],u=[],l=[],c=0;c<t.length;c++){var h=t[c].coord;u[0]=h,u[1]=0,l[0]=h,l[1]=n,e&&((0,d.Ne)(u,u,e),(0,d.Ne)(l,l,e));var f=new o.Z({shape:{x1:u[0],y1:u[1],x2:l[0],y2:l[1]},style:r,z2:2,autoBatch:!0,silent:!0});a.subPixelOptimizeLine(f.shape,f.style.lineWidth),f.anid=i+"_"+t[c].tickValue,s.push(f)}return s}e.Z=m},865958:function(t,e,n){var r=n(518299),i=n(290696),o=n(860736),a={},s=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return(0,r.ZT)(e,t),e.prototype.render=function(e,n,r,o){this.axisPointerClass&&i.iG(e),t.prototype.render.apply(this,arguments),this._doUpdateAxisPointerClass(e,r,!0)},e.prototype.updateAxisPointer=function(t,e,n,r){this._doUpdateAxisPointerClass(t,n,!1)},e.prototype.remove=function(t,e){var n=this._axisPointer;n&&n.remove(e)},e.prototype.dispose=function(e,n){this._disposeAxisPointer(n),t.prototype.dispose.apply(this,arguments)},e.prototype._doUpdateAxisPointerClass=function(t,n,r){var o=e.getAxisPointerClass(this.axisPointerClass);if(!!o){var a=i.np(t);a?(this._axisPointer||(this._axisPointer=new o)).render(t,a,n,r):this._disposeAxisPointer(n)}},e.prototype._disposeAxisPointer=function(t){this._axisPointer&&this._axisPointer.dispose(t),this._axisPointer=null},e.registerAxisPointerClass=function(t,e){a[t]=e},e.getAxisPointerClass=function(t){return t&&a[t]},e.type="axis",e}(o.Z);e.Z=s},348813:function(t,e,n){n.d(e,{$l:function(){return m},Tf:function(){return y}});var r=n(518299),i=n(807028),o=n(707498),a=n(339738),s=n(798383),u=n(220442),l=n(865958),c=n(157674),h=n(517070),f=n(981740),p=["axisLine","axisTickLabel","axisName"],d=["splitArea","splitLine","minorSplitLine"],g=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n.axisPointerClass="CartesianAxisPointer",n}return(0,r.ZT)(e,t),e.prototype.render=function(e,n,r,s){this.group.removeAll();var l=this._axisGroup;if(this._axisGroup=new o.Z,this.group.add(this._axisGroup),!!e.get("show")){var h=e.getCoordSysModel(),g=c.bK(h,e),y=new u.Z(e,i.l7({handleAutoShown:function(t){for(var n=h.coordinateSystem.getCartesians(),r=0;r<n.length;r++)if((0,f.lM)(n[r].getOtherAxis(e.axis).scale))return!0;return!1}},g));i.S6(p,y.add,y),this._axisGroup.add(y.getGroup()),i.S6(d,function(t){e.get([t,"show"])&&v[t](this,this._axisGroup,e,h)},this),!(s&&"changeAxisOrder"===s.type&&s.isInitSort)&&a.groupTransition(l,this._axisGroup,e),t.prototype.render.call(this,e,n,r,s)}},e.prototype.remove=function(){(0,h.i)(this)},e.type="cartesianAxis",e}(l.Z),v={splitLine:function(t,e,n,r){var o=n.axis;if(!o.scale.isBlank()){var u=n.getModel("splitLine"),l=u.getModel("lineStyle"),c=l.get("color");c=i.kJ(c)?c:[c];for(var h=r.coordinateSystem.getRect(),f=o.isHorizontal(),p=0,d=o.getTicksCoords({tickModel:u}),g=[],v=[],y=l.getLineStyle(),m=0;m<d.length;m++){var _=o.toGlobalCoord(d[m].coord);f?(g[0]=_,g[1]=h.y,v[0]=_,v[1]=h.y+h.height):(g[0]=h.x,g[1]=_,v[0]=h.x+h.width,v[1]=_);var x=p++%c.length,w=d[m].tickValue,S=new s.Z({anid:null!=w?"line_"+d[m].tickValue:null,autoBatch:!0,shape:{x1:g[0],y1:g[1],x2:v[0],y2:v[1]},style:i.ce({stroke:c[x]},y),silent:!0});a.subPixelOptimizeLine(S.shape,y.lineWidth),e.add(S)}}},minorSplitLine:function(t,e,n,r){var i=n.axis,o=n.getModel("minorSplitLine").getModel("lineStyle"),u=r.coordinateSystem.getRect(),l=i.isHorizontal(),c=i.getMinorTicksCoords();if(!!c.length)for(var h=[],f=[],p=o.getLineStyle(),d=0;d<c.length;d++)for(var g=0;g<c[d].length;g++){var v=i.toGlobalCoord(c[d][g].coord);l?(h[0]=v,h[1]=u.y,f[0]=v,f[1]=u.y+u.height):(h[0]=u.x,h[1]=v,f[0]=u.x+u.width,f[1]=v);var y=new s.Z({anid:"minor_line_"+c[d][g].tickValue,autoBatch:!0,shape:{x1:h[0],y1:h[1],x2:f[0],y2:f[1]},style:p,silent:!0});a.subPixelOptimizeLine(y.shape,p.lineWidth),e.add(y)}},splitArea:function(t,e,n,r){(0,h.F)(t,e,n,r)}},y=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return(0,r.ZT)(e,t),e.type="xAxis",e}(g),m=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=y.type,e}return(0,r.ZT)(e,t),e.type="yAxis",e}(g)},517070:function(t,e,n){n.d(e,{F:function(){return a},i:function(){return s}});var r=n(807028),i=n(406822),o=(0,n(133141).Yf)();function a(t,e,n,a){var s=n.axis;if(s.scale.isBlank())return;var u=n.getModel("splitArea"),l=u.getModel("areaStyle"),c=l.get("color"),h=a.coordinateSystem.getRect(),f=s.getTicksCoords({tickModel:u,clamp:!0});if(!!f.length){var p=c.length,d=o(t).splitAreaColors,g=r.kW(),v=0;if(d)for(var y=0;y<f.length;y++){var m=d.get(f[y].tickValue);if(null!=m){v=(m+(p-1)*y)%p;break}}var _=s.toGlobalCoord(f[0].coord),x=l.getAreaStyle();c=r.kJ(c)?c:[c];for(var y=1;y<f.length;y++){var w=s.toGlobalCoord(f[y].coord),S=void 0,b=void 0,M=void 0,T=void 0;s.isHorizontal()?(S=_,b=h.y,M=w-S,T=h.height,_=S+M):(S=h.x,b=_,M=h.width,T=w-b,_=b+T);var k=f[y-1].tickValue;null!=k&&g.set(k,v),e.add(new i.Z({anid:null!=k?"area_"+k:null,shape:{x:S,y:b,width:M,height:T},style:r.ce({fill:c[v]},x),autoBatch:!0,silent:!0})),v=(v+1)%p}o(t).splitAreaColors=g}}function s(t){o(t).splitAreaColors=null}},674252:function(t,e,n){var r=n(518299),i=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return(0,r.ZT)(e,t),e.type="axisPointer",e.defaultOption={show:"auto",z:50,type:"line",snap:!1,triggerTooltip:!0,triggerEmphasis:!0,value:null,status:null,link:[],animation:null,animationDurationUpdate:200,lineStyle:{color:"#B9BEC9",width:1,type:"dashed"},shadowStyle:{color:"rgba(210,219,238,0.2)"},label:{show:!0,formatter:null,precision:"auto",margin:3,color:"#fff",padding:[5,7,5,7],backgroundColor:"auto",borderColor:null,borderWidth:0,borderRadius:3},handle:{show:!1,icon:"M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7v-1.2h6.6z M13.3,22H6.7v-1.2h6.6z M13.3,19.6H6.7v-1.2h6.6z",size:45,margin:50,color:"#333",shadowBlur:3,shadowColor:"#aaa",shadowOffsetX:0,shadowOffsetY:2,throttle:40}},e}(n(882425).Z);e.Z=i},93683:function(t,e,n){var r=n(518299),i=n(390797),o=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return(0,r.ZT)(e,t),e.prototype.render=function(t,e,n){var r=e.getComponent("tooltip"),o=t.get("triggerOn")||r&&r.get("triggerOn")||"mousemove|click";i.z("axisPointer",n,function(t,e,n){"none"!==o&&("leave"===t||o.indexOf(t)>=0)&&n({type:"updateAxisPointer",currTrigger:t,x:e&&e.offsetX,y:e&&e.offsetY})})},e.prototype.remove=function(t,e){i.E("axisPointer",e)},e.prototype.dispose=function(t,e){i.E("axisPointer",e)},e.type="axisPointer",e}(n(860736).Z);e.Z=o},899868:function(t,e,n){var r=n(807028),i=n(707498),o=n(339738),a=n(345262),s=n(202953),u=n(290696),l=n(660847),c=n(71564),h=(0,n(133141).Yf)(),f=r.d9,p=r.ak,d=function(){function t(){this._dragging=!1,this.animationThreshold=15}return t.prototype.render=function(t,e,n,o){var a=e.get("value"),s=e.get("status");if(this._axisModel=t,this._axisPointerModel=e,this._api=n,!!o||this._lastValue!==a||this._lastStatus!==s){this._lastValue=a,this._lastStatus=s;var u=this._group,l=this._handle;if(!s||"hide"===s){u&&u.hide(),l&&l.hide();return}u&&u.show(),l&&l.show();var c={};this.makeElOption(c,a,t,e,n);var h=c.graphicKey;h!==this._lastGraphicKey&&this.clear(n),this._lastGraphicKey=h;var f=this._moveAnimation=this.determineAnimation(t,e);if(u){var p=r.WA(g,e,f);this.updatePointerEl(u,c,p),this.updateLabelEl(u,c,p,e)}else u=this._group=new i.Z,this.createPointerEl(u,c,t,e),this.createLabelEl(u,c,t,e),n.getZr().add(u);m(u,e,!0),this._renderHandle(a)}},t.prototype.remove=function(t){this.clear(t)},t.prototype.dispose=function(t){this.clear(t)},t.prototype.determineAnimation=function(t,e){var n=e.get("animation"),r=t.axis,i="category"===r.type,o=e.get("snap");if(!o&&!i)return!1;if("auto"===n||null==n){var a=this.animationThreshold;if(i&&r.getBandWidth()>a)return!0;if(o){var s=u.r(t).seriesDataCount,l=r.getExtent();return Math.abs(l[0]-l[1])/s>a}return!1}return!0===n},t.prototype.makeElOption=function(t,e,n,r,i){},t.prototype.createPointerEl=function(t,e,n,r){var i=e.pointer;if(i){var a=h(t).pointerEl=new o[i.type](f(e.pointer));t.add(a)}},t.prototype.createLabelEl=function(t,e,n,r){if(e.label){var i=h(t).labelEl=new a.ZP(f(e.label));t.add(i),v(i,r)}},t.prototype.updatePointerEl=function(t,e,n){var r=h(t).pointerEl;r&&e.pointer&&(r.setStyle(e.pointer.style),n(r,{shape:e.pointer.shape}))},t.prototype.updateLabelEl=function(t,e,n,r){var i=h(t).labelEl;i&&(i.setStyle(e.label.style),n(i,{x:e.label.x,y:e.label.y}),v(i,r))},t.prototype._renderHandle=function(t){if(!this._dragging&&!!this.updateHandleTransform){var e,n=this._axisPointerModel,i=this._api.getZr(),a=this._handle,s=n.getModel("handle"),u=n.get("status");if(!s.get("show")||!u||"hide"===u){a&&i.remove(a),this._handle=null;return}!this._handle&&(e=!0,a=this._handle=o.createIcon(s.get("icon"),{cursor:"move",draggable:!0,onmousemove:function(t){l.sT(t.event)},onmousedown:p(this._onHandleDragMove,this,0,0),drift:p(this._onHandleDragMove,this),ondragend:p(this._onHandleDragEnd,this)}),i.add(a)),m(a,n,!1),a.setStyle(s.getItemStyle(null,["color","borderColor","borderWidth","opacity","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"]));var h=s.get("size");!r.kJ(h)&&(h=[h,h]),a.scaleX=h[0]/2,a.scaleY=h[1]/2,c.T9(this,"_doDispatchAxisPointer",s.get("throttle")||0,"fixRate"),this._moveHandleToValue(t,e)}},t.prototype._moveHandleToValue=function(t,e){g(this._axisPointerModel,!e&&this._moveAnimation,this._handle,y(this.getHandleTransform(t,this._axisModel,this._axisPointerModel)))},t.prototype._onHandleDragMove=function(t,e){var n=this._handle;if(!!n){this._dragging=!0;var r=this.updateHandleTransform(y(n),[t,e],this._axisModel,this._axisPointerModel);this._payloadInfo=r,n.stopAnimation(),n.attr(y(r)),h(n).lastProp=null,this._doDispatchAxisPointer()}},t.prototype._doDispatchAxisPointer=function(){if(!!this._handle){var t=this._payloadInfo,e=this._axisModel;this._api.dispatchAction({type:"updateAxisPointer",x:t.cursorPoint[0],y:t.cursorPoint[1],tooltipOption:t.tooltipOption,axesInfo:[{axisDim:e.axis.dim,axisIndex:e.componentIndex}]})}},t.prototype._onHandleDragEnd=function(){if(this._dragging=!1,!!this._handle){var t=this._axisPointerModel.get("value");this._moveHandleToValue(t),this._api.dispatchAction({type:"hideTip"})}},t.prototype.clear=function(t){this._lastValue=null,this._lastStatus=null;var e=t.getZr(),n=this._group,r=this._handle;e&&n&&(this._lastGraphicKey=null,n&&e.remove(n),r&&e.remove(r),this._group=null,this._handle=null,this._payloadInfo=null),c.ZH(this,"_doDispatchAxisPointer")},t.prototype.doClear=function(){},t.prototype.buildLabel=function(t,e,n){return{x:t[n=n||0],y:t[1-n],width:e[n],height:e[1-n]}},t}();function g(t,e,n,i){!function t(e,n){if(!(r.Kn(e)&&r.Kn(n)))return e===n;var i=!0;return r.S6(n,function(n,r){i=i&&t(e[r],n)}),!!i}(h(n).lastProp,i)&&(h(n).lastProp=i,e?s.D(n,i,t):(n.stopAnimation(),n.attr(i)))}function v(t,e){t[e.get(["label","show"])?"show":"hide"]()}function y(t){return{x:t.x||0,y:t.y||0,rotation:t.rotation||0}}function m(t,e,n){var r=e.get("z"),i=e.get("zlevel");t&&t.traverse(function(t){"group"!==t.type&&(null!=r&&(t.z=r),null!=i&&(t.zlevel=i),t.silent=n)})}e.Z=d},142092:function(t,e,n){var r=n(518299),i=n(899868),o=n(902429),a=n(157674),s=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return(0,r.ZT)(e,t),e.prototype.makeElOption=function(t,e,n,r,i){var s=n.axis,c=s.grid,h=r.get("type"),f=u(c,s).getOtherAxis(s).getGlobalExtent(),p=s.toGlobalCoord(s.dataToCoord(e,!0));if(h&&"none"!==h){var d=o.fk(r),g=l[h](s,p,f);g.style=d,t.graphicKey=g.type,t.pointer=g}var v=a.bK(c.model,n);o.gf(e,t,v,n,r,i)},e.prototype.getHandleTransform=function(t,e,n){var r=a.bK(e.axis.grid.model,e,{labelInside:!1});r.labelMargin=n.get(["handle","margin"]);var i=o.Zh(e.axis,t,r);return{x:i[0],y:i[1],rotation:r.rotation+(r.labelDirection<0?Math.PI:0)}},e.prototype.updateHandleTransform=function(t,e,n,r){var i=n.axis,o=i.grid,a=i.getGlobalExtent(!0),s=u(o,i).getOtherAxis(i).getGlobalExtent(),l="x"===i.dim?0:1,c=[t.x,t.y];c[l]+=e[l],c[l]=Math.min(a[1],c[l]),c[l]=Math.max(a[0],c[l]);var h=(s[1]+s[0])/2,f=[h,h];return f[l]=c[l],{x:c[0],y:c[1],rotation:t.rotation,cursorPoint:f,tooltipOption:[{verticalAlign:"middle"},{align:"center"}][l]}},e}(i.Z);function u(t,e){var n={};return n[e.dim+"AxisIndex"]=e.index,t.getCartesian(n)}var l={line:function(t,e,n){return{type:"Line",subPixelOptimize:!0,shape:o.BL([e,n[0]],[e,n[1]],c(t))}},shadow:function(t,e,n){var r=Math.max(1,t.getBandWidth()),i=n[1]-n[0];return{type:"Rect",shape:o.uE([e-r/2,n[0]],[r,i],c(t))}}};function c(t){return"x"===t.dim?0:1}e.Z=s},925703:function(t,e,n){n.d(e,{Z:function(){return u}});var r=n(133141),i=n(290696),o=n(585575),a=n(807028),s=(0,r.Yf)();function u(t,e,n){var r=t.currTrigger,i=[t.x,t.y],u=t.dispatchAction||(0,a.ak)(n.dispatchAction,n),d=e.getComponent("axisPointer").coordSysAxesInfo;if(!!d){p(i)&&(i=(0,o.Z)({seriesIndex:t.seriesIndex,dataIndex:t.dataIndex},e).point);var g=p(i),v=t.axesInfo,y=d.axesInfo,m="leave"===r||p(i),_={},x={},w={list:[],map:{}},S={showPointer:(0,a.WA)(c,x),showTooltip:(0,a.WA)(h,w)};(0,a.S6)(d.coordSysMap,function(t,e){var n=g||t.containPoint(i);(0,a.S6)(d.coordSysAxesInfo[e],function(t,e){var r=t.axis,o=function(t,e){for(var n=0;n<(t||[]).length;n++){var r=t[n];if(e.axis.dim===r.axisDim&&e.axis.model.componentIndex===r.axisIndex)return r}}(v,t);if(!m&&n&&(!v||o)){var a=o&&o.value;null==a&&!g&&(a=r.pointToData(i)),null!=a&&l(t,a,S,!1,_)}})});var b={};return(0,a.S6)(y,function(t,e){var n=t.linkGroup;n&&!x[e]&&(0,a.S6)(n.axesInfo,function(e,r){var i=x[r];if(e!==t&&i){var o=i.value;n.mapper&&(o=t.axis.scale.parse(n.mapper(o,f(e),f(t)))),b[t.key]=o}})}),(0,a.S6)(b,function(t,e){l(y[e],t,S,!0,_)}),function(t,e,n){var r=n.axesInfo=[];(0,a.S6)(e,function(e,n){var i=e.axisPointerModel.option,o=t[n];o?(e.useHandle||(i.status="show"),i.value=o.value,i.seriesDataIndices=(o.payloadBatch||[]).slice()):e.useHandle||(i.status="hide"),"show"===i.status&&r.push({axisDim:e.axis.dim,axisIndex:e.axis.model.componentIndex,value:i.value})})}(x,y,_),function(t,e,n,r){if(p(e)||!t.list.length){r({type:"hideTip"});return}var i=((t.list[0].dataByAxis[0]||{}).seriesDataIndices||[])[0]||{};r({type:"showTip",escapeConnect:!0,x:e[0],y:e[1],tooltipOption:n.tooltipOption,position:n.position,dataIndexInside:i.dataIndexInside,dataIndex:i.dataIndex,seriesIndex:i.seriesIndex,dataByCoordSys:t.list})}(w,i,t,u),function(t,e,n){var r=n.getZr(),i="axisPointerLastHighlights",o=s(r)[i]||{},u=s(r)[i]={};(0,a.S6)(t,function(t,e){var n=t.axisPointerModel.option;"show"===n.status&&t.triggerEmphasis&&(0,a.S6)(n.seriesDataIndices,function(t){u[t.seriesIndex+" | "+t.dataIndex]=t})});var l=[],c=[];(0,a.S6)(o,function(t,e){u[e]||c.push(t)}),(0,a.S6)(u,function(t,e){o[e]||l.push(t)}),c.length&&n.dispatchAction({type:"downplay",escapeConnect:!0,notBlur:!0,batch:c}),l.length&&n.dispatchAction({type:"highlight",escapeConnect:!0,notBlur:!0,batch:l})}(y,u,n),_}}function l(t,e,n,r,i){var o=t.axis;if(!o.scale.isBlank()&&!!o.containData(e)){if(!t.involveSeries){n.showPointer(t,e);return}var s=function(t,e){var n=e.axis,r=n.dim,i=t,o=[],s=Number.MAX_VALUE,u=-1;return(0,a.S6)(e.seriesModels,function(e,l){var c,h,f=e.getData().mapDimensionsAll(r);if(e.getAxisTooltipData){var p=e.getAxisTooltipData(f,t,n);h=p.dataIndices,c=p.nestestValue}else{if(!(h=e.getData().indicesOfNearest(f[0],t,"category"===n.type?.5:null)).length)return;c=e.getData().get(f[0],h[0])}if(null!=c&&!!isFinite(c)){var d=t-c,g=Math.abs(d);g<=s&&((g<s||d>=0&&u<0)&&(s=g,u=d,i=c,o.length=0),(0,a.S6)(h,function(t){o.push({seriesIndex:e.seriesIndex,dataIndexInside:t,dataIndex:e.getData().getRawIndex(t)})}))}}),{payloadBatch:o,snapToValue:i}}(e,t),u=s.payloadBatch,l=s.snapToValue;u[0]&&null==i.seriesIndex&&(0,a.l7)(i,u[0]),!r&&t.snap&&o.containData(l)&&null!=l&&(e=l),n.showPointer(t,e,u),n.showTooltip(t,s,l)}}function c(t,e,n,r){t[e.key]={value:n,payloadBatch:r}}function h(t,e,n,r){var o=n.payloadBatch,a=e.axis,s=a.model,u=e.axisPointerModel;if(!!e.triggerTooltip&&!!o.length){var l=e.coordSys.model,c=i.zm(l),h=t.map[c];!h&&(h=t.map[c]={coordSysId:l.id,coordSysIndex:l.componentIndex,coordSysType:l.type,coordSysMainType:l.mainType,dataByAxis:[]},t.list.push(h)),h.dataByAxis.push({axisDim:a.dim,axisIndex:s.componentIndex,axisType:s.type,axisId:s.id,value:r,valueLabelOpt:{precision:u.get(["label","precision"]),formatter:u.get(["label","formatter"])},seriesDataIndices:o.slice()})}}function f(t){var e=t.axis.model,n={},r=n.axisDim=t.axis.dim;return n.axisIndex=n[r+"AxisIndex"]=e.componentIndex,n.axisName=n[r+"AxisName"]=e.name,n.axisId=n[r+"AxisId"]=e.id,n}function p(t){return!t||null==t[0]||isNaN(t[0])||null==t[1]||isNaN(t[1])}},585575:function(t,e,n){n.d(e,{Z:function(){return o}});var r=n(807028),i=n(133141);function o(t,e){var n,o=[],a=t.seriesIndex;if(null==a||!(n=e.getSeriesByIndex(a)))return{point:[]};var s=n.getData(),u=i.gO(s,t);if(null==u||u<0||r.kJ(u))return{point:[]};var l=s.getItemGraphicEl(u),c=n.coordinateSystem;if(n.getTooltipPosition)o=n.getTooltipPosition(u)||[];else if(c&&c.dataToPoint){if(t.isStacked){var h=c.getBaseAxis(),f=c.getOtherAxis(h).dim,p=h.dim,d="x"===f||"radius"===f?1:0,g=s.mapDimension(p),v=[];v[d]=s.get(g,u),v[1-d]=s.get(s.getCalculationInfo("stackResultDimension"),u),o=c.dataToPoint(v)||[]}else o=c.dataToPoint(s.getValues(r.UI(c.dimensions,function(t){return s.mapDimension(t)}),u))||[]}else if(l){var y=l.getBoundingRect().clone();y.applyTransform(l.transform),o=[y.x+y.width/2,y.y+y.height/2]}return{point:o,el:l}}},390797:function(t,e,n){n.d(e,{E:function(){return c},z:function(){return s}});var r=n(807028),i=n(939828),o=(0,n(133141).Yf)(),a=r.S6;function s(t,e,n){if(!i.Z.node){var s=e.getZr();o(s).records||(o(s).records={}),function(t,e){if(!o(t).initialized)o(t).initialized=!0,n("click",r.WA(l,"click")),n("mousemove",r.WA(l,"mousemove")),n("globalout",u);function n(n,r){t.on(n,function(n){var i=function(t){var e={showTip:[],hideTip:[]},n=function(r){var i=e[r.type];i?i.push(r):(r.dispatchAction=n,t.dispatchAction(r))};return{dispatchAction:n,pendings:e}}(e);a(o(t).records,function(t){t&&r(t,n,i.dispatchAction)}),function(t,e){var n,r=t.showTip.length,i=t.hideTip.length;r?n=t.showTip[r-1]:i&&(n=t.hideTip[i-1]),n&&(n.dispatchAction=null,e.dispatchAction(n))}(i.pendings,e)})}}(s,e),(o(s).records[t]||(o(s).records[t]={})).handler=n}}function u(t,e,n){t.handler("leave",null,n)}function l(t,e,n,r){e.handler(t,n,r)}function c(t,e){if(!i.Z.node){var n=e.getZr();(o(n).records||{})[t]&&(o(n).records[t]=null)}}},294190:function(t,e,n){n.d(e,{N:function(){return c}});var r=n(865958),i=n(142092),o=n(674252),a=n(93683),s=n(807028),u=n(290696),l=n(925703);function c(t){r.Z.registerAxisPointerClass("CartesianAxisPointer",i.Z),t.registerComponentModel(o.Z),t.registerComponentView(a.Z),t.registerPreprocessor(function(t){if(t){t.axisPointer&&0!==t.axisPointer.length||(t.axisPointer={});var e=t.axisPointer.link;e&&!(0,s.kJ)(e)&&(t.axisPointer.link=[e])}}),t.registerProcessor(t.PRIORITY.PROCESSOR.STATISTIC,function(t,e){t.getComponent("axisPointer").coordSysAxesInfo=(0,u.KM)(t,e)}),t.registerAction({type:"updateAxisPointer",event:"updateAxisPointer",update:":updateAxisPointer"},l.Z)}},290696:function(t,e,n){n.d(e,{KM:function(){return o},iG:function(){return s},np:function(){return l},r:function(){return u},zm:function(){return h}});var r=n(954069),i=n(807028);function o(t,e){var n={axesInfo:{},seriesInvolved:!1,coordSysAxesInfo:{},coordSysMap:{}};return function(t,e,n){var o=e.getComponent("tooltip"),s=e.getComponent("axisPointer"),u=s.get("link",!0)||[],l=[];(0,i.S6)(n.getCoordinateSystems(),function(n){if(!!n.axisPointerEnabled){var f=h(n.model),p=t.coordSysAxesInfo[f]={};t.coordSysMap[f]=n;var d=n.model.getModel("tooltip",o);if((0,i.S6)(n.getAxes(),(0,i.WA)(m,!1,null)),n.getTooltipAxes&&o&&d.get("show")){var g="axis"===d.get("trigger"),v="cross"===d.get(["axisPointer","type"]),y=n.getTooltipAxes(d.get(["axisPointer","axis"]));(g||v)&&(0,i.S6)(y.baseAxes,(0,i.WA)(m,!v||"cross",g)),v&&(0,i.S6)(y.otherAxes,(0,i.WA)(m,"cross",!1))}}function m(o,f,g){var v=g.model.getModel("axisPointer",s),y=v.get("show");if(!!y&&("auto"!==y||!!o||!!c(v))){null==f&&(f=v.get("triggerTooltip"));var m=(v=o?function(t,e,n,o,a,s){var u=e.getModel("axisPointer"),l={};(0,i.S6)(["type","snap","lineStyle","shadowStyle","label","animation","animationDurationUpdate","animationEasingUpdate","z"],function(t){l[t]=(0,i.d9)(u.get(t))}),l.snap="category"!==t.type&&!!s,"cross"===u.get("type")&&(l.type="line");var c=l.label||(l.label={});if(null==c.show&&(c.show=!1),"cross"===a){var h=u.get(["label","show"]);if(c.show=null==h||h,!s){var f=l.lineStyle=u.get("crossStyle");f&&(0,i.ce)(c,f.textStyle)}}return t.model.getModel("axisPointer",new r.Z(l,n,o))}(g,d,s,e,o,f):v).get("snap"),_=v.get("triggerEmphasis"),x=h(g.model),w=f||m||"category"===g.type,S=t.axesInfo[x]={key:x,axis:g,coordSys:n,axisPointerModel:v,triggerTooltip:f,triggerEmphasis:_,involveSeries:w,snap:m,useHandle:c(v),seriesModels:[],linkGroup:null};p[x]=S,t.seriesInvolved=t.seriesInvolved||w;var b=function(t,e){for(var n=e.model,r=e.dim,i=0;i<t.length;i++){var o=t[i]||{};if(a(o[r+"AxisId"],n.id)||a(o[r+"AxisIndex"],n.componentIndex)||a(o[r+"AxisName"],n.name))return i}}(u,g);if(null!=b){var M=l[b]||(l[b]={axesInfo:{}});M.axesInfo[x]=S,M.mapper=u[b].mapper,S.linkGroup=M}}}})}(n,t,e),n.seriesInvolved&&function(t,e){e.eachSeries(function(e){var n=e.coordinateSystem,r=e.get(["tooltip","trigger"],!0),o=e.get(["tooltip","show"],!0);if(!!n&&"none"!==r&&!1!==r&&"item"!==r&&!1!==o&&!1!==e.get(["axisPointer","show"],!0))(0,i.S6)(t.coordSysAxesInfo[h(n.model)],function(t){var r=t.axis;n.getAxis(r.dim)===r&&(t.seriesModels.push(e),null==t.seriesDataCount&&(t.seriesDataCount=0),t.seriesDataCount+=e.getData().count())})})}(n,t),n}function a(t,e){return"all"===t||(0,i.kJ)(t)&&(0,i.cq)(t,e)>=0||t===e}function s(t){var e=u(t);if(!!e){var n=e.axisPointerModel,r=e.axis.scale,i=n.option,o=n.get("status"),a=n.get("value");null!=a&&(a=r.parse(a));var s=c(n);null==o&&(i.status=s?"show":"hide");var l=r.getExtent().slice();l[0]>l[1]&&l.reverse(),(null==a||a>l[1])&&(a=l[1]),a<l[0]&&(a=l[0]),i.value=a,s&&(i.status=e.axis.scale.isBlank()?"hide":"show")}}function u(t){var e=(t.ecModel.getComponent("axisPointer")||{}).coordSysAxesInfo;return e&&e.axesInfo[h(t)]}function l(t){var e=u(t);return e&&e.axisPointerModel}function c(t){return!!t.get(["handle","show"])}function h(t){return t.type+"||"+t.id}},902429:function(t,e,n){n.d(e,{BL:function(){return d},Zh:function(){return f},fk:function(){return h},gf:function(){return p},uE:function(){return g}});var r=n(807028),i=n(339738),o=n(310123),a=n(84164),s=n(921715),u=n(307143),l=n(220442),c=n(931918);function h(t){var e,n=t.get("type"),r=t.getModel(n+"Style");return"line"===n?(e=r.getLineStyle()).fill=null:"shadow"===n&&((e=r.getAreaStyle()).stroke=null),e}function f(t,e,n){var r=s.Ue();return s.U1(r,r,n.rotation),s.Iu(r,r,n.position),i.applyTransform([t.dataToCoord(e),(n.labelOffset||0)+(n.labelDirection||1)*(n.labelMargin||0)],r)}function p(t,e,n,i,s,h){var p,d,g,v,y,m,_,x,w,S,b,M,T,k,D,C,I=l.Z.innerTextLayout(n.rotation,0,n.labelDirection);n.labelMargin=s.get(["label","margin"]),p=e,d=i,g=s,v=h,y={position:f(i.axis,t,n),align:I.textAlign,verticalAlign:I.textVerticalAlign},m=function(t,e,n,i,o){t=e.scale.parse(t);var a=e.scale.getLabel({value:t},{precision:o.precision}),s=o.formatter;if(s){var l={value:u.DX(e,{value:t}),axisDimension:e.dim,axisIndex:e.index,seriesData:[]};r.S6(i,function(t){var e=n.getSeriesByIndex(t.seriesIndex),r=t.dataIndexInside,i=e&&e.getDataParams(r);i&&l.seriesData.push(i)}),r.HD(s)?a=s.replace("{value}",a):r.mf(s)&&(a=s(l))}return a}(g.get("value"),d.axis,d.ecModel,g.get("seriesDataIndices"),{precision:g.get(["label","precision"]),formatter:g.get(["label","formatter"])}),_=g.getModel("label"),x=a.MY(_.get("padding")||0),w=_.getFont(),S=o.lP(m,w),b=y.position,M=S.width+x[1]+x[3],T=S.height+x[0]+x[2],"right"===(k=y.align)&&(b[0]-=M),"center"===k&&(b[0]-=M/2),"bottom"===(D=y.verticalAlign)&&(b[1]-=T),"middle"===D&&(b[1]-=T/2),function(t,e,n,r){var i=r.getWidth(),o=r.getHeight();t[0]=Math.min(t[0]+e,i)-e,t[1]=Math.min(t[1]+n,o)-n,t[0]=Math.max(t[0],0),t[1]=Math.max(t[1],0)}(b,M,T,v),(C=_.get("backgroundColor"))&&"auto"!==C||(C=d.get(["axisLine","lineStyle","color"])),p.label={x:b[0],y:b[1],style:(0,c.Lr)(_,{text:m,font:w,fill:_.getTextColor(),padding:x,backgroundColor:C}),z2:10}}function d(t,e,n){return{x1:t[n=n||0],y1:t[1-n],x2:e[n],y2:e[1-n]}}function g(t,e,n){return{x:t[n=n||0],y:t[1-n],width:e[n],height:e[1-n]}}},598929:function(t,e,n){n.d(e,{N:function(){return a}});var r=n(611182),i=n(294190),o=n(574138);function a(t){(0,o.D)(r.N),(0,o.D)(i.N)}},611182:function(t,e,n){n.d(e,{N:function(){return d}});var r=n(518299),i=n(860736),o=n(593636),a=n(406822),s=n(807028),u=n(217902),l=n(803945),c=n(702003),h=n(348813),f=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="grid",e}return(0,r.ZT)(e,t),e.prototype.render=function(t,e){this.group.removeAll(),t.get("show")&&this.group.add(new a.Z({shape:t.coordinateSystem.getRect(),style:(0,s.ce)({fill:t.get("backgroundColor")},t.getItemStyle()),silent:!0,z2:-1}))},e.type="grid",e}(i.Z),p={offset:0};function d(t){t.registerComponentView(f),t.registerComponentModel(o.Z),t.registerCoordinateSystem("cartesian2d",c.Z),(0,l.Z)(t,"x",u.I,p),(0,l.Z)(t,"y",u.I,p),t.registerComponentView(h.Tf),t.registerComponentView(h.$l),t.registerPreprocessor(function(t){t.xAxis&&t.yAxis&&!t.grid&&(t.grid={})})}},602978:function(t,e,n){n.d(e,{w:function(){return s}});var r=n(807028),i=n(849799),o=n(279681),a=n(133141);function s(t){var e,n,s,u,l=t.series,c=t.dataIndex,h=t.multipleSeries,f=l.getData(),p=f.mapDimensionsAll("defaultedTooltip"),d=p.length,g=l.getRawValue(c),v=(0,r.kJ)(g),y=(0,i.jT)(l,c);if(d>1||v&&!d){var m=function(t,e,n,a,s){var u=e.getData(),l=(0,r.u4)(t,function(t,e,n){var r=u.getDimensionInfo(n);return t=t||r&&!1!==r.tooltip&&null!=r.displayName},!1),c=[],h=[],f=[];function p(t,e){var n=u.getDimensionInfo(e);if(!!n&&!1!==n.otherDims.tooltip)l?f.push((0,i.TX)("nameValue",{markerType:"subItem",markerColor:s,name:n.displayName,value:t,valueType:n.type})):(c.push(t),h.push(n.type))}return a.length?(0,r.S6)(a,function(t){p((0,o.hk)(u,n,t),t)}):(0,r.S6)(t,p),{inlineValues:c,inlineValueTypes:h,blocks:f}}(g,l,c,p,y);e=m.inlineValues,n=m.inlineValueTypes,s=m.blocks,u=m.inlineValues[0]}else if(d){var _=f.getDimensionInfo(p[0]);u=e=(0,o.hk)(f,c,p[0]),n=_.type}else u=e=v?g[0]:g;var x=(0,a.yu)(l),w=x&&l.name||"",S=f.getName(c),b=h?w:S;return(0,i.TX)("section",{header:w,noHeader:h||!x,sortParam:u,blocks:[(0,i.TX)("nameValue",{markerType:"item",markerColor:y,name:b,noName:!(0,r.fy)(b),value:e,valueType:n,dataIndex:c})].concat(s||[])})}},849799:function(t,e,n){n.d(e,{TX:function(){return a},jT:function(){return s}});var r=n(84164),i=n(807028),o=n(31674);function a(t,e){return e.type=t,e}function s(t,e){var n=t.getData().getItemVisual(e,"style")[t.visualDrawType];return(0,r.Lz)(n)}!function(){function t(){this.richTextStyles={},this._nextStyleNameId=(0,o.jj)()}t.prototype._generateStyleName=function(){return"__EC_aUTo_"+this._nextStyleNameId++},t.prototype.makeTooltipMarker=function(t,e,n){var o="richText"===n?this._generateStyleName():null,a=(0,r.A0)({color:e,type:t,renderMode:n,markerId:o});return(0,i.HD)(a)?a:(this.richTextStyles[o]=a.style,a.content)},t.prototype.wrapRichTextStyle=function(t,e){var n={};(0,i.kJ)(e)?(0,i.S6)(e,function(t){return(0,i.l7)(n,t)}):(0,i.l7)(n,e);var r=this._generateStyleName();return this.richTextStyles[r]=n,"{"+r+"|"+t+"}"}}()},726006:function(t,e,n){var r=n(807028),i=n(31674),o=n(806745),a=[0,1],s=function(){function t(t,e,n){this.onBand=!1,this.inverse=!1,this.dim=t,this.scale=e,this._extent=n||[0,0]}return t.prototype.contain=function(t){var e=this._extent,n=Math.min(e[0],e[1]),r=Math.max(e[0],e[1]);return t>=n&&t<=r},t.prototype.containData=function(t){return this.scale.contain(t)},t.prototype.getExtent=function(){return this._extent.slice()},t.prototype.getPixelPrecision=function(t){return(0,i.M9)(t||this.scale.getExtent(),this._extent)},t.prototype.setExtent=function(t,e){var n=this._extent;n[0]=t,n[1]=e},t.prototype.dataToCoord=function(t,e){var n=this._extent,r=this.scale;return t=r.normalize(t),this.onBand&&"ordinal"===r.type&&u(n=n.slice(),r.count()),(0,i.NU)(t,a,n,e)},t.prototype.coordToData=function(t,e){var n=this._extent,r=this.scale;this.onBand&&"ordinal"===r.type&&u(n=n.slice(),r.count());var o=(0,i.NU)(t,n,a,e);return this.scale.scale(o)},t.prototype.pointToData=function(t,e){},t.prototype.getTicksCoords=function(t){var e=(t=t||{}).tickModel||this.getTickModel(),n=(0,o.ed)(this,e).ticks,a=(0,r.UI)(n,function(t){return{coord:this.dataToCoord("ordinal"===this.scale.type?this.scale.getRawOrdinalNumber(t):t),tickValue:t}},this);return function(t,e,n,o){var a,s,u=e.length;if(!!t.onBand&&!n&&!!u){var l=t.getExtent();if(1===u)e[0].coord=l[0],a=e[1]={coord:l[1]};else{var c=e[u-1].tickValue-e[0].tickValue,h=(e[u-1].coord-e[0].coord)/c;(0,r.S6)(e,function(t){t.coord-=h/2}),s=1+t.scale.getExtent()[1]-e[u-1].tickValue,a={coord:e[u-1].coord+h*s},e.push(a)}var f=l[0]>l[1];p(e[0].coord,l[0])&&(o?e[0].coord=l[0]:e.shift()),o&&p(l[0],e[0].coord)&&e.unshift({coord:l[0]}),p(l[1],a.coord)&&(o?a.coord=l[1]:e.pop()),o&&p(a.coord,l[1])&&e.push({coord:l[1]})}function p(t,e){return t=(0,i.NM)(t),e=(0,i.NM)(e),f?t>e:t<e}}(this,a,e.get("alignWithLabel"),t.clamp),a},t.prototype.getMinorTicksCoords=function(){if("ordinal"===this.scale.type)return[];var t=this.model.getModel("minorTick").get("splitNumber");!(t>0&&t<100)&&(t=5);var e=this.scale.getMinorTicks(t);return(0,r.UI)(e,function(t){return(0,r.UI)(t,function(t){return{coord:this.dataToCoord(t),tickValue:t}},this)},this)},t.prototype.getViewLabels=function(){return(0,o.Zy)(this).labels},t.prototype.getLabelModel=function(){return this.model.getModel("axisLabel")},t.prototype.getTickModel=function(){return this.model.getModel("axisTick")},t.prototype.getBandWidth=function(){var t=this._extent,e=this.scale.getExtent(),n=e[1]-e[0]+(this.onBand?1:0);return 0===n&&(n=1),Math.abs(Math.abs(t[1]-t[0]))/n},t.prototype.calculateCategoryInterval=function(){return(0,o.qw)(this)},t}();function u(t,e){var n=t[1]-t[0],r=n/e/2;t[0]+=r,t[1]-=r}e.Z=s},567622:function(t,e,n){n.d(e,{H:function(){return r}});function r(t,e){return t.type===e}},484589:function(t,e,n){n.d(e,{z:function(){return u}});var r=n(31674),i=n(177813),o=n(307143),a=n(981740),s=Math.log;function u(t,e,n){var u=i.Z.prototype,l=u.getTicks.call(n),c=u.getTicks.call(n,!0),h=l.length-1,f=u.getInterval.call(n),p=(0,o.Xv)(t,e),d=p.extent,g=p.fixMin,v=p.fixMax;if("log"===t.type){var y=s(t.base);d=[s(d[0])/y,s(d[1])/y]}t.setExtent(d[0],d[1]),t.calcNiceExtent({splitNumber:h,fixMin:g,fixMax:v});var m=u.getExtent.call(t);g&&(d[0]=m[0]),v&&(d[1]=m[1]);var _=u.getInterval.call(t),x=d[0],w=d[1];if(g&&v)_=(w-x)/h;else if(g)for(w=d[0]+_*h;w<d[1]&&isFinite(w)&&isFinite(d[1]);)_=(0,a.r1)(_),w=d[0]+_*h;else if(v)for(x=d[1]-_*h;x>d[0]&&isFinite(x)&&isFinite(d[0]);)_=(0,a.r1)(_),x=d[1]-_*h;else{t.getTicks().length-1>h&&(_=(0,a.r1)(_));var S=_*h;w=Math.ceil(d[1]/_)*_,(x=(0,r.NM)(w-S))<0&&d[0]>=0?(x=0,w=(0,r.NM)(S)):w>0&&d[1]<=0&&(w=0,x=-(0,r.NM)(S))}var b=(l[0].value-c[0].value)/f,M=(l[h].value-c[h].value)/f;u.setExtent.call(t,x+_*b,w+_*M),u.setInterval.call(t,_),(b||M)&&u.setNiceExtent.call(t,x+_,w-_)}},173839:function(t,e,n){n.d(e,{U:function(){return r}});var r={value:1,category:1,time:1,log:1}},715203:function(t,e,n){var r=n(807028),i={show:!0,z:0,inverse:!1,name:"",nameLocation:"end",nameRotate:null,nameTruncate:{maxWidth:null,ellipsis:"...",placeholder:"."},nameTextStyle:{},nameGap:15,silent:!1,triggerEvent:!1,tooltip:{show:!1},axisPointer:{},axisLine:{show:!0,onZero:!0,onZeroAxisIndex:null,lineStyle:{color:"#6E7079",width:1,type:"solid"},symbol:["none","none"],symbolSize:[10,15]},axisTick:{show:!0,inside:!1,length:5,lineStyle:{width:1}},axisLabel:{show:!0,inside:!1,rotate:0,showMinLabel:null,showMaxLabel:null,margin:8,fontSize:12},splitLine:{show:!0,lineStyle:{color:["#E0E6F1"],width:1,type:"solid"}},splitArea:{show:!1,areaStyle:{color:["rgba(250,250,250,0.2)","rgba(210,219,238,0.2)"]}}},o=r.TS({boundaryGap:!0,deduplication:null,splitLine:{show:!1},axisTick:{alignWithLabel:!1,interval:"auto"},axisLabel:{interval:"auto"}},i),a=r.TS({boundaryGap:[0,0],axisLine:{show:"auto"},axisTick:{show:"auto"},splitNumber:5,minorTick:{show:!1,splitNumber:5,length:3,lineStyle:{}},minorSplitLine:{show:!1,lineStyle:{color:"#F4F7FD",width:1}}},i),s=r.TS({splitNumber:6,axisLabel:{showMinLabel:!1,showMaxLabel:!1,rich:{primary:{fontWeight:"bold"}}},splitLine:{show:!1}},a),u=r.ce({logBase:10},a);e.Z={category:o,value:a,time:s,log:u}},307143:function(t,e,n){n.d(e,{DX:function(){return m},Do:function(){return _},J9:function(){return y},Jk:function(){return d},PY:function(){return S},WY:function(){return w},Xv:function(){return p},Yb:function(){return v},aG:function(){return g},rk:function(){return x}});var r=n(807028),i=n(529660),o=n(177813),a=n(275372),s=n(30924),u=n(339296),l=n(337149),c=n(595859),h=n(254308),f=n(647992);function p(t,e){var n=t.type,i=(0,f.Qw)(t,e,t.getExtent()).calculate();t.setBlank(i.isBlank);var o=i.min,a=i.max,u=e.ecModel;if(u&&"time"===n){var l=(0,s.Ge)("bar",u),c=!1;if(r.S6(l,function(t){c=c||t.getBaseAxis()===e.axis}),c){var h=function(t,e,n,i){var o=n.axis.getExtent(),a=o[1]-o[0],u=(0,s.G_)(i,n.axis);if(void 0===u)return{min:t,max:e};var l=1/0;r.S6(u,function(t){l=Math.min(t.offset,l)});var c=-1/0;r.S6(u,function(t){c=Math.max(t.offset+t.width,c)});var h=(l=Math.abs(l))+(c=Math.abs(c)),f=e-t,p=f/(1-(l+c)/a)-f;return e+=c/h*p,{min:t-=l/h*p,max:e}}(o,a,e,(0,s.My)(l));o=h.min,a=h.max}}return{extent:[o,a],fixMin:i.minFixed,fixMax:i.maxFixed}}function d(t,e){var n=p(t,e),r=n.extent,i=e.get("splitNumber");t instanceof c.Z&&(t.base=e.get("logBase"));var o=t.type,a=e.get("interval"),s="interval"===o||"time"===o;t.setExtent(r[0],r[1]),t.calcNiceExtent({splitNumber:i,fixMin:n.fixMin,fixMax:n.fixMax,minInterval:s?e.get("minInterval"):null,maxInterval:s?e.get("maxInterval"):null}),null!=a&&t.setInterval&&t.setInterval(a)}function g(t,e){if(e=e||t.get("type"))switch(e){case"category":return new i.Z({ordinalMeta:t.getOrdinalMeta?t.getOrdinalMeta():t.getCategories(),extent:[1/0,-1/0]});case"time":return new l.Z({locale:t.ecModel.getLocaleModel(),useUTC:t.ecModel.get("useUTC")});default:return new(a.Z.getClass(e)||o.Z)}}function v(t){var e=t.scale.getExtent(),n=e[0],r=e[1];return!(n>0&&r>0||n<0&&r<0)}function y(t){var e,n,i,o=t.getLabelModel().get("formatter"),a="category"===t.type?t.scale.getExtent()[0]:null;if("time"===t.scale.type){;return e=o,function(n,r){return t.scale.getFormattedLabel(n,r,e)}}if(r.HD(o)){;return n=o,function(e){var r=t.scale.getLabel(e);return n.replace("{value}",null!=r?r:"")}}if(!r.mf(o))return function(e){return t.scale.getLabel(e)};else{;return i=o,function(e,n){return null!=a&&(n=e.value-a),i(m(t,e),n,null!=e.level?{level:e.level}:null)}}}function m(t,e){return"category"===t.type?t.scale.getLabel(e):e.value}function _(t){var e,n,r,o=t.model,a=t.scale;if(!(!o.get(["axisLabel","show"])||a.isBlank())){var s=a.getExtent();n=a instanceof i.Z?a.count():(e=a.getTicks()).length;var l=t.getLabelModel(),c=y(t),h=1;n>40&&(h=Math.ceil(n/40));for(var f=0;f<n;f+=h){var p=c(e?e[f]:{value:s[0]+f},f),d=function(t,e){var n=e*Math.PI/180,r=t.width,i=t.height,o=r*Math.abs(Math.cos(n))+Math.abs(i*Math.sin(n)),a=r*Math.abs(Math.sin(n))+Math.abs(i*Math.cos(n));return new u.Z(t.x,t.y,o,a)}(l.getTextRect(p),l.get("rotate")||0);r?r.union(d):r=d}return r}}function x(t){var e=t.get("interval");return null==e?"auto":e}function w(t){return"category"===t.type&&0===x(t.getLabelModel())}function S(t,e){var n={};return r.S6(t.mapDimensionsAll(e),function(e){n[(0,h.IR)(t,e)]=!0}),r.XP(n)}},441390:function(t,e,n){n.d(e,{W:function(){return r}});var r=function(){function t(){}return t.prototype.getNeedCrossZero=function(){return!this.option.scale},t.prototype.getCoordSysModel=function(){},t}()},803945:function(t,e,n){n.d(e,{Z:function(){return l}});var r=n(518299),i=n(715203),o=n(918712),a=n(911992),s=n(173839),u=n(807028);function l(t,e,n,l){(0,u.S6)(s.U,function(s,h){var f=(0,u.TS)((0,u.TS)({},i.Z[h],!0),l,!0),p=function(t){function n(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e+"Axis."+h,n}return(0,r.ZT)(n,t),n.prototype.mergeDefaultAndTheme=function(t,e){var n=(0,o.YD)(this),r=n?(0,o.tE)(t):{},i=e.getTheme();(0,u.TS)(t,i.get(h+"Axis")),(0,u.TS)(t,this.getDefaultOption()),t.type=c(t),n&&(0,o.dt)(t,r,n)},n.prototype.optionUpdated=function(){"category"===this.option.type&&(this.__ordinalMeta=a.Z.createByAxisModel(this))},n.prototype.getCategories=function(t){var e=this.option;if("category"===e.type)return t?e.data:this.__ordinalMeta.categories},n.prototype.getOrdinalMeta=function(){return this.__ordinalMeta},n.type=e+"Axis."+h,n.defaultOption=f,n}(n);t.registerComponentModel(p)}),t.registerSubTypeDefaulter(e+"Axis",c)}function c(t){return t.type||(t.data?"category":"value")}},806745:function(t,e,n){n.d(e,{Zy:function(){return l},ed:function(){return c},qw:function(){return g}});var r=n(807028),i=n(310123),o=n(133141),a=n(307143),s=(0,o.Yf)();function u(t,e){var n=r.UI(e,function(e){return t.scale.parse(e)});return"time"===t.type&&n.length>0&&(n.sort(),n.unshift(n[0]),n.push(n[n.length-1])),n}function l(t){var e=t.getLabelModel().get("customValues");if(e){var n=(0,a.J9)(t);return{labels:u(t,e).map(function(e){var r={value:e};return{formattedLabel:n(r),rawLabel:t.scale.getLabel(r),tickValue:e}})}}return"category"===t.type?function(t){var e=t.getLabelModel(),n=h(t,e);return!e.get("show")||t.scale.isBlank()?{labels:[],labelCategoryInterval:n.labelCategoryInterval}:n}(t):function(t){var e=t.scale.getTicks(),n=(0,a.J9)(t);return{labels:r.UI(e,function(e,r){return{level:e.level,formattedLabel:n(e,r),rawLabel:t.scale.getLabel(e),tickValue:e.value}})}}(t)}function c(t,e){var n=t.getTickModel().get("customValues");return n?{ticks:u(t,n)}:"category"===t.type?function(t,e){var n,i,o=f(t,"ticks"),s=(0,a.rk)(e),u=p(o,s);if(u)return u;if((!e.get("show")||t.scale.isBlank())&&(n=[]),r.mf(s))n=y(t,s,!0);else if("auto"===s){var l=h(t,t.getLabelModel());i=l.labelCategoryInterval,n=r.UI(l.labels,function(t){return t.tickValue})}else n=v(t,i=s,!0);return d(o,s,{ticks:n,tickCategoryInterval:i})}(t,e):{ticks:r.UI(t.scale.getTicks(),function(t){return t.value})}}function h(t,e){var n,i,o=f(t,"labels"),u=(0,a.rk)(e),l=p(o,u);return l?l:(r.mf(u)?n=y(t,u):(i="auto"===u?function(t){var e=s(t).autoInterval;return null!=e?e:s(t).autoInterval=t.calculateCategoryInterval()}(t):u,n=v(t,i)),d(o,u,{labels:n,labelCategoryInterval:i}))}function f(t,e){return s(t)[e]||(s(t)[e]=[])}function p(t,e){for(var n=0;n<t.length;n++)if(t[n].key===e)return t[n].value}function d(t,e,n){return t.push({key:e,value:n}),n}function g(t){var e=function(t){var e=t.getLabelModel();return{axisRotate:t.getRotate?t.getRotate():t.isHorizontal&&!t.isHorizontal()?90:0,labelRotate:e.get("rotate")||0,font:e.getFont()}}(t),n=(0,a.J9)(t),r=(e.axisRotate-e.labelRotate)/180*Math.PI,o=t.scale,u=o.getExtent(),l=o.count();if(u[1]-u[0]<1)return 0;var c=1;l>40&&(c=Math.max(1,Math.floor(l/40)));for(var h=u[0],f=t.dataToCoord(h+1)-t.dataToCoord(h),p=Math.abs(f*Math.cos(r)),d=Math.abs(f*Math.sin(r)),g=0,v=0;h<=u[1];h+=c){var y=0,m=0,_=i.lP(n({value:h}),e.font,"center","top");y=1.3*_.width,m=1.3*_.height,g=Math.max(g,y,7),v=Math.max(v,m,7)}var x=g/p,w=v/d;isNaN(x)&&(x=1/0),isNaN(w)&&(w=1/0);var S=Math.max(0,Math.floor(Math.min(x,w))),b=s(t.model),M=t.getExtent(),T=b.lastAutoInterval,k=b.lastTickCount;return null!=T&&null!=k&&1>=Math.abs(T-S)&&1>=Math.abs(k-l)&&T>S&&b.axisExtent0===M[0]&&b.axisExtent1===M[1]?S=T:(b.lastTickCount=l,b.lastAutoInterval=S,b.axisExtent0=M[0],b.axisExtent1=M[1]),S}function v(t,e,n){var r=(0,a.J9)(t),i=t.scale,o=i.getExtent(),s=t.getLabelModel(),u=[],l=Math.max((e||0)+1,1),c=o[0],h=i.count();0!==c&&l>1&&h/l>2&&(c=Math.round(Math.ceil(c/l)*l));var f=(0,a.WY)(t),p=s.get("showMinLabel")||f,d=s.get("showMaxLabel")||f;p&&c!==o[0]&&v(o[0]);for(var g=c;g<=o[1];g+=l)v(g);function v(t){var e={value:t};u.push(n?t:{formattedLabel:r(e),rawLabel:i.getLabel(e),tickValue:t})}return d&&g-l!==o[1]&&v(o[1]),u}function y(t,e,n){var i=t.scale,o=(0,a.J9)(t),s=[];return r.S6(i.getTicks(),function(t){var r=i.getLabel(t),a=t.value;e(t.value,r)&&s.push(n?a:{formattedLabel:o(t),rawLabel:r,tickValue:a})}),s}},677436:function(t,e,n){var r=n(518299),i=function(t){function e(e,n,r,i,o){var a=t.call(this,e,n,r)||this;return a.index=0,a.type=i||"value",a.position=o||"bottom",a}return(0,r.ZT)(e,t),e.prototype.isHorizontal=function(){var t=this.position;return"top"===t||"bottom"===t},e.prototype.getGlobalExtent=function(t){var e=this.getExtent();return e[0]=this.toGlobalCoord(e[0]),e[1]=this.toGlobalCoord(e[1]),t&&e[0]>e[1]&&e.reverse(),e},e.prototype.pointToData=function(t,e){return this.coordToData(this.toLocalCoord(t["x"===this.dim?0:1]),e)},e.prototype.setCategorySortInfo=function(t){if("category"!==this.type)return!1;this.model.option.categorySortInfo=t,this.scale.setSortInfo(t)},e}(n(726006).Z);e.Z=i},217902:function(t,e,n){n.d(e,{I:function(){return u}});var r=n(518299),i=n(807028),o=n(882425),a=n(441390),s=n(133141),u=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return(0,r.ZT)(e,t),e.prototype.getCoordSysModel=function(){return this.getReferringComponents("grid",s.C6).models[0]},e.type="cartesian2dAxis",e}(o.Z);i.jB(u,a.W)},414954:function(t,e,n){var r=n(807028),i=function(){function t(t){this.type="cartesian",this._dimList=[],this._axes={},this.name=t||""}return t.prototype.getAxis=function(t){return this._axes[t]},t.prototype.getAxes=function(){return r.UI(this._dimList,function(t){return this._axes[t]},this)},t.prototype.getAxesByScale=function(t){return t=t.toLowerCase(),r.hX(this.getAxes(),function(e){return e.scale.type===t})},t.prototype.addAxis=function(t){var e=t.dim;this._axes[e]=t,this._dimList.push(e)},t}();e.Z=i},735612:function(t,e,n){n.d(e,{L:function(){return u}});var r=n(518299),i=n(339296),o=n(414954),a=n(921715),s=n(217961),u=["x","y"];function l(t){return"interval"===t.type||"time"===t.type}var c=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="cartesian2d",e.dimensions=u,e}return(0,r.ZT)(e,t),e.prototype.calcAffineTransform=function(){this._transform=this._invTransform=null;var t=this.getAxis("x").scale,e=this.getAxis("y").scale;if(!l(t)||!l(e))return;var n=t.getExtent(),r=e.getExtent(),i=this.dataToPoint([n[0],r[0]]),o=this.dataToPoint([n[1],r[1]]),s=n[1]-n[0],u=r[1]-r[0];if(!!s&&!!u){var c=(o[0]-i[0])/s,h=(o[1]-i[1])/u,f=i[0]-n[0]*c,p=i[1]-r[0]*h,d=this._transform=[c,0,0,h,f,p];this._invTransform=(0,a.U_)([],d)}},e.prototype.getBaseAxis=function(){return this.getAxesByScale("ordinal")[0]||this.getAxesByScale("time")[0]||this.getAxis("x")},e.prototype.containPoint=function(t){var e=this.getAxis("x"),n=this.getAxis("y");return e.contain(e.toLocalCoord(t[0]))&&n.contain(n.toLocalCoord(t[1]))},e.prototype.containData=function(t){return this.getAxis("x").containData(t[0])&&this.getAxis("y").containData(t[1])},e.prototype.containZone=function(t,e){var n=this.dataToPoint(t),r=this.dataToPoint(e),o=this.getArea(),a=new i.Z(n[0],n[1],r[0]-n[0],r[1]-n[1]);return o.intersect(a)},e.prototype.dataToPoint=function(t,e,n){n=n||[];var r=t[0],i=t[1];if(this._transform&&null!=r&&isFinite(r)&&null!=i&&isFinite(i))return(0,s.Ne)(n,t,this._transform);var o=this.getAxis("x"),a=this.getAxis("y");return n[0]=o.toGlobalCoord(o.dataToCoord(r,e)),n[1]=a.toGlobalCoord(a.dataToCoord(i,e)),n},e.prototype.clampData=function(t,e){var n=this.getAxis("x").scale,r=this.getAxis("y").scale,i=n.getExtent(),o=r.getExtent(),a=n.parse(t[0]),s=r.parse(t[1]);return(e=e||[])[0]=Math.min(Math.max(Math.min(i[0],i[1]),a),Math.max(i[0],i[1])),e[1]=Math.min(Math.max(Math.min(o[0],o[1]),s),Math.max(o[0],o[1])),e},e.prototype.pointToData=function(t,e){var n=[];if(this._invTransform)return(0,s.Ne)(n,t,this._invTransform);var r=this.getAxis("x"),i=this.getAxis("y");return n[0]=r.coordToData(r.toLocalCoord(t[0]),e),n[1]=i.coordToData(i.toLocalCoord(t[1]),e),n},e.prototype.getOtherAxis=function(t){return this.getAxis("x"===t.dim?"y":"x")},e.prototype.getArea=function(t){t=t||0;var e=this.getAxis("x").getGlobalExtent(),n=this.getAxis("y").getGlobalExtent(),r=Math.min(e[0],e[1])-t,o=Math.min(n[0],n[1])-t,a=Math.max(e[0],e[1])-r+t,s=Math.max(n[0],n[1])-o+t;return new i.Z(r,o,a,s)},e}(o.Z);e.Z=c},702003:function(t,e,n){var r=n(807028),i=n(918712),o=n(307143),a=n(735612),s=n(677436),u=n(133141),l=n(157674),c=n(981740),h=n(484589),f=function(){function t(t,e,n){this.type="grid",this._coordsMap={},this._coordsList=[],this._axesMap={},this._axesList=[],this.axisPointerEnabled=!0,this.dimensions=a.L,this._initCartesian(t,e,n),this.model=t}return t.prototype.getRect=function(){return this._rect},t.prototype.update=function(t,e){var n=this._axesMap;function i(t){var e,n=(0,r.XP)(t),i=n.length;if(!!i){for(var a=[],s=i-1;s>=0;s--){var u=t[+n[s]],l=u.model,f=u.scale;(0,c.lM)(f)&&l.get("alignTicks")&&null==l.get("interval")?a.push(u):((0,o.Jk)(f,l),(0,c.lM)(f)&&(e=u))}a.length&&(!e&&(e=a.pop(),(0,o.Jk)(e.scale,e.model)),(0,r.S6)(a,function(t){(0,h.z)(t.scale,t.model,e.scale)}))}}this._updateScale(t,this.model),i(n.x),i(n.y);var a={};(0,r.S6)(n.x,function(t){d(n,"y",t,a)}),(0,r.S6)(n.y,function(t){d(n,"x",t,a)}),this.resize(this.model,e)},t.prototype.resize=function(t,e,n){var a=t.getBoxLayoutParams(),s=!n&&t.get("containLabel"),u=(0,i.ME)(a,{width:e.getWidth(),height:e.getHeight()});this._rect=u;var l=this._axesList;function c(){(0,r.S6)(l,function(t){var e=t.isHorizontal(),n=e?[0,u.width]:[0,u.height],r=t.inverse?1:0;t.setExtent(n[r],n[1-r]),function(t,e){var n=t.getExtent(),r=n[0]+n[1];t.toGlobalCoord="x"===t.dim?function(t){return t+e}:function(t){return r-t+e},t.toLocalCoord="x"===t.dim?function(t){return t-e}:function(t){return r-t+e}}(t,e?u.x:u.y)})}c(),s&&((0,r.S6)(l,function(t){if(!t.model.get(["axisLabel","inside"])){var e=(0,o.Do)(t);if(e){var n=t.isHorizontal()?"height":"width",r=t.model.get(["axisLabel","margin"]);u[n]-=e[n]+r,"top"===t.position?u.y+=e.height+r:"left"===t.position&&(u.x+=e.width+r)}}}),c()),(0,r.S6)(this._coordsList,function(t){t.calcAffineTransform()})},t.prototype.getAxis=function(t,e){var n=this._axesMap[t];if(null!=n)return n[e||0]},t.prototype.getAxes=function(){return this._axesList.slice()},t.prototype.getCartesian=function(t,e){if(null!=t&&null!=e){var n="x"+t+"y"+e;return this._coordsMap[n]}(0,r.Kn)(t)&&(e=t.yAxisIndex,t=t.xAxisIndex);for(var i=0,o=this._coordsList;i<o.length;i++)if(o[i].getAxis("x").index===t||o[i].getAxis("y").index===e)return o[i]},t.prototype.getCartesians=function(){return this._coordsList.slice()},t.prototype.convertToPixel=function(t,e,n){var r=this._findConvertTarget(e);return r.cartesian?r.cartesian.dataToPoint(n):r.axis?r.axis.toGlobalCoord(r.axis.dataToCoord(n)):null},t.prototype.convertFromPixel=function(t,e,n){var r=this._findConvertTarget(e);return r.cartesian?r.cartesian.pointToData(n):r.axis?r.axis.coordToData(r.axis.toLocalCoord(n)):null},t.prototype._findConvertTarget=function(t){var e,n,i=t.seriesModel,o=t.xAxisModel||i&&i.getReferringComponents("xAxis",u.C6).models[0],a=t.yAxisModel||i&&i.getReferringComponents("yAxis",u.C6).models[0],s=t.gridModel,l=this._coordsList;return i?(e=i.coordinateSystem,0>(0,r.cq)(l,e)&&(e=null)):o&&a?e=this.getCartesian(o.componentIndex,a.componentIndex):o?n=this.getAxis("x",o.componentIndex):a?n=this.getAxis("y",a.componentIndex):s&&s.coordinateSystem===this&&(e=this._coordsList[0]),{cartesian:e,axis:n}},t.prototype.containPoint=function(t){var e=this._coordsList[0];if(e)return e.containPoint(t)},t.prototype._initCartesian=function(t,e,n){var i=this,u=this,l={left:!1,right:!1,top:!1,bottom:!1},c={x:{},y:{}},h={x:0,y:0};if(e.eachComponent("xAxis",f("x"),this),e.eachComponent("yAxis",f("y"),this),!h.x||!h.y){this._axesMap={},this._axesList=[];return}function f(e){return function(n,r){if(!!p(n,t)){var i=n.get("position");"x"===e?"top"!==i&&"bottom"!==i&&(i=l.bottom?"top":"bottom"):"left"!==i&&"right"!==i&&(i=l.left?"right":"left"),l[i]=!0;var a=new s.Z(e,(0,o.aG)(n),[0,0],n.get("type"),i),f="category"===a.type;a.onBand=f&&n.get("boundaryGap"),a.inverse=n.get("inverse"),n.axis=a,a.model=n,a.grid=u,a.index=r,u._axesList.push(a),c[e][r]=a,h[e]++}}}this._axesMap=c,(0,r.S6)(c.x,function(e,n){(0,r.S6)(c.y,function(r,o){var s="x"+n+"y"+o,u=new a.Z(s);u.master=i,u.model=t,i._coordsMap[s]=u,i._coordsList.push(u),u.addAxis(e),u.addAxis(r)})})},t.prototype._updateScale=function(t,e){function n(t,e){(0,r.S6)((0,o.PY)(t,e.dim),function(n){e.scale.unionExtentFromData(t,n)})}(0,r.S6)(this._axesList,function(t){if(t.scale.setExtent(1/0,-1/0),"category"===t.type){var e=t.model.get("categorySortInfo");t.scale.setSortInfo(e)}}),t.eachSeries(function(t){if((0,l.Yh)(t)){var r=(0,l.Mk)(t),i=r.xAxisModel,o=r.yAxisModel;if(!!p(i,e)&&!!p(o,e)){var a=this.getCartesian(i.componentIndex,o.componentIndex),s=t.getData(),u=a.getAxis("x"),c=a.getAxis("y");n(s,u),n(s,c)}}},this)},t.prototype.getTooltipAxes=function(t){var e=[],n=[];return(0,r.S6)(this.getCartesians(),function(i){var o=null!=t&&"auto"!==t?i.getAxis(t):i.getBaseAxis(),a=i.getOtherAxis(o);0>(0,r.cq)(e,o)&&e.push(o),0>(0,r.cq)(n,a)&&n.push(a)}),{baseAxes:e,otherAxes:n}},t.create=function(e,n){var r=[];return e.eachComponent("grid",function(i,o){var a=new t(i,e,n);a.name="grid_"+o,a.resize(i,n,!0),i.coordinateSystem=a,r.push(a)}),e.eachSeries(function(t){if(!!(0,l.Yh)(t)){var e=(0,l.Mk)(t),n=e.xAxisModel,r=e.yAxisModel,i=n.getCoordSysModel(),o=i.coordinateSystem;t.coordinateSystem=o.getCartesian(n.componentIndex,r.componentIndex)}}),r},t.dimensions=a.L,t}();function p(t,e){return t.getCoordSysModel()===e}function d(t,e,n,r){n.getAxesOnZeroOf=function(){return i?[i]:[]};var i,o=t[e],a=n.model,s=a.get(["axisLine","onZero"]),u=a.get(["axisLine","onZeroAxisIndex"]);if(!!s){if(null!=u)g(o[u])&&(i=o[u]);else for(var l in o)if(o.hasOwnProperty(l)&&g(o[l])&&!r[c(o[l])]){i=o[l];break}i&&(r[c(i)]=!0)}function c(t){return t.dim+"_"+t.index}}function g(t){return t&&"category"!==t.type&&"time"!==t.type&&(0,o.Yb)(t)}e.Z=f},593636:function(t,e,n){var r=n(518299),i=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return(0,r.ZT)(e,t),e.type="grid",e.dependencies=["xAxis","yAxis"],e.layoutMode="box",e.defaultOption={show:!1,z:0,left:"10%",top:60,right:"10%",bottom:70,containLabel:!1,backgroundColor:"rgba(0,0,0,0)",borderWidth:1,borderColor:"#ccc"},e}(n(882425).Z);e.Z=i},157674:function(t,e,n){n.d(e,{Mk:function(){return s},Yh:function(){return a},bK:function(){return o}});var r=n(807028),i=n(133141);function o(t,e,n){n=n||{};var i=t.coordinateSystem,o=e.axis,a={},s=o.getAxesOnZeroOf()[0],u=o.position,l=s?"onZero":u,c=o.dim,h=i.getRect(),f=[h.x,h.x+h.width,h.y,h.y+h.height],p={left:0,right:1,top:0,bottom:1,onZero:2},d=e.get("offset")||0,g="x"===c?[f[2]-d,f[3]+d]:[f[0]-d,f[1]+d];if(s){var v=s.toGlobalCoord(s.dataToCoord(0));g[p.onZero]=Math.max(Math.min(v,g[1]),g[0])}a.position=["y"===c?g[p[l]]:f[0],"x"===c?g[p[l]]:f[3]],a.rotation=Math.PI/2*("x"===c?0:1);a.labelDirection=a.tickDirection=a.nameDirection=({top:-1,bottom:1,left:-1,right:1})[u],a.labelOffset=s?g[p[u]]-g[p.onZero]:0,e.get(["axisTick","inside"])&&(a.tickDirection=-a.tickDirection),r.Jv(n.labelInside,e.get(["axisLabel","inside"]))&&(a.labelDirection=-a.labelDirection);var y=e.get(["axisLabel","rotate"]);return a.labelRotate="top"===l?-y:y,a.z2=1,a}function a(t){return"cartesian2d"===t.get("coordinateSystem")}function s(t){var e={xAxisModel:null,yAxisModel:null};return r.S6(e,function(n,r){var o=r.replace(/Model$/,""),a=t.getReferringComponents(o,i.C6).models[0];e[r]=a}),e}},647992:function(t,e,n){n.d(e,{Qw:function(){return u}});var r=n(807028),i=n(310123),o=function(){function t(t,e,n){this._prepareParams(t,e,n)}return t.prototype._prepareParams=function(t,e,n){n[1]<n[0]&&(n=[NaN,NaN]),this._dataMin=n[0],this._dataMax=n[1];var o=this._isOrdinal="ordinal"===t.type;this._needCrossZero="interval"===t.type&&e.getNeedCrossZero&&e.getNeedCrossZero();var a=e.get("min",!0);null==a&&(a=e.get("startValue",!0));var s=this._modelMinRaw=a;(0,r.mf)(s)?this._modelMinNum=l(t,s({min:n[0],max:n[1]})):"dataMin"!==s&&(this._modelMinNum=l(t,s));var u=this._modelMaxRaw=e.get("max",!0);if((0,r.mf)(u)?this._modelMaxNum=l(t,u({min:n[0],max:n[1]})):"dataMax"!==u&&(this._modelMaxNum=l(t,u)),o)this._axisDataLen=e.getCategories().length;else{var c=e.get("boundaryGap"),h=(0,r.kJ)(c)?c:[c||0,c||0];"boolean"==typeof h[0]||"boolean"==typeof h[1]?this._boundaryGapInner=[0,0]:this._boundaryGapInner=[(0,i.GM)(h[0],1),(0,i.GM)(h[1],1)]}},t.prototype.calculate=function(){var t=this._isOrdinal,e=this._dataMin,n=this._dataMax,i=this._axisDataLen,o=this._boundaryGapInner,a=t?null:n-e||Math.abs(e),s="dataMin"===this._modelMinRaw?e:this._modelMinNum,u="dataMax"===this._modelMaxRaw?n:this._modelMaxNum,l=null!=s,c=null!=u;null==s&&(s=t?i?0:NaN:e-o[0]*a),null==u&&(u=t?i?i-1:NaN:n+o[1]*a),null!=s&&isFinite(s)||(s=NaN),null!=u&&isFinite(u)||(u=NaN);var h=(0,r.Bu)(s)||(0,r.Bu)(u)||t&&!i;this._needCrossZero&&(s>0&&u>0&&!l&&(s=0),s<0&&u<0&&!c&&(u=0));var f=this._determinedMin,p=this._determinedMax;return null!=f&&(s=f,l=!0),null!=p&&(u=p,c=!0),{min:s,max:u,minFixed:l,maxFixed:c,isBlank:h}},t.prototype.modifyDataMinMax=function(t,e){this[s[t]]=e},t.prototype.setDeterminedMinMax=function(t,e){var n=a[t];this[n]=e},t.prototype.freeze=function(){this.frozen=!0},t}(),a={min:"_determinedMin",max:"_determinedMax"},s={min:"_dataMin",max:"_dataMax"};function u(t,e,n){var r=t.rawExtentInfo;return r?r:(r=new o(t,e,n),t.rawExtentInfo=r,r)}function l(t,e){return null==e?null:(0,r.Bu)(e)?NaN:t.parse(e)}},796603:function(t,e,n){var r=n(807028),i={},o=function(){function t(){this._coordinateSystems=[]}return t.prototype.create=function(t,e){var n=[];r.S6(i,function(r,i){var o=r.create(t,e);n=n.concat(o||[])}),this._coordinateSystems=n},t.prototype.update=function(t,e){r.S6(this._coordinateSystems,function(n){n.update&&n.update(t,e)})},t.prototype.getCoordinateSystems=function(){return this._coordinateSystems.slice()},t.register=function(t,e){i[t]=e},t.get=function(t){return i[t]},t}();e.Z=o},509962:function(t,e,n){var r=n(807028),i=["getDom","getZr","getWidth","getHeight","getDevicePixelRatio","dispatchAction","isSSR","isDisposed","on","off","getDataURL","getConnectedDataURL","getOption","getId","updateLabelLayout"];e.Z=function(t){r.S6(i,function(e){this[e]=r.ak(t[e],t)},this)}},573135:function(t,e,n){var r,i=n(807028),o=n(648446),a=n(115856),s=n(64698),u=n(509962),l=n(133141),c=function(){function t(t,e,n,r){this._stageTaskMap=(0,i.kW)(),this.ecInstance=t,this.api=e,n=this._dataProcessorHandlers=n.slice(),r=this._visualHandlers=r.slice(),this._allHandlers=n.concat(r)}return t.prototype.restoreData=function(t,e){t.restoreData(e),this._stageTaskMap.each(function(t){var e=t.overallTask;e&&e.dirty()})},t.prototype.getPerformArgs=function(t,e){if(!!t.__pipeline){var n=this._pipelineMap.get(t.__pipeline.id),r=n.context,i=!e&&n.progressiveEnabled&&(!r||r.progressiveRender)&&t.__idxInPipeline>n.blockIndex?n.step:null,o=r&&r.modDataCount,a=null!=o?Math.ceil(o/i):null;return{step:i,modBy:a,modDataCount:o}}},t.prototype.getPipeline=function(t){return this._pipelineMap.get(t)},t.prototype.updateStreamModes=function(t,e){var n=this._pipelineMap.get(t.uid),r=t.getData().count(),i=n.progressiveEnabled&&e.incrementalPrepareRender&&r>=n.threshold,o=t.get("large")&&r>=t.get("largeThreshold"),a="mod"===t.get("progressiveChunkMode")?r:null;t.pipelineContext=n.context={progressiveRender:i,modDataCount:a,large:o}},t.prototype.restorePipelines=function(t){var e=this,n=e._pipelineMap=(0,i.kW)();t.eachSeries(function(t){var r=t.getProgressive(),i=t.uid;n.set(i,{id:i,head:null,tail:null,threshold:t.getProgressiveThreshold(),progressiveEnabled:r&&!(t.preventIncremental&&t.preventIncremental()),blockIndex:-1,step:Math.round(r||700),count:0}),e._pipe(t,t.dataTask)})},t.prototype.prepareStageTasks=function(){var t=this._stageTaskMap,e=this.api.getModel(),n=this.api;(0,i.S6)(this._allHandlers,function(r){var o=t.get(r.uid)||t.set(r.uid,{});(0,i.hu)(!(r.reset&&r.overallReset),""),r.reset&&this._createSeriesStageTask(r,o,e,n),r.overallReset&&this._createOverallStageTask(r,o,e,n)},this)},t.prototype.prepareView=function(t,e,n,r){var i=t.renderTask,o=i.context;o.model=e,o.ecModel=n,o.api=r,i.__block=!t.incrementalPrepareRender,this._pipe(e,i)},t.prototype.performDataProcessorTasks=function(t,e){this._performStageTasks(this._dataProcessorHandlers,t,e,{block:!0})},t.prototype.performVisualTasks=function(t,e,n){this._performStageTasks(this._visualHandlers,t,e,n)},t.prototype._performStageTasks=function(t,e,n,r){r=r||{};var o=!1,a=this;function s(t,e){return t.setDirty&&(!t.dirtyMap||t.dirtyMap.get(e.__pipeline.id))}(0,i.S6)(t,function(t,i){if(!r.visualType||r.visualType===t.visualType){var u=a._stageTaskMap.get(t.uid),l=u.seriesTaskMap,c=u.overallTask;if(c){var h,f=c.agentStubMap;f.each(function(t){s(r,t)&&(t.dirty(),h=!0)}),h&&c.dirty(),a.updatePayload(c,n);var p=a.getPerformArgs(c,r.block);f.each(function(t){t.perform(p)}),c.perform(p)&&(o=!0)}else l&&l.each(function(i,u){s(r,i)&&i.dirty();var l=a.getPerformArgs(i,r.block);l.skip=!t.performRawSeries&&e.isSeriesFiltered(i.context.model),a.updatePayload(i,n),i.perform(l)&&(o=!0)})}}),this.unfinished=o||this.unfinished},t.prototype.performSeriesTasks=function(t){var e;t.eachSeries(function(t){e=t.dataTask.perform()||e}),this.unfinished=e||this.unfinished},t.prototype.plan=function(){this._pipelineMap.each(function(t){var e=t.tail;do{if(e.__block){t.blockIndex=e.__idxInPipeline;break}e=e.getUpstream()}while(e)})},t.prototype.updatePayload=function(t,e){"remain"!==e&&(t.context.payload=e)},t.prototype._createSeriesStageTask=function(t,e,n,r){var a=this,s=e.seriesTaskMap,u=e.seriesTaskMap=(0,i.kW)(),l=t.seriesType,c=t.getTargetSeries;function h(e){var i=e.uid,l=u.set(i,s&&s.get(i)||(0,o.v)({plan:g,reset:v,count:_}));l.context={model:e,ecModel:n,api:r,useClearVisual:t.isVisual&&!t.isLayout,plan:t.plan,reset:t.reset,scheduler:a},a._pipe(e,l)}t.createOnAllSeries?n.eachRawSeries(h):l?n.eachRawSeriesByType(l,h):c&&c(n,r).each(h)},t.prototype._createOverallStageTask=function(t,e,n,r){var a=this,s=e.overallTask=e.overallTask||(0,o.v)({reset:h});s.context={ecModel:n,api:r,overallReset:t.overallReset,scheduler:a};var u=s.agentStubMap,l=s.agentStubMap=(0,i.kW)(),c=t.seriesType,p=t.getTargetSeries,g=!0,v=!1;function y(t){var e=t.uid,n=l.set(e,u&&u.get(e)||(v=!0,(0,o.v)({reset:f,onDirty:d})));n.context={model:t,overallProgress:g},n.agent=s,n.__block=g,a._pipe(t,n)}(0,i.hu)(!t.createOnAllSeries,""),c?n.eachRawSeriesByType(c,y):p?p(n,r).each(y):(g=!1,(0,i.S6)(n.getSeries(),y)),v&&s.dirty()},t.prototype._pipe=function(t,e){var n=t.uid,r=this._pipelineMap.get(n);r.head||(r.head=e),r.tail&&r.tail.pipe(e),r.tail=e,e.__idxInPipeline=r.count++,e.__pipeline=r},t.wrapStageHandler=function(t,e){return(0,i.mf)(t)&&(t={overallReset:t,seriesType:function(t){r=null;try{t(x,w)}catch(t){}return r}(t)}),t.uid=(0,a.Kr)("stageHandler"),e&&(t.visualType=e),t},t}();function h(t){t.overallReset(t.ecModel,t.api,t.payload)}function f(t){return t.overallProgress&&p}function p(){this.agent.dirty(),this.getDownstream().dirty()}function d(){this.agent&&this.agent.dirty()}function g(t){return t.plan?t.plan(t.model,t.ecModel,t.api,t.payload):null}function v(t){t.useClearVisual&&t.data.clearAllVisual();var e=t.resetDefines=(0,l.kF)(t.reset(t.model,t.ecModel,t.api,t.payload));return e.length>1?(0,i.UI)(e,function(t,e){return m(e)}):y}var y=m(0);function m(t){return function(e,n){var r=n.data,i=n.resetDefines[t];if(i&&i.dataEach)for(var o=e.start;o<e.end;o++)i.dataEach(r,o);else i&&i.progress&&i.progress(e,r)}}function _(t){return t.data.count()}var x={},w={};function S(t,e){for(var n in e.prototype)t[n]=i.ZT}S(x,s.Z),S(w,u.Z),x.eachSeriesByType=x.eachRawSeriesByType=function(t){r=t},x.eachComponent=function(t){"series"===t.mainType&&t.subType&&(r=t.subType)},e.Z=c},269949:function(t,e,n){n.d(e,{Br:function(){return tV},Hr:function(){return th},JE:function(){return tB},OB:function(){return t$},Og:function(){return tq},Pu:function(){return tW},RS:function(){return tX},S1:function(){return tE},YK:function(){return tU},ds:function(){return tF},je:function(){return tQ},qR:function(){return tY},sq:function(){return tH},yn:function(){return tJ},zl:function(){return tG}});var r,i,o,a,s,u,l,c,h,f,p,d,g,v,y,m,_,x,w,S,b,M=n(518299),T=n(305407),k=n(807028),D=n(939828),C=n(128067),I=n(367582),A=n(64698),P=n(509962),Z=n(796603),L=n(694212),O=n(870740),R=n(381979),N=n(970597),E=n(860736),B=n(804575),z=n(406822),F=n(694923),W=n(202953),H=n(894641),V=n(276868),U=n(623815),G=n(133141),X=n(71564),Y=n(438955),q=n(675247),K=n(573135),j=n(831168),J=n(177112),Q=n(731181),$=n(159611),tt=n(398898),te=n(816691),tn=n(383831),tr=n(983281),ti=n(65763),to=n(765814),ta=n(191743),ts=n(434975),tu=n(857406),tl=n(252919),tc=n(504146),th={PROCESSOR:{FILTER:1e3,SERIES_FILTER:800,STATISTIC:5e3},VISUAL:{LAYOUT:1e3,PROGRESSIVE_LAYOUT:1100,GLOBAL:2e3,CHART:3e3,POST_CHART_LAYOUT:4600,COMPONENT:4e3,BRUSH:5e3,CHART_ITEM:4500,ARIA:6e3,DECAL:7e3}},tf="__flagInMainProcess",tp="__pendingUpdate",td="__needsUpdateStatus",tg=/^[a-zA-Z0-9_]+$/,tv="__connectUpdateStatus";function ty(t){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];if(this.isDisposed()){tT(this.id);return}return t_(this,t,e)}}function tm(t){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return t_(this,t,e)}}function t_(t,e,n){return n[0]=n[0]&&n[0].toLowerCase(),I.Z.prototype[e].apply(t,n)}var tx=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return(0,M.ZT)(e,t),e}(I.Z),tw=tx.prototype;tw.on=tm("on"),tw.off=tm("off");var tS=function(t){function e(e,n,r){var i=t.call(this,new $.v)||this;i._chartsViews=[],i._chartsMap={},i._componentsViews=[],i._componentsMap={},i._pendingActions=[],r=r||{},(0,k.HD)(n)&&(n=tP[n]),i._dom=e;r.ssr&&T.Qq(function(t){var e=(0,V.A)(t),n=e.dataIndex;if(null!=n){var r=(0,k.kW)();return r.set("series_index",e.seriesIndex),r.set("data_index",n),e.ssrType&&r.set("ssr_type",e.ssrType),r}});var o=i._zr=T.S1(e,{renderer:r.renderer||"canvas",devicePixelRatio:r.devicePixelRatio,width:r.width,height:r.height,ssr:r.ssr,useDirtyRect:(0,k.pD)(r.useDirtyRect,!1),useCoarsePointer:(0,k.pD)(r.useCoarsePointer,"auto"),pointerSize:r.pointerSize});i._ssr=r.ssr,i._throttledZrFlush=(0,X.P2)((0,k.ak)(o.flush,o),17),(n=(0,k.d9)(n))&&(0,O.Z)(n,!0),i._theme=n,i._locale=(0,to.D0)(r.locale||to.sO),i._coordSysMgr=new Z.Z;var a=i._api=x(i);function s(t,e){return t.__prio-e.__prio}return(0,C.Z)(tA,s),(0,C.Z)(tC,s),i._scheduler=new K.Z(i,a,tC,tA),i._messageCenter=new tx,i._initEvents(),i.resize=(0,k.ak)(i.resize,i),o.animation.on("frame",i._onframe,i),g(o,i),v(o,i),(0,k.s7)(i),i}return(0,M.ZT)(e,t),e.prototype._onframe=function(){if(!this._disposed){b(this);var t=this._scheduler;if(this[tp]){var e=this[tp].silent;this[tf]=!0;try{a(this),l.update.call(this,null,this[tp].updateParams)}catch(t){throw this[tf]=!1,this[tp]=null,t}this._zr.flush(),this[tf]=!1,this[tp]=null,p.call(this,e),d.call(this,e)}else if(t.unfinished){var n=1,r=this._model,i=this._api;t.unfinished=!1;do{var o=+new Date;t.performSeriesTasks(r),t.performDataProcessorTasks(r),h(this,r),t.performVisualTasks(r),_(this,this._model,i,"remain",{}),n-=+new Date-o}while(n>0&&t.unfinished);!t.unfinished&&this._zr.flush()}}},e.prototype.getDom=function(){return this._dom},e.prototype.getId=function(){return this.id},e.prototype.getZr=function(){return this._zr},e.prototype.isSSR=function(){return this._ssr},e.prototype.setOption=function(t,e,n){if(!this[tf]){if(this._disposed){tT(this.id);return}if((0,k.Kn)(e)&&(n=e.lazyUpdate,r=e.silent,i=e.replaceMerge,o=e.transition,e=e.notMerge),this[tf]=!0,!this._model||e){var r,i,o,s=new L.Z(this._api),u=this._theme,c=this._model=new A.Z;c.scheduler=this._scheduler,c.ssr=this._ssr,c.init(null,null,null,u,this._locale,s)}this._model.setOption(t,{replaceMerge:i},tI);var h={seriesTransition:o,optionChanged:!0};if(n)this[tp]={silent:r,updateParams:h},this[tf]=!1,this.getZr().wakeUp();else{try{a(this),l.update.call(this,null,h)}catch(t){throw this[tp]=null,this[tf]=!1,t}!this._ssr&&this._zr.flush(),this[tp]=null,this[tf]=!1,p.call(this,r),d.call(this,r)}}},e.prototype.setTheme=function(){(0,tn.Sh)("ECharts#setTheme() is DEPRECATED in ECharts 3.0")},e.prototype.getModel=function(){return this._model},e.prototype.getOption=function(){return this._model&&this._model.getOption()},e.prototype.getWidth=function(){return this._zr.getWidth()},e.prototype.getHeight=function(){return this._zr.getHeight()},e.prototype.getDevicePixelRatio=function(){return this._zr.painter.dpr||D.Z.hasGlobalWindow&&window.devicePixelRatio||1},e.prototype.getRenderedCanvas=function(t){return this.renderToCanvas(t)},e.prototype.renderToCanvas=function(t){t=t||{};var e=this._zr.painter;return e.getRenderedCanvas({backgroundColor:t.backgroundColor||this._model.get("backgroundColor"),pixelRatio:t.pixelRatio||this.getDevicePixelRatio()})},e.prototype.renderToSVGString=function(t){t=t||{};var e=this._zr.painter;return e.renderToString({useViewBox:t.useViewBox})},e.prototype.getSvgDataURL=function(){if(!!D.Z.svgSupported){var t=this._zr,e=t.storage.getDisplayList();return(0,k.S6)(e,function(t){t.stopAnimation(null,!0)}),t.painter.toDataURL()}},e.prototype.getDataURL=function(t){if(this._disposed){tT(this.id);return}var e=(t=t||{}).excludeComponents,n=this._model,r=[],i=this;(0,k.S6)(e,function(t){n.eachComponent({mainType:t},function(t){var e=i._componentsMap[t.__viewId];!e.group.ignore&&(r.push(e),e.group.ignore=!0)})});var o="svg"===this._zr.painter.getType()?this.getSvgDataURL():this.renderToCanvas(t).toDataURL("image/"+(t&&t.type||"png"));return(0,k.S6)(r,function(t){t.group.ignore=!1}),o},e.prototype.getConnectedDataURL=function(t){if(this._disposed){tT(this.id);return}var e="svg"===t.type,n=this.group,r=Math.min,i=Math.max,o=1/0;if(!tO[n])return this.getDataURL(t);var a=o,s=o,u=-o,l=-o,c=[],h=t&&t.pixelRatio||this.getDevicePixelRatio();(0,k.S6)(tL,function(o,h){if(o.group===n){var f=e?o.getZr().painter.getSvgDom().innerHTML:o.renderToCanvas((0,k.d9)(t)),p=o.getDom().getBoundingClientRect();a=r(p.left,a),s=r(p.top,s),u=i(p.right,u),l=i(p.bottom,l),c.push({dom:f,left:p.left,top:p.top})}}),a*=h,s*=h,u*=h,l*=h;var f=u-a,p=l-s,d=tl.qW.createCanvas(),g=T.S1(d,{renderer:e?"svg":"canvas"});if(g.resize({width:f,height:p}),!e)return t.connectedBackgroundColor&&g.add(new z.Z({shape:{x:0,y:0,width:f,height:p},style:{fill:t.connectedBackgroundColor}})),(0,k.S6)(c,function(t){var e=new F.ZP({style:{x:t.left*h-a,y:t.top*h-s,image:t.dom}});g.add(e)}),g.refreshImmediately(),d.toDataURL("image/"+(t&&t.type||"png"));var v="";return(0,k.S6)(c,function(t){var e=t.left-a,n=t.top-s;v+='<g transform="translate('+e+","+n+')">'+t.dom+"</g>"}),g.painter.getSvgRoot().innerHTML=v,t.connectedBackgroundColor&&g.painter.setBackgroundColor(t.connectedBackgroundColor),g.refreshImmediately(),g.painter.toDataURL()},e.prototype.convertToPixel=function(t,e){return c(this,"convertToPixel",t,e)},e.prototype.convertFromPixel=function(t,e){return c(this,"convertFromPixel",t,e)},e.prototype.containPixel=function(t,e){if(this._disposed){tT(this.id);return}var n,r=this._model,i=G.pm(r,t);return(0,k.S6)(i,function(t,r){r.indexOf("Models")>=0&&(0,k.S6)(t,function(t){var i=t.coordinateSystem;if(i&&i.containPoint)n=n||!!i.containPoint(e);else if("seriesModels"===r){var o=this._chartsMap[t.__viewId];o&&o.containPoint&&(n=n||o.containPoint(e,t))}},this)},this),!!n},e.prototype.getVisual=function(t,e){var n=this._model,r=G.pm(n,t,{defaultMainType:"series"}),i=r.seriesModel,o=i.getData(),a=r.hasOwnProperty("dataIndexInside")?r.dataIndexInside:r.hasOwnProperty("dataIndex")?o.indexOfRawIndex(r.dataIndex):null;return null!=a?(0,te.Or)(o,a,e):(0,te.UL)(o,e)},e.prototype.getViewOfComponentModel=function(t){return this._componentsMap[t.__viewId]},e.prototype.getViewOfSeriesModel=function(t){return this._chartsMap[t.__viewId]},e.prototype._initEvents=function(){var t=this;(0,k.S6)(tM,function(e){var n=function(n){var r,i=t.getModel(),o=n.target;if("globalout"===e?r={}:o&&(0,ta.o)(o,function(t){var e=(0,V.A)(t);if(e&&null!=e.dataIndex){var n=e.dataModel||i.getSeriesByIndex(e.seriesIndex);return r=n&&n.getDataParams(e.dataIndex,e.dataType,o)||{},!0}if(e.eventData)return r=(0,k.l7)({},e.eventData),!0},!0),r){var a=r.componentType,s=r.componentIndex;("markLine"===a||"markPoint"===a||"markArea"===a)&&(a="series",s=r.seriesIndex);var u=a&&null!=s&&i.getComponent(a,s),l=u&&t["series"===u.mainType?"_chartsMap":"_componentsMap"][u.__viewId];r.event=n,r.type=e,t._$eventProcessor.eventInfo={targetEl:o,packedEvent:r,model:u,view:l},t.trigger(e,r)}};n.zrEventfulCallAtLast=!0,t._zr.on(e,n,t)}),(0,k.S6)(tD,function(e,n){t._messageCenter.on(n,function(t){this.trigger(n,t)},t)}),(0,k.S6)(["selectchanged"],function(e){t._messageCenter.on(e,function(t){this.trigger(e,t)},t)}),(0,tr.s)(this._messageCenter,this,this._api)},e.prototype.isDisposed=function(){return this._disposed},e.prototype.clear=function(){if(this._disposed){tT(this.id);return}this.setOption({series:[]},!0)},e.prototype.dispose=function(){if(this._disposed){tT(this.id);return}this._disposed=!0,this.getDom()&&G.P$(this.getDom(),tN,"");var t=this._api,e=this._model;(0,k.S6)(this._componentsViews,function(n){n.dispose(e,t)}),(0,k.S6)(this._chartsViews,function(n){n.dispose(e,t)}),this._zr.dispose(),this._dom=this._model=this._chartsMap=this._componentsMap=this._chartsViews=this._componentsViews=this._scheduler=this._api=this._zr=this._throttledZrFlush=this._theme=this._coordSysMgr=this._messageCenter=null,delete tL[this.id]},e.prototype.resize=function(t){if(this[tf])return;if(this._disposed){tT(this.id);return}this._zr.resize(t);var e=this._model;if(this._loadingFX&&this._loadingFX.resize(),!!e){var n=e.resetOption("media"),r=t&&t.silent;this[tp]&&(null==r&&(r=this[tp].silent),n=!0,this[tp]=null),this[tf]=!0;try{n&&a(this),l.update.call(this,{type:"resize",animation:(0,k.l7)({duration:0},t&&t.animation)})}catch(t){throw this[tf]=!1,t}this[tf]=!1,p.call(this,r),d.call(this,r)}},e.prototype.showLoading=function(t,e){if(this._disposed){tT(this.id);return}if((0,k.Kn)(t)&&(e=t,t=""),t=t||"default",this.hideLoading(),!!tZ[t]){var n=tZ[t](this._api,e),r=this._zr;this._loadingFX=n,r.add(n)}},e.prototype.hideLoading=function(){if(this._disposed){tT(this.id);return}this._loadingFX&&this._zr.remove(this._loadingFX),this._loadingFX=null},e.prototype.makeActionFromEvent=function(t){var e=(0,k.l7)({},t);return e.type=tD[t.type],e},e.prototype.dispatchAction=function(t,e){if(this._disposed){tT(this.id);return}if(!(0,k.Kn)(e)&&(e={silent:!!e}),!!tk[t.type]&&!!this._model){if(this[tf]){this._pendingActions.push(t);return}var n=e.silent;f.call(this,t,n);var r=e.flush;r?this._zr.flush():!1!==r&&D.Z.browser.weChat&&this._throttledZrFlush(),p.call(this,n),d.call(this,n)}},e.prototype.updateLabelLayout=function(){tu.Z.trigger("series:layoutlabels",this._model,this._api,{updatedSeries:[]})},e.prototype.appendData=function(t){if(this._disposed){tT(this.id);return}var e=t.seriesIndex,n=this.getModel().getSeriesByIndex(e);n.appendData(t),this._scheduler.unfinished=!0,this.getZr().wakeUp()},e.internalField=function(){function t(t){t.clearColorPalette(),t.eachSeries(function(t){t.clearColorPalette()})}a=function(t){var e=t._scheduler;e.restorePipelines(t._model),e.prepareStageTasks(),s(t,!0),s(t,!1),e.plan()},s=function(t,e){for(var n=t._model,r=t._scheduler,i=e?t._componentsViews:t._chartsViews,o=e?t._componentsMap:t._chartsMap,a=t._zr,s=t._api,u=0;u<i.length;u++)i[u].__alive=!1;function l(t){var u=t.__requireNewView;t.__requireNewView=!1;var l="_ec_"+t.id+"_"+t.type,c=!u&&o[l];if(!c){var h=(0,Q.u9)(t.type),f=e?E.Z.getClass(h.main,h.sub):B.Z.getClass(h.sub);(c=new f).init(n,s),o[l]=c,i.push(c),a.add(c.group)}t.__viewId=c.__id=l,c.__alive=!0,c.__model=t,c.group.__ecComponentInfo={mainType:t.mainType,index:t.componentIndex},e||r.prepareView(c,t,n,s)}e?n.eachComponent(function(t,e){"series"!==t&&l(e)}):n.eachSeries(l);for(var u=0;u<i.length;){var c=i[u];c.__alive?u++:(e||c.renderTask.dispose(),a.remove(c.group),c.dispose(n,s),i.splice(u,1),o[c.__id]===c&&delete o[c.__id],c.__id=c.group.__ecComponentInfo=null)}},u=function(t,e,n,r,i){var o,a=t._model;if(a.setUpdatePayload(n),!r){(0,k.S6)([].concat(t._componentsViews).concat(t._chartsViews),c);return}var s={};s[r+"Id"]=n[r+"Id"],s[r+"Index"]=n[r+"Index"],s[r+"Name"]=n[r+"Name"];var u={mainType:r,query:s};i&&(u.subType=i);var l=n.excludeSeriesId;function c(r){r&&r.__alive&&r[e]&&r[e](r.__model,a,t._api,n)}null!=l&&(o=(0,k.kW)(),(0,k.S6)(G.kF(l),function(t){var e=G.U5(t,null);null!=e&&o.set(e,!0)})),a&&a.eachComponent(u,function(e){if(!o||null==o.get(e.id))if((0,U.xp)(n)){if(e instanceof N.Z)n.type===U.Ki&&!n.notBlur&&!e.get(["emphasis","disabled"])&&(0,U.UL)(e,n,t._api);else{var r=(0,U.oJ)(e.mainType,e.componentIndex,n.name,t._api),i=r.focusSelf,a=r.dispatchers;n.type===U.Ki&&i&&!n.notBlur&&(0,U.zI)(e.mainType,e.componentIndex,t._api),a&&(0,k.S6)(a,function(t){n.type===U.Ki?(0,U.fD)(t):(0,U.Mh)(t)})}}else(0,U.aG)(n)&&e instanceof N.Z&&((0,U.og)(e,n,t._api),(0,U.ci)(e),S(t))},t),a&&a.eachComponent(u,function(e){if(!o||null==o.get(e.id))c(t["series"===r?"_chartsMap":"_componentsMap"][e.__viewId])},t)},l={prepareAndUpdate:function(t){a(this),l.update.call(this,t,{optionChanged:null!=t.newOption})},update:function(e,n){var r=this._model,i=this._api,o=this._zr,a=this._coordSysMgr,s=this._scheduler;if(!!r){r.setUpdatePayload(e),s.restoreData(r,e),s.performSeriesTasks(r),a.create(r,i),s.performDataProcessorTasks(r,e),h(this,r),a.update(r,i),t(r),s.performVisualTasks(r,e),y(this,r,i,e,n);var u=r.get("backgroundColor")||"transparent",l=r.get("darkMode");o.setBackgroundColor(u),null!=l&&"auto"!==l&&o.setDarkMode(l),tu.Z.trigger("afterupdate",r,i)}},updateTransform:function(e){var n=this,r=this._model,i=this._api;if(!!r){r.setUpdatePayload(e);var o=[];r.eachComponent(function(t,a){if("series"!==t){var s=n.getViewOfComponentModel(a);if(s&&s.__alive){if(s.updateTransform){var u=s.updateTransform(a,r,i,e);u&&u.update&&o.push(s)}else o.push(s)}}});var a=(0,k.kW)();r.eachSeries(function(t){var o=n._chartsMap[t.__viewId];if(o.updateTransform){var s=o.updateTransform(t,r,i,e);s&&s.update&&a.set(t.uid,1)}else a.set(t.uid,1)}),t(r),this._scheduler.performVisualTasks(r,e,{setDirty:!0,dirtyMap:a}),_(this,r,i,e,{},a),tu.Z.trigger("afterupdate",r,i)}},updateView:function(e){var n=this._model;if(!!n)n.setUpdatePayload(e),B.Z.markUpdateMethod(e,"updateView"),t(n),this._scheduler.performVisualTasks(n,e,{setDirty:!0}),y(this,n,this._api,e,{}),tu.Z.trigger("afterupdate",n,this._api)},updateVisual:function(e){var n=this,r=this._model;if(!!r)r.setUpdatePayload(e),r.eachSeries(function(t){t.getData().clearAllVisual()}),B.Z.markUpdateMethod(e,"updateVisual"),t(r),this._scheduler.performVisualTasks(r,e,{visualType:"visual",setDirty:!0}),r.eachComponent(function(t,i){if("series"!==t){var o=n.getViewOfComponentModel(i);o&&o.__alive&&o.updateVisual(i,r,n._api,e)}}),r.eachSeries(function(t){n._chartsMap[t.__viewId].updateVisual(t,r,n._api,e)}),tu.Z.trigger("afterupdate",r,this._api)},updateLayout:function(t){l.update.call(this,t)}},c=function(t,e,n,r){if(t._disposed){tT(t.id);return}for(var i,o=t._model,a=t._coordSysMgr.getCoordinateSystems(),s=G.pm(o,n),u=0;u<a.length;u++){var l=a[u];if(l[e]&&null!=(i=l[e](o,s,r)))return i}},h=function(t,e){var n=t._chartsMap,r=t._scheduler;e.eachSeries(function(t){r.updateStreamModes(t,n[t.__viewId])})},f=function(t,e){var n,r=this,i=this.getModel(),o=t.type,s=t.escapeConnect,c=tk[o],h=c.actionInfo,f=(h.update||"update").split(":"),p=f.pop(),d=null!=f[0]&&(0,Q.u9)(f[0]);this[tf]=!0;var g=[t],v=!1;t.batch&&(v=!0,g=(0,k.UI)(t.batch,function(e){return(e=(0,k.ce)((0,k.l7)({},e),t)).batch=null,e}));var y=[],m=(0,U.aG)(t),_=(0,U.xp)(t);if(_&&(0,U.T5)(this._api),(0,k.S6)(g,function(e){if((n=(n=c.action(e,r._model,r._api))||(0,k.l7)({},e)).type=h.event||n.type,y.push(n),_){var i=G.zH(t),o=i.queryOptionMap;u(r,p,e,i.mainTypeSpecified?o.keys()[0]:"series"),S(r)}else m?(u(r,p,e,"series"),S(r)):d&&u(r,p,e,d.main,d.sub)}),"none"!==p&&!_&&!m&&!d)try{this[tp]?(a(this),l.update.call(this,t),this[tp]=null):l[p].call(this,t)}catch(t){throw this[tf]=!1,t}if(n=v?{type:h.event||o,escapeConnect:s,batch:y}:y[0],this[tf]=!1,!e){var x=this._messageCenter;if(x.trigger(n.type,n),m){var w={type:"selectchanged",escapeConnect:s,selected:(0,U.C5)(i),isFromClick:t.isFromClick||!1,fromAction:t.type,fromActionPayload:t};x.trigger(w.type,w)}}},p=function(t){for(var e=this._pendingActions;e.length;){var n=e.shift();f.call(this,n,t)}},d=function(t){t||this.trigger("updated")},g=function(t,e){t.on("rendered",function(n){e.trigger("rendered",n),t.animation.isFinished()&&!e[tp]&&!e._scheduler.unfinished&&!e._pendingActions.length&&e.trigger("finished")})},v=function(t,e){t.on("mouseover",function(t){var n=t.target,r=(0,ta.o)(n,U.Av);r&&((0,U.$l)(r,t,e._api),S(e))}).on("mouseout",function(t){var n=t.target,r=(0,ta.o)(n,U.Av);r&&((0,U.xr)(r,t,e._api),S(e))}).on("click",function(t){var n=t.target,r=(0,ta.o)(n,function(t){return null!=(0,V.A)(t).dataIndex},!0);if(r){var i=r.selected?"unselect":"select",o=(0,V.A)(r);e._api.dispatchAction({type:i,dataType:o.dataType,dataIndexInside:o.dataIndex,seriesIndex:o.seriesIndex,isFromClick:!0})}})};function e(t){for(var e=[],n=t.currentStates,r=0;r<n.length;r++){var i=n[r];!("emphasis"===i||"blur"===i||"select"===i)&&e.push(i)}t.selected&&t.states.select&&e.push("select"),t.hoverState===U.wU&&t.states.emphasis?e.push("emphasis"):t.hoverState===U.CX&&t.states.blur&&e.push("blur"),t.useStates(e)}y=function(t,e,n,r,i){!function(t){var e=[],n=[],r=!1;if(t.eachComponent(function(t,i){var o=i.get("zlevel")||0,a=i.get("z")||0,s=i.getZLevelKey();r=r||!!s,("series"===t?n:e).push({zlevel:o,z:a,idx:i.componentIndex,type:t,key:s})}),r){var i,o,a=e.concat(n);(0,C.Z)(a,function(t,e){return t.zlevel===e.zlevel?t.z-e.z:t.zlevel-e.zlevel}),(0,k.S6)(a,function(e){var n=t.getComponent(e.type,e.idx),r=e.zlevel,a=e.key;null!=i&&(r=Math.max(i,r)),a?(r===i&&a!==o&&r++,o=a):o&&(r===i&&r++,o=""),i=r,n.setZLevel(r)})}}(e),m(t,e,n,r,i),(0,k.S6)(t._chartsViews,function(t){t.__alive=!1}),_(t,e,n,r,i),(0,k.S6)(t._chartsViews,function(t){!t.__alive&&t.remove(e,n)})},m=function(t,e,o,a,s,u){(0,k.S6)(u||t._componentsViews,function(t){var s=t.__model;r(s,t),t.render(s,e,o,a),n(s,t),i(s,t)})},_=function(t,e,o,a,s,u){var l=t._scheduler;s=(0,k.l7)(s||{},{updatedSeries:e.getSeries()}),tu.Z.trigger("series:beforeupdate",e,o,s);var c=!1;e.eachSeries(function(e){var n=t._chartsMap[e.__viewId];n.__alive=!0;var i=n.renderTask;l.updatePayload(i,a),r(e,n),u&&u.get(e.uid)&&i.dirty(),i.perform(l.getPerformArgs(i))&&(c=!0),n.group.silent=!!e.get("silent"),function(t,e){var n=t.get("blendMode")||null;e.eachRendered(function(t){!t.isGroup&&(t.style.blend=n)})}(e,n),(0,U.ci)(e)}),l.unfinished=c||l.unfinished,tu.Z.trigger("series:layoutlabels",e,o,s),tu.Z.trigger("series:transition",e,o,s),e.eachSeries(function(e){var r=t._chartsMap[e.__viewId];n(e,r),i(e,r)}),function(t,e){var n=t._zr.storage,r=0;n.traverse(function(t){!t.isGroup&&r++}),r>e.get("hoverLayerThreshold")&&!D.Z.node&&!D.Z.worker&&e.eachSeries(function(e){if(!e.preventUsingHoverLayer){var n=t._chartsMap[e.__viewId];n.__alive&&n.eachRendered(function(t){t.states.emphasis&&(t.states.emphasis.hoverLayer=!0)})}})}(t,e),tu.Z.trigger("series:afterupdate",e,o,s)},S=function(t){t[td]=!0,t.getZr().wakeUp()},b=function(t){if(!!t[td])t.getZr().storage.traverse(function(t){if(!W.eq(t))e(t)}),t[td]=!1};function n(t,e){if(!t.preventAutoZ){var n=t.get("z")||0,r=t.get("zlevel")||0;e.eachRendered(function(t){return function t(e,n,r,i){var o=e.getTextContent(),a=e.getTextGuideLine();if(e.isGroup){for(var s=e.childrenRef(),u=0;u<s.length;u++)i=Math.max(t(s[u],n,r,i),i)}else e.z=n,e.zlevel=r,i=Math.max(e.z2,i);if(o&&(o.z=n,o.zlevel=r,isFinite(i)&&(o.z2=i+2)),a){var l=e.textGuideLineConfig;a.z=n,a.zlevel=r,isFinite(i)&&(a.z2=i+(l&&l.showAbove?1:-1))}return i}(t,n,r,-1/0),!0})}}function r(t,e){e.eachRendered(function(t){if(!W.eq(t)){var e=t.getTextContent(),n=t.getTextGuideLine();t.stateTransition&&(t.stateTransition=null),e&&e.stateTransition&&(e.stateTransition=null),n&&n.stateTransition&&(n.stateTransition=null),t.hasState()?(t.prevStates=t.currentStates,t.clearStates()):t.prevStates&&(t.prevStates=null)}})}function i(t,n){var r=t.getModel("stateAnimation"),i=t.isAnimationEnabled(),o=r.get("duration"),a=o>0?{duration:o,delay:r.get("delay"),easing:r.get("easing")}:null;n.eachRendered(function(t){if(t.states&&t.states.emphasis){if(!W.eq(t)){if(t instanceof H.ZP&&(0,U.e9)(t),t.__dirty){var n=t.prevStates;n&&t.useStates(n)}if(i){t.stateTransition=a;var r=t.getTextContent(),o=t.getTextGuideLine();r&&(r.stateTransition=a),o&&(o.stateTransition=a)}t.__dirty&&e(t)}}})}x=function(t){return new(function(e){function n(){return null!==e&&e.apply(this,arguments)||this}return(0,M.ZT)(n,e),n.prototype.getCoordinateSystems=function(){return t._coordSysMgr.getCoordinateSystems()},n.prototype.getComponentByElement=function(e){for(;e;){var n=e.__ecComponentInfo;if(null!=n)return t._model.getComponent(n.mainType,n.index);e=e.parent}},n.prototype.enterEmphasis=function(e,n){(0,U.fD)(e,n),S(t)},n.prototype.leaveEmphasis=function(e,n){(0,U.Mh)(e,n),S(t)},n.prototype.enterBlur=function(e){(0,U.SX)(e),S(t)},n.prototype.leaveBlur=function(e){(0,U.VP)(e),S(t)},n.prototype.enterSelect=function(e){(0,U.XX)(e),S(t)},n.prototype.leaveSelect=function(e){(0,U.SJ)(e),S(t)},n.prototype.getModel=function(){return t.getModel()},n.prototype.getViewOfComponentModel=function(e){return t.getViewOfComponentModel(e)},n.prototype.getViewOfSeriesModel=function(e){return t.getViewOfSeriesModel(e)},n}(P.Z))(t)},w=function(t){function e(t,e){for(var n=0;n<t.length;n++)t[n][tv]=e}(0,k.S6)(tD,function(n,r){t._messageCenter.on(r,function(n){if(tO[t.group]&&0!==t[tv]){if(!n||!n.escapeConnect){var r=t.makeActionFromEvent(n),i=[];(0,k.S6)(tL,function(e){e!==t&&e.group===t.group&&i.push(e)}),e(i,0),(0,k.S6)(i,function(t){1!==t[tv]&&t.dispatchAction(r)}),e(i,2)}}})})}}(),e}(I.Z),tb=tS.prototype;tb.on=ty("on"),tb.off=ty("off"),tb.one=function(t,e,n){var r=this;(0,tn.Sh)("ECharts#one is deprecated.");this.on.call(this,t,function n(){for(var i=[],o=0;o<arguments.length;o++)i[o]=arguments[o];e&&e.apply&&e.apply(this,i),r.off(t,n)},n)};var tM=["click","dblclick","mouseover","mouseout","mousemove","mousedown","mouseup","globalout","contextmenu"];function tT(t){}var tk={},tD={},tC=[],tI=[],tA=[],tP={},tZ={},tL={},tO={},tR=+new Date-0,tN="_echarts_instance_";function tE(t,e,n){var r=!(n&&n.ssr);if(r){var i=tB(t);if(i)return i}var o=new tS(t,e,n);return o.id="ec_"+tR++,tL[o.id]=o,r&&G.P$(t,tN,o.id),w(o),tu.Z.trigger("afterinit",o),o}function tB(t){return tL[G.IL(t,tN)]}function tz(t,e){tP[t]=e}function tF(t){0>(0,k.cq)(tI,t)&&tI.push(t)}function tW(t,e){tj(tC,t,e,2e3)}function tH(t){tU("afterinit",t)}function tV(t){tU("afterupdate",t)}function tU(t,e){tu.Z.on(t,e)}function tG(t,e,n){(0,k.mf)(e)&&(n=e,e="");var r=(0,k.Kn)(t)?t.type:[t,t={event:e}][0];if(t.event=(t.event||r).toLowerCase(),!tD[e=t.event])(0,k.hu)(tg.test(r)&&tg.test(e)),!tk[r]&&(tk[r]={action:n,actionInfo:t}),tD[e]=r}function tX(t,e){Z.Z.register(t,e)}function tY(t,e){tj(tA,t,e,1e3,"layout")}function tq(t,e){tj(tA,t,e,3e3,"visual")}var tK=[];function tj(t,e,n,r,i){((0,k.mf)(e)||(0,k.Kn)(e))&&(n=e,e=r);if(!((0,k.cq)(tK,n)>=0)){tK.push(n);var o=K.Z.wrapStageHandler(n,i);o.__prio=e,o.__raw=n,t.push(o)}}function tJ(t,e){tZ[t]=e}function tQ(t,e,n){var r=(0,tc.C)("registerMap");r&&r(t,e,n)}var t$=ti.DA;tq(2e3,Y.TD),tq(4500,Y.Tn),tq(4500,Y.$P),tq(2e3,tt.n),tq(4500,tt.m),tq(7e3,ts.Z),tF(O.Z),tW(900,R.Z),r=q.Z,tZ.default=r,tG({type:U.Ki,event:U.Ki,update:U.Ki},k.ZT),tG({type:U.yx,event:U.yx,update:U.yx},k.ZT),tG({type:U.Hg,event:U.Hg,update:U.Hg},k.ZT),tG({type:U.JQ,event:U.JQ,update:U.JQ},k.ZT),tG({type:U.iK,event:U.iK,update:U.iK},k.ZT),i=j.Z,tP.light=i,o=J.Z,tP.dark=o},504146:function(t,e,n){n.d(e,{C:function(){return o},M:function(){return i}});var r={};function i(t,e){r[t]=e}function o(t){return r[t]}},857406:function(t,e,n){var r=new(n(367582)).Z;e.Z=r},765814:function(t,e,n){n.d(e,{D0:function(){return f},G8:function(){return p},Li:function(){return d},sO:function(){return c}});var r=n(954069),i=n(939828),o=n(183754),a=n(414473),s=n(807028),u={},l={},c=i.Z.domSupported&&(document.documentElement.lang||navigator.language||navigator.browserLanguage||"EN").toUpperCase().indexOf("ZH")>-1?"ZH":"EN";function h(t,e){l[t=t.toUpperCase()]=new r.Z(e),u[t]=e}function f(t){if(!(0,s.HD)(t))return(0,s.TS)((0,s.d9)(t),(0,s.d9)(u.EN),!1);var e=u[t.toUpperCase()]||{};return"ZH"===t||"EN"===t?(0,s.d9)(e):(0,s.TS)((0,s.d9)(e),(0,s.d9)(u.EN),!1)}function p(t){return l[t]}function d(){return l.EN}h("EN",o.Z),h("ZH",a.Z)},648446:function(t,e,n){n.d(e,{v:function(){return i}});var r=n(807028);function i(t){return new o(t)}var o=function(){function t(t){t=t||{},this._reset=t.reset,this._plan=t.plan,this._count=t.count,this._onDirty=t.onDirty,this._dirty=!0}return t.prototype.perform=function(t){var e,n,i=this._upstream,o=t&&t.skip;if(this._dirty&&i){var a=this.context;a.data=a.outputData=i.context.outputData}this.__pipeline&&(this.__pipeline.currentTask=this),this._plan&&!o&&(e=this._plan(this.context));var s=function(t){return t>=1||(t=1),t}(this._modBy),u=this._modDataCount||0,l=function(t){return t>=1||(t=1),t}(t&&t.modBy),c=t&&t.modDataCount||0;function h(t){return t>=1||(t=1),t}(s!==l||u!==c)&&(e="reset"),(this._dirty||"reset"===e)&&(this._dirty=!1,n=this._doReset(o)),this._modBy=l,this._modDataCount=c;var f=t&&t.step;if(i?this._dueEnd=i._outputDueEnd:this._dueEnd=this._count?this._count(this.context):1/0,this._progress){var p=this._dueIndex,d=Math.min(null!=f?this._dueIndex+f:1/0,this._dueEnd);if(!o&&(n||p<d)){var g=this._progress;if((0,r.kJ)(g))for(var v=0;v<g.length;v++)this._doProgress(g[v],p,d,l,c);else this._doProgress(g,p,d,l,c)}this._dueIndex=d;var y=null!=this._settedOutputEnd?this._settedOutputEnd:d;this._outputDueEnd=y}else this._dueIndex=this._outputDueEnd=null!=this._settedOutputEnd?this._settedOutputEnd:this._dueEnd;return this.unfinished()},t.prototype.dirty=function(){this._dirty=!0,this._onDirty&&this._onDirty(this.context)},t.prototype._doProgress=function(t,e,n,r,i){a.reset(e,n,r,i),this._callingProgress=t,this._callingProgress({start:e,end:n,count:n-e,next:a.next},this.context)},t.prototype._doReset=function(t){this._dueIndex=this._outputDueEnd=this._dueEnd=0,this._settedOutputEnd=null,!t&&this._reset&&((e=this._reset(this.context))&&e.progress&&(n=e.forceFirstProgress,e=e.progress),(0,r.kJ)(e)&&!e.length&&(e=null)),this._progress=e,this._modBy=this._modDataCount=null;var e,n,i=this._downstream;return i&&i.dirty(),n},t.prototype.unfinished=function(){return this._progress&&this._dueIndex<this._dueEnd},t.prototype.pipe=function(t){(this._downstream!==t||this._dirty)&&(this._downstream=t,t._upstream=this,t.dirty())},t.prototype.dispose=function(){if(!this._disposed)this._upstream&&(this._upstream._downstream=null),this._downstream&&(this._downstream._upstream=null),this._dirty=!1,this._disposed=!0},t.prototype.getUpstream=function(){return this._upstream},t.prototype.getDownstream=function(){return this._downstream},t.prototype.setOutputEnd=function(t){this._outputDueEnd=this._settedOutputEnd=t},t}(),a=function(){var t,e,n,r,i,o={reset:function(u,l,c,h){e=u,t=l,n=c,i=Math.ceil((r=h)/n),o.next=n>1&&r>0?s:a}};return o;function a(){return e<t?e++:null}function s(){var o=e%i*n+Math.ceil(e/i),a=e>=t?null:o<r?o:e;return e++,a}}()},494388:function(t,e){function n(t){return null==t?0:t.length||1}function r(t){return t}var i=function(){function t(t,e,n,i,o,a){this._old=t,this._new=e,this._oldKeyGetter=n||r,this._newKeyGetter=i||r,this.context=o,this._diffModeMultiple="multiple"===a}return t.prototype.add=function(t){return this._add=t,this},t.prototype.update=function(t){return this._update=t,this},t.prototype.updateManyToOne=function(t){return this._updateManyToOne=t,this},t.prototype.updateOneToMany=function(t){return this._updateOneToMany=t,this},t.prototype.updateManyToMany=function(t){return this._updateManyToMany=t,this},t.prototype.remove=function(t){return this._remove=t,this},t.prototype.execute=function(){this[this._diffModeMultiple?"_executeMultiple":"_executeOneToOne"]()},t.prototype._executeOneToOne=function(){var t=this._old,e=this._new,r={},i=Array(t.length),o=Array(e.length);this._initIndexMap(t,null,i,"_oldKeyGetter"),this._initIndexMap(e,r,o,"_newKeyGetter");for(var a=0;a<t.length;a++){var s=i[a],u=r[s],l=n(u);if(l>1){var c=u.shift();1===u.length&&(r[s]=u[0]),this._update&&this._update(c,a)}else 1===l?(r[s]=null,this._update&&this._update(u,a)):this._remove&&this._remove(a)}this._performRestAdd(o,r)},t.prototype._executeMultiple=function(){var t=this._old,e=this._new,r={},i={},o=[],a=[];this._initIndexMap(t,r,o,"_oldKeyGetter"),this._initIndexMap(e,i,a,"_newKeyGetter");for(var s=0;s<o.length;s++){var u=o[s],l=r[u],c=i[u],h=n(l),f=n(c);if(h>1&&1===f)this._updateManyToOne&&this._updateManyToOne(c,l),i[u]=null;else if(1===h&&f>1)this._updateOneToMany&&this._updateOneToMany(c,l),i[u]=null;else if(1===h&&1===f)this._update&&this._update(c,l),i[u]=null;else if(h>1&&f>1)this._updateManyToMany&&this._updateManyToMany(c,l),i[u]=null;else if(h>1)for(var p=0;p<h;p++)this._remove&&this._remove(l[p]);else this._remove&&this._remove(l)}this._performRestAdd(a,i)},t.prototype._performRestAdd=function(t,e){for(var r=0;r<t.length;r++){var i=t[r],o=e[i],a=n(o);if(a>1)for(var s=0;s<a;s++)this._add&&this._add(o[s]);else 1===a&&this._add&&this._add(o);e[i]=null}},t.prototype._initIndexMap=function(t,e,r,i){for(var o=this._diffModeMultiple,a=0;a<t.length;a++){var s="_ec_"+this[i](t[a],a);if(!o&&(r[a]=s),!!e){var u=e[s],l=n(u);0===l?(e[s]=a,o&&r.push(s)):1===l?e[s]=[u,a]:u.push(a)}}},t}();e.Z=i},969012:function(t,e,n){n.d(e,{hG:function(){return c}});var r,i=n(807028),o=n(43296),a=n(429590),s="undefined",u=typeof Uint32Array===s?Array:Uint32Array,l=typeof Uint16Array===s?Array:Uint16Array,c=typeof Int32Array===s?Array:Int32Array,h=typeof Float64Array===s?Array:Float64Array,f={float:h,int:c,ordinal:Array,number:Array,time:h};function p(t){return t>65535?u:l}function d(){return[1/0,-1/0]}function g(t,e,n,r,i){var o=f[n||"float"];if(i){var a=t[e],s=a&&a.length;if(s!==r){for(var u=new o(r),l=0;l<s;l++)u[l]=a[l];t[e]=u}}else t[e]=new o(r)}var v=function(){function t(){this._chunks=[],this._rawExtent=[],this._extent=[],this._count=0,this._rawCount=0,this._calcDimNameToIdx=(0,i.kW)()}return t.prototype.initData=function(t,e,n){this._provider=t,this._chunks=[],this._indices=null,this.getRawIndex=this._getRawIdxIdentity;var o=t.getSource(),s=this.defaultDimValueGetter=r[o.sourceFormat];this._dimValueGetter=n||s,this._rawExtent=[],(0,a.QY)(o),this._dimensions=(0,i.UI)(e,function(t){return{type:t.type,property:t.property}}),this._initDataFromProvider(0,t.count())},t.prototype.getProvider=function(){return this._provider},t.prototype.getSource=function(){return this._provider.getSource()},t.prototype.ensureCalculationDimension=function(t,e){var n=this._calcDimNameToIdx,r=this._dimensions,i=n.get(t);if(null!=i){if(r[i].type===e)return i}else i=r.length;return r[i]={type:e},n.set(t,i),this._chunks[i]=new f[e||"float"](this._rawCount),this._rawExtent[i]=d(),i},t.prototype.collectOrdinalMeta=function(t,e){var n=this._chunks[t],r=this._dimensions[t],i=this._rawExtent,o=r.ordinalOffset||0,a=n.length;0===o&&(i[t]=d());for(var s=i[t],u=o;u<a;u++){var l=n[u]=e.parseAndCollect(n[u]);!isNaN(l)&&(s[0]=Math.min(l,s[0]),s[1]=Math.max(l,s[1]))}r.ordinalMeta=e,r.ordinalOffset=a,r.type="ordinal"},t.prototype.getOrdinalMeta=function(t){return this._dimensions[t].ordinalMeta},t.prototype.getDimensionProperty=function(t){var e=this._dimensions[t];return e&&e.property},t.prototype.appendData=function(t){var e=this._provider,n=this.count();e.appendData(t);var r=e.count();return!e.persistent&&(r+=n),n<r&&this._initDataFromProvider(n,r,!0),[n,r]},t.prototype.appendValues=function(t,e){for(var n=this._chunks,i=this._dimensions,o=i.length,a=this._rawExtent,s=this.count(),u=s+Math.max(t.length,e||0),l=0;l<o;l++){var c=i[l];g(n,l,c.type,u,!0)}for(var h=[],f=s;f<u;f++){for(var p=f-s,d=0;d<o;d++){var c=i[d],v=r.arrayRows.call(this,t[p]||h,c.property,p,d);n[d][f]=v;var y=a[d];v<y[0]&&(y[0]=v),v>y[1]&&(y[1]=v)}}return this._rawCount=this._count=u,{start:s,end:u}},t.prototype._initDataFromProvider=function(t,e,n){for(var r=this._provider,o=this._chunks,a=this._dimensions,s=a.length,u=this._rawExtent,l=(0,i.UI)(a,function(t){return t.property}),c=0;c<s;c++){var h=a[c];!u[c]&&(u[c]=d()),g(o,c,h.type,e,n)}if(r.fillStorage)r.fillStorage(t,e,o,u);else{for(var f=[],p=t;p<e;p++){f=r.getItem(p,f);for(var v=0;v<s;v++){var y=o[v],m=this._dimValueGetter(f,l[v],p,v);y[p]=m;var _=u[v];m<_[0]&&(_[0]=m),m>_[1]&&(_[1]=m)}}}!r.persistent&&r.clean&&r.clean(),this._rawCount=this._count=e,this._extent=[]},t.prototype.count=function(){return this._count},t.prototype.get=function(t,e){if(!(e>=0&&e<this._count))return NaN;var n=this._chunks[t];return n?n[this.getRawIndex(e)]:NaN},t.prototype.getValues=function(t,e){var n=[],r=[];if(null==e){e=t,t=[];for(var i=0;i<this._dimensions.length;i++)r.push(i)}else r=t;for(var i=0,o=r.length;i<o;i++)n.push(this.get(r[i],e));return n},t.prototype.getByRawIndex=function(t,e){if(!(e>=0&&e<this._rawCount))return NaN;var n=this._chunks[t];return n?n[e]:NaN},t.prototype.getSum=function(t){var e=this._chunks[t],n=0;if(e)for(var r=0,i=this.count();r<i;r++){var o=this.get(t,r);!isNaN(o)&&(n+=o)}return n},t.prototype.getMedian=function(t){var e=[];this.each([t],function(t){!isNaN(t)&&e.push(t)});var n=e.sort(function(t,e){return t-e}),r=this.count();return 0===r?0:r%2==1?n[(r-1)/2]:(n[r/2]+n[r/2-1])/2},t.prototype.indexOfRawIndex=function(t){if(t>=this._rawCount||t<0)return -1;if(!this._indices)return t;var e=this._indices,n=e[t];if(null!=n&&n<this._count&&n===t)return t;for(var r=0,i=this._count-1;r<=i;){var o=(r+i)/2|0;if(e[o]<t)r=o+1;else{if(!(e[o]>t))return o;i=o-1}}return -1},t.prototype.indicesOfNearest=function(t,e,n){var r=this._chunks[t],i=[];if(!r)return i;null==n&&(n=1/0);for(var o=1/0,a=-1,s=0,u=0,l=this.count();u<l;u++){var c=e-r[this.getRawIndex(u)],h=Math.abs(c);h<=n&&((h<o||h===o&&c>=0&&a<0)&&(o=h,a=c,s=0),c===a&&(i[s++]=u))}return i.length=s,i},t.prototype.getIndices=function(){var t,e=this._indices;if(e){var n=e.constructor,r=this._count;if(n===Array){t=new n(r);for(var i=0;i<r;i++)t[i]=e[i]}else t=new n(e.buffer,0,r)}else{var n=p(this._rawCount);t=new n(this.count());for(var i=0;i<t.length;i++)t[i]=i}return t},t.prototype.filter=function(t,e){if(!this._count)return this;for(var n=this.clone(),r=n.count(),i=new(p(n._rawCount))(r),o=[],a=t.length,s=0,u=t[0],l=n._chunks,c=0;c<r;c++){var h=void 0,f=n.getRawIndex(c);if(0===a)h=e(c);else if(1===a)h=e(l[u][f],c);else{for(var d=0;d<a;d++)o[d]=l[t[d]][f];o[d]=c,h=e.apply(null,o)}h&&(i[s++]=f)}return s<r&&(n._indices=i),n._count=s,n._extent=[],n._updateGetRawIdx(),n},t.prototype.selectRange=function(t){var e=this.clone(),n=e._count;if(!n)return this;var r=(0,i.XP)(t),o=r.length;if(!o)return this;var a=e.count(),s=new(p(e._rawCount))(a),u=0,l=r[0],c=t[l][0],h=t[l][1],f=e._chunks,d=!1;if(!e._indices){var g=0;if(1===o){for(var v=f[r[0]],y=0;y<n;y++){var m=v[y];(m>=c&&m<=h||isNaN(m))&&(s[u++]=g),g++}d=!0}else if(2===o){for(var v=f[r[0]],_=f[r[1]],x=t[r[1]][0],w=t[r[1]][1],y=0;y<n;y++){var m=v[y],S=_[y];(m>=c&&m<=h||isNaN(m))&&(S>=x&&S<=w||isNaN(S))&&(s[u++]=g),g++}d=!0}}if(!d){if(1===o)for(var y=0;y<a;y++){var b=e.getRawIndex(y),m=f[r[0]][b];(m>=c&&m<=h||isNaN(m))&&(s[u++]=b)}else for(var y=0;y<a;y++){for(var M=!0,b=e.getRawIndex(y),T=0;T<o;T++){var k=r[T],m=f[k][b];(m<t[k][0]||m>t[k][1])&&(M=!1)}M&&(s[u++]=e.getRawIndex(y))}}return u<a&&(e._indices=s),e._count=u,e._extent=[],e._updateGetRawIdx(),e},t.prototype.map=function(t,e){var n=this.clone(t);return this._updateDims(n,t,e),n},t.prototype.modify=function(t,e){this._updateDims(this,t,e)},t.prototype._updateDims=function(t,e,n){for(var r=t._chunks,i=[],o=e.length,a=t.count(),s=[],u=t._rawExtent,l=0;l<e.length;l++)u[e[l]]=d();for(var c=0;c<a;c++){for(var h=t.getRawIndex(c),f=0;f<o;f++)s[f]=r[e[f]][h];s[o]=c;var p=n&&n.apply(null,s);if(null!=p){"object"!=typeof p&&(i[0]=p,p=i);for(var l=0;l<p.length;l++){var g=e[l],v=p[l],y=u[g],m=r[g];m&&(m[h]=v),v<y[0]&&(y[0]=v),v>y[1]&&(y[1]=v)}}}},t.prototype.lttbDownSample=function(t,e){var n,r,i,o=this.clone([t],!0),a=o._chunks[t],s=this.count(),u=0,l=Math.floor(1/e),c=this.getRawIndex(0),h=new(p(this._rawCount))(Math.min((Math.ceil(s/l)+2)*2,s));h[u++]=c;for(var f=1;f<s-1;f+=l){for(var d=Math.min(f+l,s-1),g=Math.min(f+2*l,s),v=(g+d)/2,y=0,m=d;m<g;m++){var _=this.getRawIndex(m),x=a[_];if(!isNaN(x))y+=x}y/=g-d;var w=f,S=Math.min(f+l,s),b=f-1,M=a[c];n=-1,i=w;for(var T=-1,k=0,m=w;m<S;m++){var _=this.getRawIndex(m),x=a[_];if(isNaN(x)){k++,T<0&&(T=_);continue}(r=Math.abs((b-v)*(x-M)-(b-m)*(y-M)))>n&&(n=r,i=_)}k>0&&k<S-w&&(h[u++]=Math.min(T,i),i=Math.max(T,i)),h[u++]=i,c=i}return h[u++]=this.getRawIndex(s-1),o._count=u,o._indices=h,o.getRawIndex=this._getRawIdx,o},t.prototype.downSample=function(t,e,n,r){for(var i=this.clone([t],!0),o=i._chunks,a=[],s=Math.floor(1/e),u=o[t],l=this.count(),c=i._rawExtent[t]=d(),h=new(p(this._rawCount))(Math.ceil(l/s)),f=0,g=0;g<l;g+=s){s>l-g&&(s=l-g,a.length=s);for(var v=0;v<s;v++){var y=this.getRawIndex(g+v);a[v]=u[y]}var m=n(a),_=this.getRawIndex(Math.min(g+r(a,m)||0,l-1));u[_]=m,m<c[0]&&(c[0]=m),m>c[1]&&(c[1]=m),h[f++]=_}return i._count=f,i._indices=h,i._updateGetRawIdx(),i},t.prototype.each=function(t,e){if(!!this._count)for(var n=t.length,r=this._chunks,i=0,o=this.count();i<o;i++){var a=this.getRawIndex(i);switch(n){case 0:e(i);break;case 1:e(r[t[0]][a],i);break;case 2:e(r[t[0]][a],r[t[1]][a],i);break;default:for(var s=0,u=[];s<n;s++)u[s]=r[t[s]][a];u[s]=i,e.apply(null,u)}}},t.prototype.getDataExtent=function(t){var e,n=this._chunks[t],r=d();if(!n)return r;var i=this.count();if(!this._indices)return this._rawExtent[t].slice();if(e=this._extent[t])return e.slice();for(var o=(e=r)[0],a=e[1],s=0;s<i;s++){var u=n[this.getRawIndex(s)];u<o&&(o=u),u>a&&(a=u)}return e=[o,a],this._extent[t]=e,e},t.prototype.getRawDataItem=function(t){var e=this.getRawIndex(t);if(this._provider.persistent)return this._provider.getItem(e);for(var n=[],r=this._chunks,i=0;i<r.length;i++)n.push(r[i][e]);return n},t.prototype.clone=function(e,n){var r=new t,o=this._chunks,a=e&&(0,i.u4)(e,function(t,e){return t[e]=!0,t},{});if(a)for(var s=0;s<o.length;s++)r._chunks[s]=a[s]?function(t){var e=t.constructor;return e===Array?t.slice():new e(t)}(o[s]):o[s];else r._chunks=o;return this._copyCommonProps(r),!n&&(r._indices=this._cloneIndices()),r._updateGetRawIdx(),r},t.prototype._copyCommonProps=function(t){t._count=this._count,t._rawCount=this._rawCount,t._provider=this._provider,t._dimensions=this._dimensions,t._extent=(0,i.d9)(this._extent),t._rawExtent=(0,i.d9)(this._rawExtent)},t.prototype._cloneIndices=function(){if(this._indices){var t=this._indices.constructor,e=void 0;if(t===Array){var n=this._indices.length;e=new t(n);for(var r=0;r<n;r++)e[r]=this._indices[r]}else e=new t(this._indices);return e}return null},t.prototype._getRawIdxIdentity=function(t){return t},t.prototype._getRawIdx=function(t){return t<this._count&&t>=0?this._indices[t]:-1},t.prototype._updateGetRawIdx=function(){this.getRawIndex=this._indices?this._getRawIdx:this._getRawIdxIdentity},t.internalField=function(){function t(t,e,n,r){return(0,o.yQ)(t[r],this._dimensions[r])}r={arrayRows:t,objectRows:function(t,e,n,r){return(0,o.yQ)(t[e],this._dimensions[r])},keyedColumns:t,original:function(t,e,n,r){var i=t&&(null==t.value?t:t.value);return(0,o.yQ)(i instanceof Array?i[r]:i,this._dimensions[r])},typedArray:function(t,e,n,r){return t[r]}}}(),t}();e.ZP=v},911992:function(t,e,n){var r=n(807028),i=0,o=function(){function t(t){this.categories=t.categories||[],this._needCollect=t.needCollect,this._deduplication=t.deduplication,this.uid=++i}return t.createByAxisModel=function(e){var n=e.option,i=n.data,o=i&&(0,r.UI)(i,a);return new t({categories:o,needCollect:!o,deduplication:!1!==n.dedplication})},t.prototype.getOrdinal=function(t){return this._getOrCreateMap().get(t)},t.prototype.parseAndCollect=function(t){var e,n=this._needCollect;if(!(0,r.HD)(t)&&!n)return t;if(n&&!this._deduplication)return e=this.categories.length,this.categories[e]=t,e;var i=this._getOrCreateMap();return null==(e=i.get(t))&&(n?(e=this.categories.length,this.categories[e]=t,i.set(t,e)):e=NaN),e},t.prototype._getOrCreateMap=function(){return this._map||(this._map=(0,r.kW)(this.categories))},t}();function a(t){return(0,r.Kn)(t)&&null!=t.value?t.value:t+""}e.Z=o},670943:function(t,e,n){var r,i,o,a,s,u,l,c=n(807028),h=n(954069),f=n(494388),p=n(279681),d=n(220016),g=n(395381),v=n(894870),y=n(133141),m=n(276868),_=n(429590),x=n(969012),w=n(138600),S=c.Kn,b=c.UI,M="undefined"==typeof Int32Array?Array:Int32Array,T=["hasItemOption","_nameList","_idList","_invertedIndicesMap","_dimSummary","userOutput","_rawData","_dimValueGetter","_nameDimIdx","_idDimIdx","_nameRepeatCount"],k=["_approximateExtent"],D=function(){function t(t,e){this.type="list",this._dimOmitted=!1,this._nameList=[],this._idList=[],this._visual={},this._layout={},this._itemVisuals=[],this._itemLayouts=[],this._graphicEls=[],this._approximateExtent={},this._calculationInfo={},this.hasItemOption=!1,this.TRANSFERABLE_METHODS=["cloneShallow","downSample","lttbDownSample","map"],this.CHANGABLE_METHODS=["filterSelf","selectRange"],this.DOWNSAMPLE_METHODS=["downSample","lttbDownSample"];var n,r=!1;(0,w.bB)(t)?(n=t.dimensions,this._dimOmitted=t.isDimensionOmitted(),this._schema=t):(r=!0,n=t),n=n||["x","y"];for(var i={},o=[],a={},s=!1,u={},l=0;l<n.length;l++){var h=n[l],f=c.HD(h)?new g.Z({name:h}):h instanceof g.Z?h:new g.Z(h),p=f.name;f.type=f.type||"float",!f.coordDim&&(f.coordDim=p,f.coordDimIndex=0);var d=f.otherDims=f.otherDims||{};o.push(p),i[p]=f,null!=u[p]&&(s=!0),f.createInvertedIndices&&(a[p]=[]),0===d.itemName&&(this._nameDimIdx=l),0===d.itemId&&(this._idDimIdx=l);r&&(f.storeDimIndex=l)}if(this.dimensions=o,this._dimInfos=i,this._initGetDimensionInfo(s),this.hostModel=e,this._invertedIndicesMap=a,this._dimOmitted){var v=this._dimIdxToName=c.kW();c.S6(o,function(t){v.set(i[t].storeDimIndex,t)})}}return t.prototype.getDimension=function(t){var e=this._recognizeDimIndex(t);if(null==e)return t;if(e=t,!this._dimOmitted)return this.dimensions[e];var n=this._dimIdxToName.get(e);if(null!=n)return n;var r=this._schema.getSourceDimension(e);if(r)return r.name},t.prototype.getDimensionIndex=function(t){var e=this._recognizeDimIndex(t);if(null!=e)return e;if(null==t)return -1;var n=this._getDimInfo(t);return n?n.storeDimIndex:this._dimOmitted?this._schema.getSourceDimensionIndex(t):-1},t.prototype._recognizeDimIndex=function(t){if(c.hj(t)||null!=t&&!isNaN(t)&&!this._getDimInfo(t)&&(!this._dimOmitted||0>this._schema.getSourceDimensionIndex(t)))return+t},t.prototype._getStoreDimIndex=function(t){var e=this.getDimensionIndex(t);return e},t.prototype.getDimensionInfo=function(t){return this._getDimInfo(this.getDimension(t))},t.prototype._initGetDimensionInfo=function(t){var e=this._dimInfos;this._getDimInfo=t?function(t){return e.hasOwnProperty(t)?e[t]:void 0}:function(t){return e[t]}},t.prototype.getDimensionsOnCoord=function(){return this._dimSummary.dataDimsOnCoord.slice()},t.prototype.mapDimension=function(t,e){var n=this._dimSummary;if(null==e)return n.encodeFirstDimNotExtra[t];var r=n.encode[t];return r?r[e]:null},t.prototype.mapDimensionsAll=function(t){return(this._dimSummary.encode[t]||[]).slice()},t.prototype.getStore=function(){return this._store},t.prototype.initData=function(t,e,n){var r,i=this;if(t instanceof x.ZP&&(r=t),!r){var o=this.dimensions,a=(0,_.Ld)(t)||c.zG(t)?new p.Pl(t,o.length):t;r=new x.ZP;var s=b(o,function(t){return{type:i._dimInfos[t].type,property:t}});r.initData(a,s,n)}this._store=r,this._nameList=(e||[]).slice(),this._idList=[],this._nameRepeatCount={},this._doInit(0,r.count()),this._dimSummary=(0,d.y)(this,this._schema),this.userOutput=this._dimSummary.userOutput},t.prototype.appendData=function(t){var e=this._store.appendData(t);this._doInit(e[0],e[1])},t.prototype.appendValues=function(t,e){var n=this._store.appendValues(t,e.length),r=n.start,i=n.end,o=this._shouldMakeIdFromName();if(this._updateOrdinalMeta(),e)for(var a=r;a<i;a++){var s=a-r;this._nameList[a]=e[s],o&&l(this,a)}},t.prototype._updateOrdinalMeta=function(){for(var t=this._store,e=this.dimensions,n=0;n<e.length;n++){var r=this._dimInfos[e[n]];r.ordinalMeta&&t.collectOrdinalMeta(r.storeDimIndex,r.ordinalMeta)}},t.prototype._shouldMakeIdFromName=function(){var t=this._store.getProvider();return null==this._idDimIdx&&t.getSource().sourceFormat!==v.J5&&!t.fillStorage},t.prototype._doInit=function(t,e){if(!(t>=e)){var n=this._store.getProvider();this._updateOrdinalMeta();var i=this._nameList,o=this._idList;if(n.getSource().sourceFormat===v.cy&&!n.pure){for(var a=[],s=t;s<e;s++){var u=n.getItem(s,a);if(!this.hasItemOption&&(0,y.Co)(u)&&(this.hasItemOption=!0),u){var c=u.name;null==i[s]&&null!=c&&(i[s]=(0,y.U5)(c,null));var h=u.id;null==o[s]&&null!=h&&(o[s]=(0,y.U5)(h,null))}}}if(this._shouldMakeIdFromName())for(var s=t;s<e;s++)l(this,s);r(this)}},t.prototype.getApproximateExtent=function(t){return this._approximateExtent[t]||this._store.getDataExtent(this._getStoreDimIndex(t))},t.prototype.setApproximateExtent=function(t,e){e=this.getDimension(e),this._approximateExtent[e]=t.slice()},t.prototype.getCalculationInfo=function(t){return this._calculationInfo[t]},t.prototype.setCalculationInfo=function(t,e){S(t)?c.l7(this._calculationInfo,t):this._calculationInfo[t]=e},t.prototype.getName=function(t){var e=this.getRawIndex(t),n=this._nameList[e];return null==n&&null!=this._nameDimIdx&&(n=o(this,this._nameDimIdx,e)),null==n&&(n=""),n},t.prototype._getCategory=function(t,e){var n=this._store.get(t,e),r=this._store.getOrdinalMeta(t);return r?r.categories[n]:n},t.prototype.getId=function(t){return i(this,this.getRawIndex(t))},t.prototype.count=function(){return this._store.count()},t.prototype.get=function(t,e){var n=this._store,r=this._dimInfos[t];if(r)return n.get(r.storeDimIndex,e)},t.prototype.getByRawIndex=function(t,e){var n=this._store,r=this._dimInfos[t];if(r)return n.getByRawIndex(r.storeDimIndex,e)},t.prototype.getIndices=function(){return this._store.getIndices()},t.prototype.getDataExtent=function(t){return this._store.getDataExtent(this._getStoreDimIndex(t))},t.prototype.getSum=function(t){return this._store.getSum(this._getStoreDimIndex(t))},t.prototype.getMedian=function(t){return this._store.getMedian(this._getStoreDimIndex(t))},t.prototype.getValues=function(t,e){var n=this,r=this._store;return c.kJ(t)?r.getValues(b(t,function(t){return n._getStoreDimIndex(t)}),e):r.getValues(t)},t.prototype.hasValue=function(t){for(var e=this._dimSummary.dataDimIndicesOnCoord,n=0,r=e.length;n<r;n++)if(isNaN(this._store.get(e[n],t)))return!1;return!0},t.prototype.indexOfName=function(t){for(var e=0,n=this._store.count();e<n;e++)if(this.getName(e)===t)return e;return -1},t.prototype.getRawIndex=function(t){return this._store.getRawIndex(t)},t.prototype.indexOfRawIndex=function(t){return this._store.indexOfRawIndex(t)},t.prototype.rawIndexOf=function(t,e){var n=t&&this._invertedIndicesMap[t],r=n[e];return null==r||isNaN(r)?-1:r},t.prototype.indicesOfNearest=function(t,e,n){return this._store.indicesOfNearest(this._getStoreDimIndex(t),e,n)},t.prototype.each=function(t,e,n){c.mf(t)&&(n=e,e=t,t=[]);var r=n||this,i=b(a(t),this._getStoreDimIndex,this);this._store.each(i,r?c.ak(e,r):e)},t.prototype.filterSelf=function(t,e,n){c.mf(t)&&(n=e,e=t,t=[]);var r=n||this,i=b(a(t),this._getStoreDimIndex,this);return this._store=this._store.filter(i,r?c.ak(e,r):e),this},t.prototype.selectRange=function(t){var e=this,n={},r=c.XP(t),i=[];return c.S6(r,function(r){var o=e._getStoreDimIndex(r);n[o]=t[r],i.push(o)}),this._store=this._store.selectRange(n),this},t.prototype.mapArray=function(t,e,n){c.mf(t)&&(n=e,e=t,t=[]),n=n||this;var r=[];return this.each(t,function(){r.push(e&&e.apply(this,arguments))},n),r},t.prototype.map=function(t,e,n,r){var i=n||r||this,o=b(a(t),this._getStoreDimIndex,this),s=u(this);return s._store=this._store.map(o,i?c.ak(e,i):e),s},t.prototype.modify=function(t,e,n,r){var i=n||r||this,o=b(a(t),this._getStoreDimIndex,this);this._store.modify(o,i?c.ak(e,i):e)},t.prototype.downSample=function(t,e,n,r){var i=u(this);return i._store=this._store.downSample(this._getStoreDimIndex(t),e,n,r),i},t.prototype.lttbDownSample=function(t,e){var n=u(this);return n._store=this._store.lttbDownSample(this._getStoreDimIndex(t),e),n},t.prototype.getRawDataItem=function(t){return this._store.getRawDataItem(t)},t.prototype.getItemModel=function(t){var e=this.hostModel,n=this.getRawDataItem(t);return new h.Z(n,e,e&&e.ecModel)},t.prototype.diff=function(t){var e=this;return new f.Z(t?t.getStore().getIndices():[],this.getStore().getIndices(),function(e){return i(t,e)},function(t){return i(e,t)})},t.prototype.getVisual=function(t){var e=this._visual;return e&&e[t]},t.prototype.setVisual=function(t,e){this._visual=this._visual||{},S(t)?c.l7(this._visual,t):this._visual[t]=e},t.prototype.getItemVisual=function(t,e){var n=this._itemVisuals[t],r=n&&n[e];return null==r?this.getVisual(e):r},t.prototype.hasItemVisual=function(){return this._itemVisuals.length>0},t.prototype.ensureUniqueItemVisual=function(t,e){var n=this._itemVisuals,r=n[t];!r&&(r=n[t]={});var i=r[e];return null==i&&(i=this.getVisual(e),c.kJ(i)?i=i.slice():S(i)&&(i=c.l7({},i)),r[e]=i),i},t.prototype.setItemVisual=function(t,e,n){var r=this._itemVisuals[t]||{};this._itemVisuals[t]=r,S(e)?c.l7(r,e):r[e]=n},t.prototype.clearAllVisual=function(){this._visual={},this._itemVisuals=[]},t.prototype.setLayout=function(t,e){S(t)?c.l7(this._layout,t):this._layout[t]=e},t.prototype.getLayout=function(t){return this._layout[t]},t.prototype.getItemLayout=function(t){return this._itemLayouts[t]},t.prototype.setItemLayout=function(t,e,n){this._itemLayouts[t]=n?c.l7(this._itemLayouts[t]||{},e):e},t.prototype.clearItemLayouts=function(){this._itemLayouts.length=0},t.prototype.setItemGraphicEl=function(t,e){var n=this.hostModel&&this.hostModel.seriesIndex;(0,m.Q)(n,this.dataType,t,e),this._graphicEls[t]=e},t.prototype.getItemGraphicEl=function(t){return this._graphicEls[t]},t.prototype.eachItemGraphicEl=function(t,e){c.S6(this._graphicEls,function(n,r){n&&t&&t.call(e,n,r)})},t.prototype.cloneShallow=function(e){return!e&&(e=new t(this._schema?this._schema:b(this.dimensions,this._getDimInfo,this),this.hostModel)),s(e,this),e._store=this._store,e},t.prototype.wrapMethod=function(t,e){var n=this[t];if(!!c.mf(n))this.__wrappedMethods=this.__wrappedMethods||[],this.__wrappedMethods.push(t),this[t]=function(){var t=n.apply(this,arguments);return e.apply(this,[t].concat(c.tP(arguments)))}},t.internalField=void(r=function(t){var e=t._invertedIndicesMap;c.S6(e,function(n,r){var i=t._dimInfos[r],o=i.ordinalMeta,a=t._store;if(o){n=e[r]=new M(o.categories.length);for(var s=0;s<n.length;s++)n[s]=-1;for(var s=0;s<a.count();s++)n[a.get(i.storeDimIndex,s)]=s}})},o=function(t,e,n){return(0,y.U5)(t._getCategory(e,n),null)},i=function(t,e){var n=t._idList[e];return null==n&&null!=t._idDimIdx&&(n=o(t,t._idDimIdx,e)),null==n&&(n="e\0\0"+e),n},a=function(t){return!c.kJ(t)&&(t=null!=t?[t]:[]),t},u=function(e){var n=new t(e._schema?e._schema:b(e.dimensions,e._getDimInfo,e),e.hostModel);return s(n,e),n},s=function(t,e){c.S6(T.concat(e.__wrappedMethods||[]),function(n){e.hasOwnProperty(n)&&(t[n]=e[n])}),t.__wrappedMethods=e.__wrappedMethods,c.S6(k,function(n){t[n]=c.d9(e[n])}),t._calculationInfo=c.l7({},e._calculationInfo)},l=function(t,e){var n=t._nameList,r=t._idList,i=t._nameDimIdx,a=t._idDimIdx,s=n[e],u=r[e];if(null==s&&null!=i&&(n[e]=s=o(t,i,e)),null==u&&null!=a&&(r[e]=u=o(t,a,e)),null==u&&null!=s){var l=t._nameRepeatCount,c=l[s]=(l[s]||0)+1;u=s,c>1&&(u+="__ec__"+c),r[e]=u}}),t}();e.Z=D},395381:function(t,e,n){var r=n(807028);e.Z=function(t){this.otherDims={},null!=t&&r.l7(this,t)}},429590:function(t,e,n){n.d(e,{Kp:function(){return f},Ld:function(){return u},ML:function(){return h},QY:function(){return g},_P:function(){return l},nx:function(){return c}});var r=n(807028),i=n(894870),o=n(133141),a=n(457714),s=function(t){this.data=t.data||(t.sourceFormat===i.hL?{}:[]),this.sourceFormat=t.sourceFormat||i.RA,this.seriesLayoutBy=t.seriesLayoutBy||i.fY,this.startIndex=t.startIndex||0,this.dimensionsDetectedCount=t.dimensionsDetectedCount,this.metaRawOption=t.metaRawOption;var e=this.dimensionsDefine=t.dimensionsDefine;if(e)for(var n=0;n<e.length;n++){var r=e[n];null==r.type&&(0,a.u7)(this,n)===a.Dq.Must&&(r.type="ordinal")}};function u(t){return t instanceof s}function l(t,e,n){n=n||f(t);var a=e.seriesLayoutBy,u=function(t,e,n,a,s){if(!t)return{dimensionsDefine:p(s),startIndex:l,dimensionsDetectedCount:u};if(e===i.XD){var u,l;"auto"===a||null==a?d(function(t){null!=t&&"-"!==t&&((0,r.HD)(t)?null==l&&(l=1):l=0)},n,t,10):l=(0,r.hj)(a)?a:a?1:0,!s&&1===l&&(s=[],d(function(t,e){s[e]=null!=t?t+"":""},n,t,1/0)),u=s?s.length:n===i.Wc?t.length:t[0]?t[0].length:null}else if(e===i.qb)!s&&(s=function(t){for(var e,n=0;n<t.length&&!(e=t[n++]););if(e)return(0,r.XP)(e)}(t));else if(e===i.hL)!s&&(s=[],(0,r.S6)(t,function(t,e){s.push(e)}));else if(e===i.cy){var c=(0,o.C4)(t[0]);u=(0,r.kJ)(c)&&c.length||1}else i.J5;return{startIndex:l,dimensionsDefine:p(s),dimensionsDetectedCount:u}}(t,n,a,e.sourceHeader,e.dimensions);return new s({data:t,sourceFormat:n,seriesLayoutBy:a,dimensionsDefine:u.dimensionsDefine,startIndex:u.startIndex,dimensionsDetectedCount:u.dimensionsDetectedCount,metaRawOption:(0,r.d9)(e)})}function c(t){return new s({data:t,sourceFormat:(0,r.fU)(t)?i.J5:i.cy})}function h(t){return new s({data:t.data,sourceFormat:t.sourceFormat,seriesLayoutBy:t.seriesLayoutBy,dimensionsDefine:(0,r.d9)(t.dimensionsDefine),startIndex:t.startIndex,dimensionsDetectedCount:t.dimensionsDetectedCount})}function f(t){var e=i.RA;if((0,r.fU)(t))e=i.J5;else if((0,r.kJ)(t)){0===t.length&&(e=i.XD);for(var n=0,o=t.length;n<o;n++){var a=t[n];if(null!=a){if((0,r.kJ)(a)||(0,r.fU)(a)){e=i.XD;break}else if((0,r.Kn)(a)){e=i.qb;break}}}}else if((0,r.Kn)(t)){for(var s in t)if((0,r.RI)(t,s)&&(0,r.zG)(t[s])){e=i.hL;break}}return e}function p(t){if(!!t){var e=(0,r.kW)();return(0,r.UI)(t,function(t,n){var i={name:(t=(0,r.Kn)(t)?t:{name:t}).name,displayName:t.displayName,type:t.type};if(null==i.name)return i;i.name+="",null==i.displayName&&(i.displayName=i.name);var o=e.get(i.name);return o?i.name+="-"+o.count++:e.set(i.name,{count:1}),i})}}function d(t,e,n,r){if(e===i.Wc)for(var o=0;o<n.length&&o<r;o++)t(n[o]?n[o][0]:null,o);else{for(var a=n[0]||[],o=0;o<a.length&&o<r;o++)t(a[o],o)}}function g(t){var e=t.sourceFormat;return e===i.qb||e===i.hL}},138600:function(t,e,n){n.d(e,{Eo:function(){return u},Jj:function(){return h},Jl:function(){return f},bB:function(){return l},v5:function(){return c}});var r=n(807028),i=n(133141),o=n(429590),a=(0,i.Yf)(),s={float:"f",int:"i",ordinal:"o",number:"n",time:"t"},u=function(){function t(t){this.dimensions=t.dimensions,this._dimOmitted=t.dimensionOmitted,this.source=t.source,this._fullDimCount=t.fullDimensionCount,this._updateDimOmitted(t.dimensionOmitted)}return t.prototype.isDimensionOmitted=function(){return this._dimOmitted},t.prototype._updateDimOmitted=function(t){if(this._dimOmitted=t,!!t)!this._dimNameMap&&(this._dimNameMap=h(this.source))},t.prototype.getSourceDimensionIndex=function(t){return(0,r.pD)(this._dimNameMap.get(t),-1)},t.prototype.getSourceDimension=function(t){var e=this.source.dimensionsDefine;if(e)return e[t]},t.prototype.makeStoreSchema=function(){for(var t=this._fullDimCount,e=(0,o.QY)(this.source),n=!f(t),r="",i=[],a=0,u=0;a<t;a++){var l=void 0,c=void 0,h=void 0,p=this.dimensions[u];if(p&&p.storeDimIndex===a)l=e?p.name:null,c=p.type,h=p.ordinalMeta,u++;else{var d=this.getSourceDimension(a);d&&(l=e?d.name:null,c=d.type)}i.push({property:l,type:c,ordinalMeta:h}),e&&null!=l&&(!p||!p.isCalculationCoord)&&(r+=n?l.replace(/\`/g,"`1").replace(/\$/g,"`2"):l),r+="$"+(s[c]||"f"),h&&(r+=h.uid),r+="$"}var g=this.source;return{dimensions:i,hash:[g.seriesLayoutBy,g.startIndex,r].join("$$")}},t.prototype.makeOutputDimensionNames=function(){for(var t=[],e=0,n=0;e<this._fullDimCount;e++){var r=void 0,i=this.dimensions[n];if(i&&i.storeDimIndex===e)!i.isCalculationCoord&&(r=i.name),n++;else{var o=this.getSourceDimension(e);o&&(r=o.name)}t.push(r)}return t},t.prototype.appendCalculationDimension=function(t){this.dimensions.push(t),t.isCalculationCoord=!0,this._fullDimCount++,this._updateDimOmitted(!0)},t}();function l(t){return t instanceof u}function c(t){for(var e=(0,r.kW)(),n=0;n<(t||[]).length;n++){var i=t[n],o=(0,r.Kn)(i)?i.name:i;null!=o&&null==e.get(o)&&e.set(o,n)}return e}function h(t){var e=a(t);return e.dimNameMap||(e.dimNameMap=c(t.dimensionsDefine))}function f(t){return t>30}},298322:function(t,e,n){n.d(e,{Z:function(){return h}});var r=n(894870),i=n(395381),o=n(807028),a=n(429590),s=n(969012),u=n(133141),l=n(457714),c=n(138600);function h(t,e){!(0,a.Ld)(t)&&(t=(0,a.nx)(t));var n=(e=e||{}).coordDimensions||[],h=e.dimensionsDefine||t.dimensionsDefine||[],f=(0,o.kW)(),p=[],d=function(t,e,n,r){var i=Math.max(t.dimensionsDetectedCount||1,e.length,n.length,r||0);return(0,o.S6)(e,function(t){var e;(0,o.Kn)(t)&&(e=t.dimsDef)&&(i=Math.max(i,e.length))}),i}(t,n,h,e.dimensionsCount),g=e.canOmitUnusedDimensions&&(0,c.Jl)(d),v=h===t.dimensionsDefine,y=v?(0,c.Jj)(t):(0,c.v5)(h),m=e.encodeDefine;!m&&e.encodeDefaulter&&(m=e.encodeDefaulter(t,d));for(var _=(0,o.kW)(m),x=new s.hG(d),w=0;w<x.length;w++)x[w]=-1;function S(t){var e=x[t];if(e<0){var n=h[t],r=(0,o.Kn)(n)?n:{name:n},a=new i.Z,s=r.name;null!=s&&null!=y.get(s)&&(a.name=a.displayName=s),null!=r.type&&(a.type=r.type),null!=r.displayName&&(a.displayName=r.displayName);var u=p.length;return x[t]=u,a.storeDimIndex=t,p.push(a),a}return p[e]}if(!g)for(var w=0;w<d;w++)S(w);_.each(function(t,e){var n=(0,u.kF)(t).slice();if(1===n.length&&!(0,o.HD)(n[0])&&n[0]<0){_.set(e,!1);return}var r=_.set(e,[]);(0,o.S6)(n,function(t,n){var i=(0,o.HD)(t)?y.get(t):t;null!=i&&i<d&&(r[n]=i,M(S(i),e,n))})});var b=0;function M(t,e,n){null!=r.f7.get(e)?t.otherDims[e]=n:(t.coordDim=e,t.coordDimIndex=n,f.set(e,!0))}(0,o.S6)(n,function(t){if((0,o.HD)(t))e=t,i={};else{e=(i=t).name;var e,n,r,i,a=i.ordinalMeta;i.ordinalMeta=null,(i=(0,o.l7)({},i)).ordinalMeta=a,n=i.dimsDef,r=i.otherDims,i.name=i.coordDim=i.coordDimIndex=i.dimsDef=i.otherDims=null}var s=_.get(e);if(!1!==s){if(!(s=(0,u.kF)(s)).length)for(var l=0;l<(n&&n.length||1);l++){for(;b<d&&null!=S(b).coordDim;)b++;b<d&&s.push(b++)}(0,o.S6)(s,function(t,a){var s=S(t);if(v&&null!=i.type&&(s.type=i.type),M((0,o.ce)(s,i),e,a),null==s.name&&n){var u=n[a];(0,o.Kn)(u)||(u={name:u}),s.name=s.displayName=u.name,s.defaultTooltip=u.defaultTooltip}r&&(0,o.ce)(s.otherDims,r)})}});var T=e.generateCoord,k=e.generateCoordCount,D=null!=k;k=T?k||1:0;var C=T||"value";function I(t){null==t.name&&(t.name=t.coordDim)}if(g)(0,o.S6)(p,function(t){I(t)}),p.sort(function(t,e){return t.storeDimIndex-e.storeDimIndex});else for(var A=0;A<d;A++){var P=S(A);null==P.coordDim&&(P.coordDim=function(t,e,n){if(n||e.hasKey(t)){for(var r=0;e.hasKey(t+r);)r++;t+=r}return e.set(t,!0),t}(C,f,D),P.coordDimIndex=0,(!T||k<=0)&&(P.isExtraCoord=!0),k--),I(P),null==P.type&&((0,l.u7)(t,A)===l.Dq.Must||P.isExtraCoord&&(null!=P.otherDims.itemName||null!=P.otherDims.seriesName))&&(P.type="ordinal")}return function(t){for(var e=(0,o.kW)(),n=0;n<t.length;n++){var r=t[n],i=r.name,a=e.get(i)||0;a>0&&(r.name=i+(a-1)),a++,e.set(i,a)}}(p),new c.Eo({source:t,dimensions:p,fullDimensionCount:d,dimensionOmitted:g})}},279681:function(t,e,n){n.d(e,{Pl:function(){return f},_j:function(){return g},a:function(){return m},hk:function(){return b},tB:function(){return w}});var r,i,o,a,s,u=n(807028),l=n(133141),c=n(429590),h=n(894870),f=function(){var t;function e(t,e){var n=(0,c.Ld)(t)?t:(0,c.nx)(t);this._source=n;var r=this._data=n.data;n.sourceFormat===h.J5&&(this._offset=0,this._dimSize=e,this._data=r),s(this,r,n)}return e.prototype.getSource=function(){return this._source},e.prototype.count=function(){return 0},e.prototype.getItem=function(t,e){},e.prototype.appendData=function(t){},e.prototype.clean=function(){},e.protoInitialize=void((t=e.prototype).pure=!1,t.persistent=!0),e.internalField=function(){s=function(t,i,o){var s=o.sourceFormat,l=o.seriesLayoutBy,c=o.startIndex,f=o.dimensionsDefine,p=a[S(s,l)];if((0,u.l7)(t,p),s===h.J5)t.getItem=e,t.count=r,t.fillStorage=n;else{var d=g(s,l);t.getItem=(0,u.ak)(d,null,i,c,f);var v=m(s,l);t.count=(0,u.ak)(v,null,i,c,f)}};var t,e=function(t,e){t-=this._offset,e=e||[];for(var n=this._data,r=this._dimSize,i=r*t,o=0;o<r;o++)e[o]=n[i+o];return e},n=function(t,e,n,r){for(var i=this._data,o=this._dimSize,a=0;a<o;a++){for(var s=r[a],u=null==s[0]?1/0:s[0],l=null==s[1]?-1/0:s[1],c=e-t,h=n[a],f=0;f<c;f++){var p=i[f*o+a];h[t+f]=p,p<u&&(u=p),p>l&&(l=p)}s[0]=u,s[1]=l}},r=function(){return this._data?this._data.length/this._dimSize:0};function i(t){for(var e=0;e<t.length;e++)this._data.push(t[e])}(t={})[h.XD+"_"+h.fY]={pure:!0,appendData:i},t[h.XD+"_"+h.Wc]={pure:!0,appendData:function(){throw Error('Do not support appendData when set seriesLayoutBy: "row".')}},t[h.qb]={pure:!0,appendData:i},t[h.hL]={pure:!0,appendData:function(t){var e=this._data;(0,u.S6)(t,function(t,n){for(var r=e[n]||(e[n]=[]),i=0;i<(t||[]).length;i++)r.push(t[i])})}},t[h.cy]={appendData:i},t[h.J5]={persistent:!1,pure:!0,appendData:function(t){this._data=t},clean:function(){this._offset+=this.count(),this._data=null}},a=t}(),e}(),p=function(t,e,n,r){return t[r]},d=((r={})[h.XD+"_"+h.fY]=function(t,e,n,r){return t[r+e]},r[h.XD+"_"+h.Wc]=function(t,e,n,r,i){r+=e;for(var o=i||[],a=0;a<t.length;a++){var s=t[a];o[a]=s?s[r]:null}return o},r[h.qb]=p,r[h.hL]=function(t,e,n,r,i){for(var o=i||[],a=0;a<n.length;a++){var s=n[a].name,u=t[s];o[a]=u?u[r]:null}return o},r[h.cy]=p,r);function g(t,e){var n=d[S(t,e)];return n}var v=function(t,e,n){return t.length},y=((i={})[h.XD+"_"+h.fY]=function(t,e,n){return Math.max(0,t.length-e)},i[h.XD+"_"+h.Wc]=function(t,e,n){var r=t[0];return r?Math.max(0,r.length-e):0},i[h.qb]=v,i[h.hL]=function(t,e,n){var r=n[0].name,i=t[r];return i?i.length:0},i[h.cy]=v,i);function m(t,e){var n=y[S(t,e)];return n}var _=function(t,e,n){return t[e]},x=((o={})[h.XD]=_,o[h.qb]=function(t,e,n){return t[n]},o[h.hL]=_,o[h.cy]=function(t,e,n){var r=(0,l.C4)(t);return r instanceof Array?r[e]:r},o[h.J5]=_,o);function w(t){var e=x[t];return e}function S(t,e){return t===h.XD?t+"_"+e:t}function b(t,e,n){if(!t)return;var r=t.getRawDataItem(e);if(null!=r){var i=t.getStore(),o=i.getSource().sourceFormat;if(null!=n){var a=t.getDimensionIndex(n),s=i.getDimensionProperty(a);return w(o)(r,a,s)}var u=r;return o===h.cy&&(u=(0,l.C4)(r)),u}}},254308:function(t,e,n){n.d(e,{BM:function(){return o},IR:function(){return s},M:function(){return a}});var r=n(807028),i=n(138600);function o(t,e,n){var o,a,s,u,l,c,h,f=(n=n||{}).byIndex,p=n.stackedCoordDimension;(function(t){return!(0,i.bB)(t.schema)})(e)?o=e:(o=(a=e.schema).dimensions,s=e.store);var d=!!(t&&t.get("stack"));if((0,r.S6)(o,function(t,e){(0,r.HD)(t)&&(o[e]=t={name:t}),d&&!t.isExtraCoord&&(!f&&!u&&t.ordinalMeta&&(u=t),!l&&"ordinal"!==t.type&&"time"!==t.type&&(!p||p===t.coordDim)&&(l=t))}),l&&!f&&!u&&(f=!0),l){c="__\0ecstackresult_"+t.id,h="__\0ecstackedover_"+t.id,u&&(u.createInvertedIndices=!0);var g=l.coordDim,v=l.type,y=0;(0,r.S6)(o,function(t){t.coordDim===g&&y++});var m={name:c,coordDim:g,coordDimIndex:y,type:v,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:o.length},_={name:h,coordDim:h,coordDimIndex:y+1,type:v,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:o.length+1};a?(s&&(m.storeDimIndex=s.ensureCalculationDimension(h,v),_.storeDimIndex=s.ensureCalculationDimension(c,v)),a.appendCalculationDimension(m),a.appendCalculationDimension(_)):(o.push(m),o.push(_))}return{stackedDimension:l&&l.name,stackedByDimension:u&&u.name,isStackedByIndex:f,stackedOverDimension:h,stackResultDimension:c}}function a(t,e){return!!e&&e===t.getCalculationInfo("stackedDimension")}function s(t,e){return a(t,e)?t.getCalculationInfo("stackResultDimension"):e}},43296:function(t,e,n){n.d(e,{yQ:function(){return a}});var r=n(31674),i=n(807028),o=n(383831);function a(t,e){var n=e&&e.type;return"ordinal"===n?t:("time"===n&&!(0,i.hj)(t)&&null!=t&&"-"!==t&&(t=+(0,r.sG)(t)),null==t||""===t?NaN:Number(t))}(0,i.kW)({number:function(t){return parseFloat(t)},time:function(t){return+(0,r.sG)(t)},trim:function(t){return(0,i.HD)(t)?(0,i.fy)(t):t}});var s={lt:function(t,e){return t<e},lte:function(t,e){return t<=e},gt:function(t,e){return t>e},gte:function(t,e){return t>=e}};(function(t,e){!(0,i.hj)(e)&&(0,o._y)(""),this._opFn=s[t],this._rvalFloat=(0,r.FK)(e)}).prototype.evaluate=function(t){return(0,i.hj)(t)?this._opFn(t,this._rvalFloat):this._opFn((0,r.FK)(t),this._rvalFloat)},(function(t,e){var n="desc"===t;this._resultLT=n?1:-1,null==e&&(e=n?"min":"max"),this._incomparable="min"===e?-1/0:1/0}).prototype.evaluate=function(t,e){var n=(0,i.hj)(t)?t:(0,r.FK)(t),o=(0,i.hj)(e)?e:(0,r.FK)(e),a=isNaN(n),s=isNaN(o);if(a&&(n=this._incomparable),s&&(o=this._incomparable),a&&s){var u=(0,i.HD)(t),l=(0,i.HD)(e);u&&(n=l?t:0),l&&(o=u?e:0)}return n<o?this._resultLT:n>o?-this._resultLT:0},(function(t,e){this._rval=e,this._isEQ=t,this._rvalTypeof=typeof e,this._rvalFloat=(0,r.FK)(e)}).prototype.evaluate=function(t){var e=t===this._rval;if(!e){var n=typeof t;n!==this._rvalTypeof&&("number"===n||"number"===this._rvalTypeof)&&(e=(0,r.FK)(t)===this._rvalFloat)}return this._isEQ?e:!e}},220016:function(t,e,n){n.d(e,{T:function(){return u},y:function(){return a}});var r=n(807028),i=n(894870),o=function(){function t(t,e){this._encode=t,this._schema=e}return t.prototype.get=function(){return{fullDimensions:this._getFullDimensionNames(),encode:this._encode}},t.prototype._getFullDimensionNames=function(){return!this._cachedDimNames&&(this._cachedDimNames=this._schema?this._schema.makeOutputDimensionNames():[]),this._cachedDimNames},t}();function a(t,e){var n={},a=n.encode={},u=(0,r.kW)(),l=[],c=[],h={};(0,r.S6)(t.dimensions,function(e){var n=t.getDimensionInfo(e),r=n.coordDim;if(r){var o=n.coordDimIndex;s(a,r)[o]=e,!n.isExtraCoord&&(u.set(r,1),function(t){return!("ordinal"===t||"time"===t)}(n.type)&&(l[0]=e),s(h,r)[o]=t.getDimensionIndex(n.name)),n.defaultTooltip&&c.push(e)}i.f7.each(function(t,e){var r=s(a,e),i=n.otherDims[e];null!=i&&!1!==i&&(r[i]=n.name)})});var f=[],p={};u.each(function(t,e){var n=a[e];p[e]=n[0],f=f.concat(n)}),n.dataDimsOnCoord=f,n.dataDimIndicesOnCoord=(0,r.UI)(f,function(e){return t.getDimensionInfo(e).storeDimIndex}),n.encodeFirstDimNotExtra=p;var d=a.label;d&&d.length&&(l=d.slice());var g=a.tooltip;return g&&g.length?c=g.slice():!c.length&&(c=l.slice()),a.defaultedLabel=l,a.defaultedTooltip=c,n.userOutput=new o(h,e),n}function s(t,e){return!t.hasOwnProperty(e)&&(t[e]=[]),t[e]}function u(t){return"category"===t?"ordinal":"time"===t?"time":"float"}},457714:function(t,e,n){n.d(e,{Dq:function(){return a},JT:function(){return h},Wd:function(){return c},md:function(){return u},pY:function(){return l},u7:function(){return f}});var r=n(133141),i=n(807028),o=n(894870),a={Must:1,Might:2,Not:3},s=(0,r.Yf)();function u(t){s(t).datasetMap=(0,i.kW)()}function l(t,e,n){var r,o,a={},u=c(e);if(!u||!t)return a;var l=[],h=[],f=s(e.ecModel).datasetMap,p=u.uid+"_"+n.seriesLayoutBy;t=t.slice(),(0,i.S6)(t,function(e,n){var s=(0,i.Kn)(e)?e:t[n]={name:e};"ordinal"===s.type&&null==r&&(r=n,o=v(s)),a[s.name]=[]});var d=f.get(p)||f.set(p,{categoryWayDim:o,valueWayDim:0});function g(t,e,n){for(var r=0;r<n;r++)t.push(e+r)}function v(t){var e=t.dimsDef;return e?e.length:1}return(0,i.S6)(t,function(t,e){var n=t.name,i=v(t);if(null==r){var o=d.valueWayDim;g(a[n],o,i),g(h,o,i),d.valueWayDim+=i}else if(r===e)g(a[n],0,i),g(l,0,i);else{var o=d.categoryWayDim;g(a[n],o,i),g(h,o,i),d.categoryWayDim+=i}}),l.length&&(a.itemName=l),h.length&&(a.seriesName=h),a}function c(t){if(!t.get("data",!0))return(0,r.HZ)(t.ecModel,"dataset",{index:t.get("datasetIndex",!0),id:t.get("datasetId",!0)},r.C6).models[0]}function h(t){return t.get("transform",!0)||t.get("fromTransformResult",!0)?(0,r.HZ)(t.ecModel,"dataset",{index:t.get("fromDatasetIndex",!0),id:t.get("fromDatasetId",!0)},r.C6).models:[]}function f(t,e){return function(t,e,n,s,u,l){var c,h,f;if((0,i.fU)(t))return a.Not;if(s){var p=s[l];(0,i.Kn)(p)?(h=p.name,f=p.type):(0,i.HD)(p)&&(h=p)}if(null!=f)return"ordinal"===f?a.Must:a.Not;if(e===o.XD){if(n===o.Wc){for(var d=t[l],g=0;g<(d||[]).length&&g<5;g++)if(null!=(c=_(d[u+g])))return c}else for(var g=0;g<t.length&&g<5;g++){var v=t[u+g];if(v&&null!=(c=_(v[l])))return c}}else if(e===o.qb){if(!h)return a.Not;for(var g=0;g<t.length&&g<5;g++){var y=t[g];if(y&&null!=(c=_(y[h])))return c}}else if(e===o.hL){if(!h)return a.Not;var d=t[h];if(!d||(0,i.fU)(d))return a.Not;for(var g=0;g<d.length&&g<5;g++)if(null!=(c=_(d[g])))return c}else if(e===o.cy)for(var g=0;g<t.length&&g<5;g++){var y=t[g],m=(0,r.C4)(y);if(!(0,i.kJ)(m))return a.Not;if(null!=(c=_(m[l])))return c}function _(t){var e=(0,i.HD)(t);return null!=t&&Number.isFinite(Number(t))&&""!==t?e?a.Might:a.Not:e&&"-"!==t?a.Must:void 0}return a.Not}(t.data,t.sourceFormat,t.seriesLayoutBy,t.dimensionsDefine,t.startIndex,e)}},540644:function(t,e,n){n.d(e,{U:function(){return c}});var r=n(807028),i=n(429590),o=n(894870),a=n(457714),s=n(65763),u=n(969012),l=n(279681),c=function(){function t(t){this._sourceList=[],this._storeList=[],this._upstreamSignList=[],this._versionSignBase=0,this._dirty=!0,this._sourceHost=t}return t.prototype.dirty=function(){this._setLocalSource([],[]),this._storeList=[],this._dirty=!0},t.prototype._setLocalSource=function(t,e){this._sourceList=t,this._upstreamSignList=e,this._versionSignBase++,this._versionSignBase>9e10&&(this._versionSignBase=0)},t.prototype._getVersionSign=function(){return this._sourceHost.uid+"_"+this._versionSignBase},t.prototype.prepareSource=function(){this._isDirty()&&(this._createSource(),this._dirty=!1)},t.prototype._createSource=function(){this._setLocalSource([],[]);var t,e,n=this._sourceHost,a=this._getUpstreamSourceManagers(),s=!!a.length;if(h(n)){var u=void 0,l=void 0,c=void 0;if(s){var f=a[0];f.prepareSource(),u=(c=f.getSource()).data,l=c.sourceFormat,e=[f._getVersionSign()]}else u=n.get("data",!0),l=(0,r.fU)(u)?o.J5:o.cy,e=[];var p=this._getSourceMetaRawOption()||{},d=c&&c.metaRawOption||{},g=(0,r.pD)(p.seriesLayoutBy,d.seriesLayoutBy)||null,v=(0,r.pD)(p.sourceHeader,d.sourceHeader),y=(0,r.pD)(p.dimensions,d.dimensions);t=g!==d.seriesLayoutBy||!!v!=!!d.sourceHeader||y?[(0,i._P)(u,{seriesLayoutBy:g,sourceHeader:v,dimensions:y},l)]:[]}else if(s){var m=this._applyTransform(a);t=m.sourceList,e=m.upstreamSignList}else{var _=n.get("source",!0);t=[(0,i._P)(_,this._getSourceMetaRawOption(),null)],e=[]}this._setLocalSource(t,e)},t.prototype._applyTransform=function(t){var e,n=this._sourceHost,o=n.get("transform",!0),a=n.get("fromTransformResult",!0);null!=a&&1!==t.length&&f("");var u=[],l=[];return(0,r.S6)(t,function(t){t.prepareSource();var e=t.getSource(a||0);null!=a&&!e&&f(""),u.push(e),l.push(t._getVersionSign())}),o?e=(0,s.vK)(o,u,{datasetIndex:n.componentIndex}):null!=a&&(e=[(0,i.ML)(u[0])]),{sourceList:e,upstreamSignList:l}},t.prototype._isDirty=function(){if(this._dirty)return!0;for(var t=this._getUpstreamSourceManagers(),e=0;e<t.length;e++){var n=t[e];if(n._isDirty()||this._upstreamSignList[e]!==n._getVersionSign())return!0}},t.prototype.getSource=function(t){t=t||0;var e=this._sourceList[t];if(!e){var n=this._getUpstreamSourceManagers();return n[0]&&n[0].getSource(t)}return e},t.prototype.getSharedDataStore=function(t){var e=t.makeStoreSchema();return this._innerGetDataStore(e.dimensions,t.source,e.hash)},t.prototype._innerGetDataStore=function(t,e,n){var r=this._storeList,i=r[0];!i&&(i=r[0]={});var o=i[n];if(!o){var a=this._getUpstreamSourceManagers()[0];h(this._sourceHost)&&a?o=a._innerGetDataStore(t,e,n):(o=new u.ZP).initData(new l.Pl(e,t.length),t),i[n]=o}return o},t.prototype._getUpstreamSourceManagers=function(){var t=this._sourceHost;if(!h(t))return(0,r.UI)((0,a.JT)(t),function(t){return t.getSourceManager()});var e=(0,a.Wd)(t);return e?[e.getSourceManager()]:[]},t.prototype._getSourceMetaRawOption=function(){var t,e,n,r=this._sourceHost;return h(r)?(t=r.get("seriesLayoutBy",!0),e=r.get("sourceHeader",!0),n=r.get("dimensions",!0)):!this._getUpstreamSourceManagers().length&&(t=r.get("seriesLayoutBy",!0),e=r.get("sourceHeader",!0),n=r.get("dimensions",!0)),{seriesLayoutBy:t,sourceHeader:e,dimensions:n}},t}();function h(t){return"series"===t.mainType}function f(t){throw Error(t)}},65763:function(t,e,n){n.d(e,{DA:function(){return v},vK:function(){return y}});var r=n(894870),i=n(133141),o=n(807028),a=n(279681),s=n(43296),u=n(383831),l=n(429590),c=function(){function t(){}return t.prototype.getRawData=function(){throw Error("not supported")},t.prototype.getRawDataItem=function(t){throw Error("not supported")},t.prototype.cloneRawData=function(){},t.prototype.getDimensionInfo=function(t){},t.prototype.cloneAllDimensionInfo=function(){},t.prototype.count=function(){},t.prototype.retrieveValue=function(t,e){},t.prototype.retrieveValueFromItem=function(t,e){},t.prototype.convertValue=function(t,e){return(0,s.yQ)(t,e)},t}();function h(t){return!m(t.sourceFormat)&&(0,u._y)(""),t.data}function f(t){var e=t.sourceFormat,n=t.data;if(!m(e)&&(0,u._y)(""),e===r.XD){for(var i=[],a=0,s=n.length;a<s;a++)i.push(n[a].slice());return i}if(e===r.qb){for(var i=[],a=0,s=n.length;a<s;a++)i.push((0,o.l7)({},n[a]));return i}}function p(t,e,n){return null==n?void 0:!(0,o.hj)(n)&&(isNaN(n)||(0,o.RI)(e,n))?(0,o.RI)(e,n)?e[n]:void 0:t[n]}function d(t){return(0,o.d9)(t)}var g=(0,o.kW)();function v(t){var e=(t=(0,o.d9)(t)).type;!e&&(0,u._y)("");var n=e.split(":");2!==n.length&&(0,u._y)("");var r=!1;"echarts"===n[0]&&(e=n[1],r=!0),t.__isBuiltIn=r,g.set(e,t)}function y(t,e,n){var s=(0,i.kF)(t),v=s.length;!v&&(0,u._y)("");for(var y=0;y<v;y++)e=function(t,e,n,s){!e.length&&(0,u._y)(""),!(0,o.Kn)(t)&&(0,u._y)("");var v=t.type,y=g.get(v);!y&&(0,u._y)("");var _=(0,o.UI)(e,function(t){return function(t,e){var n=new c,i=t.data,s=n.sourceFormat=t.sourceFormat,l=t.startIndex;t.seriesLayoutBy!==r.fY&&(0,u._y)("");var g=[],v={},y=t.dimensionsDefine;if(y)(0,o.S6)(y,function(t,e){var n=t.name,r={index:e,name:n,displayName:t.displayName};g.push(r),null!=n&&((0,o.RI)(v,n)&&(0,u._y)(""),v[n]=r)});else for(var m=0;m<t.dimensionsDetectedCount;m++)g.push({index:m});var _=(0,a._j)(s,r.fY);e.__isBuiltIn&&(n.getRawDataItem=function(t){return _(i,l,g,t)},n.getRawData=(0,o.ak)(h,null,t)),n.cloneRawData=(0,o.ak)(f,null,t);var x=(0,a.a)(s,r.fY);n.count=(0,o.ak)(x,null,i,l,g);var w=(0,a.tB)(s);n.retrieveValue=function(t,e){return S(_(i,l,g,t),e)};var S=n.retrieveValueFromItem=function(t,e){if(null!=t){var n=g[e];if(n)return w(t,e,n.name)}};return n.getDimensionInfo=(0,o.ak)(p,null,g,v),n.cloneAllDimensionInfo=(0,o.ak)(d,null,g),n}(t,y)}),x=(0,i.kF)(y.transform({upstream:_[0],upstreamList:_,config:(0,o.d9)(t.config)}));return(0,o.UI)(x,function(t,n){!(0,o.Kn)(t)&&(0,u._y)(""),!t.data&&(0,u._y)(""),!m((0,l.Kp)(t.data))&&(0,u._y)("");var i,a=e[0];if(a&&0===n&&!t.dimensions){var s=a.startIndex;s&&(t.data=a.data.slice(0,s).concat(t.data)),i={seriesLayoutBy:r.fY,sourceHeader:s,dimensions:a.metaRawOption.dimensions}}else i={seriesLayoutBy:r.fY,sourceHeader:0,dimensions:t.dimensions};return(0,l._P)(t.data,i,null)})}(s[y],e,n,1===v?null:y),y!==v-1&&(e.length=Math.max(e.length,1));return e}function m(t){return t===r.XD||t===r.qb}},574138:function(t,e,n){n.d(e,{D:function(){return function t(e){if((0,u.kJ)(e)){(0,u.S6)(e,function(e){t(e)});return}if(!((0,u.cq)(h,e)>=0))h.push(e),(0,u.mf)(e)&&(e={install:e}),e.install(f)}}});var r=n(269949),i=n(860736),o=n(804575),a=n(882425),s=n(970597),u=n(807028),l=n(504146),c=n(305407),h=[],f={registerPreprocessor:r.ds,registerProcessor:r.Pu,registerPostInit:r.sq,registerPostUpdate:r.Br,registerUpdateLifecycle:r.YK,registerAction:r.zl,registerCoordinateSystem:r.RS,registerLayout:r.qR,registerVisual:r.Og,registerTransform:r.OB,registerLoading:r.yn,registerMap:r.je,registerImpl:l.M,PRIORITY:r.Hr,ComponentModel:a.Z,ComponentView:i.Z,SeriesModel:s.Z,ChartView:o.Z,registerComponentModel:function(t){a.Z.registerClass(t)},registerComponentView:function(t){i.Z.registerClass(t)},registerSeriesModel:function(t){s.Z.registerClass(t)},registerChartView:function(t){o.Z.registerClass(t)},registerSubTypeDefaulter:function(t,e){a.Z.registerSubTypeDefaulter(t,e)},registerPainter:function(t,e){(0,c.wm)(t,e)}}},183754:function(t,e){e.Z={time:{month:["January","February","March","April","May","June","July","August","September","October","November","December"],monthAbbr:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],dayOfWeek:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],dayOfWeekAbbr:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"]},legend:{selector:{all:"All",inverse:"Inv"}},toolbox:{brush:{title:{rect:"Box Select",polygon:"Lasso Select",lineX:"Horizontally Select",lineY:"Vertically Select",keep:"Keep Selections",clear:"Clear Selections"}},dataView:{title:"Data View",lang:["Data View","Close","Refresh"]},dataZoom:{title:{zoom:"Zoom",back:"Zoom Reset"}},magicType:{title:{line:"Switch to Line Chart",bar:"Switch to Bar Chart",stack:"Stack",tiled:"Tile"}},restore:{title:"Restore"},saveAsImage:{title:"Save as Image",lang:["Right Click to Save Image"]}},series:{typeNames:{pie:"Pie chart",bar:"Bar chart",line:"Line chart",scatter:"Scatter plot",effectScatter:"Ripple scatter plot",radar:"Radar chart",tree:"Tree",treemap:"Treemap",boxplot:"Boxplot",candlestick:"Candlestick",k:"K line chart",heatmap:"Heat map",map:"Map",parallel:"Parallel coordinate map",lines:"Line graph",graph:"Relationship graph",sankey:"Sankey diagram",funnel:"Funnel chart",gauge:"Gauge",pictorialBar:"Pictorial bar",themeRiver:"Theme River Map",sunburst:"Sunburst",custom:"Custom chart",chart:"Chart"}},aria:{general:{withTitle:'This is a chart about "{title}"',withoutTitle:"This is a chart"},series:{single:{prefix:"",withName:" with type {seriesType} named {seriesName}.",withoutName:" with type {seriesType}."},multiple:{prefix:". It consists of {seriesCount} series count.",withName:" The {seriesId} series is a {seriesType} representing {seriesName}.",withoutName:" The {seriesId} series is a {seriesType}.",separator:{middle:"",end:""}}},data:{allData:"The data is as follows: ",partialData:"The first {displayCnt} items are: ",withName:"the data for {name} is {value}",withoutName:"{value}",separator:{middle:", ",end:". "}}}}},414473:function(t,e){e.Z={time:{month:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"],monthAbbr:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],dayOfWeek:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"],dayOfWeekAbbr:["日","一","二","三","四","五","六"]},legend:{selector:{all:"全选",inverse:"反选"}},toolbox:{brush:{title:{rect:"矩形选择",polygon:"圈选",lineX:"横向选择",lineY:"纵向选择",keep:"保持选择",clear:"清除选择"}},dataView:{title:"数据视图",lang:["数据视图","关闭","刷新"]},dataZoom:{title:{zoom:"区域缩放",back:"区域缩放还原"}},magicType:{title:{line:"切换为折线图",bar:"切换为柱状图",stack:"切换为堆叠",tiled:"切换为平铺"}},restore:{title:"还原"},saveAsImage:{title:"保存为图片",lang:["右键另存为图片"]}},series:{typeNames:{pie:"饼图",bar:"柱状图",line:"折线图",scatter:"散点图",effectScatter:"涟漪散点图",radar:"雷达图",tree:"树图",treemap:"矩形树图",boxplot:"箱型图",candlestick:"K线图",k:"K线图",heatmap:"热力图",map:"地图",parallel:"平行坐标图",lines:"线图",graph:"关系图",sankey:"桑基图",funnel:"漏斗图",gauge:"仪表盘图",pictorialBar:"象形柱图",themeRiver:"主题河流图",sunburst:"旭日图",custom:"自定义图表",chart:"图表"}},aria:{general:{withTitle:"这是一个关于“{title}”的图表。",withoutTitle:"这是一个图表，"},series:{single:{prefix:"",withName:"图表类型是{seriesType}，表示{seriesName}。",withoutName:"图表类型是{seriesType}。"},multiple:{prefix:"它由{seriesCount}个图表系列组成。",withName:"第{seriesId}个系列是一个表示{seriesName}的{seriesType}，",withoutName:"第{seriesId}个系列是一个{seriesType}，",separator:{middle:"；",end:"。"}}},data:{allData:"其数据是——",partialData:"其中，前{displayCnt}项是——",withName:"{name}的数据是{value}",withoutName:"{value}",separator:{middle:"，",end:""}}}}},289186:function(t,e,n){n.d(e,{GI:function(){return u},VT:function(){return o},WE:function(){return s},yl:function(){return l}});var r=n(660487),i=n(339296);function o(t){for(var e=[],n=0;n<t.length;n++){var i=t[n];if(!i.defaultAttr.ignore){var o=i.label,a=o.getComputedTransform(),s=o.getBoundingRect(),u=!a||a[1]<1e-5&&a[2]<1e-5,l=o.style.margin||0,c=s.clone();c.applyTransform(a),c.x-=l/2,c.y-=l/2,c.width+=l,c.height+=l;var h=u?new r.Z(s,a):null;e.push({label:o,labelLine:i.labelLine,rect:c,localRect:s,obb:h,priority:i.priority,defaultAttr:i.defaultAttr,layoutOption:i.computedLayoutOption,axisAligned:u,transform:a})}}return e}function a(t,e,n,r,i,o){var a,s,u,l=t.length;if(!(l<2)){t.sort(function(t,n){return t.rect[e]-n.rect[e]});for(var c=0,h=!1,f=[],p=0,d=0;d<l;d++){var g=t[d],v=g.rect;(a=v[e]-c)<0&&(v[e]-=a,g.label[e]-=a,h=!0);var y=Math.max(-a,0);f.push(y),p+=y,c=v[e]+v[n]}p>0&&o&&S(-p/l,0,l);var m=t[0],_=t[l-1];return x(),s<0&&b(-s,.8),u<0&&b(u,.8),x(),w(s,u,1),w(u,s,-1),x(),s<0&&M(-s),u<0&&M(u),h}function x(){s=m.rect[e]-r,u=i-_.rect[e]-_.rect[n]}function w(t,e,n){if(t<0){var r=Math.min(e,-t);if(r>0){S(r*n,0,l);var i=r+t;i<0&&b(-i*n,1)}else b(-t*n,1)}}function S(n,r,i){0!==n&&(h=!0);for(var o=r;o<i;o++){var a=t[o],s=a.rect;s[e]+=n,a.label[e]+=n}}function b(r,i){for(var o=[],a=0,s=1;s<l;s++){var u=t[s-1].rect,c=Math.max(t[s].rect[e]-u[e]-u[n],0);o.push(c),a+=c}if(!!a){var h=Math.min(Math.abs(r)/a,i);if(r>0)for(var s=0;s<l-1;s++){var f=o[s]*h;S(f,0,s+1)}else for(var s=l-1;s>0;s--){var f=o[s-1]*h;S(-f,s,l)}}}function M(t){for(var e=t<0?-1:1,n=Math.ceil((t=Math.abs(t))/(l-1)),r=0;r<l-1;r++)if(e>0?S(n,0,r+1):S(-n,l-r-1,l),(t-=n)<=0)return}}function s(t,e,n,r){return a(t,"x","width",e,n,r)}function u(t,e,n,r){return a(t,"y","height",e,n,r)}function l(t){var e=[];t.sort(function(t,e){return e.priority-t.priority});var n=new i.Z(0,0,0,0);function o(t){if(!t.ignore){var e=t.ensureState("emphasis");null==e.ignore&&(e.ignore=!1)}t.ignore=!0}for(var a=0;a<t.length;a++){var s=t[a],u=s.axisAligned,l=s.localRect,c=s.transform,h=s.label,f=s.labelLine;n.copy(s.rect),n.width-=.1,n.height-=.1,n.x+=.05,n.y+=.05;for(var p=s.obb,d=!1,g=0;g<e.length;g++){var v=e[g];if(!!n.intersect(v.rect)){if(u&&v.axisAligned){d=!0;break}if(!v.obb&&(v.obb=new r.Z(v.localRect,v.transform)),!p&&(p=new r.Z(l,c)),p.intersect(v.obb)){d=!0;break}}}d?(o(h),f&&o(f)):(h.attr("ignore",s.defaultAttr.ignore),f&&f.attr("ignore",s.defaultAttr.labelGuideIgnore),e.push(s))}}},931918:function(t,e,n){n.d(e,{Lr:function(){return p},k3:function(){return f},ni:function(){return h},pe:function(){return w},qA:function(){return x},qT:function(){return _},tD:function(){return S}});var r=n(345262),i=n(807028),o=n(623815),a=n(133141),s=n(202953),u={};function l(t,e){for(var n=0;n<o.L1.length;n++){var r=o.L1[n],i=e[r],a=t.ensureState(r);a.style=a.style||{},a.style.text=i}var s=t.currentStates.slice();t.clearStates(!0),t.setStyle({text:e.normal}),t.useStates(s,!0)}function c(t,e,n){var r,a=t.labelFetcher,s=t.labelDataIndex,u=t.labelDimIndex,l=e.normal;a&&(r=a.getFormattedLabel(s,"normal",null,u,l&&l.get("formatter"),null!=n?{interpolatedValue:n}:null)),null==r&&(r=(0,i.mf)(t.defaultText)?t.defaultText(s,t,n):t.defaultText);for(var c={normal:r},h=0;h<o.L1.length;h++){var f=o.L1[h],p=e[f];c[f]=(0,i.pD)(a?a.getFormattedLabel(s,f,null,u,p&&p.get("formatter")):null,r)}return c}function h(t,e,n,a){n=n||u;for(var s=t instanceof r.ZP,h=!1,f=0;f<o.qc.length;f++){var g=e[o.qc[f]];if(g&&g.getShallow("show")){h=!0;break}}var v=s?t:t.getTextContent();if(h){!s&&(!v&&(v=new r.ZP,t.setTextContent(v)),t.stateProxy&&(v.stateProxy=t.stateProxy));var y=c(n,e),m=e.normal,_=!!m.getShallow("show"),w=p(m,a&&a.normal,n,!1,!s);w.text=y.normal,!s&&t.setTextConfig(d(m,n,!1));for(var f=0;f<o.L1.length;f++){var S=o.L1[f],g=e[S];if(g){var b=v.ensureState(S),M=!!(0,i.pD)(g.getShallow("show"),_);M!==_&&(b.ignore=!M),b.style=p(g,a&&a[S],n,!0,!s),b.style.text=y[S],!s&&(t.ensureState(S).textConfig=d(g,n,!0))}}v.silent=!!m.getShallow("silent"),null!=v.style.x&&(w.x=v.style.x),null!=v.style.y&&(w.y=v.style.y),v.ignore=!_,v.useStyle(w),v.dirty(),n.enableTextSetter&&(x(v).setLabelText=function(t){var r=c(n,e,t);l(v,r)})}else v&&(v.ignore=!0);t.dirty()}function f(t,e){e=e||"label";for(var n={normal:t.getModel(e)},r=0;r<o.L1.length;r++){var i=o.L1[r];n[i]=t.getModel([i,e])}return n}function p(t,e,n,r,o){var a={};return function(t,e,n,r,o){n=n||u;var a,s=e.ecModel,l=s&&s.option.textStyle,c=function(t){for(var e;t&&t!==t.ecModel;){var n=(t.option||u).rich;if(n){e=e||{};for(var r=(0,i.XP)(n),o=0;o<r.length;o++)e[r[o]]=1}t=t.parentModel}return e}(e);if(c){for(var h in a={},c)if(c.hasOwnProperty(h)){var f=e.getModel(["rich",h]);m(a[h]={},f,l,n,r,o,!1,!0)}}a&&(t.rich=a);var p=e.get("overflow");p&&(t.overflow=p);var d=e.get("minMargin");null!=d&&(t.margin=d),m(t,e,l,n,r,o,!0,!1)}(a,t,n,r,o),e&&(0,i.l7)(a,e),a}function d(t,e,n){e=e||{};var r,o={},a=t.getShallow("rotate"),s=(0,i.pD)(t.getShallow("distance"),n?null:5),u=t.getShallow("offset");return"outside"===(r=t.getShallow("position")||(n?null:"inside"))&&(r=e.defaultOutsidePosition||"top"),null!=r&&(o.position=r),null!=u&&(o.offset=u),null!=a&&(a*=Math.PI/180,o.rotation=a),null!=s&&(o.distance=s),o.outsideFill="inherit"===t.get("color")?e.inheritColor||null:"auto",o}var g=["fontStyle","fontWeight","fontSize","fontFamily","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY"],v=["align","lineHeight","width","height","tag","verticalAlign","ellipsis"],y=["padding","borderWidth","borderRadius","borderDashOffset","backgroundColor","borderColor","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"];function m(t,e,n,r,o,a,s,l){n=!o&&n||u;var c=r&&r.inheritColor,h=e.getShallow("color"),f=e.getShallow("textBorderColor"),p=(0,i.pD)(e.getShallow("opacity"),n.opacity);("inherit"===h||"auto"===h)&&(h=c?c:null),("inherit"===f||"auto"===f)&&(f=c?c:null),!a&&(h=h||n.color,f=f||n.textBorderColor),null!=h&&(t.fill=h),null!=f&&(t.stroke=f);var d=(0,i.pD)(e.getShallow("textBorderWidth"),n.textBorderWidth);null!=d&&(t.lineWidth=d);var m=(0,i.pD)(e.getShallow("textBorderType"),n.textBorderType);null!=m&&(t.lineDash=m);var _=(0,i.pD)(e.getShallow("textBorderDashOffset"),n.textBorderDashOffset);null!=_&&(t.lineDashOffset=_),!o&&null==p&&!l&&(p=r&&r.defaultOpacity),null!=p&&(t.opacity=p),!o&&!a&&null==t.fill&&r.inheritColor&&(t.fill=r.inheritColor);for(var x=0;x<g.length;x++){var w=g[x],S=(0,i.pD)(e.getShallow(w),n[w]);null!=S&&(t[w]=S)}for(var x=0;x<v.length;x++){var w=v[x],S=e.getShallow(w);null!=S&&(t[w]=S)}if(null==t.verticalAlign){var b=e.getShallow("baseline");null!=b&&(t.verticalAlign=b)}if(!s||!r.disableBox){for(var x=0;x<y.length;x++){var w=y[x],S=e.getShallow(w);null!=S&&(t[w]=S)}var M=e.getShallow("borderType");null!=M&&(t.borderDash=M),("auto"===t.backgroundColor||"inherit"===t.backgroundColor)&&c&&(t.backgroundColor=c),("auto"===t.borderColor||"inherit"===t.borderColor)&&c&&(t.borderColor=c)}}function _(t,e){var n=e&&e.getModel("textStyle");return(0,i.fy)([t.fontStyle||n&&n.getShallow("fontStyle")||"",t.fontWeight||n&&n.getShallow("fontWeight")||"",(t.fontSize||n&&n.getShallow("fontSize")||12)+"px",t.fontFamily||n&&n.getShallow("fontFamily")||"sans-serif"].join(" "))}var x=(0,a.Yf)();function w(t,e,n,r){if(!!t){var i=x(t);i.prevValue=i.value,i.value=n;var o=e.normal;i.valueAnimation=o.get("valueAnimation"),i.valueAnimation&&(i.precision=o.get("precision"),i.defaultInterpolatedText=r,i.statesModels=e)}}function S(t,e,n,r,o){var u=x(t);if(!!u.valueAnimation&&u.prevValue!==u.value){var h=u.defaultInterpolatedText,f=(0,i.pD)(u.interpolatedValue,u.prevValue),p=u.value;t.percent=0,(null==u.prevValue?s.KZ:s.D)(t,{percent:1},r,e,null,function(r){var i=(0,a.pk)(n,u.precision,f,p,r);u.interpolatedValue=1===r?null:i,l(t,c({labelDataIndex:e,labelFetcher:o,defaultText:h?h(i):i+""},u.statesModels,i))})}}},30924:function(t,e,n){n.d(e,{Bk:function(){return d},G_:function(){return f},Ge:function(){return c},My:function(){return h},bK:function(){return p}});var r=n(807028),i=n(31674),o=n(254308),a=n(685043),s=n(901843);function u(t){return t.get("stack")||"__ec_stack_"+t.seriesIndex}function l(t){return t.dim+t.index}function c(t,e){var n=[];return e.eachSeriesByType(t,function(t){g(t)&&n.push(t)}),n}function h(t){var e=function(t){var e={};(0,r.S6)(t,function(t){var n=t.coordinateSystem.getBaseAxis();if("time"===n.type||"value"===n.type)for(var r=t.getData(),i=n.dim+"_"+n.index,o=r.getDimensionIndex(r.mapDimension(n.dim)),a=r.getStore(),s=0,u=a.count();s<u;++s){var l=a.get(o,s);e[i]?e[i].push(l):e[i]=[l]}});var n={};for(var i in e)if(e.hasOwnProperty(i)){var o=e[i];if(o){o.sort(function(t,e){return t-e});for(var a=null,s=1;s<o.length;++s){var u=o[s]-o[s-1];u>0&&(a=null===a?u:Math.min(a,u))}n[i]=a}}return n}(t),n=[];return(0,r.S6)(t,function(t){var r,o=t.coordinateSystem.getBaseAxis(),a=o.getExtent();if("category"===o.type)r=o.getBandWidth();else if("value"===o.type||"time"===o.type){var s=e[o.dim+"_"+o.index],c=Math.abs(a[1]-a[0]),h=o.scale.getExtent(),f=Math.abs(h[1]-h[0]);r=s?c/f*s:c}else{var p=t.getData();r=Math.abs(a[1]-a[0])/p.count()}var d=(0,i.GM)(t.get("barWidth"),r),g=(0,i.GM)(t.get("barMaxWidth"),r),y=(0,i.GM)(t.get("barMinWidth")||(v(t)?.5:1),r),m=t.get("barGap"),_=t.get("barCategoryGap");n.push({bandWidth:r,barWidth:d,barMaxWidth:g,barMinWidth:y,barGap:m,barCategoryGap:_,axisKey:l(o),stackId:u(t)})}),function(t){var e={};(0,r.S6)(t,function(t,n){var r=t.axisKey,i=t.bandWidth,o=e[r]||{bandWidth:i,remainedWidth:i,autoWidthCount:0,categoryGap:null,gap:"20%",stacks:{}},a=o.stacks;e[r]=o;var s=t.stackId;!a[s]&&o.autoWidthCount++,a[s]=a[s]||{width:0,maxWidth:0};var u=t.barWidth;u&&!a[s].width&&(a[s].width=u,u=Math.min(o.remainedWidth,u),o.remainedWidth-=u);var l=t.barMaxWidth;l&&(a[s].maxWidth=l);var c=t.barMinWidth;c&&(a[s].minWidth=c);var h=t.barGap;null!=h&&(o.gap=h);var f=t.barCategoryGap;null!=f&&(o.categoryGap=f)});var n={};return(0,r.S6)(e,function(t,e){n[e]={};var o,a=t.stacks,s=t.bandWidth,u=t.categoryGap;null==u&&(u=Math.max(35-4*(0,r.XP)(a).length,15)+"%");var l=(0,i.GM)(u,s),c=(0,i.GM)(t.gap,1),h=t.remainedWidth,f=t.autoWidthCount,p=(h-l)/(f+(f-1)*c);p=Math.max(p,0),(0,r.S6)(a,function(t){var e=t.maxWidth,n=t.minWidth;if(t.width){var r=t.width;e&&(r=Math.min(r,e)),n&&(r=Math.max(r,n)),t.width=r,h-=r+c*r,f--}else{var r=p;e&&e<r&&(r=Math.min(e,h)),n&&n>r&&(r=n),r!==p&&(t.width=r,h-=r+c*r,f--)}}),p=Math.max(p=(h-l)/(f+(f-1)*c),0);var d=0;(0,r.S6)(a,function(t,e){!t.width&&(t.width=p),o=t,d+=t.width*(1+c)}),o&&(d-=o.width*c);var g=-d/2;(0,r.S6)(a,function(t,r){n[e][r]=n[e][r]||{bandWidth:s,offset:g,width:t.width},g+=t.width*(1+c)})}),n}(n)}function f(t,e,n){if(t&&e){var r=t[l(e)];return null!=r&&null!=n?r[u(n)]:r}}function p(t,e){var n=c(t,e),i=h(n);(0,r.S6)(n,function(t){var e=t.getData(),n=t.coordinateSystem.getBaseAxis(),r=u(t),o=i[l(n)][r],a=o.offset,s=o.width;e.setLayout({bandWidth:o.bandWidth,offset:a,size:s})})}function d(t){return{seriesType:t,plan:(0,a.Z)(),reset:function(t){if(!!g(t)){var e=t.getData(),n=t.coordinateSystem,r=n.getBaseAxis(),i=n.getOtherAxis(r),a=e.getDimensionIndex(e.mapDimension(i.dim)),u=e.getDimensionIndex(e.mapDimension(r.dim)),l=t.get("showBackground",!0),c=e.mapDimension(i.dim),h=e.getCalculationInfo("stackResultDimension"),f=(0,o.M)(e,c)&&!!e.getCalculationInfo("stackedOnSeries"),p=i.isHorizontal(),d=function(t,e){var n=e.model.get("startValue");return!n&&(n=0),e.toGlobalCoord(e.dataToCoord("log"===e.type?n>0?n:1:n))}(r,i),y=v(t),m=t.get("barMinHeight")||0,_=h&&e.getDimensionIndex(h),x=e.getLayout("size"),w=e.getLayout("offset");return{progress:function(t,e){for(var r,i=t.count,o=y&&(0,s.o)(3*i),c=y&&l&&(0,s.o)(3*i),h=y&&(0,s.o)(i),g=n.master.getRect(),v=p?g.width:g.height,S=e.getStore(),b=0;null!=(r=t.next());){var M=S.get(f?_:a,r),T=S.get(u,r),k=d,D=void 0;f&&(D=+M-S.get(a,r));var C=void 0,I=void 0,A=void 0,P=void 0;if(p){var Z=n.dataToPoint([M,T]);if(f){var L=n.dataToPoint([D,T]);k=L[0]}C=k,I=Z[1]+w,A=Z[0]-k,P=x,Math.abs(A)<m&&(A=(A<0?-1:1)*m)}else{var Z=n.dataToPoint([T,M]);if(f){var L=n.dataToPoint([T,D]);k=L[1]}C=Z[0]+w,I=k,A=x,Math.abs(P=Z[1]-k)<m&&(P=(P<=0?-1:1)*m)}y?(o[b]=C,o[b+1]=I,o[b+2]=p?A:P,c&&(c[b]=p?g.x:C,c[b+1]=p?I:g.y,c[b+2]=v),h[r]=r):e.setItemLayout(r,{x:C,y:I,width:A,height:P}),b+=3}y&&e.setLayout({largePoints:o,largeDataIndices:h,largeBackgroundPoints:c,valueAxisHorizontal:p})}}}}}}function g(t){return t.coordinateSystem&&"cartesian2d"===t.coordinateSystem.type}function v(t){return t.pipelineContext&&t.pipelineContext.large}},983281:function(t,e,n){n.d(e,{s:function(){return a}});var r=n(807028),i=n(133141);function o(t,e,n,o,a){var s=t+e;!n.isSilent(s)&&o.eachComponent({mainType:"series",subType:"pie"},function(t){for(var e=t.seriesIndex,o=t.option.selectedMap,u=a.selected,l=0;l<u.length;l++)if(u[l].seriesIndex===e){var c=t.getData(),h=(0,i.gO)(c,a.fromActionPayload);n.trigger(s,{type:s,seriesId:t.id,name:(0,r.kJ)(h)?c.getName(h[0]):c.getName(h),selected:(0,r.HD)(o)?o:(0,r.l7)({},o)})}})}function a(t,e,n){t.on("selectchanged",function(t){var r=n.getModel();t.isFromClick?(o("map","selectchanged",e,r,t),o("pie","selectchanged",e,r,t)):"select"===t.fromAction?(o("map","selected",e,r,t),o("pie","selected",e,r,t)):"unselect"===t.fromAction&&(o("map","unselected",e,r,t),o("pie","unselected",e,r,t))})}},675247:function(t,e,n){n.d(e,{Z:function(){return l}});var r=n(807028),i=n(707498),o=n(406822),a=n(345262),s=n(671466),u=Math.PI;function l(t,e){e=e||{},r.ce(e,{text:"loading",textColor:"#000",fontSize:12,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif",maskColor:"rgba(255, 255, 255, 0.8)",showSpinner:!0,color:"#5470c6",spinnerRadius:10,lineWidth:5,zlevel:0});var n,l=new i.Z,c=new o.Z({style:{fill:e.maskColor},zlevel:e.zlevel,z:1e4});l.add(c);var h=new a.ZP({style:{text:e.text,fill:e.textColor,fontSize:e.fontSize,fontWeight:e.fontWeight,fontStyle:e.fontStyle,fontFamily:e.fontFamily},zlevel:e.zlevel,z:10001}),f=new o.Z({style:{fill:"none"},textContent:h,textConfig:{position:"right",distance:10},zlevel:e.zlevel,z:10001});return l.add(f),e.showSpinner&&((n=new s.Z({shape:{startAngle:-u/2,endAngle:-u/2+.1,r:e.spinnerRadius},style:{stroke:e.color,lineCap:"round",lineWidth:e.lineWidth},zlevel:e.zlevel,z:10001})).animateShape(!0).when(1e3,{endAngle:3*u/2}).start("circularInOut"),n.animateShape(!0).when(1e3,{startAngle:3*u/2}).delay(300).start("circularInOut"),l.add(n)),l.resize=function(){var r=h.getBoundingRect().width,i=e.showSpinner?e.spinnerRadius:0,o=(t.getWidth()-2*i-(e.showSpinner&&r?10:0)-r)/2-(e.showSpinner&&r?0:5+r/2)+(e.showSpinner?0:r/2)+(r?0:i),a=t.getHeight()/2;e.showSpinner&&n.setShape({cx:o,cy:a}),f.setShape({x:o-i,y:a-i,width:2*i,height:2*i}),c.setShape({x:0,y:0,width:t.getWidth(),height:t.getHeight()})},l.resize(),l}},882425:function(t,e,n){var r=n(518299),i=n(807028),o=n(954069),a=n(115856),s=n(731181),u=n(133141),l=n(918712),c=(0,u.Yf)(),h=function(t){var e;function n(e,n,r){var i=t.call(this,e,n,r)||this;return i.uid=a.Kr("ec_cpt_model"),i}return(0,r.ZT)(n,t),n.prototype.init=function(t,e,n){this.mergeDefaultAndTheme(t,n)},n.prototype.mergeDefaultAndTheme=function(t,e){var n=l.YD(this),r=n?l.tE(t):{},o=e.getTheme();i.TS(t,o.get(this.mainType)),i.TS(t,this.getDefaultOption()),n&&l.dt(t,r,n)},n.prototype.mergeOption=function(t,e){i.TS(this.option,t,!0);var n=l.YD(this);n&&l.dt(this.option,t,n)},n.prototype.optionUpdated=function(t,e){},n.prototype.getDefaultOption=function(){var t=this.constructor;if(!(0,s.PT)(t))return t.defaultOption;var e=c(this);if(!e.defaultOption){for(var n=[],r=t;r;){var o=r.prototype.defaultOption;o&&n.push(o),r=r.superClass}for(var a={},u=n.length-1;u>=0;u--)a=i.TS(a,n[u],!0);e.defaultOption=a}return e.defaultOption},n.prototype.getReferringComponents=function(t,e){return(0,u.HZ)(this.ecModel,t,{index:this.get(t+"Index",!0),id:this.get(t+"Id",!0)},e)},n.prototype.getBoxLayoutParams=function(){return{left:this.get("left"),top:this.get("top"),right:this.get("right"),bottom:this.get("bottom"),width:this.get("width"),height:this.get("height")}},n.prototype.getZLevelKey=function(){return""},n.prototype.setZLevel=function(t){this.option.zlevel=t},n.protoInitialize=void((e=n.prototype).type="component",e.id="",e.name="",e.mainType="",e.subType="",e.componentIndex=0),n}(o.Z);(0,s.pw)(h,o.Z),(0,s.au)(h),a.cj(h),a.jS(h,function(t){var e=[];return i.S6(h.getClassesByMainType(t),function(t){e=e.concat(t.dependencies||t.prototype.dependencies||[])}),e=i.UI(e,function(t){return(0,s.u9)(t).main}),"dataset"!==t&&0>=i.cq(e,"dataset")&&e.unshift("dataset"),e});e.Z=h},64698:function(t,e,n){var r,i,o,a=n(518299),s=n(807028),u=n(133141),l=n(954069),c=n(882425),h=n(787082),f=n(457714),p=n(839001),d=n(779780);n(383831);var g="\0_ec_inner",v=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return(0,a.ZT)(e,t),e.prototype.init=function(t,e,n,r,i,o){r=r||{},this.option=null,this._theme=new l.Z(r),this._locale=new l.Z(i),this._optionManager=o},e.prototype.setOption=function(t,e,n){var r=_(e);this._optionManager.setOption(t,n,r),this._resetOption(null,r)},e.prototype.resetOption=function(t,e){return this._resetOption(t,_(e))},e.prototype._resetOption=function(t,e){var n=!1,r=this._optionManager;if(!t||"recreate"===t){var i=r.mountOption("recreate"===t);this.option&&"recreate"!==t?(this.restoreData(),this._mergeOption(i,e)):o(this,i),n=!0}if(("timeline"===t||"media"===t)&&this.restoreData(),!t||"recreate"===t||"timeline"===t){var a=r.getTimelineOption(this);a&&(n=!0,this._mergeOption(a,e))}if(!t||"recreate"===t||"media"===t){var u=r.getMediaOption(this);u.length&&(0,s.S6)(u,function(t){n=!0,this._mergeOption(t,e)},this)}return n},e.prototype.mergeOption=function(t){this._mergeOption(t,null)},e.prototype._mergeOption=function(t,e){var n=this.option,i=this._componentsMap,o=this._componentsCount,a=[],l=(0,s.kW)(),h=e&&e.replaceMergeMainTypeMap;(0,f.md)(this),(0,s.S6)(t,function(t,e){if(null!=t)c.Z.hasClass(e)?e&&(a.push(e),l.set(e,!0)):n[e]=null==n[e]?(0,s.d9)(t):(0,s.TS)(n[e],t,!0)}),h&&h.each(function(t,e){c.Z.hasClass(e)&&!l.get(e)&&(a.push(e),l.set(e,!0))}),c.Z.topologicalTravel(a,c.Z.getAllClassMainTypes(),function(e){var a,l=(0,p.R)(this,e,u.kF(t[e])),f=i.get(e),d=f?h&&h.get(e)?"replaceMerge":"normalMerge":"replaceAll",g=u.ab(f,l,d);u.O0(g,e,c.Z),n[e]=null,i.set(e,null),o.set(e,0);var v=[],y=[],m=0;(0,s.S6)(g,function(t,n){var r=t.existing,i=t.newOption;if(i){var o="series"===e,u=c.Z.getClass(e,t.keyInfo.subType,!o);if(!u)return;if("tooltip"===e){if(a)return;a=!0}if(r&&r.constructor===u)r.name=t.keyInfo.name,r.mergeOption(i,this),r.optionUpdated(i,!1);else{var l=(0,s.l7)({componentIndex:n},t.keyInfo);r=new u(i,this,this,l),(0,s.l7)(r,l),t.brandNew&&(r.__requireNewView=!0),r.init(i,this,this),r.optionUpdated(null,!0)}}else r&&(r.mergeOption({},this),r.optionUpdated({},!1));r?(v.push(r.option),y.push(r),m++):(v.push(void 0),y.push(void 0))},this),n[e]=v,i.set(e,y),o.set(e,m),"series"===e&&r(this)},this);!this._seriesIndices&&r(this)},e.prototype.getOption=function(){var t=(0,s.d9)(this.option);return(0,s.S6)(t,function(e,n){if(c.Z.hasClass(n)){for(var r=u.kF(e),i=r.length,o=!1,a=i-1;a>=0;a--)r[a]&&!u.lY(r[a])?o=!0:(r[a]=null,!o&&i--);r.length=i,t[n]=r}}),delete t[g],t},e.prototype.getTheme=function(){return this._theme},e.prototype.getLocaleModel=function(){return this._locale},e.prototype.setUpdatePayload=function(t){this._payload=t},e.prototype.getUpdatePayload=function(){return this._payload},e.prototype.getComponent=function(t,e){var n=this._componentsMap.get(t);if(n){var r=n[e||0];if(r)return r;if(null==e){for(var i=0;i<n.length;i++)if(n[i])return n[i]}}},e.prototype.queryComponents=function(t){var e,n=t.mainType;if(!n)return[];var r=t.index,i=t.id,o=t.name,a=this._componentsMap.get(n);return a&&a.length?(null!=r?(e=[],(0,s.S6)(u.kF(r),function(t){a[t]&&e.push(a[t])})):e=null!=i?y("id",i,a):null!=o?y("name",o,a):(0,s.hX)(a,function(t){return!!t}),m(e,t)):[]},e.prototype.findComponents=function(t){var e=t.query,n=t.mainType,r=function(t){var e=n+"Index",r=n+"Id",i=n+"Name";return t&&(null!=t[e]||null!=t[r]||null!=t[i])?{mainType:n,index:t[e],id:t[r],name:t[i]}:null}(e);return function(e){return t.filter?(0,s.hX)(e,t.filter):e}(m(r?this.queryComponents(r):(0,s.hX)(this._componentsMap.get(n),function(t){return!!t}),t))},e.prototype.eachComponent=function(t,e,n){var r=this._componentsMap;if((0,s.mf)(t))r.each(function(n,r){for(var i=0;n&&i<n.length;i++){var o=n[i];o&&t.call(e,r,o,o.componentIndex)}});else{for(var i=(0,s.HD)(t)?r.get(t):(0,s.Kn)(t)?this.findComponents(t):null,o=0;i&&o<i.length;o++){var a=i[o];a&&e.call(n,a,a.componentIndex)}}},e.prototype.getSeriesByName=function(t){var e=u.U5(t,null);return(0,s.hX)(this._componentsMap.get("series"),function(t){return!!t&&null!=e&&t.name===e})},e.prototype.getSeriesByIndex=function(t){return this._componentsMap.get("series")[t]},e.prototype.getSeriesByType=function(t){return(0,s.hX)(this._componentsMap.get("series"),function(e){return!!e&&e.subType===t})},e.prototype.getSeries=function(){return(0,s.hX)(this._componentsMap.get("series"),function(t){return!!t})},e.prototype.getSeriesCount=function(){return this._componentsCount.get("series")},e.prototype.eachSeries=function(t,e){i(this),(0,s.S6)(this._seriesIndices,function(n){var r=this._componentsMap.get("series")[n];t.call(e,r,n)},this)},e.prototype.eachRawSeries=function(t,e){(0,s.S6)(this._componentsMap.get("series"),function(n){n&&t.call(e,n,n.componentIndex)})},e.prototype.eachSeriesByType=function(t,e,n){i(this),(0,s.S6)(this._seriesIndices,function(r){var i=this._componentsMap.get("series")[r];i.subType===t&&e.call(n,i,r)},this)},e.prototype.eachRawSeriesByType=function(t,e,n){return(0,s.S6)(this.getSeriesByType(t),e,n)},e.prototype.isSeriesFiltered=function(t){return i(this),null==this._seriesIndicesMap.get(t.componentIndex)},e.prototype.getCurrentSeriesIndices=function(){return(this._seriesIndices||[]).slice()},e.prototype.filterSeries=function(t,e){i(this);var n=[];(0,s.S6)(this._seriesIndices,function(r){var i=this._componentsMap.get("series")[r];t.call(e,i,r)&&n.push(r)},this),this._seriesIndices=n,this._seriesIndicesMap=(0,s.kW)(n)},e.prototype.restoreData=function(t){r(this);var e=this._componentsMap,n=[];e.each(function(t,e){c.Z.hasClass(e)&&n.push(e)}),c.Z.topologicalTravel(n,c.Z.getAllClassMainTypes(),function(n){(0,s.S6)(e.get(n),function(e){e&&("series"!==n||!function(t,e){if(e){var n=e.seriesIndex,r=e.seriesId,i=e.seriesName;return null!=n&&t.componentIndex!==n||null!=r&&t.id!==r||null!=i&&t.name!==i}}(e,t))&&e.restoreData()})})},e.internalField=void(r=function(t){var e=t._seriesIndices=[];(0,s.S6)(t._componentsMap.get("series"),function(t){t&&e.push(t.componentIndex)}),t._seriesIndicesMap=(0,s.kW)(e)},i=function(t){},o=function(t,e){t.option={},t.option[g]=1,t._componentsMap=(0,s.kW)({series:[]}),t._componentsCount=(0,s.kW)();var n=e.aria;(0,s.Kn)(n)&&null==n.enabled&&(n.enabled=!0),function(t,e){var n=t.color&&!t.colorLayer;(0,s.S6)(e,function(e,r){if("colorLayer"!==r||!n)!c.Z.hasClass(r)&&("object"==typeof e?t[r]=t[r]?(0,s.TS)(t[r],e,!1):(0,s.d9)(e):null==t[r]&&(t[r]=e))})}(e,t._theme.option),(0,s.TS)(e,h.Z,!1),t._mergeOption(e,null)}),e}(l.Z);function y(t,e,n){if((0,s.kJ)(e)){var r=(0,s.kW)();return(0,s.S6)(e,function(t){null!=t&&null!=u.U5(t,null)&&r.set(t,!0)}),(0,s.hX)(n,function(e){return e&&r.get(e[t])})}var i=u.U5(e,null);return(0,s.hX)(n,function(e){return e&&null!=i&&e[t]===i})}function m(t,e){return e.hasOwnProperty("subType")?(0,s.hX)(t,function(t){return t&&t.subType===e.subType}):t}function _(t){var e=(0,s.kW)();return t&&(0,s.S6)(u.kF(t.replaceMerge),function(t){e.set(t,!0)}),{replaceMergeMainTypeMap:e}}(0,s.jB)(v,d._),e.Z=v},954069:function(t,e,n){var r=n(939828),i=n(731181),o=n(328504),a=n(470650),s=n(969570),u=n(34312),l=n(807028),c=function(){function t(t,e,n){this.parentModel=e,this.ecModel=n,this.option=t}return t.prototype.init=function(t,e,n){for(var r=[],i=3;i<arguments.length;i++)r[i-3]=arguments[i]},t.prototype.mergeOption=function(t,e){(0,l.TS)(this.option,t,!0)},t.prototype.get=function(t,e){return null==t?this.option:this._doGet(this.parsePath(t),!e&&this.parentModel)},t.prototype.getShallow=function(t,e){var n=this.option,r=null==n?n:n[t];if(null==r&&!e){var i=this.parentModel;i&&(r=i.getShallow(t))}return r},t.prototype.getModel=function(e,n){var r=null!=e,i=r?this.parsePath(e):null,o=r?this._doGet(i):this.option;return new t(o,n=n||this.parentModel&&this.parentModel.getModel(this.resolveParentPath(i)),this.ecModel)},t.prototype.isEmpty=function(){return null==this.option},t.prototype.restoreData=function(){},t.prototype.clone=function(){return new this.constructor((0,l.d9)(this.option))},t.prototype.parsePath=function(t){return"string"==typeof t?t.split("."):t},t.prototype.resolveParentPath=function(t){return t},t.prototype.isAnimationEnabled=function(){if(!r.Z.node&&this.option){if(null!=this.option.animation)return!!this.option.animation;if(this.parentModel)return this.parentModel.isAnimationEnabled()}},t.prototype._doGet=function(t,e){var n=this.option;if(!t)return n;for(var r=0;r<t.length;r++){if(!!t[r]){if(null==(n=n&&"object"==typeof n?n[t[r]]:null))break}}return null==n&&e&&(n=e._doGet(this.resolveParentPath(t),e.parentModel)),n},t}();(0,i.dm)(c),(0,i.Qj)(c),(0,l.jB)(c,s.K),(0,l.jB)(c,u.D),(0,l.jB)(c,o.i),(0,l.jB)(c,a.Z),e.Z=c},694212:function(t,e,n){var r=n(133141),i=n(807028),o=/^(min|max)?(.+)$/,a=function(){function t(t){this._timelineOptions=[],this._mediaList=[],this._currentMediaIndices=[],this._api=t}return t.prototype.setOption=function(t,e,n){t&&((0,i.S6)((0,r.kF)(t.series),function(t){t&&t.data&&(0,i.fU)(t.data)&&(0,i.s7)(t.data)}),(0,i.S6)((0,r.kF)(t.dataset),function(t){t&&t.source&&(0,i.fU)(t.source)&&(0,i.s7)(t.source)})),t=(0,i.d9)(t);var o=this._optionBackup,a=function(t,e,n){var r,o,a=[],s=t.baseOption,u=t.timeline,l=t.options,c=t.media,h=!!t.media,f=!!(l||u||s&&s.timeline);function p(t){(0,i.S6)(e,function(e){e(t,n)})}return s?!(o=s).timeline&&(o.timeline=u):((f||h)&&(t.options=t.media=null),o=t),h&&(0,i.kJ)(c)&&(0,i.S6)(c,function(t){t&&t.option&&(t.query?a.push(t):!r&&(r=t))}),p(o),(0,i.S6)(l,function(t){return p(t)}),(0,i.S6)(a,function(t){return p(t.option)}),{baseOption:o,timelineOptions:l||[],mediaDefault:r,mediaList:a}}(t,e,!o);this._newBaseOption=a.baseOption,o?(a.timelineOptions.length&&(o.timelineOptions=a.timelineOptions),a.mediaList.length&&(o.mediaList=a.mediaList),a.mediaDefault&&(o.mediaDefault=a.mediaDefault)):this._optionBackup=a},t.prototype.mountOption=function(t){var e=this._optionBackup;return this._timelineOptions=e.timelineOptions,this._mediaList=e.mediaList,this._mediaDefault=e.mediaDefault,this._currentMediaIndices=[],(0,i.d9)(t?e.baseOption:this._newBaseOption)},t.prototype.getTimelineOption=function(t){var e,n=this._timelineOptions;if(n.length){var r=t.getComponent("timeline");r&&(e=(0,i.d9)(n[r.getCurrentIndex()]))}return e},t.prototype.getMediaOption=function(t){var e=this._api.getWidth(),n=this._api.getHeight(),r=this._mediaList,a=this._mediaDefault,s=[],u=[];if(!r.length&&!a)return u;for(var l=0,c=r.length;l<c;l++)(function(t,e,n){var r={width:e,height:n,aspectratio:e/n},a=!0;return(0,i.S6)(t,function(t,e){var n=e.match(o);if(!!n&&!!n[1]&&!!n[2]){var i=n[1];!function(t,e,n){return"min"===n?t>=e:"max"===n?t<=e:t===e}(r[n[2].toLowerCase()],t,i)&&(a=!1)}}),a})(r[l].query,e,n)&&s.push(l);return!s.length&&a&&(s=[-1]),s.length&&!function(t,e){return t.join(",")===e.join(",")}(s,this._currentMediaIndices)&&(u=(0,i.UI)(s,function(t){return(0,i.d9)(-1===t?a.option:r[t].option)})),this._currentMediaIndices=s,u},t}();e.Z=a},970597:function(t,e,n){var r=n(518299),i=n(807028),o=n(939828),a=n(133141),s=n(882425),u=n(779780),l=n(541537),c=n(918712),h=n(648446),f=n(731181),p=n(540644),d=n(602978),g=a.Yf();function v(t,e){return t.getName(e)||t.getId(e)}var y=function(t){var e;function n(){var e=null!==t&&t.apply(this,arguments)||this;return e._selectedDataIndicesMap={},e}return(0,r.ZT)(n,t),n.prototype.init=function(t,e,n){this.seriesIndex=this.componentIndex,this.dataTask=(0,h.v)({count:_,reset:x}),this.dataTask.context={model:this},this.mergeDefaultAndTheme(t,n),(g(this).sourceManager=new p.U(this)).prepareSource();var r=this.getInitialData(t,n);S(r,this),this.dataTask.context.data=r;g(this).dataBeforeProcessed=r,m(this),this._initSelectedMapFromData(r)},n.prototype.mergeDefaultAndTheme=function(t,e){var n=(0,c.YD)(this),r=n?(0,c.tE)(t):{},o=this.subType;s.Z.hasClass(o)&&(o+="Series"),i.TS(t,e.getTheme().get(this.subType)),i.TS(t,this.getDefaultOption()),a.Cc(t,"label",["show"]),this.fillDataTextStyle(t.data),n&&(0,c.dt)(t,r,n)},n.prototype.mergeOption=function(t,e){t=i.TS(this.option,t,!0),this.fillDataTextStyle(t.data);var n=(0,c.YD)(this);n&&(0,c.dt)(this.option,t,n);var r=g(this).sourceManager;r.dirty(),r.prepareSource();var o=this.getInitialData(t,e);S(o,this),this.dataTask.dirty(),this.dataTask.context.data=o,g(this).dataBeforeProcessed=o,m(this),this._initSelectedMapFromData(o)},n.prototype.fillDataTextStyle=function(t){if(t&&!i.fU(t)){for(var e=["show"],n=0;n<t.length;n++)t[n]&&t[n].label&&a.Cc(t[n],"label",e)}},n.prototype.getInitialData=function(t,e){},n.prototype.appendData=function(t){this.getRawData().appendData(t.data)},n.prototype.getData=function(t){var e=M(this);if(!e)return g(this).data;var n=e.context.data;return null!=t&&n.getLinkedData?n.getLinkedData(t):n},n.prototype.getAllData=function(){var t=this.getData();return t&&t.getLinkedDataAll?t.getLinkedDataAll():[{data:t}]},n.prototype.setData=function(t){var e=M(this);if(e){var n=e.context;n.outputData=t,e!==this.dataTask&&(n.data=t)}g(this).data=t},n.prototype.getEncode=function(){var t=this.get("encode",!0);if(t)return i.kW(t)},n.prototype.getSourceManager=function(){return g(this).sourceManager},n.prototype.getSource=function(){return this.getSourceManager().getSource()},n.prototype.getRawData=function(){return g(this).dataBeforeProcessed},n.prototype.getColorBy=function(){return this.get("colorBy")||"series"},n.prototype.isColorBySeries=function(){return"series"===this.getColorBy()},n.prototype.getBaseAxis=function(){var t=this.coordinateSystem;return t&&t.getBaseAxis&&t.getBaseAxis()},n.prototype.formatTooltip=function(t,e,n){return(0,d.w)({series:this,dataIndex:t,multipleSeries:e})},n.prototype.isAnimationEnabled=function(){var t=this.ecModel;if(o.Z.node&&!(t&&t.ssr))return!1;var e=this.getShallow("animation");return e&&this.getData().count()>this.getShallow("animationThreshold")&&(e=!1),!!e},n.prototype.restoreData=function(){this.dataTask.dirty()},n.prototype.getColorFromPalette=function(t,e,n){var r=this.ecModel,i=u._.prototype.getColorFromPalette.call(this,t,e,n);return!i&&(i=r.getColorFromPalette(t,e,n)),i},n.prototype.coordDimToDataDim=function(t){return this.getRawData().mapDimensionsAll(t)},n.prototype.getProgressive=function(){return this.get("progressive")},n.prototype.getProgressiveThreshold=function(){return this.get("progressiveThreshold")},n.prototype.select=function(t,e){this._innerSelect(this.getData(e),t)},n.prototype.unselect=function(t,e){var n=this.option.selectedMap;if(!!n){var r=this.option.selectedMode,i=this.getData(e);if("series"===r||"all"===n){this.option.selectedMap={},this._selectedDataIndicesMap={};return}for(var o=0;o<t.length;o++){var a=v(i,t[o]);n[a]=!1,this._selectedDataIndicesMap[a]=-1}}},n.prototype.toggleSelect=function(t,e){for(var n=[],r=0;r<t.length;r++)n[0]=t[r],this.isSelected(t[r],e)?this.unselect(n,e):this.select(n,e)},n.prototype.getSelectedDataIndices=function(){if("all"===this.option.selectedMap)return[].slice.call(this.getData().getIndices());for(var t=this._selectedDataIndicesMap,e=i.XP(t),n=[],r=0;r<e.length;r++){var o=t[e[r]];o>=0&&n.push(o)}return n},n.prototype.isSelected=function(t,e){var n=this.option.selectedMap;if(!n)return!1;var r=this.getData(e);return("all"===n||n[v(r,t)])&&!r.getItemModel(t).get(["select","disabled"])},n.prototype.isUniversalTransitionEnabled=function(){if(this.__universalTransitionEnabled)return!0;var t=this.option.universalTransition;return!!t&&(!0===t||t&&t.enabled)},n.prototype._innerSelect=function(t,e){var n,r,o=this.option,a=o.selectedMode,s=e.length;if(!!a&&!!s){if("series"===a)o.selectedMap="all";else if("multiple"===a){!i.Kn(o.selectedMap)&&(o.selectedMap={});for(var u=o.selectedMap,l=0;l<s;l++){var c=e[l],h=v(t,c);u[h]=!0,this._selectedDataIndicesMap[h]=t.getRawIndex(c)}}else if("single"===a||!0===a){var f=e[s-1],h=v(t,f);o.selectedMap=((n={})[h]=!0,n),this._selectedDataIndicesMap=((r={})[h]=t.getRawIndex(f),r)}}},n.prototype._initSelectedMapFromData=function(t){if(!this.option.selectedMap){var e=[];t.hasItemOption&&t.each(function(n){var r=t.getRawDataItem(n);r&&r.selected&&e.push(n)}),e.length>0&&this._innerSelect(t,e)}},n.registerClass=function(t){return s.Z.registerClass(t)},n.protoInitialize=void((e=n.prototype).type="series.__base__",e.seriesIndex=0,e.ignoreStyleOnData=!1,e.hasSymbolVisual=!1,e.defaultSymbol="circle",e.visualStyleAccessPath="itemStyle",e.visualDrawType="fill"),n}(s.Z);function m(t){var e=t.name;!a.yu(t)&&(t.name=function(t){var e=t.getRawData(),n=e.mapDimensionsAll("seriesName"),r=[];return i.S6(n,function(t){var n=e.getDimensionInfo(t);n.displayName&&r.push(n.displayName)}),r.join(" ")}(t)||e)}i.jB(y,l.X),i.jB(y,u._),(0,f.pw)(y,s.Z);function _(t){return t.model.getRawData().count()}function x(t){var e=t.model;return e.setData(e.getRawData().cloneShallow()),w}function w(t,e){e.outputData&&t.end>e.outputData.count()&&e.model.getRawData().cloneShallow(e.outputData)}function S(t,e){i.S6(i.WW(t.CHANGABLE_METHODS,t.DOWNSAMPLE_METHODS),function(n){t.wrapMethod(n,i.WA(b,e))})}function b(t,e){var n=M(t);return n&&n.setOutputEnd((e||this).count()),e}function M(t){var e=(t.ecModel||{}).scheduler,n=e&&e.getPipeline(t.uid);if(n){var r=n.currentTask;if(r){var i=r.agentStubMap;i&&(r=i.get(t.uid))}return r}}e.Z=y},787082:function(t,e){var n="";"undefined"!=typeof navigator&&(n=navigator.platform||"");var r="rgba(0, 0, 0, 0.2)";e.Z={darkMode:"auto",colorBy:"series",color:["#5470c6","#91cc75","#fac858","#ee6666","#73c0de","#3ba272","#fc8452","#9a60b4","#ea7ccc"],gradientColor:["#f6efa6","#d88273","#bf444c"],aria:{decal:{decals:[{color:r,dashArrayX:[1,0],dashArrayY:[2,5],symbolSize:1,rotation:Math.PI/6},{color:r,symbol:"circle",dashArrayX:[[8,8],[0,8,8,0]],dashArrayY:[6,0],symbolSize:.8},{color:r,dashArrayX:[1,0],dashArrayY:[4,3],rotation:-Math.PI/4},{color:r,dashArrayX:[[6,6],[0,6,6,0]],dashArrayY:[6,0]},{color:r,dashArrayX:[[1,0],[1,6]],dashArrayY:[1,0,6,0],rotation:Math.PI/4},{color:r,symbol:"triangle",dashArrayX:[[9,9],[0,9,9,0]],dashArrayY:[7,2],symbolSize:.75}]}},textStyle:{fontFamily:n.match(/^Win/)?"Microsoft YaHei":"sans-serif",fontSize:12,fontStyle:"normal",fontWeight:"normal"},blendMode:null,stateAnimation:{duration:300,easing:"cubicOut"},animation:"auto",animationDuration:1e3,animationDurationUpdate:500,animationEasing:"cubicInOut",animationEasingUpdate:"cubicInOut",animationThreshold:2e3,progressiveThreshold:3e3,progressive:400,hoverLayerThreshold:3e3,useUTC:!1}},839001:function(t,e,n){n.d(e,{R:function(){return i}});var r=(0,n(807028).kW)();function i(t,e,n){var i=r.get(e);if(!i)return n;var o=i(t);return o?n.concat(o):n}},328504:function(t,e,n){n.d(e,{i:function(){return o}});var r=n(751827),i=(0,r.Z)([["fill","color"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["opacity"],["shadowColor"]]),o=function(){function t(){}return t.prototype.getAreaStyle=function(t,e){return i(this,t,e)},t}()},541537:function(t,e,n){n.d(e,{X:function(){return s}});var r=n(807028),i=n(279681),o=n(84164),a=/\{@(.+?)\}/g,s=function(){function t(){}return t.prototype.getDataParams=function(t,e){var n=this.getData(e),r=this.getRawValue(t,e),i=n.getRawIndex(t),o=n.getName(t),a=n.getRawDataItem(t),s=n.getItemVisual(t,"style"),u=s&&s[n.getItemVisual(t,"drawType")||"fill"],l=s&&s.stroke,c=this.mainType,h="series"===c,f=n.userOutput&&n.userOutput.get();return{componentType:c,componentSubType:this.subType,componentIndex:this.componentIndex,seriesType:h?this.subType:null,seriesIndex:this.seriesIndex,seriesId:h?this.id:null,seriesName:h?this.name:null,name:o,dataIndex:i,data:a,dataType:e,value:r,color:u,borderColor:l,dimensionNames:f?f.fullDimensions:null,encode:f?f.encode:null,$vars:["seriesName","name","value"]}},t.prototype.getFormattedLabel=function(t,e,n,s,u,l){e=e||"normal";var c=this.getData(n),h=this.getDataParams(t,n);return(l&&(h.value=l.interpolatedValue),null!=s&&r.kJ(h.value)&&(h.value=h.value[s]),!u&&(u=c.getItemModel(t).get("normal"===e?["label","formatter"]:[e,"label","formatter"])),r.mf(u))?(h.status=e,h.dimensionIndex=s,u(h)):r.HD(u)?(0,o.kF)(u,h).replace(a,function(e,n){var o=n.length,a=n;"["===a.charAt(0)&&"]"===a.charAt(o-1)&&(a=+a.slice(1,o-1));var s=(0,i.hk)(c,t,a);if(l&&r.kJ(l.interpolatedValue)){var u=c.getDimensionIndex(a);u>=0&&(s=l.interpolatedValue[u])}return null!=s?s+"":""}):void 0},t.prototype.getRawValue=function(t,e){return(0,i.hk)(this.getData(e),t)},t.prototype.formatTooltip=function(t,e,n){},t}()},34312:function(t,e,n){n.d(e,{D:function(){return a},t:function(){return i}});var r=n(751827),i=[["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","borderType"],["lineDashOffset","borderDashOffset"],["lineCap","borderCap"],["lineJoin","borderJoin"],["miterLimit","borderMiterLimit"]],o=(0,r.Z)(i),a=function(){function t(){}return t.prototype.getItemStyle=function(t,e){return o(this,t,e)},t}()},969570:function(t,e,n){n.d(e,{K:function(){return a},v:function(){return i}});var r=n(751827),i=[["lineWidth","width"],["stroke","color"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","type"],["lineDashOffset","dashOffset"],["lineCap","cap"],["lineJoin","join"],["miterLimit"]],o=(0,r.Z)(i),a=function(){function t(){}return t.prototype.getLineStyle=function(t){return o(this,t)},t}()},751827:function(t,e,n){n.d(e,{Z:function(){return i}});var r=n(807028);function i(t,e){for(var n=0;n<t.length;n++)!t[n][1]&&(t[n][1]=t[n][0]);return e=e||!1,function(n,i,o){for(var a={},s=0;s<t.length;s++){var u=t[s][1];if(!(i&&r.cq(i,u)>=0||o&&0>r.cq(o,u))){var l=n.getShallow(u,e);null!=l&&(a[t[s][0]]=l)}}return a}}},779780:function(t,e,n){n.d(e,{_:function(){return o}});var r=n(133141),i=(0,r.Yf)();(0,r.Yf)();var o=function(){function t(){}return t.prototype.getColorFromPalette=function(t,e,n){return function(t,e,n,r,i,o,a){var s=e(o=o||t),u=s.paletteIdx||0,l=s.paletteNameMap=s.paletteNameMap||{};if(l.hasOwnProperty(i))return l[i];var c=null!=a&&r?function(t,e){for(var n=t.length,r=0;r<n;r++)if(t[r].length>e)return t[r];return t[n-1]}(r,a):n;if(!!(c=c||n)&&!!c.length){var h=c[u];return i&&(l[i]=h),s.paletteIdx=(u+1)%c.length,h}}(this,i,(0,r.kF)(this.get("color",!0)),this.get("colorLayer",!0),t,e,n)},t.prototype.clearColorPalette=function(){(function(t,e){e(t).paletteIdx=0,e(t).paletteNameMap={}})(this,i)},t}()},470650:function(t,e,n){var r=n(931918),i=n(345262),o=["textStyle","color"],a=["fontStyle","fontWeight","fontSize","fontFamily","padding","lineHeight","rich","width","height","overflow"],s=new i.ZP,u=function(){function t(){}return t.prototype.getTextColor=function(t){var e=this.ecModel;return this.getShallow("color")||(!t&&e?e.get(o):null)},t.prototype.getFont=function(){return(0,r.qT)({fontStyle:this.getShallow("fontStyle"),fontWeight:this.getShallow("fontWeight"),fontSize:this.getShallow("fontSize"),fontFamily:this.getShallow("fontFamily")},this.ecModel)},t.prototype.getTextRect=function(t){for(var e={text:t,verticalAlign:this.getShallow("verticalAlign")||this.getShallow("baseline")},n=0;n<a.length;n++)e[a[n]]=this.getShallow(a[n]);return s.useStyle(e),s.update(),s.getBoundingRect()},t}();e.Z=u},386953:function(t,e,n){n.d(e,{b:function(){return a}});var r=n(807028),i=n(133141),o=function(t){this.coordSysDims=[],this.axisMap=(0,r.kW)(),this.categoryAxisMap=(0,r.kW)(),this.coordSysName=t};function a(t){var e=t.get("coordinateSystem"),n=new o(e),r=s[e];if(r)return r(t,n,n.axisMap,n.categoryAxisMap),n}var s={cartesian2d:function(t,e,n,r){var o=t.getReferringComponents("xAxis",i.C6).models[0],a=t.getReferringComponents("yAxis",i.C6).models[0];e.coordSysDims=["x","y"],n.set("x",o),n.set("y",a),u(o)&&(r.set("x",o),e.firstCategoryDimIndex=0),u(a)&&(r.set("y",a),null==e.firstCategoryDimIndex&&(e.firstCategoryDimIndex=1))},singleAxis:function(t,e,n,r){var o=t.getReferringComponents("singleAxis",i.C6).models[0];e.coordSysDims=["single"],n.set("single",o),u(o)&&(r.set("single",o),e.firstCategoryDimIndex=0)},polar:function(t,e,n,r){var o=t.getReferringComponents("polar",i.C6).models[0],a=o.findAxisModel("radiusAxis"),s=o.findAxisModel("angleAxis");e.coordSysDims=["radius","angle"],n.set("radius",a),n.set("angle",s),u(a)&&(r.set("radius",a),e.firstCategoryDimIndex=0),u(s)&&(r.set("angle",s),null==e.firstCategoryDimIndex&&(e.firstCategoryDimIndex=1))},geo:function(t,e,n,r){e.coordSysDims=["lng","lat"]},parallel:function(t,e,n,i){var o=t.ecModel,a=o.getComponent("parallel",t.get("parallelIndex")),s=e.coordSysDims=a.dimensions.slice();(0,r.S6)(a.parallelAxisIndex,function(t,r){var a=o.getComponent("parallelAxis",t),l=s[r];n.set(l,a),u(a)&&(i.set(l,a),null==e.firstCategoryDimIndex&&(e.firstCategoryDimIndex=r))})}};function u(t){return"category"===t.get("type")}},870740:function(t,e,n){n.d(e,{Z:function(){return p}});var r=n(807028),i=n(884466),o=n(133141);function a(t){t&&(0,r.S6)(s,function(e){e[0]in t&&!(e[1]in t)&&(t[e[1]]=t[e[0]])})}var s=[["x","left"],["y","top"],["x2","right"],["y2","bottom"]],u=["grid","geo","parallel","legend","toolbox","title","visualMap","dataZoom","timeline"],l=[["borderRadius","barBorderRadius"],["borderColor","barBorderColor"],["borderWidth","barBorderWidth"]];function c(t){var e=t&&t.itemStyle;if(e)for(var n=0;n<l.length;n++){var r=l[n][1],i=l[n][0];null!=e[r]&&(e[i]=e[r])}}function h(t){if(!!t)"edge"===t.alignTo&&null!=t.margin&&null==t.edgeDistance&&(t.edgeDistance=t.margin)}function f(t){if(!!t)t.downplay&&!t.blur&&(t.blur=t.downplay)}function p(t,e){(0,i.Z)(t,e),t.series=(0,o.kF)(t.series),(0,r.S6)(t.series,function(t){if(!!(0,r.Kn)(t)){var e=t.type;if("line"===e)null!=t.clipOverflow&&(t.clip=t.clipOverflow);else if("pie"===e||"gauge"===e){null!=t.clockWise&&(t.clockwise=t.clockWise),h(t.label);var n=t.data;if(n&&!(0,r.fU)(n))for(var i=0;i<n.length;i++)h(n[i]);null!=t.hoverOffset&&(t.emphasis=t.emphasis||{},t.emphasis.scaleSize=null)}else if("gauge"===e){var o=function(t,e){for(var n=e.split(","),r=t,i=0;i<n.length&&null!=(r=r&&r[n[i]]);i++);return r}(t,"pointer.color");null!=o&&function(t,e,n,r){for(var i,o=e.split(","),a=t,s=0;s<o.length-1;s++)null==a[i=o[s]]&&(a[i]={}),a=a[i];null==a[o[s]]&&(a[o[s]]=n)}(t,"itemStyle.color",o)}else if("bar"===e){c(t),c(t.backgroundStyle),c(t.emphasis);var n=t.data;if(n&&!(0,r.fU)(n))for(var i=0;i<n.length;i++)"object"==typeof n[i]&&(c(n[i]),c(n[i]&&n[i].emphasis))}else if("sunburst"===e){var s=t.highlightPolicy;s&&(t.emphasis=t.emphasis||{},!t.emphasis.focus&&(t.emphasis.focus=s)),f(t),!function t(e,n){if(e)for(var r=0;r<e.length;r++)n(e[r]),e[r]&&t(e[r].children,n)}(t.data,f)}else"graph"===e||"sankey"===e?!function(t){if(!!t)null!=t.focusNodeAdjacency&&(t.emphasis=t.emphasis||{},null==t.emphasis.focus&&(t.emphasis.focus="adjacency"))}(t):"map"===e&&(t.mapType&&!t.map&&(t.map=t.mapType),t.mapLocation&&(0,r.ce)(t,t.mapLocation));null!=t.hoverAnimation&&(t.emphasis=t.emphasis||{},t.emphasis&&null==t.emphasis.scale&&(t.emphasis.scale=t.hoverAnimation)),a(t)}}),t.dataRange&&(t.visualMap=t.dataRange),(0,r.S6)(u,function(e){var n=t[e];n&&(!(0,r.kJ)(n)&&(n=[n]),(0,r.S6)(n,function(t){a(t)}))})}},884466:function(t,e,n){n.d(e,{Z:function(){return g}});var r=n(807028),i=n(133141),o=r.S6,a=r.Kn,s=["areaStyle","lineStyle","nodeStyle","linkStyle","chordStyle","label","labelLine"];function u(t){var e=t&&t.itemStyle;if(!!e)for(var n=0,i=s.length;n<i;n++){var o=s[n],a=e.normal,u=e.emphasis;a&&a[o]&&(t[o]=t[o]||{},t[o].normal?r.TS(t[o].normal,a[o]):t[o].normal=a[o],a[o]=null),u&&u[o]&&(t[o]=t[o]||{},t[o].emphasis?r.TS(t[o].emphasis,u[o]):t[o].emphasis=u[o],u[o]=null)}}function l(t,e,n){if(t&&t[e]&&(t[e].normal||t[e].emphasis)){var i=t[e].normal,o=t[e].emphasis;i&&(n?(t[e].normal=t[e].emphasis=null,r.ce(t[e],i)):t[e]=i),o&&(t.emphasis=t.emphasis||{},t.emphasis[e]=o,o.focus&&(t.emphasis.focus=o.focus),o.blurScope&&(t.emphasis.blurScope=o.blurScope))}}function c(t){l(t,"itemStyle"),l(t,"lineStyle"),l(t,"areaStyle"),l(t,"label"),l(t,"labelLine"),l(t,"upperLabel"),l(t,"edgeLabel")}function h(t,e){var n=a(t)&&t[e],r=a(n)&&n.textStyle;if(r)for(var o=0,s=i.Td.length;o<s;o++){var u=i.Td[o];r.hasOwnProperty(u)&&(n[u]=r[u])}}function f(t){t&&(c(t),h(t,"label"),t.emphasis&&h(t.emphasis,"label"))}function p(t){return r.kJ(t)?t:t?[t]:[]}function d(t){return(r.kJ(t)?t[0]:t)||{}}function g(t,e){o(p(t.series),function(t){a(t)&&function(t){if(!!a(t)){u(t),c(t),h(t,"label"),h(t,"upperLabel"),h(t,"edgeLabel"),t.emphasis&&(h(t.emphasis,"label"),h(t.emphasis,"upperLabel"),h(t.emphasis,"edgeLabel"));var e=t.markPoint;e&&(u(e),f(e));var n=t.markLine;n&&(u(n),f(n));var i=t.markArea;i&&f(i);var o=t.data;if("graph"===t.type){o=o||t.nodes;var s=t.links||t.edges;if(s&&!r.fU(s))for(var p=0;p<s.length;p++)f(s[p]);r.S6(t.categories,function(t){c(t)})}if(o&&!r.fU(o))for(var p=0;p<o.length;p++)f(o[p]);if((e=t.markPoint)&&e.data){for(var d=e.data,p=0;p<d.length;p++)f(d[p])}if((n=t.markLine)&&n.data){for(var g=n.data,p=0;p<g.length;p++)r.kJ(g[p])?(f(g[p][0]),f(g[p][1])):f(g[p])}"gauge"===t.type?(h(t,"axisLabel"),h(t,"title"),h(t,"detail")):"treemap"===t.type?(l(t.breadcrumb,"itemStyle"),r.S6(t.levels,function(t){c(t)})):"tree"===t.type&&c(t.leaves)}}(t)});var n=["xAxis","yAxis","radiusAxis","angleAxis","singleAxis","parallelAxis","radar"];e&&n.push("valueAxis","categoryAxis","logAxis","timeAxis"),o(n,function(e){o(p(t[e]),function(t){t&&(h(t,"axisLabel"),h(t.axisPointer,"label"))})}),o(p(t.parallel),function(t){var e=t&&t.parallelAxisDefault;h(e,"axisLabel"),h(e&&e.axisPointer,"label")}),o(p(t.calendar),function(t){l(t,"itemStyle"),h(t,"dayLabel"),h(t,"monthLabel"),h(t,"yearLabel")}),o(p(t.radar),function(t){h(t,"name"),t.name&&null==t.axisName&&(t.axisName=t.name,delete t.name),null!=t.nameGap&&null==t.axisNameGap&&(t.axisNameGap=t.nameGap,delete t.nameGap)}),o(p(t.geo),function(t){a(t)&&(f(t),o(p(t.regions),function(t){f(t)}))}),o(p(t.timeline),function(t){f(t),l(t,"label"),l(t,"itemStyle"),l(t,"controlStyle",!0);var e=t.data;r.kJ(e)&&r.S6(e,function(t){r.Kn(t)&&(l(t,"label"),l(t,"itemStyle"))})}),o(p(t.toolbox),function(t){l(t,"iconStyle"),o(t.feature,function(t){l(t,"iconStyle")})}),h(d(t.axisPointer),"label"),h(d(t.tooltip).axisPointer,"label")}},951092:function(t,e,n){n.d(e,{Z:function(){return a}});var r=n(807028),i={average:function(t){for(var e=0,n=0,r=0;r<t.length;r++)!isNaN(t[r])&&(e+=t[r],n++);return 0===n?NaN:e/n},sum:function(t){for(var e=0,n=0;n<t.length;n++)e+=t[n]||0;return e},max:function(t){for(var e=-1/0,n=0;n<t.length;n++)t[n]>e&&(e=t[n]);return isFinite(e)?e:NaN},min:function(t){for(var e=1/0,n=0;n<t.length;n++)t[n]<e&&(e=t[n]);return isFinite(e)?e:NaN},minmax:function(t){for(var e=-1/0,n=-1/0,r=0;r<t.length;r++){var i=t[r],o=Math.abs(i);o>e&&(e=o,n=i)}return isFinite(n)?n:NaN},nearest:function(t){return t[0]}},o=function(t){return Math.round(t.length/2)};function a(t){return{seriesType:t,reset:function(t,e,n){var a=t.getData(),s=t.get("sampling"),u=t.coordinateSystem,l=a.count();if(l>10&&"cartesian2d"===u.type&&s){var c=u.getBaseAxis(),h=u.getOtherAxis(c),f=c.getExtent(),p=n.getDevicePixelRatio(),d=Math.round(l/(Math.abs(f[1]-f[0])*(p||1)));if(isFinite(d)&&d>1){"lttb"===s&&t.setData(a.lttbDownSample(a.mapDimension(h.dim),1/d));var g=void 0;(0,r.HD)(s)?g=i[s]:(0,r.mf)(s)&&(g=s),g&&t.setData(a.downSample(a.mapDimension(h.dim),1/d,g,o))}}}}}},381979:function(t,e,n){n.d(e,{Z:function(){return o}});var r=n(807028),i=n(31674);function o(t){var e=(0,r.kW)();t.eachSeries(function(t){var n=t.get("stack");if(n){var r=e.get(n)||e.set(n,[]),i=t.getData(),o={stackResultDimension:i.getCalculationInfo("stackResultDimension"),stackedOverDimension:i.getCalculationInfo("stackedOverDimension"),stackedDimension:i.getCalculationInfo("stackedDimension"),stackedByDimension:i.getCalculationInfo("stackedByDimension"),isStackedByIndex:i.getCalculationInfo("isStackedByIndex"),data:i,seriesModel:t};if(!o.stackedDimension||!(o.isStackedByIndex||o.stackedByDimension))return;r.length&&i.setCalculationInfo("stackedOnSeries",r[r.length-1].seriesModel),r.push(o)}}),e.each(a)}function a(t){(0,r.S6)(t,function(e,n){var r=[],o=[NaN,NaN],a=[e.stackResultDimension,e.stackedOverDimension],s=e.data,u=e.isStackedByIndex,l=e.seriesModel.get("stackStrategy")||"samesign";s.modify(a,function(a,c,h){var f,p,d=s.get(e.stackedDimension,h);if(isNaN(d))return o;u?p=s.getRawIndex(h):f=s.get(e.stackedByDimension,h);for(var g=NaN,v=n-1;v>=0;v--){var y=t[v];if(!u&&(p=y.data.rawIndexOf(y.stackedByDimension,f)),p>=0){var m=y.data.getByRawIndex(y.stackResultDimension,p);if("all"===l||"positive"===l&&m>0||"negative"===l&&m<0||"samesign"===l&&d>=0&&m>0||"samesign"===l&&d<=0&&m<0){d=(0,i.S$)(d,m),g=m;break}}}return r[0]=d,r[1]=g,r})})}},177813:function(t,e,n){var r=n(518299),i=n(31674),o=n(84164),a=n(275372),s=n(981740),u=i.NM,l=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="interval",e._interval=0,e._intervalPrecision=2,e}return(0,r.ZT)(e,t),e.prototype.parse=function(t){return t},e.prototype.contain=function(t){return s.XS(t,this._extent)},e.prototype.normalize=function(t){return s.Fv(t,this._extent)},e.prototype.scale=function(t){return s.bA(t,this._extent)},e.prototype.setExtent=function(t,e){var n=this._extent;!isNaN(t)&&(n[0]=parseFloat(t)),!isNaN(e)&&(n[1]=parseFloat(e))},e.prototype.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1]),this.setExtent(e[0],e[1])},e.prototype.getInterval=function(){return this._interval},e.prototype.setInterval=function(t){this._interval=t,this._niceExtent=this._extent.slice(),this._intervalPrecision=s.lb(t)},e.prototype.getTicks=function(t){var e=this._interval,n=this._extent,r=this._niceExtent,i=this._intervalPrecision,o=[];if(!e)return o;n[0]<r[0]&&(t?o.push({value:u(r[0]-e,i)}):o.push({value:n[0]}));for(var a=r[0];a<=r[1]&&(o.push({value:a}),(a=u(a+e,i))!==o[o.length-1].value);){;if(o.length>1e4)return[]}var s=o.length?o[o.length-1].value:r[1];return n[1]>s&&(t?o.push({value:u(s+e,i)}):o.push({value:n[1]})),o},e.prototype.getMinorTicks=function(t){for(var e=this.getTicks(!0),n=[],r=this.getExtent(),i=1;i<e.length;i++){for(var o=e[i],a=e[i-1],s=0,l=[],c=(o.value-a.value)/t;s<t-1;){var h=u(a.value+(s+1)*c);h>r[0]&&h<r[1]&&l.push(h),s++}n.push(l)}return n},e.prototype.getLabel=function(t,e){if(null==t)return"";var n=e&&e.precision;null==n?n=i.p8(t.value)||0:"auto"===n&&(n=this._intervalPrecision);var r=u(t.value,n,!0);return o.OD(r)},e.prototype.calcNiceTicks=function(t,e,n){t=t||5;var r=this._extent,i=r[1]-r[0];if(!!isFinite(i)){i<0&&(i=-i,r.reverse());var o=s.Qf(r,t,e,n);this._intervalPrecision=o.intervalPrecision,this._interval=o.interval,this._niceExtent=o.niceTickExtent}},e.prototype.calcNiceExtent=function(t){var e=this._extent;if(e[0]===e[1]){if(0!==e[0]){var n=Math.abs(e[0]);t.fixMax||(e[1]+=n/2),e[0]-=n/2}else e[1]=1}!isFinite(e[1]-e[0])&&(e[0]=0,e[1]=1),this.calcNiceTicks(t.splitNumber,t.minInterval,t.maxInterval);var r=this._interval;!t.fixMin&&(e[0]=u(Math.floor(e[0]/r)*r)),!t.fixMax&&(e[1]=u(Math.ceil(e[1]/r)*r))},e.prototype.setNiceExtent=function(t,e){this._niceExtent=[t,e]},e.type="interval",e}(a.Z);a.Z.registerClass(l),e.Z=l},595859:function(t,e,n){var r=n(518299),i=n(807028),o=n(275372),a=n(31674),s=n(981740),u=n(177813),l=o.Z.prototype,c=u.Z.prototype,h=a.NM,f=Math.floor,p=Math.ceil,d=Math.pow,g=Math.log,v=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="log",e.base=10,e._originalScale=new u.Z,e._interval=0,e}return(0,r.ZT)(e,t),e.prototype.getTicks=function(t){var e=this._originalScale,n=this._extent,r=e.getExtent(),o=c.getTicks.call(this,t);return i.UI(o,function(t){var e=t.value,i=a.NM(d(this.base,e));return i=e===n[0]&&this._fixMin?m(i,r[0]):i,{value:i=e===n[1]&&this._fixMax?m(i,r[1]):i}},this)},e.prototype.setExtent=function(t,e){var n=g(this.base);t=g(Math.max(0,t))/n,e=g(Math.max(0,e))/n,c.setExtent.call(this,t,e)},e.prototype.getExtent=function(){var t=this.base,e=l.getExtent.call(this);e[0]=d(t,e[0]),e[1]=d(t,e[1]);var n=this._originalScale.getExtent();return this._fixMin&&(e[0]=m(e[0],n[0])),this._fixMax&&(e[1]=m(e[1],n[1])),e},e.prototype.unionExtent=function(t){this._originalScale.unionExtent(t);var e=this.base;t[0]=g(t[0])/g(e),t[1]=g(t[1])/g(e),l.unionExtent.call(this,t)},e.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},e.prototype.calcNiceTicks=function(t){t=t||10;var e=this._extent,n=e[1]-e[0];if(n!==1/0&&!(n<=0)){var r=a.Xd(n);for(t/n*r<=.5&&(r*=10);!isNaN(r)&&1>Math.abs(r)&&Math.abs(r)>0;)r*=10;var i=[a.NM(p(e[0]/r)*r),a.NM(f(e[1]/r)*r)];this._interval=r,this._niceExtent=i}},e.prototype.calcNiceExtent=function(t){c.calcNiceExtent.call(this,t),this._fixMin=t.fixMin,this._fixMax=t.fixMax},e.prototype.parse=function(t){return t},e.prototype.contain=function(t){return t=g(t)/g(this.base),s.XS(t,this._extent)},e.prototype.normalize=function(t){return t=g(t)/g(this.base),s.Fv(t,this._extent)},e.prototype.scale=function(t){return t=s.bA(t,this._extent),d(this.base,t)},e.type="log",e}(o.Z),y=v.prototype;function m(t,e){return h(t,a.p8(e))}y.getMinorTicks=c.getMinorTicks,y.getLabel=c.getLabel,o.Z.registerClass(v),e.Z=v},529660:function(t,e,n){var r=n(518299),i=n(275372),o=n(911992),a=n(981740),s=n(807028),u=function(t){function e(e){var n=t.call(this,e)||this;n.type="ordinal";var r=n.getSetting("ordinalMeta");return!r&&(r=new o.Z({})),(0,s.kJ)(r)&&(r=new o.Z({categories:(0,s.UI)(r,function(t){return(0,s.Kn)(t)?t.value:t})})),n._ordinalMeta=r,n._extent=n.getSetting("extent")||[0,r.categories.length-1],n}return(0,r.ZT)(e,t),e.prototype.parse=function(t){return null==t?NaN:(0,s.HD)(t)?this._ordinalMeta.getOrdinal(t):Math.round(t)},e.prototype.contain=function(t){return t=this.parse(t),a.XS(t,this._extent)&&null!=this._ordinalMeta.categories[t]},e.prototype.normalize=function(t){return t=this._getTickNumber(this.parse(t)),a.Fv(t,this._extent)},e.prototype.scale=function(t){return t=Math.round(a.bA(t,this._extent)),this.getRawOrdinalNumber(t)},e.prototype.getTicks=function(){for(var t=[],e=this._extent,n=e[0];n<=e[1];)t.push({value:n}),n++;return t},e.prototype.getMinorTicks=function(t){},e.prototype.setSortInfo=function(t){if(null==t){this._ordinalNumbersByTick=this._ticksByOrdinalNumber=null;return}for(var e=t.ordinalNumbers,n=this._ordinalNumbersByTick=[],r=this._ticksByOrdinalNumber=[],i=0,o=this._ordinalMeta.categories.length,a=Math.min(o,e.length);i<a;++i){var s=e[i];n[i]=s,r[s]=i}for(var u=0;i<o;++i){for(;null!=r[u];)u++;n.push(u),r[u]=i}},e.prototype._getTickNumber=function(t){var e=this._ticksByOrdinalNumber;return e&&t>=0&&t<e.length?e[t]:t},e.prototype.getRawOrdinalNumber=function(t){var e=this._ordinalNumbersByTick;return e&&t>=0&&t<e.length?e[t]:t},e.prototype.getLabel=function(t){if(!this.isBlank()){var e=this.getRawOrdinalNumber(t.value),n=this._ordinalMeta.categories[e];return null==n?"":n+""}},e.prototype.count=function(){return this._extent[1]-this._extent[0]+1},e.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},e.prototype.isInExtentRange=function(t){return t=this._getTickNumber(t),this._extent[0]<=t&&this._extent[1]>=t},e.prototype.getOrdinalMeta=function(){return this._ordinalMeta},e.prototype.calcNiceTicks=function(){},e.prototype.calcNiceExtent=function(){},e.type="ordinal",e}(i.Z);i.Z.registerClass(u),e.Z=u},275372:function(t,e,n){var r=n(731181),i=function(){function t(t){this._setting=t||{},this._extent=[1/0,-1/0]}return t.prototype.getSetting=function(t){return this._setting[t]},t.prototype.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1])},t.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},t.prototype.getExtent=function(){return this._extent.slice()},t.prototype.setExtent=function(t,e){var n=this._extent;!isNaN(t)&&(n[0]=t),!isNaN(e)&&(n[1]=e)},t.prototype.isInExtentRange=function(t){return this._extent[0]<=t&&this._extent[1]>=t},t.prototype.isBlank=function(){return this._isBlank},t.prototype.setBlank=function(t){this._isBlank=t},t}();r.au(i),e.Z=i},337149:function(t,e,n){var r=n(518299),i=n(31674),o=n(744829),a=n(981740),s=n(177813),u=n(275372),l=n(807028),c=function(t,e,n,r){for(;n<r;){var i=n+r>>>1;t[i][1]<e?n=i+1:r=i}return n},h=function(t){function e(e){var n=t.call(this,e)||this;return n.type="time",n}return(0,r.ZT)(e,t),e.prototype.getLabel=function(t){var e=this.getSetting("useUTC");return(0,o.WU)(t.value,o.V8[(0,o.xC)((0,o.Tj)(this._minLevelUnit))]||o.V8.second,e,this.getSetting("locale"))},e.prototype.getFormattedLabel=function(t,e,n){var r=this.getSetting("useUTC"),i=this.getSetting("locale");return(0,o.k7)(t,e,n,i,r)},e.prototype.getTicks=function(){var t=this._interval,e=this._extent,n=[];if(!t)return n;n.push({value:e[0],level:0});var r=this.getSetting("useUTC"),a=function(t,e,n,r){for(var a=o.FW,s=0,u=[],c=[],h=0,f=0,d=0;d<a.length&&s++<1e4;++d){var g=(0,o.Tj)(a[d]);if(!!(0,o.$K)(a[d])){if(!function(t,a,s){var u=[],l=!a.length;if(!function(t,e,n,r){var a=i.sG(e),s=i.sG(n),u=function(t){return(0,o.q5)(a,t,r)===(0,o.q5)(s,t,r)},l=function(){return u("year")},c=function(){return l()&&u("month")},h=function(){return c()&&u("day")},f=function(){return h()&&u("hour")},p=function(){return f()&&u("minute")},d=function(){return p()&&u("second")};switch(t){case"year":return l();case"month":return c();case"day":return h();case"hour":return f();case"minute":return p();case"second":return d();case"millisecond":return d()&&u("millisecond")}}((0,o.Tj)(t),r[0],r[1],n)){l&&(a=[{value:function(t,e,n){var r=new Date(t);switch((0,o.Tj)(e)){case"year":case"month":r[(0,o.vh)(n)](0);case"day":r[(0,o.f5)(n)](1);case"hour":r[(0,o.En)(n)](0);case"minute":r[(0,o.eN)(n)](0);case"second":r[(0,o.rM)(n)](0),r[(0,o.cb)(n)](0)}return r.getTime()}(new Date(r[0]),t,n)},{value:r[1]}]);for(var c=0;c<a.length-1;c++){var h,f,d,g,v,y=a[c].value,m=a[c+1].value;if(y!==m){var _=void 0,x=void 0,w=void 0,S=!1;switch(t){case"year":_=Math.max(1,Math.round(e/o.s2/365)),x=(0,o.sx)(n),w=(0,o.xL)(n);break;case"half-year":case"quarter":case"month":;_=(h=e/(30*o.s2))>6?6:h>3?3:h>2?2:1,x=(0,o.CW)(n),w=(0,o.vh)(n);break;case"week":case"half-week":case"day":;d=0,_=(f=e/o.s2)>16?16:f>7.5?7:f>3.5?4:f>1.5?2:1,x=(0,o.xz)(n),w=(0,o.f5)(n),S=!0;break;case"half-day":case"quarter-day":case"hour":;_=(g=e/o.dV)>12?12:g>6?6:g>3.5?4:g>2?2:1,x=(0,o.Wp)(n),w=(0,o.En)(n);break;case"minute":_=p(e,!0),x=(0,o.fn)(n),w=(0,o.eN)(n);break;case"second":_=p(e,!1),x=(0,o.MV)(n),w=(0,o.rM)(n);break;case"millisecond":;v=e,_=i.kx(v,!0),x=(0,o.RZ)(n),w=(0,o.cb)(n)}!function(t,e,n,i,o,a,s){for(var u=new Date(e),l=e,c=u[i]();l<n&&l<=r[1];)s.push({value:l}),c+=t,u[o](c),l=u.getTime();s.push({value:l,notAdd:!0})}(_,y,m,x,w,0,u),"year"===t&&s.length>1&&0===c&&s.unshift({value:s[0].value-_})}}for(var c=0;c<u.length;c++)s.push(u[c]);}}(a[d],u[u.length-1]||[],c),g!==(a[d+1]?(0,o.Tj)(a[d+1]):null)){if(c.length){f=h,c.sort(function(t,e){return t.value-e.value});for(var v=[],y=0;y<c.length;++y){var m=c[y].value;(0===y||c[y-1].value!==m)&&(v.push(c[y]),m>=r[0]&&m<=r[1]&&h++)}var _=(r[1]-r[0])/e;if(h>1.5*_&&f>_/1.5)break;if(u.push(v),h>_||t===a[d])break}c=[]}}}for(var x=(0,l.hX)((0,l.UI)(u,function(t){return(0,l.hX)(t,function(t){return t.value>=r[0]&&t.value<=r[1]&&!t.notAdd})}),function(t){return t.length>0}),w=[],S=x.length-1,d=0;d<x.length;++d){for(var b=x[d],M=0;M<b.length;++M)w.push({value:b[M].value,level:S-d})}w.sort(function(t,e){return t.value-e.value});for(var T=[],d=0;d<w.length;++d)(0===d||w[d].value!==w[d-1].value)&&T.push(w[d]);return T}(this._minLevelUnit,this._approxInterval,r,e);return(n=n.concat(a)).push({value:e[1],level:0}),n},e.prototype.calcNiceExtent=function(t){var e=this._extent;if(e[0]===e[1]&&(e[0]-=o.s2,e[1]+=o.s2),e[1]===-1/0&&e[0]===1/0){var n=new Date;e[1]=+new Date(n.getFullYear(),n.getMonth(),n.getDate()),e[0]=e[1]-o.s2}this.calcNiceTicks(t.splitNumber,t.minInterval,t.maxInterval)},e.prototype.calcNiceTicks=function(t,e,n){t=t||10;var r=this._extent,i=r[1]-r[0];this._approxInterval=i/t,null!=e&&this._approxInterval<e&&(this._approxInterval=e),null!=n&&this._approxInterval>n&&(this._approxInterval=n);var o=f.length,a=Math.min(c(f,this._approxInterval,0,o),o-1);this._interval=f[a][1],this._minLevelUnit=f[Math.max(a-1,0)][0]},e.prototype.parse=function(t){return(0,l.hj)(t)?t:+i.sG(t)},e.prototype.contain=function(t){return a.XS(this.parse(t),this._extent)},e.prototype.normalize=function(t){return a.Fv(this.parse(t),this._extent)},e.prototype.scale=function(t){return a.bA(t,this._extent)},e.type="time",e}(s.Z),f=[["second",o.WT],["minute",o.yR],["hour",o.dV],["quarter-day",6*o.dV],["half-day",12*o.dV],["day",1.2*o.s2],["half-week",3.5*o.s2],["week",7*o.s2],["month",31*o.s2],["quarter",95*o.s2],["half-year",o.P5/2],["year",o.P5]];function p(t,e){return(t/=e?o.yR:o.WT)>30?30:t>20?20:t>15?15:t>10?10:t>5?5:t>2?2:1}u.Z.registerClass(h),e.Z=h},981740:function(t,e,n){n.d(e,{Fv:function(){return c},Qf:function(){return o},XS:function(){return l},bA:function(){return h},lM:function(){return i},lb:function(){return s},r1:function(){return a}});var r=n(31674);function i(t){return"interval"===t.type||"log"===t.type}function o(t,e,n,i){var o={},a=t[1]-t[0],l=o.interval=(0,r.kx)(a/e,!0);null!=n&&l<n&&(l=o.interval=n),null!=i&&l>i&&(l=o.interval=i);var c=o.intervalPrecision=s(l);return function(t,e){isFinite(t[0])||(t[0]=e[0]),isFinite(t[1])||(t[1]=e[1]),u(t,0,e),u(t,1,e),t[0]>t[1]&&(t[0]=t[1])}(o.niceTickExtent=[(0,r.NM)(Math.ceil(t[0]/l)*l,c),(0,r.NM)(Math.floor(t[1]/l)*l,c)],t),o}function a(t){var e=Math.pow(10,(0,r.xW)(t)),n=t/e;return n?2===n?n=3:3===n?n=5:n*=2:n=1,(0,r.NM)(n*e)}function s(t){return(0,r.p8)(t)+2}function u(t,e,n){t[e]=Math.max(Math.min(t[e],n[1]),n[0])}function l(t,e){return t>=e[0]&&t<=e[1]}function c(t,e){return e[1]===e[0]?.5:(t-e[0])/(e[1]-e[0])}function h(t,e){return t*(e[1]-e[0])+e[0]}},177112:function(t,e){var n="#B9B8CE",r="#100C2A",i=function(){return{axisLine:{lineStyle:{color:n}},splitLine:{lineStyle:{color:"#484753"}},splitArea:{areaStyle:{color:["rgba(255,255,255,0.02)","rgba(255,255,255,0.05)"]}},minorSplitLine:{lineStyle:{color:"#20203B"}}}},o=["#4992ff","#7cffb2","#fddd60","#ff6e76","#58d9f9","#05c091","#ff8a45","#8d48e3","#dd79ff"],a={darkMode:!0,color:o,backgroundColor:r,axisPointer:{lineStyle:{color:"#817f91"},crossStyle:{color:"#817f91"},label:{color:"#fff"}},legend:{textStyle:{color:n}},textStyle:{color:n},title:{textStyle:{color:"#EEF1FA"},subtextStyle:{color:"#B9B8CE"}},toolbox:{iconStyle:{borderColor:n}},dataZoom:{borderColor:"#71708A",textStyle:{color:n},brushStyle:{color:"rgba(135,163,206,0.3)"},handleStyle:{color:"#353450",borderColor:"#C5CBE3"},moveHandleStyle:{color:"#B0B6C3",opacity:.3},fillerColor:"rgba(135,163,206,0.2)",emphasis:{handleStyle:{borderColor:"#91B7F2",color:"#4D587D"},moveHandleStyle:{color:"#636D9A",opacity:.7}},dataBackground:{lineStyle:{color:"#71708A",width:1},areaStyle:{color:"#71708A"}},selectedDataBackground:{lineStyle:{color:"#87A3CE"},areaStyle:{color:"#87A3CE"}}},visualMap:{textStyle:{color:n}},timeline:{lineStyle:{color:n},label:{color:n},controlStyle:{color:n,borderColor:n}},calendar:{itemStyle:{color:r},dayLabel:{color:n},monthLabel:{color:n},yearLabel:{color:n}},timeAxis:i(),logAxis:i(),valueAxis:i(),categoryAxis:i(),line:{symbol:"circle"},graph:{color:o},gauge:{title:{color:n},axisLine:{lineStyle:{color:[[1,"rgba(207,212,219,0.2)"]]}},axisLabel:{color:n},detail:{color:"#EEF1FA"}},candlestick:{itemStyle:{color:"#f64e56",color0:"#54ea92",borderColor:"#f64e56",borderColor0:"#54ea92"}}};a.categoryAxis.splitLine.show=!1,e.Z=a},831168:function(t,e){var n=["#37A2DA","#32C5E9","#67E0E3","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#E062AE","#E690D1","#e7bcf3","#9d96f5","#8378EA","#96BFFF"];e.Z={color:n,colorLayer:[["#37A2DA","#ffd85c","#fd7b5f"],["#37A2DA","#67E0E3","#FFDB5C","#ff9f7f","#E062AE","#9d96f5"],["#37A2DA","#32C5E9","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#e7bcf3","#8378EA","#96BFFF"],n]}},159611:function(t,e,n){n.d(e,{v:function(){return o}});var r=n(807028),i=n(731181),o=function(){function t(){}return t.prototype.normalizeQuery=function(t){var e={},n={},o={};if(r.HD(t)){var a=(0,i.u9)(t);e.mainType=a.main||null,e.subType=a.sub||null}else{var s=["Index","Name","Id"],u={name:1,dataIndex:1,dataType:1};r.S6(t,function(t,r){for(var i=!1,a=0;a<s.length;a++){var l=s[a],c=r.lastIndexOf(l);if(c>0&&c===r.length-l.length){var h=r.slice(0,c);"data"!==h&&(e.mainType=h,e[l.toLowerCase()]=t,i=!0)}}u.hasOwnProperty(r)&&(n[r]=t,i=!0),!i&&(o[r]=t)})}return{cptQuery:e,dataQuery:n,otherQuery:o}},t.prototype.filter=function(t,e){var n=this.eventInfo;if(!n)return!0;var r=n.targetEl,i=n.packedEvent,o=n.model,a=n.view;if(!o||!a)return!0;var s=e.cptQuery,u=e.dataQuery;return l(s,o,"mainType")&&l(s,o,"subType")&&l(s,o,"index","componentIndex")&&l(s,o,"name")&&l(s,o,"id")&&l(u,i,"name")&&l(u,i,"dataIndex")&&l(u,i,"dataType")&&(!a.filterForExposedEvent||a.filterForExposedEvent(t,e.otherQuery,r,i));function l(t,e,n,r){return null==t[n]||e[r||n]===t[n]}},t.prototype.afterTrigger=function(){this.eventInfo=null},t}()},731181:function(t,e,n){n.d(e,{PT:function(){return u},Qj:function(){return f},au:function(){return g},dm:function(){return l},pw:function(){return c},u9:function(){return s}});var r=n(518299),i=n(807028),o="___EC__COMPONENT__CONTAINER___",a="___EC__EXTENDED_CLASS___";function s(t){var e={main:"",sub:""};if(t){var n=t.split(".");e.main=n[0]||"",e.sub=n[1]||""}return e}function u(t){return!!(t&&t[a])}function l(t,e){t.$constructor=t,t.extend=function(t){var e,n=this;return function(t){return i.mf(t)&&/^class\s/.test(Function.prototype.toString.call(t))}(n)?e=function(t){function e(){return t.apply(this,arguments)||this}return(0,r.ZT)(e,t),e}(n):(e=function(){(t.$constructor||n).apply(this,arguments)},i.XW(e,this)),i.l7(e.prototype,t),e[a]=!0,e.extend=this.extend,e.superCall=p,e.superApply=d,e.superClass=n,e}}function c(t,e){t.extend=e.extend}var h=Math.round(10*Math.random());function f(t){var e=["__\0is_clz",h++].join("_");t.prototype[e]=!0;t.isInstance=function(t){return!!(t&&t[e])}}function p(t,e){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];return this.superClass.prototype[e].apply(t,n)}function d(t,e,n){return this.superClass.prototype[e].apply(t,n)}function g(t){var e={};t.registerClass=function(t){var n=t.type||t.prototype.type;if(n){r=n,i.hu(/^[a-zA-Z0-9_]+([.][a-zA-Z0-9_]+)?$/.test(r),'componentType "'+r+'" illegal'),t.prototype.type=n;var r,a=s(n);a.sub?a.sub!==o&&(function(t){var n=e[t.main];return(!n||!n[o])&&((n=e[t.main]={})[o]=!0),n}(a)[a.sub]=t):e[a.main]=t}return t},t.getClass=function(t,n,r){var i=e[t];if(i&&i[o]&&(i=n?i[n]:null),r&&!i)throw Error(n?"Component "+t+"."+(n||"")+" is used but not imported.":t+".type should be specified.");return i},t.getClassesByMainType=function(t){var n=s(t),r=[],a=e[n.main];return a&&a[o]?i.S6(a,function(t,e){e!==o&&r.push(t)}):r.push(a),r},t.hasClass=function(t){return!!e[s(t).main]},t.getAllClassMainTypes=function(){var t=[];return i.S6(e,function(e,n){t.push(n)}),t},t.hasSubTypes=function(t){var n=e[s(t).main];return n&&n[o]}}},115856:function(t,e,n){n.d(e,{Kr:function(){return a},ZL:function(){return l},cj:function(){return s},jS:function(){return u}});var r=n(807028),i=n(731181),o=Math.round(10*Math.random());function a(t){return[t||"",o++].join("_")}function s(t){var e={};t.registerSubTypeDefaulter=function(t,n){e[(0,i.u9)(t).main]=n},t.determineSubType=function(n,r){var o=r.type;if(!o){var a=(0,i.u9)(n).main;t.hasSubTypes(n)&&e[a]&&(o=e[a](r))}return o}}function u(t,e){t.topologicalTravel=function(t,i,o,a){if(!!t.length){var s=function(t){var i={},o=[];return r.S6(t,function(a){var s=n(i,a),u=function(t,e){var n=[];return r.S6(t,function(t){r.cq(e,t)>=0&&n.push(t)}),n}(s.originalDeps=e(a),t);s.entryCount=u.length,0===s.entryCount&&o.push(a),r.S6(u,function(t){0>r.cq(s.predecessor,t)&&s.predecessor.push(t);var e=n(i,t);0>r.cq(e.successor,t)&&e.successor.push(a)})}),{graph:i,noEntryList:o}}(i),u=s.graph,l=s.noEntryList,c={};for(r.S6(t,function(t){c[t]=!0});l.length;){var h=l.pop(),f=u[h],p=!!c[h];p&&(o.call(a,h,f.originalDeps.slice()),delete c[h]),r.S6(f.successor,p?g:d)}r.S6(c,function(){throw Error("")})}function d(t){u[t].entryCount--,0===u[t].entryCount&&l.push(t)}function g(t){c[t]=!0,d(t)}};function n(t,e){return!t[e]&&(t[e]={predecessor:[],successor:[]}),t[e]}}function l(t,e){return r.TS(r.TS({},t,!0),e,!0)}},973724:function(t,e,n){n.d(e,{I:function(){return p}});var r=n(833572),i=n(445471),o=n(807028),a=n(31674),s=n(19750),u=n(157301),l=n(252919),c=new r.Z,h=new i.ZP(100),f=["symbol","symbolSize","symbolKeepAspect","color","backgroundColor","dashArrayX","dashArrayY","maxTileWidth","maxTileHeight"];function p(t,e){if("none"===t)return null;var n=e.getDevicePixelRatio(),r=e.getZr(),i="svg"===r.painter.type;t.dirty&&c.delete(t);var p=c.get(t);if(p)return p;var g=(0,o.ce)(t,{symbol:"rect",symbolSize:1,symbolKeepAspect:!0,color:"rgba(0, 0, 0, 0.2)",backgroundColor:null,dashArrayX:5,dashArrayY:5,rotation:0,maxTileWidth:512,maxTileHeight:512});"none"===g.backgroundColor&&(g.backgroundColor=null);var v={repeat:"repeat"};return function(t){for(var e,c,p=[n],v=!0,y=0;y<f.length;++y){var m=g[f[y]];if(null!=m&&!(0,o.kJ)(m)&&!(0,o.HD)(m)&&!(0,o.hj)(m)&&"boolean"!=typeof m){v=!1;break}p.push(m)}if(v){e=p.join(",")+(i?"-svg":"");var _=h.get(e);_&&(i?t.svgElement=_:t.image=_)}var x=function t(e){if(!e||0===e.length)return[[0,0]];if((0,o.hj)(e)){var n=Math.ceil(e);return[[n,n]]}for(var r=!0,i=0;i<e.length;++i)if(!(0,o.hj)(e[i])){r=!1;break}if(r)return t([e]);for(var a=[],i=0;i<e.length;++i)if((0,o.hj)(e[i])){var n=Math.ceil(e[i]);a.push([n,n])}else{var n=(0,o.UI)(e[i],function(t){return Math.ceil(t)});n.length%2==1?a.push(n.concat(n)):a.push(n)}return a}(g.dashArrayX),w=function(t){if(!t||"object"==typeof t&&0===t.length)return[0,0];if((0,o.hj)(t)){var e=Math.ceil(t);return[e,e]}var n=(0,o.UI)(t,function(t){return Math.ceil(t)});return t.length%2?n.concat(n):n}(g.dashArrayY),S=function t(e){if(!e||0===e.length)return[["rect"]];if((0,o.HD)(e))return[[e]];for(var n=!0,r=0;r<e.length;++r)if(!(0,o.HD)(e[r])){n=!1;break}if(n)return t([e]);for(var i=[],r=0;r<e.length;++r)(0,o.HD)(e[r])?i.push([e[r]]):i.push(e[r]);return i}(g.symbol),b=function(t){return(0,o.UI)(t,function(t){return d(t)})}(x),M=d(w),T=!i&&l.qW.createCanvas(),k=i&&{tag:"g",attrs:{},key:"dcl",children:[]},D=function(){for(var t=1,e=0,n=b.length;e<n;++e)t=(0,a.nl)(t,b[e]);for(var r=1,e=0,n=S.length;e<n;++e)r=(0,a.nl)(r,S[e].length);t*=r;var i=M*b.length*S.length;return{width:Math.max(1,Math.min(t,g.maxTileWidth)),height:Math.max(1,Math.min(i,g.maxTileHeight))}}();T&&(T.width=D.width*n,T.height=D.height*n,c=T.getContext("2d")),function(){c&&(c.clearRect(0,0,T.width,T.height),g.backgroundColor&&(c.fillStyle=g.backgroundColor,c.fillRect(0,0,T.width,T.height)));for(var t=0,e=0;e<w.length;++e)t+=w[e];if(!(t<=0))for(var o=-M,a=0,l=0,h=0;o<D.height;){if(a%2==0){for(var f=l/2%S.length,p=0,d=0,v=0;p<2*D.width;){for(var y=0,e=0;e<x[h].length;++e)y+=x[h][e];if(y<=0)break;if(d%2==0){var m=(1-g.symbolSize)*.5,_=p+x[h][d]*m,b=o+w[a]*m,C=x[h][d]*g.symbolSize,I=w[a]*g.symbolSize,A=v/2%S[f].length;(function(t,e,o,a,l){var h=i?1:n,f=(0,s.th)(l,t*h,e*h,o*h,a*h,g.color,g.symbolKeepAspect);if(i){var p=r.painter.renderOneToVNode(f);p&&k.children.push(p)}else(0,u.RV)(c,f)})(_,b,C,I,S[f][A])}p+=x[h][d],++v,++d===x[h].length&&(d=0)}++h===x.length&&(h=0)}o+=w[a],++l,++a===w.length&&(a=0)}}(),v&&h.put(e,T||k),t.image=T,t.svgElement=k,t.svgWidth=D.width,t.svgHeight=D.height}(v),v.rotation=g.rotation,v.scaleX=v.scaleY=i?1:1/n,c.set(t,v),t.dirty=!1,v}function d(t){for(var e=0,n=0;n<t.length;++n)e+=t[n];return t.length%2==1?2*e:e}},191743:function(t,e,n){n.d(e,{o:function(){return r}});function r(t,e,n){for(var r;t&&(!e(t)||(r=t,!n));){;t=t.__hostTarget||t.parent}return r}},84164:function(t,e,n){n.d(e,{A0:function(){return h},Lz:function(){return f},MI:function(){return p},MY:function(){return s},OD:function(){return a},kF:function(){return c}});var r=n(807028),i=n(734477),o=n(31674);function a(t){if(!(0,o.kE)(t))return r.HD(t)?t:"-";var e=(t+"").split(".");return e[0].replace(/(\d{1,3})(?=(?:\d{3})+(?!\d))/g,"$1,")+(e.length>1?"."+e[1]:"")}var s=r.MY,u=["a","b","c","d","e","f","g"],l=function(t,e){return"{"+t+(null==e?"":e)+"}"};function c(t,e,n){!r.kJ(e)&&(e=[e]);var o=e.length;if(!o)return"";for(var a=e[0].$vars||[],s=0;s<a.length;s++){var c=u[s];t=t.replace(l(c),l(c,0))}for(var h=0;h<o;h++)for(var f=0;f<a.length;f++){var p=e[h][a[f]];t=t.replace(l(u[f],h),n?(0,i.F1)(p):p)}return t}function h(t,e){var n=r.HD(t)?{color:t,extraCssText:e}:t||{},o=n.color,a=n.type;e=n.extraCssText;var s=n.renderMode||"html";return o?"html"===s?"subItem"===a?'<span style="display:inline-block;vertical-align:middle;margin-right:8px;margin-left:3px;border-radius:4px;width:4px;height:4px;background-color:'+(0,i.F1)(o)+";"+(e||"")+'"></span>':'<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:'+(0,i.F1)(o)+";"+(e||"")+'"></span>':{renderMode:s,content:"{"+(n.markerId||"markerX")+"|}  ",style:"subItem"===a?{width:4,height:4,borderRadius:2,backgroundColor:o}:{width:10,height:10,borderRadius:5,backgroundColor:o}}:""}function f(t,e){return e=e||"transparent",r.HD(t)?t:r.Kn(t)&&t.colorStops&&(t.colorStops[0]||{}).color||e}function p(t,e){if("_blank"===e||"blank"===e){var n=window.open();n.opener=null,n.location.href=t}else window.open(t,e)}},339738:function(t,e,n){n.r(e),n.d(e,{Arc:function(){return A.Z},BezierCurve:function(){return I.Z},BoundingRect:function(){return O.Z},Circle:function(){return w.Z},CompoundPath:function(){return P.Z},Ellipse:function(){return S.Z},Group:function(){return _.Z},Image:function(){return m.ZP},IncrementalDisplayable:function(){return E.Z},Line:function(){return C.Z},LinearGradient:function(){return Z.Z},OrientedBoundingRect:function(){return R.Z},Path:function(){return v.ZP},Point:function(){return N.Z},Polygon:function(){return T.Z},Polyline:function(){return k.Z},RadialGradient:function(){return L.Z},Rect:function(){return D.Z},Ring:function(){return M.Z},Sector:function(){return b.C},Text:function(){return x.ZP},applyTransform:function(){return to},clipPointsByRect:function(){return tl},clipRectByRect:function(){return tc},createIcon:function(){return th},extendPath:function(){return Y},extendShape:function(){return G},getShapeClass:function(){return K},getTransform:function(){return ti},groupTransition:function(){return tu},initProps:function(){return W.KZ},isElementRemoved:function(){return W.eq},lineLineIntersect:function(){return tp},linePolygonIntersect:function(){return tf},makeImage:function(){return J},makePath:function(){return j},mergePath:function(){return $},registerShape:function(){return q},removeElement:function(){return W.bX},removeElementWithFadeOut:function(){return W.XD},resizePath:function(){return tt},setTooltipConfig:function(){return tg},subPixelOptimize:function(){return tr},subPixelOptimizeLine:function(){return te},subPixelOptimizeRect:function(){return tn},transformDirection:function(){return ta},traverseElements:function(){return ty},updateProps:function(){return W.D}});var r,i,o,a,s,u,l,c,h,f,p=n(692606),d=n(921715),g=n(217961),v=n(894641),y=n(762680),m=n(694923),_=n(707498),x=n(345262),w=n(418819),S=n(183859),b=n(171093),M=n(980890),T=n(638144),k=n(714720),D=n(406822),C=n(798383),I=n(686475),A=n(671466),P=n(702654),Z=n(550025),L=n(938083),O=n(339296),R=n(660487),N=n(317080),E=n(874543),B=n(408432),z=n(807028),F=n(276868),W=n(202953),H=Math.max,V=Math.min,U={};function G(t){return v.ZP.extend(t)}var X=p.Pc;function Y(t,e){return X(t,e)}function q(t,e){U[t]=e}function K(t){if(U.hasOwnProperty(t))return U[t]}function j(t,e,n,r){var i=p.iR(t,e);return n&&("center"===r&&(n=Q(n,i.getBoundingRect())),tt(i,n)),i}function J(t,e,n){var r=new m.ZP({style:{image:t,x:e.x,y:e.y,width:e.width,height:e.height},onload:function(t){if("center"===n){var i={width:t.width,height:t.height};r.setStyle(Q(e,i))}}});return r}function Q(t,e){var n,r=e.width/e.height,i=t.height*r;return n=i<=t.width?t.height:(i=t.width)/r,{x:t.x+t.width/2-i/2,y:t.y+t.height/2-n/2,width:i,height:n}}var $=p.AA;function tt(t,e){if(!!t.applyTransform){var n=t.getBoundingRect().calculateTransform(e);t.applyTransform(n)}}function te(t,e){return B._3(t,t,{lineWidth:e}),t}function tn(t){return B.Pw(t.shape,t.shape,t.style),t}var tr=B.vu;function ti(t,e){for(var n=d.yR([]);t&&t!==e;)d.dC(n,t.getLocalTransform(),n),t=t.parent;return n}function to(t,e,n){return e&&!(0,z.zG)(e)&&(e=y.ZP.getLocalTransform(e)),n&&(e=d.U_([],e)),g.Ne([],t,e)}function ta(t,e,n){var r=0===e[4]||0===e[5]||0===e[0]?1:Math.abs(2*e[4]/e[0]),i=0===e[4]||0===e[5]||0===e[2]?1:Math.abs(2*e[4]/e[2]),o=["left"===t?-r:"right"===t?r:0,"top"===t?-i:"bottom"===t?i:0];return Math.abs((o=to(o,e,n))[0])>Math.abs(o[1])?o[0]>0?"right":"left":o[1]>0?"bottom":"top"}function ts(t){return!t.isGroup}function tu(t,e,n){if(!!t&&!!e){var r,i=(r={},t.traverse(function(t){if(!t.isGroup&&t.anid)r[t.anid]=t}),r);e.traverse(function(t){if(!t.isGroup&&t.anid){var e=i[t.anid];if(e){var r=o(t);t.attr(o(e)),(0,W.D)(t,r,n,(0,F.A)(t).dataIndex)}}})}function o(t){var e={x:t.x,y:t.y,rotation:t.rotation};if(null!=t.shape)e.shape=(0,z.l7)({},t.shape);return e}}function tl(t,e){return(0,z.UI)(t,function(t){var n=t[0];n=V(n=H(n,e.x),e.x+e.width);var r=t[1];return[n,r=V(r=H(r,e.y),e.y+e.height)]})}function tc(t,e){var n=H(t.x,e.x),r=V(t.x+t.width,e.x+e.width),i=H(t.y,e.y),o=V(t.y+t.height,e.y+e.height);if(r>=n&&o>=i)return{x:n,y:i,width:r-n,height:o-i}}function th(t,e,n){var r=(0,z.l7)({rectHover:!0},e),i=r.style={strokeNoScale:!0};if(n=n||{x:-1,y:-1,width:2,height:2},t)return 0===t.indexOf("image://")?(i.image=t.slice(8),(0,z.ce)(i,n),new m.ZP(r)):j(t.replace("path://",""),r,n,"center")}function tf(t,e,n,r,i){for(var o=0,a=i[i.length-1];o<i.length;o++){var s=i[o];if(tp(t,e,n,r,s[0],s[1],a[0],a[1]))return!0;a=s}}function tp(t,e,n,r,i,o,a,s){var u=n-t,l=r-e,c=a-i,h=s-o,f=function(t,e,n,r){return t*r-n*e}(c,h,u,l);if(function(t){return t<=1e-6&&t>=-.000001}(f))return!1;var p=t-i,d=e-o,g=function(t,e,n,r){return t*r-n*e}(p,d,u,l)/f;if(g<0||g>1)return!1;var v=function(t,e,n,r){return t*r-n*e}(p,d,c,h)/f;return!(v<0)&&!(v>1)&&!0}function td(t,e,n,r){return t*r-n*e}function tg(t){var e=t.itemTooltipOption,n=t.componentModel,r=t.itemName,i=(0,z.HD)(e)?{formatter:e}:e,o=n.mainType,a=n.componentIndex,s={componentType:o,name:r,$vars:["name"]};s[o+"Index"]=a;var u=t.formatterParamsExtra;u&&(0,z.S6)((0,z.XP)(u),function(t){!(0,z.RI)(s,t)&&(s[t]=u[t],s.$vars.push(t))});var l=(0,F.A)(t.el);l.componentMainType=o,l.componentIndex=a,l.tooltipConfig={name:r,option:(0,z.ce)({content:r,encodeHTMLContent:!0,formatterParams:s},i)}}function tv(t,e){var n;t.isGroup&&(n=e(t)),!n&&t.traverse(e)}function ty(t,e){if(t){if((0,z.kJ)(t))for(var n=0;n<t.length;n++)tv(t[n],e);else tv(t,e)}}r=w.Z,U.circle=r,i=S.Z,U.ellipse=i,o=b.C,U.sector=o,a=M.Z,U.ring=a,s=T.Z,U.polygon=s,u=k.Z,U.polyline=u,l=D.Z,U.rect=l,c=C.Z,U.line=c,h=I.Z,U.bezierCurve=h,f=A.Z,U.arc=f},276868:function(t,e,n){n.d(e,{A:function(){return r},Q:function(){return i}});var r=(0,n(133141).Yf)(),i=function(t,e,n,i){if(i){var o=r(i);o.dataIndex=n,o.dataType=e,o.seriesIndex=t,o.ssrType="chart","group"===i.type&&i.traverse(function(i){var o=r(i);o.seriesIndex=t,o.dataIndex=n,o.dataType=e,o.ssrType="chart"})}}},918712:function(t,e,n){n.d(e,{BZ:function(){return h},ME:function(){return f},YD:function(){return d},dt:function(){return g},p$:function(){return p},tE:function(){return v}});var r=n(807028),i=n(339296),o=n(31674),a=n(84164),s=r.S6,u=["left","right","top","bottom","width","height"],l=[["width","left","right"],["height","top","bottom"]];function c(t,e,n,r,i){var o=0,a=0;null==r&&(r=1/0),null==i&&(i=1/0);var s=0;e.eachChild(function(u,l){var c,h,f=u.getBoundingRect(),p=e.childAt(l+1),d=p&&p.getBoundingRect();if("horizontal"===t){var g=f.width+(d?-d.x+f.x:0);(c=o+g)>r||u.newline?(o=0,c=g,a+=s+n,s=f.height):s=Math.max(s,f.height)}else{var v=f.height+(d?-d.y+f.y:0);(h=a+v)>i||u.newline?(o+=s+n,a=0,h=v,s=f.width):s=Math.max(s,f.width)}if(!u.newline)u.x=o,u.y=a,u.markRedraw(),"horizontal"===t?o=c+n:a=h+n})}var h=c;function f(t,e,n){n=a.MY(n||0);var r=e.width,s=e.height,u=(0,o.GM)(t.left,r),l=(0,o.GM)(t.top,s),c=(0,o.GM)(t.right,r),h=(0,o.GM)(t.bottom,s),f=(0,o.GM)(t.width,r),p=(0,o.GM)(t.height,s),d=n[2]+n[0],g=n[1]+n[3],v=t.aspect;switch(isNaN(f)&&(f=r-c-g-u),isNaN(p)&&(p=s-h-d-l),null!=v&&(isNaN(f)&&isNaN(p)&&(v>r/s?f=.8*r:p=.8*s),isNaN(f)&&(f=v*p),isNaN(p)&&(p=f/v)),isNaN(u)&&(u=r-c-f-g),isNaN(l)&&(l=s-h-p-d),t.left||t.right){case"center":u=r/2-f/2-n[3];break;case"right":u=r-f-g}switch(t.top||t.bottom){case"middle":case"center":l=s/2-p/2-n[0];break;case"bottom":l=s-p-d}u=u||0,l=l||0,isNaN(f)&&(f=r-g-u-(c||0)),isNaN(p)&&(p=s-d-l-(h||0));var y=new i.Z(u+n[3],l+n[0],f,p);return y.margin=n,y}function p(t,e,n,o,a,s){var u,l=!a||!a.hv||a.hv[0],c=!a||!a.hv||a.hv[1],h=a&&a.boundingMode||"all";if((s=s||t).x=t.x,s.y=t.y,!l&&!c)return!1;if("raw"===h)u="group"===t.type?new i.Z(0,0,+e.width||0,+e.height||0):t.getBoundingRect();else if(u=t.getBoundingRect(),t.needLocalTransform()){var p=t.getLocalTransform();(u=u.clone()).applyTransform(p)}var d=f(r.ce({width:u.width,height:u.height},e),n,o),g=l?d.x-u.x:0,v=c?d.y-u.y:0;return"raw"===h?(s.x=g,s.y=v):(s.x+=g,s.y+=v),s===t&&t.markRedraw(),!0}function d(t){var e=t.layoutMode||t.constructor.layoutMode;return r.Kn(e)?e:e?{type:e}:null}function g(t,e,n){var i=n&&n.ignoreSize;r.kJ(i)||(i=[i,i]);var o=u(l[0],0),a=u(l[1],1);function u(n,r){var o={},a=0,u={},l=0;if(s(n,function(e){u[e]=t[e]}),s(n,function(t){c(e,t)&&(o[t]=u[t]=e[t]),h(o,t)&&a++,h(u,t)&&l++}),i[r])return h(e,n[1])?u[n[2]]=null:h(e,n[2])&&(u[n[1]]=null),u;if(2===l||!a)return u;if(a>=2)return o;for(var f=0;f<n.length;f++){var p=n[f];if(!c(o,p)&&c(t,p)){o[p]=t[p];break}}return o}function c(t,e){return t.hasOwnProperty(e)}function h(t,e){return null!=t[e]&&"auto"!==t[e]}function f(t,e,n){s(t,function(t){e[t]=n[t]})}f(l[0],t,o),f(l[1],t,a)}function v(t){return function(t,e){return e&&t&&s(u,function(n){e.hasOwnProperty(n)&&(t[n]=e[n])}),t}({},t)}r.WA(c,"vertical"),r.WA(c,"horizontal")},383831:function(t,e,n){n.d(e,{Sh:function(){return a},_y:function(){return s},vU:function(){return o}});var r={},i="undefined"!=typeof console&&console.warn&&console.log;function o(t,e){!function(t,e,n){if(i){if(n){if(r[e])return;r[e]=!0}console[t]("[ECharts] "+e)}}("error",t,e)}function a(t){}function s(t){throw Error(t)}},133141:function(t,e,n){n.d(e,{C4:function(){return c},C6:function(){return T},Cc:function(){return u},Co:function(){return h},HZ:function(){return k},IL:function(){return C},O0:function(){return m},P$:function(){return D},Td:function(){return l},U5:function(){return g},XI:function(){return _},Yf:function(){return w},ab:function(){return f},gO:function(){return x},kF:function(){return s},lY:function(){return y},pk:function(){return I},pm:function(){return b},yu:function(){return v},zH:function(){return M}});var r=n(807028),i=n(31674);function o(t,e,n){return(e-t)*n+t}var a="series\0";function s(t){return t instanceof Array?t:null==t?[]:[t]}function u(t,e,n){if(t){t[e]=t[e]||{},t.emphasis=t.emphasis||{},t.emphasis[e]=t.emphasis[e]||{};for(var r=0,i=n.length;r<i;r++){var o=n[r];!t.emphasis[e].hasOwnProperty(o)&&t[e].hasOwnProperty(o)&&(t.emphasis[e][o]=t[e][o])}}}var l=["fontStyle","fontWeight","fontSize","fontFamily","rich","tag","color","textBorderColor","textBorderWidth","width","height","lineHeight","align","verticalAlign","baseline","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY","backgroundColor","borderColor","borderWidth","borderRadius","padding"];function c(t){return!(0,r.Kn)(t)||(0,r.kJ)(t)||t instanceof Date?t:t.value}function h(t){return(0,r.Kn)(t)&&!(t instanceof Array)}function f(t,e,n){var i="normalMerge"===n,o="replaceMerge"===n,s="replaceAll"===n;t=t||[],e=(e||[]).slice();var u=(0,r.kW)();(0,r.S6)(e,function(t,n){if(!(0,r.Kn)(t)){e[n]=null;return}});var l=function(t,e,n){var r=[];if("replaceAll"===n)return r;for(var i=0;i<t.length;i++){var o=t[i];o&&null!=o.id&&e.set(o.id,i),r.push({existing:"replaceMerge"===n||y(o)?null:o,newOption:null,keyInfo:null,brandNew:null})}return r}(t,u,n);return(i||o)&&function(t,e,n,i){(0,r.S6)(i,function(o,a){if(!!o&&null!=o.id){var s=d(o.id),u=n.get(s);if(null!=u){var l=t[u];(0,r.hu)(!l.newOption,'Duplicated option on id "'+s+'".'),l.newOption=o,l.existing=e[u],i[a]=null}}})}(l,t,u,e),i&&function(t,e){(0,r.S6)(e,function(n,r){if(!!n&&null!=n.name)for(var i=0;i<t.length;i++){var o=t[i].existing;if(!t[i].newOption&&o&&(null==o.id||null==n.id)&&!y(n)&&!y(o)&&p("name",o,n)){t[i].newOption=n,e[r]=null;return}}})}(l,e),i||o?function(t,e,n){(0,r.S6)(e,function(e){if(!!e){for(var r,i=0;(r=t[i])&&(r.newOption||y(r.existing)||r.existing&&null!=e.id&&!p("id",e,r.existing));)i++;r?(r.newOption=e,r.brandNew=n):t.push({newOption:e,brandNew:n,existing:null,keyInfo:null}),i++}})}(l,e,o):s&&function(t,e){(0,r.S6)(e,function(e){t.push({newOption:e,brandNew:!0,existing:null,keyInfo:null})})}(l,e),function(t){var e=(0,r.kW)();(0,r.S6)(t,function(t){var n=t.existing;n&&e.set(n.id,t)}),(0,r.S6)(t,function(t){var n=t.newOption;(0,r.hu)(!n||null==n.id||!e.get(n.id)||e.get(n.id)===t,"id duplicates: "+(n&&n.id)),n&&null!=n.id&&e.set(n.id,t),t.keyInfo||(t.keyInfo={})}),(0,r.S6)(t,function(t,n){var i=t.existing,o=t.newOption,s=t.keyInfo;if(!!(0,r.Kn)(o)){if(s.name=null!=o.name?d(o.name):i?i.name:a+n,i)s.id=d(i.id);else if(null!=o.id)s.id=d(o.id);else{var u=0;do s.id="\0"+s.name+"\0"+u++;while(e.get(s.id))}e.set(s.id,t)}})}(l),l}function p(t,e,n){var r=g(e[t],null),i=g(n[t],null);return null!=r&&null!=i&&r===i}function d(t){return g(t,"")}function g(t,e){return null==t?e:(0,r.HD)(t)?t:(0,r.hj)(t)||(0,r.cd)(t)?t+"":e}function v(t){var e=t.name;return!!(e&&e.indexOf(a))}function y(t){return t&&null!=t.id&&0===d(t.id).indexOf("\0_ec_\0")}function m(t,e,n){(0,r.S6)(t,function(t){var i=t.newOption;(0,r.Kn)(i)&&(t.keyInfo.mainType=e,t.keyInfo.subType=function(t,e,n,r){return e.type?e.type:n?n.subType:r.determineSubType(t,e)}(e,i,t.existing,n))})}function _(t,e){var n={},r={};return i(t||[],n),i(e||[],r,n),[o(n),o(r)];function i(t,e,n){for(var r=0,i=t.length;r<i;r++){var o=g(t[r].seriesId,null);if(null==o)return;for(var a=s(t[r].dataIndex),u=n&&n[o],l=0,c=a.length;l<c;l++){var h=a[l];u&&u[h]?u[h]=null:(e[o]||(e[o]={}))[h]=1}}}function o(t,e){var n=[];for(var r in t)if(t.hasOwnProperty(r)&&null!=t[r]){if(e)n.push(+r);else{var i=o(t[r],!0);i.length&&n.push({seriesId:r,dataIndex:i})}}return n}}function x(t,e){if(null!=e.dataIndexInside)return e.dataIndexInside;if(null!=e.dataIndex)return(0,r.kJ)(e.dataIndex)?(0,r.UI)(e.dataIndex,function(e){return t.indexOfRawIndex(e)}):t.indexOfRawIndex(e.dataIndex);if(null!=e.name)return(0,r.kJ)(e.name)?(0,r.UI)(e.name,function(e){return t.indexOfName(e)}):t.indexOfName(e.name)}function w(){var t="__ec_inner_"+S++;return function(e){return e[t]||(e[t]={})}}var S=(0,i.jj)();function b(t,e,n){var r=M(e,n),i=r.mainTypeSpecified,o=r.queryOptionMap,a=r.others,s=n?n.defaultMainType:null;return!i&&s&&o.set(s,{}),o.each(function(e,r){var i=k(t,r,e,{useDefault:s===r,enableAll:!n||null==n.enableAll||n.enableAll,enableNone:!n||null==n.enableNone||n.enableNone});a[r+"Models"]=i.models,a[r+"Model"]=i.models[0]}),a}function M(t,e){if((0,r.HD)(t)){var n,i={};i[t+"Index"]=0,n=i}else n=t;var o=(0,r.kW)(),a={},s=!1;return(0,r.S6)(n,function(t,n){if("dataIndex"===n||"dataIndexInside"===n){a[n]=t;return}var i=n.match(/^(\w+)(Index|Id|Name)$/)||[],u=i[1],l=(i[2]||"").toLowerCase();if(!(!u||!l||e&&e.includeMainTypes&&0>(0,r.cq)(e.includeMainTypes,u)))s=s||!!u,(o.get(u)||o.set(u,{}))[l]=t}),{mainTypeSpecified:s,queryOptionMap:o,others:a}}var T={useDefault:!0,enableAll:!1,enableNone:!1};function k(t,e,n,i){i=i||T;var o=n.index,a=n.id,s=n.name,u={models:null,specified:null!=o||null!=a||null!=s};if(!u.specified){var l=void 0;return u.models=i.useDefault&&(l=t.getComponent(e))?[l]:[],u}return"none"===o||!1===o?((0,r.hu)(i.enableNone,'`"none"` or `false` is not a valid value on index option.'),u.models=[],u):("all"===o&&((0,r.hu)(i.enableAll,'`"all"` is not a valid value on index option.'),o=a=s=null),u.models=t.queryComponents({mainType:e,index:o,id:a,name:s}),u)}function D(t,e,n){t.setAttribute?t.setAttribute(e,n):t[e]=n}function C(t,e){return t.getAttribute?t.getAttribute(e):t[e]}function I(t,e,n,o,a){var s=null==e||"auto"===e;if(null==o)return o;if((0,r.hj)(o)){var u,l=(o-(u=n||0))*a+u;return(0,i.NM)(l,s?Math.max((0,i.p8)(n||0),(0,i.p8)(o)):e)}if((0,r.HD)(o))return a<1?n:o;for(var c=[],h=Math.max(n?n.length:0,o.length),f=0;f<h;++f){var p=t.getDimensionInfo(f);if(p&&"ordinal"===p.type)c[f]=(a<1&&n?n:o)[f];else{var d,g=n&&n[f]?n[f]:0,v=o[f];var l=(v-(d=g))*a+d;c[f]=(0,i.NM)(l,s?Math.max((0,i.p8)(g),(0,i.p8)(v)):e)}}return c}},31674:function(t,e,n){n.d(e,{FK:function(){return _},GM:function(){return o},M9:function(){return l},NM:function(){return a},NU:function(){return i},S$:function(){return c},Xd:function(){return g},dt:function(){return s},jj:function(){return w},kE:function(){return x},kx:function(){return y},mW:function(){return f},nR:function(){return m},nl:function(){return S},p8:function(){return u},sG:function(){return d},wW:function(){return h},xW:function(){return v}});var r=n(807028);function i(t,e,n,r){var i=e[0],o=e[1],a=n[0],s=n[1],u=o-i,l=s-a;if(0===u)return 0===l?a:(a+s)/2;if(r){if(u>0){if(t<=i)return a;if(t>=o)return s}else{if(t>=i)return a;if(t<=o)return s}}else{if(t===i)return a;if(t===o)return s}return(t-i)/u*l+a}function o(t,e){switch(t){case"center":case"middle":t="50%";break;case"left":case"top":t="0%";break;case"right":case"bottom":t="100%"}if(r.HD(t))return t.replace(/^\s+|\s+$/g,"").match(/%$/)?parseFloat(t)/100*e:parseFloat(t);return null==t?NaN:+t}function a(t,e,n){return null==e&&(e=10),e=Math.min(Math.max(0,e),20),t=(+t).toFixed(e),n?t:+t}function s(t){return t.sort(function(t,e){return t-e}),t}function u(t){if(isNaN(t=+t))return 0;if(t>1e-14){for(var e=1,n=0;n<15;n++,e*=10)if(Math.round(t*e)/e===t)return n}return function(t){var e=t.toString().toLowerCase(),n=e.indexOf("e"),r=n>0?+e.slice(n+1):0,i=n>0?n:e.length,o=e.indexOf(".");return Math.max(0,(o<0?0:i-1-o)-r)}(t)}function l(t,e){var n=Math.log,r=Math.LN10,i=Math.min(Math.max(-Math.floor(n(t[1]-t[0])/r)+Math.round(n(Math.abs(e[1]-e[0]))/r),0),20);return isFinite(i)?i:20}function c(t,e){var n=Math.max(u(t),u(e)),r=t+e;return n>20?r:a(r,n)}function h(t){var e=2*Math.PI;return(t%e+e)%e}function f(t){return t>-.0001&&t<1e-4}var p=/^(?:(\d{4})(?:[-\/](\d{1,2})(?:[-\/](\d{1,2})(?:[T ](\d{1,2})(?::(\d{1,2})(?::(\d{1,2})(?:[.,](\d+))?)?)?(Z|[\+\-]\d\d:?\d\d)?)?)?)?)?$/;function d(t){if(t instanceof Date)return t;if(r.HD(t)){var e=p.exec(t);if(!e)return new Date(NaN);if(!e[8])return new Date(+e[1],+(e[2]||1)-1,+e[3]||1,+e[4]||0,+(e[5]||0),+e[6]||0,e[7]?+e[7].substring(0,3):0);var n=+e[4]||0;return"Z"!==e[8].toUpperCase()&&(n-=+e[8].slice(0,3)),new Date(Date.UTC(+e[1],+(e[2]||1)-1,+e[3]||1,n,+(e[5]||0),+e[6]||0,e[7]?+e[7].substring(0,3):0))}if(null==t)return new Date(NaN);return new Date(Math.round(t))}function g(t){return Math.pow(10,v(t))}function v(t){if(0===t)return 0;var e=Math.floor(Math.log(t)/Math.LN10);return t/Math.pow(10,e)>=10&&e++,e}function y(t,e){var n,r=v(t),i=Math.pow(10,r),o=t/i;return t=(n=e?o<1.5?1:o<2.5?2:o<4?3:o<7?5:10:o<1?1:o<2?2:o<3?3:o<5?5:10)*i,r>=-20?+t.toFixed(r<0?-r:0):t}function m(t){t.sort(function(t,e){return function t(e,n,r){return e.interval[r]<n.interval[r]||e.interval[r]===n.interval[r]&&(e.close[r]-n.close[r]==(r?-1:1)||!r&&t(e,n,1))}(t,e,0)?-1:1});for(var e=-1/0,n=1,r=0;r<t.length;){for(var i=t[r].interval,o=t[r].close,a=0;a<2;a++)i[a]<=e&&(i[a]=e,o[a]=a?1:1-n),e=i[a],n=o[a];i[0]===i[1]&&o[0]*o[1]!=1?t.splice(r,1):r++}return t}function _(t){var e=parseFloat(t);return e==t&&(0!==e||!r.HD(t)||0>=t.indexOf("x"))?e:NaN}function x(t){return!isNaN(_(t))}function w(){return Math.round(9*Math.random())}function S(t,e){return null==t?e:null==e?t:t*e/function t(e,n){return 0===n?e:t(n,e%n)}(t,e)}},623815:function(t,e,n){n.d(e,{$l:function(){return q},Av:function(){return to},C5:function(){return Q},CX:function(){return f},Gl:function(){return P},Hg:function(){return m},JQ:function(){return _},Ki:function(){return v},L1:function(){return d},Mh:function(){return E},Nj:function(){return ti},RW:function(){return ta},SJ:function(){return W},SX:function(){return B},T5:function(){return V},UL:function(){return X},VP:function(){return z},WO:function(){return tr},XX:function(){return F},aG:function(){return ts},ci:function(){return J},e9:function(){return tl},fD:function(){return N},iK:function(){return x},k5:function(){return tt},oJ:function(){return Y},og:function(){return j},qc:function(){return g},vF:function(){return $},wU:function(){return p},xp:function(){return tu},xr:function(){return K},yx:function(){return y},zI:function(){return G}});var r=n(807028),i=n(276868),o=n(529134),a=n(133141),s=n(894641),u=1,l={},c=(0,a.Yf)(),h=(0,a.Yf)(),f=1,p=2,d=["emphasis","blur","select"],g=["normal","emphasis","blur","select"],v="highlight",y="downplay",m="select",_="unselect",x="toggleSelect";function w(t){return null!=t&&"none"!==t}function S(t,e,n){t.onHoverStateChange&&(t.hoverState||0)!==n&&t.onHoverStateChange(e),t.hoverState=n}function b(t){S(t,"emphasis",p)}function M(t){t.hoverState===p&&S(t,"normal",0)}function T(t){S(t,"blur",f)}function k(t){t.hoverState===f&&S(t,"normal",0)}function D(t){t.selected=!0}function C(t){t.selected=!1}function I(t,e,n){e(t,n)}function A(t,e,n){e(t,n),t.isGroup&&t.traverse(function(t){e(t,n)})}function P(t,e){switch(e){case"emphasis":t.hoverState=p;break;case"normal":t.hoverState=0;break;case"blur":t.hoverState=f;break;case"select":t.selected=!0}}function Z(t,e){var n,i,a,u,l,h,f,p=this.states[t];if(this.style){if("emphasis"===t)return function(t,e,n,i){var a=n&&(0,r.cq)(n,"select")>=0,u=!1;if(t instanceof s.ZP){var l=c(t),h=a&&l.selectFill||l.normalFill,f=a&&l.selectStroke||l.normalStroke;if(w(h)||w(f)){var p=(i=i||{}).style||{};"inherit"===p.fill?(u=!0,i=(0,r.l7)({},i),(p=(0,r.l7)({},p)).fill=h):!w(p.fill)&&w(h)?(u=!0,i=(0,r.l7)({},i),(p=(0,r.l7)({},p)).fill=(0,o.fD)(h)):!w(p.stroke)&&w(f)&&(!u&&(i=(0,r.l7)({},i),p=(0,r.l7)({},p)),p.stroke=(0,o.fD)(f)),i.style=p}}if(i&&null==i.z2){!u&&(i=(0,r.l7)({},i));var d=t.z2EmphasisLift;i.z2=t.z2+(null!=d?d:10)}return i}(this,0,e,p);if("blur"===t){;return n=this,i=t,a=p,u=(0,r.cq)(n.currentStates,i)>=0,l=n.style.opacity,h=u?null:function(t,e,n,r){for(var i=t.style,o={},a=0;a<e.length;a++){var s=e[a],u=i[s];o[s]=null==u?r&&r[s]:u}for(var a=0;a<t.animators.length;a++){var l=t.animators[a];l.__fromStateTransition&&0>l.__fromStateTransition.indexOf(n)&&"style"===l.targetName&&l.saveTo(o,e)}return o}(n,["opacity"],i,{opacity:1}),null==(f=(a=a||{}).style||{}).opacity&&(a=(0,r.l7)({},a),f=(0,r.l7)({opacity:u?l:.1*h.opacity},f),a.style=f),a}else if("select"===t)return function(t,e,n){if(n&&null==n.z2){n=(0,r.l7)({},n);var i=t.z2SelectLift;n.z2=t.z2+(null!=i?i:9)}return n}(this,0,p)}return p}function L(t){t.stateProxy=Z;var e=t.getTextContent(),n=t.getTextGuideLine();e&&(e.stateProxy=Z),n&&(n.stateProxy=Z)}function O(t,e){H(t,e)||t.__highByOuter||A(t,b)}function R(t,e){H(t,e)||t.__highByOuter||A(t,M)}function N(t,e){t.__highByOuter|=1<<(e||0),A(t,b)}function E(t,e){(t.__highByOuter&=~(1<<(e||0)))||A(t,M)}function B(t){A(t,T)}function z(t){A(t,k)}function F(t){A(t,D)}function W(t){A(t,C)}function H(t,e){return t.__highDownSilentOnTouch&&e.zrByTouch}function V(t){var e=t.getModel(),n=[],i=[];e.eachComponent(function(e,r){var o=h(r),a="series"===e,s=a?t.getViewOfSeriesModel(r):t.getViewOfComponentModel(r);a||i.push(s),o.isBlured&&(s.group.traverse(function(t){k(t)}),a&&n.push(r)),o.isBlured=!1}),(0,r.S6)(i,function(t){t&&t.toggleBlurSeries&&t.toggleBlurSeries(n,!1,e)})}function U(t,e,n,i){var o=i.getModel();function a(t,e){for(var n=0;n<e.length;n++){var r=t.getItemGraphicEl(e[n]);r&&z(r)}}if(n=n||"coordinateSystem",null!=t&&!!e&&"none"!==e){var s=o.getSeriesByIndex(t),u=s.coordinateSystem;u&&u.master&&(u=u.master);var l=[];o.eachSeries(function(t){var o=s===t,c=t.coordinateSystem;c&&c.master&&(c=c.master);var f=c&&u?c===u:o;if(!("series"===n&&!o||"coordinateSystem"===n&&!f||"series"===e&&o)){if(i.getViewOfSeriesModel(t).group.traverse(function(t){if(!t.__highByOuter||!o||"self"!==e)T(t)}),(0,r.zG)(e))a(t.getData(),e);else if((0,r.Kn)(e)){for(var p=(0,r.XP)(e),d=0;d<p.length;d++)a(t.getData(p[d]),e[p[d]])}l.push(t),h(t).isBlured=!0}}),o.eachComponent(function(t,e){if("series"!==t){var n=i.getViewOfComponentModel(e);n&&n.toggleBlurSeries&&n.toggleBlurSeries(l,!0,o)}})}}function G(t,e,n){if(null==t||null==e)return;var r=n.getModel().getComponent(t,e);if(!r)return;h(r).isBlured=!0;var i=n.getViewOfComponentModel(r);if(!!i&&!!i.focusBlurEnabled)i.group.traverse(function(t){T(t)})}function X(t,e,n){var o=t.seriesIndex,s=t.getData(e.dataType);if(!!s){var u=(0,a.gO)(s,e);u=((0,r.kJ)(u)?u[0]:u)||0;var l=s.getItemGraphicEl(u);if(!l){for(var c=s.count(),h=0;!l&&h<c;)l=s.getItemGraphicEl(h++)}if(l){var f=(0,i.A)(l);U(o,f.focus,f.blurScope,n)}else{var p=t.get(["emphasis","focus"]),d=t.get(["emphasis","blurScope"]);null!=p&&U(o,p,d,n)}}}function Y(t,e,n,r){var o,a={focusSelf:!1,dispatchers:null};if(null==t||"series"===t||null==e||null==n)return a;var s=r.getModel().getComponent(t,e);if(!s)return a;var u=r.getViewOfComponentModel(s);if(!u||!u.findHighDownDispatchers)return a;for(var l=u.findHighDownDispatchers(n),c=0;c<l.length;c++)if("self"===(0,i.A)(l[c]).focus){o=!0;break}return{focusSelf:o,dispatchers:l}}function q(t,e,n){var o=(0,i.A)(t),a=Y(o.componentMainType,o.componentIndex,o.componentHighDownName,n),s=a.dispatchers,u=a.focusSelf;s?(u&&G(o.componentMainType,o.componentIndex,n),(0,r.S6)(s,function(t){return O(t,e)})):(U(o.seriesIndex,o.focus,o.blurScope,n),"self"===o.focus&&G(o.componentMainType,o.componentIndex,n),O(t,e))}function K(t,e,n){V(n);var o=(0,i.A)(t),a=Y(o.componentMainType,o.componentIndex,o.componentHighDownName,n).dispatchers;a?(0,r.S6)(a,function(t){return R(t,e)}):R(t,e)}function j(t,e,n){if(!!ts(e)){var i=e.dataType,o=t.getData(i),s=(0,a.gO)(o,e);!(0,r.kJ)(s)&&(s=[s]),t[e.type===x?"toggleSelect":e.type===m?"select":"unselect"](s,i)}}function J(t){var e=t.getAllData();(0,r.S6)(e,function(e){var n=e.data,r=e.type;n.eachItemGraphicEl(function(e,n){t.isSelected(n,r)?F(e):W(e)})})}function Q(t){var e=[];return t.eachSeries(function(t){var n=t.getAllData();(0,r.S6)(n,function(n){n.data;var r=n.type,i=t.getSelectedDataIndices();if(i.length>0){var o={dataIndex:i,seriesIndex:t.seriesIndex};null!=r&&(o.dataType=r),e.push(o)}})}),e}function $(t,e,n){ti(t,!0),A(t,L),function(t,e,n){var r=(0,i.A)(t);null!=e?(r.focus=e,r.blurScope=n):r.focus&&(r.focus=null)}(t,e,n)}function tt(t,e,n,r){r?ti(t,!1):$(t,e,n)}var te=["emphasis","blur","select"],tn={itemStyle:"getItemStyle",lineStyle:"getLineStyle",areaStyle:"getAreaStyle"};function tr(t,e,n,r){n=n||"itemStyle";for(var i=0;i<te.length;i++){var o=te[i],a=e.getModel([o,n]);t.ensureState(o).style=r?r(a):a[tn[n]]()}}function ti(t,e){var n=!1===e;t.highDownSilentOnTouch&&(t.__highDownSilentOnTouch=t.highDownSilentOnTouch),(!n||t.__highDownDispatcher)&&(t.__highByOuter=t.__highByOuter||0,t.__highDownDispatcher=!n)}function to(t){return!!(t&&t.__highDownDispatcher)}function ta(t){var e=l[t];return null==e&&u<=32&&(e=l[t]=u++),e}function ts(t){var e=t.type;return e===m||e===_||e===x}function tu(t){var e=t.type;return e===v||e===y}function tl(t){var e=c(t);e.normalFill=t.style.fill,e.normalStroke=t.style.stroke;var n=t.states.select||{};e.selectFill=n.style&&n.style.fill||null,e.selectStroke=n.style&&n.style.stroke||null}},19750:function(t,e,n){n.d(e,{Cq:function(){return b},th:function(){return w},zp:function(){return S}});var r=n(807028),i=n(894641),o=n(798383),a=n(406822),s=n(418819),u=n(339738),l=n(339296),c=n(310123),h=n(31674),f=i.ZP.extend({type:"triangle",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,r=e.cy,i=e.width/2,o=e.height/2;t.moveTo(n,r-o),t.lineTo(n+i,r+o),t.lineTo(n-i,r+o),t.closePath()}}),p=i.ZP.extend({type:"diamond",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,r=e.cy,i=e.width/2,o=e.height/2;t.moveTo(n,r-o),t.lineTo(n+i,r),t.lineTo(n,r+o),t.lineTo(n-i,r),t.closePath()}}),d=i.ZP.extend({type:"pin",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.x,r=e.y,i=e.width/5*3,o=Math.max(i,e.height),a=i/2,s=a*a/(o-a),u=r-o+a+s,l=Math.asin(s/a),c=Math.cos(l)*a,h=Math.sin(l),f=Math.cos(l),p=.6*a,d=.7*a;t.moveTo(n-c,u+s),t.arc(n,u,a,Math.PI-l,2*Math.PI+l),t.bezierCurveTo(n+c-h*p,u+s+f*p,n,r-d,n,r),t.bezierCurveTo(n,r-d,n-c+h*p,u+s+f*p,n-c,u+s),t.closePath()}}),g=i.ZP.extend({type:"arrow",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.height,r=e.width,i=e.x,o=e.y,a=r/3*2;t.moveTo(i,o),t.lineTo(i+a,o+n),t.lineTo(i,o+n/4*3),t.lineTo(i-a,o+n),t.lineTo(i,o),t.closePath()}}),v={line:o.Z,rect:a.Z,roundRect:a.Z,square:a.Z,circle:s.Z,diamond:p,pin:d,arrow:g,triangle:f},y={line:function(t,e,n,r,i){i.x1=t,i.y1=e+r/2,i.x2=t+n,i.y2=e+r/2},rect:function(t,e,n,r,i){i.x=t,i.y=e,i.width=n,i.height=r},roundRect:function(t,e,n,r,i){i.x=t,i.y=e,i.width=n,i.height=r,i.r=Math.min(n,r)/4},square:function(t,e,n,r,i){var o=Math.min(n,r);i.x=t,i.y=e,i.width=o,i.height=o},circle:function(t,e,n,r,i){i.cx=t+n/2,i.cy=e+r/2,i.r=Math.min(n,r)/2},diamond:function(t,e,n,r,i){i.cx=t+n/2,i.cy=e+r/2,i.width=n,i.height=r},pin:function(t,e,n,r,i){i.x=t+n/2,i.y=e+r/2,i.width=n,i.height=r},arrow:function(t,e,n,r,i){i.x=t+n/2,i.y=e+r/2,i.width=n,i.height=r},triangle:function(t,e,n,r,i){i.cx=t+n/2,i.cy=e+r/2,i.width=n,i.height=r}},m={};(0,r.S6)(v,function(t,e){m[e]=new t});var _=i.ZP.extend({type:"symbol",shape:{symbolType:"",x:0,y:0,width:0,height:0},calculateTextPosition:function(t,e,n){var r=(0,c.wI)(t,e,n),i=this.shape;return i&&"pin"===i.symbolType&&"inside"===e.position&&(r.y=n.y+.4*n.height),r},buildPath:function(t,e,n){var r=e.symbolType;if("none"!==r){var i=m[r];!i&&(i=m[r="rect"]),y[r](e.x,e.y,e.width,e.height,i.shape),i.buildPath(t,i.shape,n)}}});function x(t,e){if("image"!==this.type){var n=this.style;this.__isEmptyBrush?(n.stroke=t,n.fill=e||"#fff",n.lineWidth=2):"line"===this.shape.symbolType?n.stroke=t:n.fill=t,this.markRedraw()}}function w(t,e,n,r,i,o,a){var s,c=0===t.indexOf("empty");return c&&(t=t.substr(5,1).toLowerCase()+t.substr(6)),(s=0===t.indexOf("image://")?u.makeImage(t.slice(8),new l.Z(e,n,r,i),a?"center":"cover"):0===t.indexOf("path://")?u.makePath(t.slice(7),{},new l.Z(e,n,r,i),a?"center":"cover"):new _({shape:{symbolType:t,x:e,y:n,width:r,height:i}})).__isEmptyBrush=c,s.setColor=x,o&&s.setColor(o),s}function S(t){return!(0,r.kJ)(t)&&(t=[+t,+t]),[t[0]||0,t[1]||0]}function b(t,e){if(null!=t)return!(0,r.kJ)(t)&&(t=[t,t]),[(0,h.GM)(t[0],e[0])||0,(0,h.GM)((0,r.pD)(t[1],t[0]),e[1])||0]}},71564:function(t,e,n){n.d(e,{P2:function(){return a},T9:function(){return s},ZH:function(){return u}});var r="\0__throttleOriginMethod",i="\0__throttleRate",o="\0__throttleType";function a(t,e,n){var r,i,o,a,s,u=0,l=0,c=null;function h(){l=new Date().getTime(),c=null,t.apply(o,a||[])}e=e||0;var f=function(){for(var t=[],f=0;f<arguments.length;f++)t[f]=arguments[f];r=new Date().getTime(),o=this,a=t;var p=s||e,d=s||n;s=null,i=r-(d?u:l)-p,clearTimeout(c),d?c=setTimeout(h,p):i>=0?h():c=setTimeout(h,-i),u=r};return f.clear=function(){c&&(clearTimeout(c),c=null)},f.debounceNextCall=function(t){s=t},f}function s(t,e,n,s){var u=t[e];if(!!u){var l=u[r]||u,c=u[o];if(u[i]!==n||c!==s){if(null==n||!s)return t[e]=l;(u=t[e]=a(l,n,"debounce"===s))[r]=l,u[o]=s,u[i]=n}return u}}function u(t,e){var n=t[e];n&&n[r]&&(n.clear&&n.clear(),t[e]=n[r])}},744829:function(t,e,n){n.d(e,{$K:function(){return _},CW:function(){return k},En:function(){return R},FW:function(){return v},MV:function(){return A},P5:function(){return h},RZ:function(){return P},Tj:function(){return m},V8:function(){return d},WT:function(){return s},WU:function(){return w},Wp:function(){return C},cb:function(){return B},dV:function(){return l},eN:function(){return N},f5:function(){return O},fn:function(){return I},k7:function(){return S},q5:function(){return M},rM:function(){return E},s2:function(){return c},sx:function(){return T},vh:function(){return L},xC:function(){return x},xL:function(){return Z},xz:function(){return D},yR:function(){return u}});var r=n(807028),i=n(31674),o=n(765814),a=n(954069),s=1e3,u=6e4,l=36e5,c=864e5,h=31536e6,f={year:"{yyyy}",month:"{MMM}",day:"{d}",hour:"{HH}:{mm}",minute:"{HH}:{mm}",second:"{HH}:{mm}:{ss}",millisecond:"{HH}:{mm}:{ss} {SSS}",none:"{yyyy}-{MM}-{dd} {HH}:{mm}:{ss} {SSS}"},p="{yyyy}-{MM}-{dd}",d={year:"{yyyy}",month:"{yyyy}-{MM}",day:p,hour:p+" "+f.hour,minute:p+" "+f.minute,second:p+" "+f.second,millisecond:f.none},g=["year","month","day","hour","minute","second","millisecond"],v=["year","half-year","quarter","month","week","half-week","day","half-day","quarter-day","hour","minute","second","millisecond"];function y(t,e){return t+="","0000".substr(0,e-t.length)+t}function m(t){switch(t){case"half-year":case"quarter":return"month";case"week":case"half-week":return"day";case"half-day":case"quarter-day":return"hour";default:return t}}function _(t){return t===m(t)}function x(t){switch(t){case"year":case"month":return"day";case"millisecond":return"millisecond";default:return"second"}}function w(t,e,n,r){var s=i.sG(t),u=s[T(n)](),l=s[k(n)]()+1,c=Math.floor((l-1)/3)+1,h=s[D(n)](),f=s["get"+(n?"UTC":"")+"Day"](),p=s[C(n)](),d=(p-1)%12+1,g=s[I(n)](),v=s[A(n)](),m=s[P(n)](),_=p>=12?"pm":"am",x=_.toUpperCase(),w=(r instanceof a.Z?r:(0,o.G8)(r||o.sO)||(0,o.Li)()).getModel("time"),S=w.get("month"),b=w.get("monthAbbr"),M=w.get("dayOfWeek"),Z=w.get("dayOfWeekAbbr");return(e||"").replace(/{a}/g,_+"").replace(/{A}/g,x+"").replace(/{yyyy}/g,u+"").replace(/{yy}/g,y(u%100+"",2)).replace(/{Q}/g,c+"").replace(/{MMMM}/g,S[l-1]).replace(/{MMM}/g,b[l-1]).replace(/{MM}/g,y(l,2)).replace(/{M}/g,l+"").replace(/{dd}/g,y(h,2)).replace(/{d}/g,h+"").replace(/{eeee}/g,M[f]).replace(/{ee}/g,Z[f]).replace(/{e}/g,f+"").replace(/{HH}/g,y(p,2)).replace(/{H}/g,p+"").replace(/{hh}/g,y(d+"",2)).replace(/{h}/g,d+"").replace(/{mm}/g,y(g,2)).replace(/{m}/g,g+"").replace(/{ss}/g,y(v,2)).replace(/{s}/g,v+"").replace(/{SSS}/g,y(m,3)).replace(/{S}/g,m+"")}function S(t,e,n,i,o){var a=null;if(r.HD(n))a=n;else if(r.mf(n))a=n(t.value,e,{level:t.level});else{var s=r.l7({},f);if(t.level>0)for(var u=0;u<g.length;++u)s[g[u]]="{primary|"+s[g[u]]+"}";var l=n?!1===n.inherit?n:r.ce(n,s):s,c=b(t.value,o);if(l[c])a=l[c];else if(l.inherit){for(var h=v.indexOf(c),u=h-1;u>=0;--u)if(l[c]){a=l[c];break}a=a||s.none}if(r.kJ(a)){var p=null==t.level?0:t.level>=0?t.level:a.length+t.level;p=Math.min(p,a.length-1),a=a[p]}}return w(new Date(t.value),a,o,i)}function b(t,e){var n=i.sG(t),r=n[k(e)]()+1,o=n[D(e)](),a=n[C(e)](),s=n[I(e)](),u=n[A(e)](),l=0===n[P(e)](),c=l&&0===u,h=c&&0===s,f=h&&0===a,p=f&&1===o;if(p&&1===r)return"year";if(p)return"month";if(f)return"day";else if(h)return"hour";else if(c)return"minute";else if(l)return"second";else return"millisecond"}function M(t,e,n){var o=r.hj(t)?i.sG(t):t;switch(e=e||b(t,n)){case"year":return o[T(n)]();case"half-year":return o[k(n)]()>=6?1:0;case"quarter":return Math.floor((o[k(n)]()+1)/4);case"month":return o[k(n)]();case"day":return o[D(n)]();case"half-day":return o[C(n)]()/24;case"hour":return o[C(n)]();case"minute":return o[I(n)]();case"second":return o[A(n)]();case"millisecond":return o[P(n)]()}}function T(t){return t?"getUTCFullYear":"getFullYear"}function k(t){return t?"getUTCMonth":"getMonth"}function D(t){return t?"getUTCDate":"getDate"}function C(t){return t?"getUTCHours":"getHours"}function I(t){return t?"getUTCMinutes":"getMinutes"}function A(t){return t?"getUTCSeconds":"getSeconds"}function P(t){return t?"getUTCMilliseconds":"getMilliseconds"}function Z(t){return t?"setUTCFullYear":"setFullYear"}function L(t){return t?"setUTCMonth":"setMonth"}function O(t){return t?"setUTCDate":"setDate"}function R(t){return t?"setUTCHours":"setHours"}function N(t){return t?"setUTCMinutes":"setMinutes"}function E(t){return t?"setUTCSeconds":"setSeconds"}function B(t){return t?"setUTCMilliseconds":"setMilliseconds"}},894870:function(t,e,n){n.d(e,{J5:function(){return u},RA:function(){return l},Wc:function(){return h},XD:function(){return o},cy:function(){return i},f7:function(){return r},fY:function(){return c},hL:function(){return s},qb:function(){return a}});var r=(0,n(807028).kW)(["tooltip","label","itemName","itemId","itemGroupId","itemChildGroupId","seriesName"]),i="original",o="arrayRows",a="objectRows",s="keyedColumns",u="typedArray",l="unknown",c="column",h="row"},901843:function(t,e,n){n.d(e,{o:function(){return a}});var r=n(807028),i="undefined"!=typeof Float32Array,o=i?Float32Array:Array;function a(t){return(0,r.kJ)(t)?i?new Float32Array(t):t:new o(t)}},804575:function(t,e,n){var r=n(807028),i=n(707498),o=n(115856),a=n(731181),s=n(133141),u=n(623815),l=n(648446),c=n(685043),h=n(339738),f=s.Yf(),p=(0,c.Z)(),d=function(){function t(){this.group=new i.Z,this.uid=o.Kr("viewChart"),this.renderTask=(0,l.v)({plan:y,reset:m}),this.renderTask.context={view:this}}return t.prototype.init=function(t,e){},t.prototype.render=function(t,e,n,r){},t.prototype.highlight=function(t,e,n,r){var i=t.getData(r&&r.dataType);if(!!i)v(i,r,"emphasis")},t.prototype.downplay=function(t,e,n,r){var i=t.getData(r&&r.dataType);if(!!i)v(i,r,"normal")},t.prototype.remove=function(t,e){this.group.removeAll()},t.prototype.dispose=function(t,e){},t.prototype.updateView=function(t,e,n,r){this.render(t,e,n,r)},t.prototype.updateLayout=function(t,e,n,r){this.render(t,e,n,r)},t.prototype.updateVisual=function(t,e,n,r){this.render(t,e,n,r)},t.prototype.eachRendered=function(t){(0,h.traverseElements)(this.group,t)},t.markUpdateMethod=function(t,e){f(t).updateMethod=e},t.protoInitialize=void(t.prototype.type="chart"),t}();function g(t,e,n){t&&(0,u.Av)(t)&&("emphasis"===e?u.fD:u.Mh)(t,n)}function v(t,e,n){var i=s.gO(t,e),o=e&&null!=e.highlightKey?(0,u.RW)(e.highlightKey):null;null!=i?(0,r.S6)(s.kF(i),function(e){g(t.getItemGraphicEl(e),n,o)}):t.eachItemGraphicEl(function(t){g(t,n,o)})}function y(t){return p(t.model)}function m(t){var e=t.model,n=t.ecModel,r=t.api,i=t.payload,o=e.pipelineContext.progressiveRender,a=t.view,s=i&&f(i).updateMethod,u=o?"incrementalPrepareRender":s&&a[s]?s:"render";return"render"!==u&&a[u](e,n,r,i),_[u]}a.dm(d,["dispose"]),a.au(d);var _={incrementalPrepareRender:{progress:function(t,e){e.view.incrementalRender(t,e.model,e.ecModel,e.api,e.payload)}},render:{forceFirstProgress:!0,progress:function(t,e){e.view.render(e.model,e.ecModel,e.api,e.payload)}}};e.Z=d},860736:function(t,e,n){var r=n(707498),i=n(115856),o=n(731181),a=function(){function t(){this.group=new r.Z,this.uid=i.Kr("viewComponent")}return t.prototype.init=function(t,e){},t.prototype.render=function(t,e,n,r){},t.prototype.dispose=function(t,e){},t.prototype.updateView=function(t,e,n,r){},t.prototype.updateLayout=function(t,e,n,r){},t.prototype.updateVisual=function(t,e,n,r){},t.prototype.toggleBlurSeries=function(t,e,n){},t.prototype.eachRendered=function(t){var e=this.group;e&&e.traverse(t)},t}();o.dm(a),o.au(a),e.Z=a},434975:function(t,e,n){n.d(e,{Z:function(){return i}});var r=n(973724);function i(t,e){t.eachRawSeries(function(n){if(!t.isSeriesFiltered(n)){var i=n.getData();i.hasItemVisual()&&i.each(function(t){var n=i.getItemVisual(t,"decal");n&&(i.ensureUniqueItemVisual(t,"style").decal=(0,r.I)(n,e))});var o=i.getVisual("decal");o&&(i.getVisual("style").decal=(0,r.I)(o,e))}})}},816691:function(t,e,n){function r(t,e,n){switch(n){case"color":return t.getItemVisual(e,"style")[t.getVisual("drawType")];case"opacity":return t.getItemVisual(e,"style").opacity;case"symbol":case"symbolSize":case"liftZ":return t.getItemVisual(e,n)}}function i(t,e){switch(e){case"color":return t.getVisual("style")[t.getVisual("drawType")];case"opacity":return t.getVisual("style").opacity;case"symbol":case"symbolSize":case"liftZ":return t.getVisual(e)}}function o(t,e,n,r){switch(n){case"color":t.ensureUniqueItemVisual(e,"style")[t.getVisual("drawType")]=r,t.setItemVisual(e,"colorFromPalette",!1);break;case"opacity":t.ensureUniqueItemVisual(e,"style").opacity=r;break;case"symbol":case"symbolSize":case"liftZ":t.setItemVisual(e,n,r)}}n.d(e,{LZ:function(){return o},Or:function(){return r},UL:function(){return i}})},438955:function(t,e,n){n.d(e,{$P:function(){return v},TD:function(){return p},Tn:function(){return g}});var r=n(807028),i=n(751827),o=n(34312),a=n(969570),s=n(954069),u=(0,n(133141).Yf)(),l={itemStyle:(0,i.Z)(o.t,!0),lineStyle:(0,i.Z)(a.v,!0)},c={lineStyle:"stroke",itemStyle:"fill"};function h(t,e){var n=t.visualStyleMapper||l[e];return n?n:(console.warn("Unknown style type '"+e+"'."),l.itemStyle)}function f(t,e){var n=t.visualDrawType||c[e];return n?n:(console.warn("Unknown style type '"+e+"'."),"fill")}var p={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){var n=t.getData(),i=t.visualStyleAccessPath||"itemStyle",o=t.getModel(i),a=h(t,i)(o),s=o.getShallow("decal");s&&(n.setVisual("decal",s),s.dirty=!0);var u=f(t,i),l=a[u],c=(0,r.mf)(l)?l:null,p="auto"===a.fill||"auto"===a.stroke;if(!a[u]||c||p){var d=t.getColorFromPalette(t.name,null,e.getSeriesCount());!a[u]&&(a[u]=d,n.setVisual("colorFromPalette",!0)),a.fill="auto"===a.fill||(0,r.mf)(a.fill)?d:a.fill,a.stroke="auto"===a.stroke||(0,r.mf)(a.stroke)?d:a.stroke}if(n.setVisual("style",a),n.setVisual("drawType",u),!e.isSeriesFiltered(t)&&c)return n.setVisual("colorFromPalette",!1),{dataEach:function(e,n){var i=t.getDataParams(n),o=(0,r.l7)({},a);o[u]=c(i),e.setItemVisual(n,"style",o)}}}},d=new s.Z,g={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){if(!(t.ignoreStyleOnData||e.isSeriesFiltered(t))){var n=t.getData(),i=t.visualStyleAccessPath||"itemStyle",o=h(t,i),a=n.getVisual("drawType");return{dataEach:n.hasItemOption?function(t,e){var n=t.getRawDataItem(e);if(n&&n[i]){d.option=n[i];var s=o(d),u=t.ensureUniqueItemVisual(e,"style");(0,r.l7)(u,s),d.option.decal&&(t.setItemVisual(e,"decal",d.option.decal),d.option.decal.dirty=!0),a in s&&t.setItemVisual(e,"colorFromPalette",!1)}}:null}}}},v={performRawSeries:!0,overallReset:function(t){var e=(0,r.kW)();t.eachSeries(function(t){var n=t.getColorBy();if(!t.isColorBySeries()){var r=t.type+"-"+n,i=e.get(r);!i&&(i={},e.set(r,i)),u(t).scope=i}}),t.eachSeries(function(e){if(!(e.isColorBySeries()||t.isSeriesFiltered(e))){var n=e.getRawData(),r={},i=e.getData(),o=u(e).scope,a=e.visualStyleAccessPath||"itemStyle",s=f(e,a);i.each(function(t){r[i.getRawIndex(t)]=t}),n.each(function(t){var a=r[t];if(i.getItemVisual(a,"colorFromPalette")){var u=i.ensureUniqueItemVisual(a,"style"),l=n.getName(t)||t+"",c=n.count();u[s]=e.getColorFromPalette(l,o,c)}})}})}}},398898:function(t,e,n){n.d(e,{m:function(){return s},n:function(){return a}});var r=n(807028),i=["symbol","symbolSize","symbolRotate","symbolOffset"],o=i.concat(["symbolKeepAspect"]),a={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){var n=t.getData();if(t.legendIcon&&n.setVisual("legendIcon",t.legendIcon),!t.hasSymbolVisual)return;for(var o={},a={},s=!1,u=0;u<i.length;u++){var l=i[u],c=t.get(l);(0,r.mf)(c)?(s=!0,a[l]=c):o[l]=c}if(o.symbol=o.symbol||t.defaultSymbol,n.setVisual((0,r.l7)({legendIcon:t.legendIcon||o.symbol,symbolKeepAspect:t.get("symbolKeepAspect")},o)),!e.isSeriesFiltered(t)){var h=(0,r.XP)(a);return{dataEach:s?function(e,n){for(var r=t.getRawValue(n),i=t.getDataParams(n),o=0;o<h.length;o++){var s=h[o];e.setItemVisual(n,s,a[s](r,i))}}:null}}}},s={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){if(!(!t.hasSymbolVisual||e.isSeriesFiltered(t))){var n=t.getData();return{dataEach:n.hasItemOption?function(t,e){for(var n=t.getItemModel(e),r=0;r<o.length;r++){var i=o[r],a=n.getShallow(i,!0);null!=a&&t.setItemVisual(e,i,a)}}:null}}}}},674194:function(t,e,n){var r=n(762680),i=n(336692),o=n(339296),a=n(367582),s=n(310123),u=n(807028),l=n(113525),c=n(529134),h=n(360577),f="__zr_normal__",p=r.dN.concat(["ignore"]),d=(0,u.u4)(r.dN,function(t,e){return t[e]=!0,t},{ignore:!1}),g={},v=new o.Z(0,0,0,0),y=function(){function t(t){this.id=(0,u.M8)(),this.animators=[],this.currentStates=[],this.states={},this._init(t)}return t.prototype._init=function(t){this.attr(t)},t.prototype.drift=function(t,e,n){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0}var r=this.transform;!r&&(r=this.transform=[1,0,0,1,0,0]),r[4]+=t,r[5]+=e,this.decomposeTransform(),this.markRedraw()},t.prototype.beforeUpdate=function(){},t.prototype.afterUpdate=function(){},t.prototype.update=function(){this.updateTransform(),this.__dirty&&this.updateInnerText()},t.prototype.updateInnerText=function(t){var e=this._textContent;if(e&&(!e.ignore||t)){!this.textConfig&&(this.textConfig={});var n=this.textConfig,r=n.local,i=e.innerTransformable,o=void 0,a=void 0,u=!1;i.parent=r?this:null;var l=!1;if(i.copyTransform(e),null!=n.position){n.layoutRect?v.copy(n.layoutRect):v.copy(this.getBoundingRect()),!r&&v.applyTransform(this.transform),this.calculateTextPosition?this.calculateTextPosition(g,n,v):(0,s.wI)(g,n,v),i.x=g.x,i.y=g.y,o=g.align,a=g.verticalAlign;var c=n.origin;if(c&&null!=n.rotation){var f=void 0,p=void 0;"center"===c?(f=.5*v.width,p=.5*v.height):(f=(0,s.GM)(c[0],v.width),p=(0,s.GM)(c[1],v.height)),l=!0,i.originX=-i.x+f+(r?0:v.x),i.originY=-i.y+p+(r?0:v.y)}}null!=n.rotation&&(i.rotation=n.rotation);var d=n.offset;d&&(i.x+=d[0],i.y+=d[1],!l&&(i.originX=-d[0],i.originY=-d[1]));var y=null==n.inside?"string"==typeof n.position&&n.position.indexOf("inside")>=0:n.inside,m=this._innerTextDefaultStyle||(this._innerTextDefaultStyle={}),_=void 0,x=void 0,w=void 0;y&&this.canBeInsideText()?(_=n.insideFill,x=n.insideStroke,(null==_||"auto"===_)&&(_=this.getInsideTextFill()),(null==x||"auto"===x)&&(x=this.getInsideTextStroke(_),w=!0)):(_=n.outsideFill,x=n.outsideStroke,(null==_||"auto"===_)&&(_=this.getOutsideFill()),(null==x||"auto"===x)&&(x=this.getOutsideStroke(_),w=!0)),((_=_||"#000")!==m.fill||x!==m.stroke||w!==m.autoStroke||o!==m.align||a!==m.verticalAlign)&&(u=!0,m.fill=_,m.stroke=x,m.autoStroke=w,m.align=o,m.verticalAlign=a,e.setDefaultTextStyle(m)),e.__dirty|=h.YV,u&&e.dirtyStyle(!0)}},t.prototype.canBeInsideText=function(){return!0},t.prototype.getInsideTextFill=function(){return"#fff"},t.prototype.getInsideTextStroke=function(t){return"#000"},t.prototype.getOutsideFill=function(){return this.__zr&&this.__zr.isDarkMode()?l.GD:l.vU},t.prototype.getOutsideStroke=function(t){var e=this.__zr&&this.__zr.getBackgroundColor(),n="string"==typeof e&&(0,c.Qc)(e);!n&&(n=[255,255,255,1]);for(var r=n[3],i=this.__zr.isDarkMode(),o=0;o<3;o++)n[o]=n[o]*r+(i?0:255)*(1-r);return n[3]=1,(0,c.Pz)(n,"rgba")},t.prototype.traverse=function(t,e){},t.prototype.attrKV=function(t,e){"textConfig"===t?this.setTextConfig(e):"textContent"===t?this.setTextContent(e):"clipPath"===t?this.setClipPath(e):"extra"===t?(this.extra=this.extra||{},(0,u.l7)(this.extra,e)):this[t]=e},t.prototype.hide=function(){this.ignore=!0,this.markRedraw()},t.prototype.show=function(){this.ignore=!1,this.markRedraw()},t.prototype.attr=function(t,e){if("string"==typeof t)this.attrKV(t,e);else if((0,u.Kn)(t)){for(var n=(0,u.XP)(t),r=0;r<n.length;r++){var i=n[r];this.attrKV(i,t[i])}}return this.markRedraw(),this},t.prototype.saveCurrentToNormalState=function(t){this._innerSaveToNormal(t);for(var e=this._normalState,n=0;n<this.animators.length;n++){var r=this.animators[n],i=r.__fromStateTransition;if(!r.getLoop()&&(!i||i===f)){var o=r.targetName,a=o?e[o]:e;r.saveTo(a)}}},t.prototype._innerSaveToNormal=function(t){var e=this._normalState;!e&&(e=this._normalState={}),t.textConfig&&!e.textConfig&&(e.textConfig=this.textConfig),this._savePrimaryToNormal(t,e,p)},t.prototype._savePrimaryToNormal=function(t,e,n){for(var r=0;r<n.length;r++){var i=n[r];null!=t[i]&&!(i in e)&&(e[i]=this[i])}},t.prototype.hasState=function(){return this.currentStates.length>0},t.prototype.getState=function(t){return this.states[t]},t.prototype.ensureState=function(t){var e=this.states;return!e[t]&&(e[t]={}),e[t]},t.prototype.clearStates=function(t){this.useState(f,!1,t)},t.prototype.useState=function(t,e,n,r){var i,o=t===f;if(!this.hasState()&&o)return;var a=this.currentStates,s=this.stateTransition;if(!((0,u.cq)(a,t)>=0)||!e&&1!==a.length){if(this.stateProxy&&!o&&(i=this.stateProxy(t)),!i&&(i=this.states&&this.states[t]),!i&&!o){(0,u.H)("State "+t+" not exists.");return}!o&&this.saveCurrentToNormalState(i);var l=!!(i&&i.hoverLayer||r);l&&this._toggleHoverLayerFlag(!0),this._applyStateObj(t,i,this._normalState,e,!n&&!this.__inHover&&s&&s.duration>0,s);var c=this._textContent,p=this._textGuide;return c&&c.useState(t,e,n,l),p&&p.useState(t,e,n,l),o?(this.currentStates=[],this._normalState={}):e?this.currentStates.push(t):this.currentStates=[t],this._updateAnimationTargets(),this.markRedraw(),!l&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~h.YV),i}},t.prototype.useStates=function(t,e,n){if(t.length){var r=[],i=this.currentStates,o=t.length,a=o===i.length;if(a){for(var s=0;s<o;s++)if(t[s]!==i[s]){a=!1;break}}if(!a){for(var s=0;s<o;s++){var u=t[s],l=void 0;this.stateProxy&&(l=this.stateProxy(u,t)),!l&&(l=this.states[u]),l&&r.push(l)}var c=r[o-1],f=!!(c&&c.hoverLayer||n);f&&this._toggleHoverLayerFlag(!0);var p=this._mergeStates(r),d=this.stateTransition;this.saveCurrentToNormalState(p),this._applyStateObj(t.join(","),p,this._normalState,!1,!e&&!this.__inHover&&d&&d.duration>0,d);var g=this._textContent,v=this._textGuide;g&&g.useStates(t,e,f),v&&v.useStates(t,e,f),this._updateAnimationTargets(),this.currentStates=t.slice(),this.markRedraw(),!f&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~h.YV)}}else this.clearStates()},t.prototype.isSilent=function(){for(var t=this.silent,e=this.parent;!t&&e;){if(e.silent){t=!0;break}e=e.parent}return t},t.prototype._updateAnimationTargets=function(){for(var t=0;t<this.animators.length;t++){var e=this.animators[t];e.targetName&&e.changeTarget(this[e.targetName])}},t.prototype.removeState=function(t){var e=(0,u.cq)(this.currentStates,t);if(e>=0){var n=this.currentStates.slice();n.splice(e,1),this.useStates(n)}},t.prototype.replaceState=function(t,e,n){var r=this.currentStates.slice(),i=(0,u.cq)(r,t),o=(0,u.cq)(r,e)>=0;i>=0?o?r.splice(i,1):r[i]=e:n&&!o&&r.push(e),this.useStates(r)},t.prototype.toggleState=function(t,e){e?this.useState(t,!0):this.removeState(t)},t.prototype._mergeStates=function(t){for(var e,n={},r=0;r<t.length;r++){var i=t[r];(0,u.l7)(n,i),i.textConfig&&(e=e||{},(0,u.l7)(e,i.textConfig))}return e&&(n.textConfig=e),n},t.prototype._applyStateObj=function(t,e,n,r,i,o){var a=!(e&&r);e&&e.textConfig?(this.textConfig=(0,u.l7)({},r?this.textConfig:n.textConfig),(0,u.l7)(this.textConfig,e.textConfig)):a&&n.textConfig&&(this.textConfig=n.textConfig);for(var s={},l=!1,c=0;c<p.length;c++){var h=p[c],f=i&&d[h];e&&null!=e[h]?f?(l=!0,s[h]=e[h]):this[h]=e[h]:a&&null!=n[h]&&(f?(l=!0,s[h]=n[h]):this[h]=n[h])}if(!i)for(var c=0;c<this.animators.length;c++){var g=this.animators[c],v=g.targetName;!g.getLoop()&&g.__changeFinalValue(v?(e||n)[v]:e||n)}l&&this._transitionState(t,s,o)},t.prototype._attachComponent=function(t){if((!t.__zr||!!t.__hostTarget)&&t!==this){var e=this.__zr;e&&t.addSelfToZr(e),t.__zr=e,t.__hostTarget=this}},t.prototype._detachComponent=function(t){t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__hostTarget=null},t.prototype.getClipPath=function(){return this._clipPath},t.prototype.setClipPath=function(t){this._clipPath&&this._clipPath!==t&&this.removeClipPath(),this._attachComponent(t),this._clipPath=t,this.markRedraw()},t.prototype.removeClipPath=function(){var t=this._clipPath;t&&(this._detachComponent(t),this._clipPath=null,this.markRedraw())},t.prototype.getTextContent=function(){return this._textContent},t.prototype.setTextContent=function(t){var e=this._textContent;if(e!==t){e&&e!==t&&this.removeTextContent();t.innerTransformable=new r.ZP,this._attachComponent(t),this._textContent=t,this.markRedraw()}},t.prototype.setTextConfig=function(t){!this.textConfig&&(this.textConfig={}),(0,u.l7)(this.textConfig,t),this.markRedraw()},t.prototype.removeTextConfig=function(){this.textConfig=null,this.markRedraw()},t.prototype.removeTextContent=function(){var t=this._textContent;t&&(t.innerTransformable=null,this._detachComponent(t),this._textContent=null,this._innerTextDefaultStyle=null,this.markRedraw())},t.prototype.getTextGuideLine=function(){return this._textGuide},t.prototype.setTextGuideLine=function(t){this._textGuide&&this._textGuide!==t&&this.removeTextGuideLine(),this._attachComponent(t),this._textGuide=t,this.markRedraw()},t.prototype.removeTextGuideLine=function(){var t=this._textGuide;t&&(this._detachComponent(t),this._textGuide=null,this.markRedraw())},t.prototype.markRedraw=function(){this.__dirty|=h.YV;var t=this.__zr;t&&(this.__inHover?t.refreshHover():t.refresh()),this.__hostTarget&&this.__hostTarget.markRedraw()},t.prototype.dirty=function(){this.markRedraw()},t.prototype._toggleHoverLayerFlag=function(t){this.__inHover=t;var e=this._textContent,n=this._textGuide;e&&(e.__inHover=t),n&&(n.__inHover=t)},t.prototype.addSelfToZr=function(t){if(this.__zr!==t){this.__zr=t;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.addAnimator(e[n]);this._clipPath&&this._clipPath.addSelfToZr(t),this._textContent&&this._textContent.addSelfToZr(t),this._textGuide&&this._textGuide.addSelfToZr(t)}},t.prototype.removeSelfFromZr=function(t){if(!!this.__zr){this.__zr=null;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.removeAnimator(e[n]);this._clipPath&&this._clipPath.removeSelfFromZr(t),this._textContent&&this._textContent.removeSelfFromZr(t),this._textGuide&&this._textGuide.removeSelfFromZr(t)}},t.prototype.animate=function(t,e,n){var r=t?this[t]:this,o=new i.Z(r,e,n);return t&&(o.targetName=t),this.addAnimator(o,t),o},t.prototype.addAnimator=function(t,e){var n=this.__zr,r=this;t.during(function(){r.updateDuringAnimation(e)}).done(function(){var e=r.animators,n=(0,u.cq)(e,t);n>=0&&e.splice(n,1)}),this.animators.push(t),n&&n.animation.addAnimator(t),n&&n.wakeUp()},t.prototype.updateDuringAnimation=function(t){this.markRedraw()},t.prototype.stopAnimation=function(t,e){for(var n=this.animators,r=n.length,i=[],o=0;o<r;o++){var a=n[o];t&&t!==a.scope?i.push(a):a.stop(e)}return this.animators=i,this},t.prototype.animateTo=function(t,e,n){m(this,t,e,n)},t.prototype.animateFrom=function(t,e,n){m(this,t,e,n,!0)},t.prototype._transitionState=function(t,e,n,r){for(var i=m(this,e,n,r),o=0;o<i.length;o++)i[o].__fromStateTransition=t},t.prototype.getBoundingRect=function(){return null},t.prototype.getPaintRect=function(){return null},t.initDefaultProps=function(){var e=t.prototype;function n(t,n,r,i){Object.defineProperty(e,t,{get:function(){return!this[n]&&o(this,this[n]=[]),this[n]},set:function(t){this[r]=t[0],this[i]=t[1],this[n]=t,o(this,t)}});function o(t,e){Object.defineProperty(e,0,{get:function(){return t[r]},set:function(e){t[r]=e}}),Object.defineProperty(e,1,{get:function(){return t[i]},set:function(e){t[i]=e}})}}e.type="element",e.name="",e.ignore=e.silent=e.isGroup=e.draggable=e.dragging=e.ignoreClip=e.__inHover=!1,e.__dirty=h.YV,Object.defineProperty&&(n("position","_legacyPos","x","y"),n("scale","_legacyScale","scaleX","scaleY"),n("origin","_legacyOrigin","originX","originY"))}(),t}();function m(t,e,n,r,o){var a=[];(function t(e,n,r,o,a,s,l,c){for(var h=(0,u.XP)(o),f=a.duration,p=a.delay,d=a.additive,g=a.setToFinal,v=!(0,u.Kn)(s),y=e.animators,m=[],x=0;x<h.length;x++){var w=h[x],S=o[w];if(null!=S&&null!=r[w]&&(v||s[w])){if(!(0,u.Kn)(S)||(0,u.zG)(S)||(0,u.Qq)(S))m.push(w);else{if(n){!c&&(r[w]=S,e.updateDuringAnimation(n));continue}t(e,w,r[w],S,a,s&&s[w],l,c)}}else!c&&(r[w]=S,e.updateDuringAnimation(n),m.push(w))}var b=m.length;if(!d&&b)for(var M=0;M<y.length;M++){var T=y[M];if(T.targetName===n&&T.stopTracks(m)){var k=(0,u.cq)(y,T);y.splice(k,1)}}if(!a.force&&(b=(m=(0,u.hX)(m,function(t){var e,n;return e=o[t],!(e===(n=r[t])||(0,u.zG)(e)&&(0,u.zG)(n)&&function(t,e){var n=t.length;if(n!==e.length)return!1;for(var r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}(e,n))})).length),b>0||a.force&&!l.length){var D=void 0,C=void 0,I=void 0;if(c){C={},g&&(D={});for(var M=0;M<b;M++){var w=m[M];C[w]=r[w],g?D[w]=o[w]:r[w]=o[w]}}else if(g){I={};for(var M=0;M<b;M++){var w=m[M];I[w]=(0,i.V)(r[w]),!function(t,e,n){if((0,u.zG)(e[n])){if(!(0,u.zG)(t[n])&&(t[n]=[]),(0,u.fU)(e[n])){var r=e[n].length;t[n].length!==r&&(t[n]=new e[n].constructor(r),_(t[n],e[n],r))}else{var i,o=e[n],a=t[n],s=o.length;if(i=o,(0,u.zG)(i[0])){for(var l=o[0].length,c=0;c<s;c++)a[c]?_(a[c],o[c],l):a[c]=Array.prototype.slice.call(o[c])}else _(a,o,s);a.length=o.length}}else t[n]=e[n]}(r,o,w)}}var T=new i.Z(r,!1,!1,d?(0,u.hX)(y,function(t){return t.targetName===n}):null);T.targetName=n,a.scope&&(T.scope=a.scope),g&&D&&T.whenWithKeys(0,D,m),I&&T.whenWithKeys(0,I,m),T.whenWithKeys(null==f?500:f,c?C:o,m).delay(p||0),e.addAnimator(T,n),l.push(T)}})(t,"",t,e,n=n||{},r,a,o);var s=a.length,l=!1,c=n.done,h=n.aborted,f=function(){l=!0,--s<=0&&(l?c&&c():h&&h())},p=function(){--s<=0&&(l?c&&c():h&&h())};!s&&c&&c(),a.length>0&&n.during&&a[0].during(function(t,e){n.during(e)});for(var d=0;d<a.length;d++){var g=a[d];f&&g.done(f),p&&g.aborted(p),n.force&&g.duration(n.duration),g.start(n.easing)}return a}function _(t,e,n){for(var r=0;r<n;r++)t[r]=e[r]}(0,u.jB)(y,a.Z),(0,u.jB)(y,r.ZP);e.Z=y},265105:function(t,e,n){var r=n(904311),i=n(807028),o=n(217961),a=n(684107),s=n(367582),u=n(660847),l=n(273515),c=n(339296),h="silent";function f(){u.sT(this.event)}var p=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.handler=null,e}return(0,r.ZT)(e,t),e.prototype.dispose=function(){},e.prototype.setCursor=function(){},e}(s.Z),d=function(t,e){this.x=t,this.y=e},g=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],v=new c.Z(0,0,0,0),y=function(t){function e(e,n,r,i,o){var s=t.call(this)||this;return s._hovered=new d(0,0),s.storage=e,s.painter=n,s.painterRoot=i,s._pointerSize=o,r=r||new p,s.proxy=null,s.setHandlerProxy(r),s._draggingMgr=new a.Z(s),s}return(0,r.ZT)(e,t),e.prototype.setHandlerProxy=function(t){this.proxy&&this.proxy.dispose(),t&&(i.S6(g,function(e){t.on&&t.on(e,this[e],this)},this),t.handler=this),this.proxy=t},e.prototype.mousemove=function(t){var e=t.zrX,n=t.zrY,r=_(this,e,n),i=this._hovered,o=i.target;o&&!o.__zr&&(o=(i=this.findHover(i.x,i.y)).target);var a=this._hovered=r?new d(e,n):this.findHover(e,n),s=a.target,u=this.proxy;u.setCursor&&u.setCursor(s?s.cursor:"default"),o&&s!==o&&this.dispatchToElement(i,"mouseout",t),this.dispatchToElement(a,"mousemove",t),s&&s!==o&&this.dispatchToElement(a,"mouseover",t)},e.prototype.mouseout=function(t){var e=t.zrEventControl;"only_globalout"!==e&&this.dispatchToElement(this._hovered,"mouseout",t),"no_globalout"!==e&&this.trigger("globalout",{type:"globalout",event:t})},e.prototype.resize=function(){this._hovered=new d(0,0)},e.prototype.dispatch=function(t,e){var n=this[t];n&&n.call(this,e)},e.prototype.dispose=function(){this.proxy.dispose(),this.storage=null,this.proxy=null,this.painter=null},e.prototype.setCursorStyle=function(t){var e=this.proxy;e.setCursor&&e.setCursor(t)},e.prototype.dispatchToElement=function(t,e,n){var r,i,o,a=(t=t||{}).target;if(!a||!a.silent){var s="on"+e;for(var u=(r=e,i=t,{type:r,event:o=n,target:i.target,topTarget:i.topTarget,cancelBubble:!1,offsetX:o.zrX,offsetY:o.zrY,gestureEvent:o.gestureEvent,pinchX:o.pinchX,pinchY:o.pinchY,pinchScale:o.pinchScale,wheelDelta:o.zrDelta,zrByTouch:o.zrByTouch,which:o.which,stop:f});a&&(a[s]&&(u.cancelBubble=!!a[s].call(a,u)),a.trigger(e,u),a=a.__hostTarget?a.__hostTarget:a.parent,!u.cancelBubble););!u.cancelBubble&&(this.trigger(e,u),this.painter&&this.painter.eachOtherLayer&&this.painter.eachOtherLayer(function(t){"function"==typeof t[s]&&t[s].call(t,u),t.trigger&&t.trigger(e,u)}))}},e.prototype.findHover=function(t,e,n){var r=this.storage.getDisplayList(),i=new d(t,e);if(m(r,i,t,e,n),this._pointerSize&&!i.target){for(var o=[],a=this._pointerSize,s=a/2,u=new c.Z(t-s,e-s,a,a),l=r.length-1;l>=0;l--){var h=r[l];h!==n&&!h.ignore&&!h.ignoreCoarsePointer&&(!h.parent||!h.parent.ignoreCoarsePointer)&&(v.copy(h.getBoundingRect()),h.transform&&v.applyTransform(h.transform),v.intersect(u)&&o.push(h))}if(o.length){for(var f=Math.PI/12,p=2*Math.PI,g=0;g<s;g+=4)for(var y=0;y<p;y+=f)if(m(o,i,t+g*Math.cos(y),e+g*Math.sin(y),n),i.target)return i}}return i},e.prototype.processGesture=function(t,e){!this._gestureMgr&&(this._gestureMgr=new l.y);var n=this._gestureMgr;"start"===e&&n.clear();var r=n.recognize(t,this.findHover(t.zrX,t.zrY,null).target,this.proxy.dom);if("end"===e&&n.clear(),r){var i=r.type;t.gestureEvent=i;var o=new d;o.target=r.target,this.dispatchToElement(o,i,r.event)}},e}(s.Z);i.S6(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(t){y.prototype[t]=function(e){var n,r,i=e.zrX,a=e.zrY,s=_(this,i,a);if(("mouseup"!==t||!s)&&(r=(n=this.findHover(i,a)).target),"mousedown"===t)this._downEl=r,this._downPoint=[e.zrX,e.zrY],this._upEl=r;else if("mouseup"===t)this._upEl=r;else if("click"===t){if(this._downEl!==this._upEl||!this._downPoint||o.TK(this._downPoint,[e.zrX,e.zrY])>4)return;this._downPoint=null}this.dispatchToElement(n,t,e)}});function m(t,e,n,r,i){for(var o=t.length-1;o>=0;o--){var a=t[o],s=void 0;if(a!==i&&!a.ignore&&(s=function(t,e,n){if(t[t.rectHover?"rectContain":"contain"](e,n)){for(var r=t,i=void 0,o=!1;r;){if(r.ignoreClip&&(o=!0),!o){var a=r.getClipPath();if(a&&!a.contain(e,n))return!1}r.silent&&(i=!0);var s=r.__hostTarget;r=s||r.parent}return!i||h}return!1}(a,n,r))&&(e.topTarget||(e.topTarget=a),s!==h)){e.target=a;break}}}function _(t,e,n){var r=t.painter;return e<0||e>r.getWidth()||n<0||n>r.getHeight()}e.Z=y},555053:function(t,e,n){var r=n(807028),i=n(128067),o=n(360577),a=!1;function s(){if(!a)a=!0,console.warn("z / z2 / zlevel of displayable is invalid, which may cause unexpected errors")}function u(t,e){if(t.zlevel===e.zlevel)return t.z===e.z?t.z2-e.z2:t.z-e.z;return t.zlevel-e.zlevel}var l=function(){function t(){this._roots=[],this._displayList=[],this._displayListLen=0,this.displayableSortFunc=u}return t.prototype.traverse=function(t,e){for(var n=0;n<this._roots.length;n++)this._roots[n].traverse(t,e)},t.prototype.getDisplayList=function(t,e){e=e||!1;var n=this._displayList;return(t||!n.length)&&this.updateDisplayList(e),n},t.prototype.updateDisplayList=function(t){this._displayListLen=0;for(var e=this._roots,n=this._displayList,r=0,o=e.length;r<o;r++)this._updateAndAddDisplayable(e[r],null,t);n.length=this._displayListLen,(0,i.Z)(n,u)},t.prototype._updateAndAddDisplayable=function(t,e,n){if(!t.ignore||!!n){t.beforeUpdate(),t.update(),t.afterUpdate();var r=t.getClipPath();if(t.ignoreClip)e=null;else if(r){e=e?e.slice():[];for(var i=r,a=t;i;)i.parent=a,i.updateTransform(),e.push(i),a=i,i=i.getClipPath()}if(t.childrenRef){for(var u=t.childrenRef(),l=0;l<u.length;l++){var c=u[l];t.__dirty&&(c.__dirty|=o.YV),this._updateAndAddDisplayable(c,e,n)}t.__dirty=0}else e&&e.length?t.__clipPaths=e:t.__clipPaths&&t.__clipPaths.length>0&&(t.__clipPaths=[]),isNaN(t.z)&&(s(),t.z=0),isNaN(t.z2)&&(s(),t.z2=0),isNaN(t.zlevel)&&(s(),t.zlevel=0),this._displayList[this._displayListLen++]=t;var h=t.getDecalElement&&t.getDecalElement();h&&this._updateAndAddDisplayable(h,e,n);var f=t.getTextGuideLine();f&&this._updateAndAddDisplayable(f,e,n);var p=t.getTextContent();p&&this._updateAndAddDisplayable(p,e,n)}},t.prototype.addRoot=function(t){if(!t.__zr||t.__zr.storage!==this)this._roots.push(t)},t.prototype.delRoot=function(t){if(t instanceof Array){for(var e=0,n=t.length;e<n;e++)this.delRoot(t[e]);return}var i=r.cq(this._roots,t);i>=0&&this._roots.splice(i,1)},t.prototype.delAllRoots=function(){this._roots=[],this._displayList=[],this._displayListLen=0},t.prototype.getRoots=function(){return this._roots},t.prototype.dispose=function(){this._displayList=null,this._roots=null},t}();e.Z=l},477367:function(t,e,n){n.d(e,{h:function(){return s}});var r=n(904311),i=n(367582),o=n(980254),a=n(336692);function s(){return new Date().getTime()}var u=function(t){function e(e){var n=t.call(this)||this;return n._running=!1,n._time=0,n._pausedTime=0,n._pauseStart=0,n._paused=!1,e=e||{},n.stage=e.stage||{},n}return(0,r.ZT)(e,t),e.prototype.addClip=function(t){t.animation&&this.removeClip(t),this._head?(this._tail.next=t,t.prev=this._tail,t.next=null,this._tail=t):this._head=this._tail=t,t.animation=this},e.prototype.addAnimator=function(t){t.animation=this;var e=t.getClip();e&&this.addClip(e)},e.prototype.removeClip=function(t){if(!!t.animation){var e=t.prev,n=t.next;e?e.next=n:this._head=n,n?n.prev=e:this._tail=e,t.next=t.prev=t.animation=null}},e.prototype.removeAnimator=function(t){var e=t.getClip();e&&this.removeClip(e),t.animation=null},e.prototype.update=function(t){for(var e=s()-this._pausedTime,n=e-this._time,r=this._head;r;){var i=r.next;r.step(e,n)&&(r.ondestroy(),this.removeClip(r)),r=i}this._time=e,!t&&(this.trigger("frame",n),this.stage.update&&this.stage.update())},e.prototype._startLoop=function(){var t=this;this._running=!0;(0,o.Z)(function e(){t._running&&((0,o.Z)(e),t._paused||t.update())})},e.prototype.start=function(){if(!this._running)this._time=s(),this._pausedTime=0,this._startLoop()},e.prototype.stop=function(){this._running=!1},e.prototype.pause=function(){!this._paused&&(this._pauseStart=s(),this._paused=!0)},e.prototype.resume=function(){this._paused&&(this._pausedTime+=s()-this._pauseStart,this._paused=!1)},e.prototype.clear=function(){for(var t=this._head;t;){var e=t.next;t.prev=t.next=t.animation=null,t=e}this._head=this._tail=null},e.prototype.isFinished=function(){return null==this._head},e.prototype.animate=function(t,e){e=e||{},this.start();var n=new a.Z(t,e.loop);return this.addAnimator(n),n},e}(i.Z);e.Z=u},336692:function(t,e,n){n.d(e,{V:function(){return d}});var r=n(347933),i=n(529134),o=n(807028),a=n(933106),s=n(973298),u=n(201974),l=Array.prototype.slice;function c(t,e,n){return(e-t)*n+t}function h(t,e,n,r){for(var i,o,a=e.length,s=0;s<a;s++){;t[s]=(i=e[s],o=n[s],(o-i)*r+i)}return t}function f(t,e,n,r){for(var i=e.length,o=0;o<i;o++)t[o]=e[o]+n[o]*r;return t}function p(t,e,n,r){for(var i=e.length,o=i&&e[0].length,a=0;a<i;a++){!t[a]&&(t[a]=[]);for(var s=0;s<o;s++)t[a][s]=e[a][s]+n[a][s]*r}return t}function d(t){if((0,o.zG)(t)){var e=t.length;if((0,o.zG)(t[0])){for(var n=[],r=0;r<e;r++)n.push(l.call(t[r]));return n}return l.call(t)}return t}function g(t){return t[0]=Math.floor(t[0])||0,t[1]=Math.floor(t[1])||0,t[2]=Math.floor(t[2])||0,t[3]=null==t[3]?1:t[3],"rgba("+t.join(",")+")"}function v(t){return 4===t||5===t}function y(t){return 1===t||2===t}var m=[0,0,0,0],_=function(){function t(t){this.keyframes=[],this.discrete=!1,this._invalid=!1,this._needsSort=!1,this._lastFr=0,this._lastFrP=0,this.propName=t}return t.prototype.isFinished=function(){return this._finished},t.prototype.setFinished=function(){this._finished=!0,this._additiveTrack&&this._additiveTrack.setFinished()},t.prototype.needsAnimate=function(){return this.keyframes.length>=1},t.prototype.getAdditiveTrack=function(){return this._additiveTrack},t.prototype.addKeyframe=function(t,e,n){this._needsSort=!0;var r=this.keyframes,l=r.length,c=!1,h=6,f=e;if((0,o.zG)(e)){var p,d=(p=e,(0,o.zG)(p&&p[0])?2:1);h=d,(1===d&&!(0,o.hj)(e[0])||2===d&&!(0,o.hj)(e[0][0]))&&(c=!0)}else if((0,o.hj)(e)&&!(0,o.Bu)(e))h=0;else if((0,o.HD)(e)){if(isNaN(+e)){var g=i.Qc(e);g&&(f=g,h=3)}else h=0}else if((0,o.Qq)(e)){var v=(0,o.l7)({},f);v.colorStops=(0,o.UI)(e.colorStops,function(t){return{offset:t.offset,color:i.Qc(t.color)}}),(0,u.I1)(e)?h=4:(0,u.gO)(e)&&(h=5),f=v}0===l?this.valType=h:(h!==this.valType||6===h)&&(c=!0),this.discrete=this.discrete||c;var y={time:t,value:f,rawValue:e,percent:0};return n&&(y.easing=n,y.easingFunc=(0,o.mf)(n)?n:a.Z[n]||(0,s.H)(n)),r.push(y),y},t.prototype.prepare=function(t,e){var n=this.keyframes;this._needsSort&&n.sort(function(t,e){return t.time-e.time});for(var r=this.valType,i=n.length,o=n[i-1],a=this.discrete,s=y(r),u=v(r),c=0;c<i;c++){var h=n[c],d=h.value,g=o.value;h.percent=h.time/t,!a&&(s&&c!==i-1?!function(t,e,n){if(!!t.push&&!!e.push){var r=t.length,i=e.length;if(r!==i){if(r>i)t.length=i;else for(var o=r;o<i;o++)t.push(1===n?e[o]:l.call(e[o]))}for(var a=t[0]&&t[0].length,o=0;o<t.length;o++)if(1===n)isNaN(t[o])&&(t[o]=e[o]);else for(var s=0;s<a;s++)isNaN(t[o][s])&&(t[o][s]=e[o][s])}}(d,g,r):u&&!function(t,e){for(var n=t.length,r=e.length,i=n>r?e:t,o=Math.min(n,r),a=i[o-1]||{color:[0,0,0,0],offset:0},s=o;s<Math.max(n,r);s++)i.push({offset:a.offset,color:a.color.slice()})}(d.colorStops,g.colorStops))}if(!a&&5!==r&&e&&this.needsAnimate()&&e.needsAnimate()&&r===e.valType&&!e._finished){this._additiveTrack=e;for(var m=n[0].value,c=0;c<i;c++)0===r?n[c].additiveValue=n[c].value-m:3===r?n[c].additiveValue=f([],n[c].value,m,-1):y(r)&&(n[c].additiveValue=1===r?f([],n[c].value,m,-1):p([],n[c].value,m,-1))}},t.prototype.step=function(t,e){if(this._finished)return;this._additiveTrack&&this._additiveTrack._finished&&(this._additiveTrack=null);var n,r,i,a=null!=this._additiveTrack,s=a?"additiveValue":"value",u=this.valType,l=this.keyframes,c=l.length,f=this.propName,p=3===u,d=this._lastFr,_=Math.min;if(1===c)r=i=l[0];else{if(e<0)n=0;else if(e<this._lastFrP){;for(n=_(d+1,c-1);n>=0&&!(l[n].percent<=e);n--);n=_(n,c-2)}else{for(n=d;n<c&&!(l[n].percent>e);n++);n=_(n-1,c-2)}i=l[n+1],r=l[n]}if(!!(r&&i)){this._lastFr=n,this._lastFrP=e;var x=i.percent-r.percent,w=0===x?1:_((e-r.percent)/x,1);i.easingFunc&&(w=i.easingFunc(w));var S=a?this._additiveValue:p?m:t[f];if((y(u)||p)&&!S&&(S=this._additiveValue=[]),this.discrete)t[f]=w<1?r.rawValue:i.rawValue;else if(y(u))1===u?h(S,r[s],i[s],w):function(t,e,n,r){for(var i=e.length,o=i&&e[0].length,a=0;a<i;a++){!t[a]&&(t[a]=[]);for(var s,u,l=0;l<o;l++){;t[a][l]=(s=e[a][l],u=n[a][l],(u-s)*r+s)}}return t}(S,r[s],i[s],w);else if(v(u)){var b,M,T,k,D,C,I,A,P,Z,L,O=r[s],R=i[s],N=4===u;if(t[f]={type:N?"linear":"radial",x:(M=O.x,T=R.x,(T-M)*w+M),y:(k=O.y,D=R.y,(D-k)*w+k),colorStops:(0,o.UI)(O.colorStops,function(t,e){var n,r,i=R.colorStops[e];return{offset:(n=t.offset,r=i.offset,(r-n)*w+n),color:g(h([],t.color,i.color,w))}}),global:R.global},N){;t[f].x2=(C=O.x2,I=R.x2,(I-C)*w+C),t[f].y2=(A=O.y2,P=R.y2,(P-A)*w+A)}else{;t[f].r=(Z=O.r,L=R.r,(L-Z)*w+Z)}}else if(p)h(S,r[s],i[s],w),!a&&(t[f]=g(S));else{var E,B,z=(E=r[s],B=i[s],(B-E)*w+E);a?this._additiveValue=z:t[f]=z}a&&this._addToTarget(t)}},t.prototype._addToTarget=function(t){var e=this.valType,n=this.propName,r=this._additiveValue;0===e?t[n]=t[n]+r:3===e?(i.Qc(t[n],m),f(m,m,r,1),t[n]=g(m)):1===e?f(t[n],t[n],r,1):2===e&&p(t[n],t[n],r,1)},t}(),x=function(){function t(t,e,n,r){if(this._tracks={},this._trackKeys=[],this._maxTime=0,this._started=0,this._clip=null,this._target=t,this._loop=e,e&&r){(0,o.H)("Can' use additive animation on looped animation.");return}this._additiveAnimators=r,this._allowDiscrete=n}return t.prototype.getMaxTime=function(){return this._maxTime},t.prototype.getDelay=function(){return this._delay},t.prototype.getLoop=function(){return this._loop},t.prototype.getTarget=function(){return this._target},t.prototype.changeTarget=function(t){this._target=t},t.prototype.when=function(t,e,n){return this.whenWithKeys(t,e,(0,o.XP)(e),n)},t.prototype.whenWithKeys=function(t,e,n,r){for(var i=this._tracks,o=0;o<n.length;o++){var a=n[o],s=i[a];if(!s){s=i[a]=new _(a);var u=void 0,l=this._getAdditiveTrack(a);if(l){var c=l.keyframes,h=c[c.length-1];u=h&&h.value,3===l.valType&&u&&(u=g(u))}else u=this._target[a];if(null==u)continue;t>0&&s.addKeyframe(0,d(u),r),this._trackKeys.push(a)}s.addKeyframe(t,d(e[a]),r)}return this._maxTime=Math.max(this._maxTime,t),this},t.prototype.pause=function(){this._clip.pause(),this._paused=!0},t.prototype.resume=function(){this._clip.resume(),this._paused=!1},t.prototype.isPaused=function(){return!!this._paused},t.prototype.duration=function(t){return this._maxTime=t,this._force=!0,this},t.prototype._doneCallback=function(){this._setTracksFinished(),this._clip=null;var t=this._doneCbs;if(t){for(var e=t.length,n=0;n<e;n++)t[n].call(this)}},t.prototype._abortedCallback=function(){this._setTracksFinished();var t=this.animation,e=this._abortedCbs;if(t&&t.removeClip(this._clip),this._clip=null,e)for(var n=0;n<e.length;n++)e[n].call(this)},t.prototype._setTracksFinished=function(){for(var t=this._tracks,e=this._trackKeys,n=0;n<e.length;n++)t[e[n]].setFinished()},t.prototype._getAdditiveTrack=function(t){var e,n=this._additiveAnimators;if(n)for(var r=0;r<n.length;r++){var i=n[r].getTrack(t);i&&(e=i)}return e},t.prototype.start=function(t){if(!(this._started>0)){this._started=1;for(var e=this,n=[],i=this._maxTime||0,o=0;o<this._trackKeys.length;o++){var a=this._trackKeys[o],s=this._tracks[a],u=this._getAdditiveTrack(a),l=s.keyframes,c=l.length;if(s.prepare(i,u),s.needsAnimate()){if(!this._allowDiscrete&&s.discrete){var h=l[c-1];h&&(e._target[s.propName]=h.rawValue),s.setFinished()}else n.push(s)}}if(n.length||this._force){var f=new r.Z({life:i,loop:this._loop,delay:this._delay||0,onframe:function(t){e._started=2;var r=e._additiveAnimators;if(r){for(var i=!1,o=0;o<r.length;o++)if(r[o]._clip){i=!0;break}!i&&(e._additiveAnimators=null)}for(var o=0;o<n.length;o++)n[o].step(e._target,t);var a=e._onframeCbs;if(a)for(var o=0;o<a.length;o++)a[o](e._target,t)},ondestroy:function(){e._doneCallback()}});this._clip=f,this.animation&&this.animation.addClip(f),t&&f.setEasing(t)}else this._doneCallback();return this}},t.prototype.stop=function(t){if(!!this._clip){var e=this._clip;t&&e.onframe(1),this._abortedCallback()}},t.prototype.delay=function(t){return this._delay=t,this},t.prototype.during=function(t){return t&&(!this._onframeCbs&&(this._onframeCbs=[]),this._onframeCbs.push(t)),this},t.prototype.done=function(t){return t&&(!this._doneCbs&&(this._doneCbs=[]),this._doneCbs.push(t)),this},t.prototype.aborted=function(t){return t&&(!this._abortedCbs&&(this._abortedCbs=[]),this._abortedCbs.push(t)),this},t.prototype.getClip=function(){return this._clip},t.prototype.getTrack=function(t){return this._tracks[t]},t.prototype.getTracks=function(){var t=this;return(0,o.UI)(this._trackKeys,function(e){return t._tracks[e]})},t.prototype.stopTracks=function(t,e){if(!t.length||!this._clip)return!0;for(var n=this._tracks,r=this._trackKeys,i=0;i<t.length;i++){var o=n[t[i]];o&&!o.isFinished()&&(e?o.step(this._target,1):1===this._started&&o.step(this._target,0),o.setFinished())}for(var a=!0,i=0;i<r.length;i++)if(!n[r[i]].isFinished()){a=!1;break}return a&&this._abortedCallback(),a},t.prototype.saveTo=function(t,e,n){if(!!t){e=e||this._trackKeys;for(var r=0;r<e.length;r++){var i=e[r],o=this._tracks[i];if(!(!o||o.isFinished())){var a=o.keyframes,s=a[n?0:a.length-1];s&&(t[i]=d(s.rawValue))}}}},t.prototype.__changeFinalValue=function(t,e){e=e||(0,o.XP)(t);for(var n=0;n<e.length;n++){var r=e[n],i=this._tracks[r];if(!!i){var a=i.keyframes;if(a.length>1){var s=a.pop();i.addKeyframe(s.time,t[r]),i.prepare(this._maxTime,i.getAdditiveTrack())}}}},t}();e.Z=x},347933:function(t,e,n){var r=n(933106),i=n(807028),o=n(973298),a=function(){function t(t){this._inited=!1,this._startTime=0,this._pausedTime=0,this._paused=!1,this._life=t.life||1e3,this._delay=t.delay||0,this.loop=t.loop||!1,this.onframe=t.onframe||i.ZT,this.ondestroy=t.ondestroy||i.ZT,this.onrestart=t.onrestart||i.ZT,t.easing&&this.setEasing(t.easing)}return t.prototype.step=function(t,e){if(!this._inited&&(this._startTime=t+this._delay,this._inited=!0),this._paused){this._pausedTime+=e;return}var n=this._life,r=t-this._startTime-this._pausedTime,i=r/n;i<0&&(i=0),i=Math.min(i,1);var o=this.easingFunc,a=o?o(i):i;if(this.onframe(a),1===i){if(!this.loop)return!0;this._startTime=t-r%n,this._pausedTime=0,this.onrestart()}return!1},t.prototype.pause=function(){this._paused=!0},t.prototype.resume=function(){this._paused=!1},t.prototype.setEasing=function(t){this.easing=t,this.easingFunc=(0,i.mf)(t)?t:r.Z[t]||(0,o.H)(t)},t}();e.Z=a},973298:function(t,e,n){n.d(e,{H:function(){return a}});var r=n(343556),i=n(807028),o=/cubic-bezier\(([0-9,\.e ]+)\)/;function a(t){var e=t&&o.exec(t);if(e){var n=e[1].split(","),a=+(0,i.fy)(n[0]),s=+(0,i.fy)(n[1]),u=+(0,i.fy)(n[2]),l=+(0,i.fy)(n[3]);if(isNaN(a+s+u+l))return;var c=[];return function(t){return t<=0?0:t>=1?1:(0,r.kD)(0,a,u,1,t,c)&&(0,r.af)(0,s,l,1,c[0])}}}},933106:function(t,e){var n={linear:function(t){return t},quadraticIn:function(t){return t*t},quadraticOut:function(t){return t*(2-t)},quadraticInOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)},cubicIn:function(t){return t*t*t},cubicOut:function(t){return--t*t*t+1},cubicInOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)},quarticIn:function(t){return t*t*t*t},quarticOut:function(t){return 1- --t*t*t*t},quarticInOut:function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)},quinticIn:function(t){return t*t*t*t*t},quinticOut:function(t){return--t*t*t*t*t+1},quinticInOut:function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)},sinusoidalIn:function(t){return 1-Math.cos(t*Math.PI/2)},sinusoidalOut:function(t){return Math.sin(t*Math.PI/2)},sinusoidalInOut:function(t){return .5*(1-Math.cos(Math.PI*t))},exponentialIn:function(t){return 0===t?0:Math.pow(1024,t-1)},exponentialOut:function(t){return 1===t?1:1-Math.pow(2,-10*t)},exponentialInOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?.5*Math.pow(1024,t-1):.5*(-Math.pow(2,-10*(t-1))+2)},circularIn:function(t){return 1-Math.sqrt(1-t*t)},circularOut:function(t){return Math.sqrt(1- --t*t)},circularInOut:function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)},elasticIn:function(t){var e,n=.1;return 0===t?0:1===t?1:(!n||n<1?(n=1,e=.1):e=.4*Math.asin(1/n)/(2*Math.PI),-(n*Math.pow(2,10*(t-=1))*Math.sin(2*Math.PI*(t-e)/.4)))},elasticOut:function(t){var e,n=.1;return 0===t?0:1===t?1:(!n||n<1?(n=1,e=.1):e=.4*Math.asin(1/n)/(2*Math.PI),n*Math.pow(2,-10*t)*Math.sin(2*Math.PI*(t-e)/.4)+1)},elasticInOut:function(t){var e,n=.1;return 0===t?0:1===t?1:(!n||n<1?(n=1,e=.1):e=.4*Math.asin(1/n)/(2*Math.PI),(t*=2)<1)?-.5*(n*Math.pow(2,10*(t-=1))*Math.sin(2*Math.PI*(t-e)/.4)):n*Math.pow(2,-10*(t-=1))*Math.sin(2*Math.PI*(t-e)/.4)*.5+1},backIn:function(t){return t*t*(2.70158*t-1.70158)},backOut:function(t){return--t*t*(2.70158*t+1.70158)+1},backInOut:function(t){return(t*=2)<1?t*t*(3.5949095*t-2.5949095)*.5:.5*((t-=2)*t*(3.5949095*t+2.5949095)+2)},bounceIn:function(t){return 1-n.bounceOut(1-t)},bounceOut:function(t){if(t<1/2.75)return 7.5625*t*t;if(t<2/2.75)return 7.5625*(t-=1.5/2.75)*t+.75;if(t<2.5/2.75)return 7.5625*(t-=2.25/2.75)*t+.9375;else return 7.5625*(t-=2.625/2.75)*t+.984375},bounceInOut:function(t){return t<.5?.5*n.bounceIn(2*t):.5*n.bounceOut(2*t-1)+.5}};e.Z=n},980254:function(t,e,n){var r;r=n(939828).Z.hasGlobalWindow&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(t){return setTimeout(t,16)},e.Z=r},861996:function(t,e,n){n.d(e,{a:function(){return i}});var r=n(807028);function i(t){var e,n,i=t.style;var o=i.lineDash&&i.lineWidth>0&&(e=i.lineDash,n=i.lineWidth,e&&"solid"!==e&&n>0?"dashed"===e?[4*n,2*n]:"dotted"===e?[n]:(0,r.hj)(e)?[e]:(0,r.kJ)(e)?e:null:null),a=i.lineDashOffset;if(o){var s=i.strokeNoScale&&t.getLineScale?t.getLineScale():1;s&&1!==s&&(o=(0,r.UI)(o,function(t){return t/s}),a/=s)}return[o,a]}},157301:function(t,e,n){n.d(e,{Dm:function(){return I},RV:function(){return C},RZ:function(){return x}});var r=n(783821),i=n(351415),o=n(175873),a=n(638687),s=n(894641),u=n(694923),l=n(613410),c=n(807028),h=n(861996),f=n(360577),p=n(252919),d=new i.Z(!0);function g(t){var e=t.stroke;return!(null==e||"none"===e||!(t.lineWidth>0))}function v(t){return"string"==typeof t&&"none"!==t}function y(t){var e=t.fill;return null!=e&&"none"!==e}function m(t,e){if(null!=e.fillOpacity&&1!==e.fillOpacity){var n=t.globalAlpha;t.globalAlpha=e.fillOpacity*e.opacity,t.fill(),t.globalAlpha=n}else t.fill()}function _(t,e){if(null!=e.strokeOpacity&&1!==e.strokeOpacity){var n=t.globalAlpha;t.globalAlpha=e.strokeOpacity*e.opacity,t.stroke(),t.globalAlpha=n}else t.stroke()}function x(t,e,n){var r=(0,o.Gq)(e.image,e.__image,n);if((0,o.v5)(r)){var i=t.createPattern(r,e.repeat||"repeat");if("function"==typeof DOMMatrix&&i&&i.setTransform){var a=new DOMMatrix;a.translateSelf(e.x||0,e.y||0),a.rotateSelf(0,0,(e.rotation||0)*c.I3),a.scaleSelf(e.scaleX||1,e.scaleY||1),i.setTransform(a)}return i}}var w=["shadowBlur","shadowOffsetX","shadowOffsetY"],S=[["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]];function b(t,e,n,i,o){var a=!1;if(!i&&e===(n=n||{}))return!1;if(i||e.opacity!==n.opacity){k(t,o),a=!0;var s=Math.max(Math.min(e.opacity,1),0);t.globalAlpha=isNaN(s)?r.tj.opacity:s}(i||e.blend!==n.blend)&&(!a&&(k(t,o),a=!0),t.globalCompositeOperation=e.blend||r.tj.blend);for(var u=0;u<w.length;u++){var l=w[u];(i||e[l]!==n[l])&&(!a&&(k(t,o),a=!0),t[l]=t.dpr*(e[l]||0))}return(i||e.shadowColor!==n.shadowColor)&&(!a&&(k(t,o),a=!0),t.shadowColor=e.shadowColor||r.tj.shadowColor),a}function M(t,e,n,r,i){var o=D(e,i.inHover),a=r?null:n&&D(n,i.inHover)||{};if(o===a)return!1;var s=b(t,o,a,r,i);if((r||o.fill!==a.fill)&&(!s&&(k(t,i),s=!0),v(o.fill)&&(t.fillStyle=o.fill)),(r||o.stroke!==a.stroke)&&(!s&&(k(t,i),s=!0),v(o.stroke)&&(t.strokeStyle=o.stroke)),(r||o.opacity!==a.opacity)&&(!s&&(k(t,i),s=!0),t.globalAlpha=null==o.opacity?1:o.opacity),e.hasStroke()){var u=o.lineWidth/(o.strokeNoScale&&e.getLineScale?e.getLineScale():1);t.lineWidth!==u&&(!s&&(k(t,i),s=!0),t.lineWidth=u)}for(var l=0;l<S.length;l++){var c=S[l],h=c[0];(r||o[h]!==a[h])&&(!s&&(k(t,i),s=!0),t[h]=o[h]||c[1])}return s}function T(t,e){var n=e.transform,r=t.dpr||1;n?t.setTransform(r*n[0],r*n[1],r*n[2],r*n[3],r*n[4],r*n[5]):t.setTransform(r,0,0,r,0,0)}function k(t,e){e.batchFill&&t.fill(),e.batchStroke&&t.stroke(),e.batchFill="",e.batchStroke=""}function D(t,e){return e&&t.__hoverStyle||t.style}function C(t,e){I(t,e,{inHover:!1,viewWidth:0,viewHeight:0},!0)}function I(t,e,n,r){var i,c,v,w,S,C,A,P,Z,L,O=e.transform;if(!e.shouldBePainted(n.viewWidth,n.viewHeight,!1,!1)){e.__dirty&=~f.YV,e.__isRendered=!1;return}var R=e.__clipPaths,N=n.prevElClipPaths,E=!1,B=!1;if((!N||(0,a.cF)(R,N))&&(N&&N.length&&(k(t,n),t.restore(),B=E=!0,n.prevElClipPaths=null,n.allClipped=!1,n.prevEl=null),R&&R.length&&(k(t,n),t.save(),!function(t,e,n){for(var r=!1,i=0;i<t.length;i++){var o=t[i];r=r||o.isZeroArea(),T(e,o),e.beginPath(),o.buildPath(e,o.shape),e.clip()}n.allClipped=r}(R,t,n),E=!0),n.prevElClipPaths=R),n.allClipped){e.__isRendered=!1;return}e.beforeBrush&&e.beforeBrush(),e.innerBeforeBrush();var z=n.prevEl;!z&&(B=E=!0);var F=e instanceof s.ZP&&e.autoBatch&&(c=y(i=e.style),v=g(i),!(i.lineDash||!(+c^+v)||c&&"string"!=typeof i.fill||v&&"string"!=typeof i.stroke||i.strokePercent<1||i.strokeOpacity<1||i.fillOpacity<1));if(!E&&(w=O,S=z.transform,w&&S?w[0]===S[0]&&w[1]===S[1]&&w[2]===S[2]&&w[3]===S[3]&&w[4]===S[4]&&w[5]===S[5]:w||S?0:1))!F&&k(t,n);else k(t,n),T(t,e);var W=D(e,n.inHover);if(e instanceof s.ZP)1!==n.lastDrawType&&(B=!0,n.lastDrawType=1),M(t,e,z,B,n),(!F||!n.batchFill&&!n.batchStroke)&&t.beginPath(),!function(t,e,n,r){var i,o,s,u=g(n),l=y(n),c=n.strokePercent,p=c<1,v=!e.path;(!e.silent||p)&&v&&e.createPathProxy();var w=e.path||d,S=e.__dirty;if(!r){var b=n.fill,M=n.stroke,T=l&&!!b.colorStops,k=u&&!!M.colorStops,D=l&&!!b.image,C=u&&!!M.image,I=void 0,A=void 0,P=void 0,Z=void 0,L=void 0;(T||k)&&(L=e.getBoundingRect()),T&&(I=S?(0,a.ZF)(t,b,L):e.__canvasFillGradient,e.__canvasFillGradient=I),k&&(A=S?(0,a.ZF)(t,M,L):e.__canvasStrokeGradient,e.__canvasStrokeGradient=A),D&&(P=S||!e.__canvasFillPattern?x(t,b,e):e.__canvasFillPattern,e.__canvasFillPattern=P),C&&(Z=S||!e.__canvasStrokePattern?x(t,M,e):e.__canvasStrokePattern,e.__canvasStrokePattern=P),T?t.fillStyle=I:D&&(P?t.fillStyle=P:l=!1),k?t.strokeStyle=A:C&&(Z?t.strokeStyle=Z:u=!1)}var O=e.getGlobalScale();w.setScale(O[0],O[1],e.segmentIgnoreThreshold),t.setLineDash&&n.lineDash&&(o=(i=(0,h.a)(e))[0],s=i[1]);var R=!0;(v||S&f.RH)&&(w.setDPR(t.dpr),p?w.setContext(null):(w.setContext(t),R=!1),w.reset(),e.buildPath(w,e.shape,r),w.toStatic(),e.pathUpdated()),R&&w.rebuildPath(t,p?c:1),o&&(t.setLineDash(o),t.lineDashOffset=s),!r&&(n.strokeFirst?(u&&_(t,n),l&&m(t,n)):(l&&m(t,n),u&&_(t,n))),o&&t.setLineDash([])}(t,e,W,F),F&&(n.batchFill=W.fill||"",n.batchStroke=W.stroke||"");else if(e instanceof l.Z)3!==n.lastDrawType&&(B=!0,n.lastDrawType=3),M(t,e,z,B,n),!function(t,e,n){var r,i=n.text;if(null!=i&&(i+=""),i){t.font=n.font||p.Uo,t.textAlign=n.textAlign,t.textBaseline=n.textBaseline;var o=void 0,a=void 0;t.setLineDash&&n.lineDash&&(o=(r=(0,h.a)(e))[0],a=r[1]),o&&(t.setLineDash(o),t.lineDashOffset=a),n.strokeFirst?(g(n)&&t.strokeText(i,n.x,n.y),y(n)&&t.fillText(i,n.x,n.y)):(y(n)&&t.fillText(i,n.x,n.y),g(n)&&t.strokeText(i,n.x,n.y)),o&&t.setLineDash([])}}(t,e,W);else if(e instanceof u.ZP){;2!==n.lastDrawType&&(B=!0,n.lastDrawType=2),C=t,A=e,P=z,Z=B,b(C,D(A,(L=n).inHover),P&&D(P,L.inHover),Z,L),!function(t,e,n){var r=e.__image=(0,o.Gq)(n.image,e.__image,e,e.onload);if(!!r&&!!(0,o.v5)(r)){var i=n.x||0,a=n.y||0,s=e.getWidth(),u=e.getHeight(),l=r.width/r.height;if(null==s&&null!=u?s=u*l:null==u&&null!=s?u=s/l:null==s&&null==u&&(s=r.width,u=r.height),n.sWidth&&n.sHeight){var c=n.sx||0,h=n.sy||0;t.drawImage(r,c,h,n.sWidth,n.sHeight,i,a,s,u)}else if(n.sx&&n.sy){var c=n.sx,h=n.sy,f=s-c,p=u-h;t.drawImage(r,c,h,f,p,i,a,s,u)}else t.drawImage(r,i,a,s,u)}}(t,e,W)}else e.getTemporalDisplayables&&(4!==n.lastDrawType&&(B=!0,n.lastDrawType=4),function(t,e,n){var r,i,o=e.getDisplayables(),a=e.getTemporalDisplayables();t.save();var s={prevElClipPaths:null,prevEl:null,allClipped:!1,viewWidth:n.viewWidth,viewHeight:n.viewHeight,inHover:n.inHover};for(r=e.getCursor(),i=o.length;r<i;r++){var u=o[r];u.beforeBrush&&u.beforeBrush(),u.innerBeforeBrush(),I(t,u,s,r===i-1),u.innerAfterBrush(),u.afterBrush&&u.afterBrush(),s.prevEl=u}for(var l=0,c=a.length;l<c;l++){var u=a[l];u.beforeBrush&&u.beforeBrush(),u.innerBeforeBrush(),I(t,u,s,l===c-1),u.innerAfterBrush(),u.afterBrush&&u.afterBrush(),s.prevEl=u}e.clearTemporalDisplayables(),e.notClear=!0,t.restore()}(t,e,n));F&&r&&k(t,n),e.innerAfterBrush(),e.afterBrush&&e.afterBrush(),n.prevEl=e,e.__dirty=0,e.__isRendered=!0}},638687:function(t,e,n){function r(t){return isFinite(t)}n.d(e,{ZF:function(){return i},ap:function(){return s},cF:function(){return o}});function i(t,e,n){for(var i,o,a,s,u,l,c,h,f,p,d,g,v,y,m,_,x="radial"===e.type?(i=t,o=e,s=(a=n).width,l=Math.min(s,u=a.height),c=null==o.x?.5:o.x,h=null==o.y?.5:o.y,f=null==o.r?.5:o.r,!o.global&&(c=c*s+a.x,h=h*u+a.y,f*=l),c=r(c)?c:.5,h=r(h)?h:.5,f=f>=0&&r(f)?f:.5,i.createRadialGradient(c,h,0,c,h,f)):(p=t,d=e,g=n,v=null==d.x?0:d.x,y=null==d.x2?1:d.x2,m=null==d.y?0:d.y,_=null==d.y2?0:d.y2,!d.global&&(v=v*g.width+g.x,y=y*g.width+g.x,m=m*g.height+g.y,_=_*g.height+g.y),v=r(v)?v:0,y=r(y)?y:1,m=r(m)?m:0,_=r(_)?_:0,p.createLinearGradient(v,m,y,_)),w=e.colorStops,S=0;S<w.length;S++)x.addColorStop(w[S].offset,w[S].color);return x}function o(t,e){if(t===e||!t&&!e)return!1;if(!t||!e||t.length!==e.length)return!0;for(var n=0;n<t.length;n++)if(t[n]!==e[n])return!0;return!1}function a(t){return parseInt(t,10)}function s(t,e,n){var r=["width","height"][e],i=["clientWidth","clientHeight"][e],o=["paddingLeft","paddingTop"][e],s=["paddingRight","paddingBottom"][e];if(null!=n[r]&&"auto"!==n[r])return parseFloat(n[r]);var u=document.defaultView.getComputedStyle(t);return(t[i]||a(u[r])||a(t.style[r]))-(a(u[o])||0)-(a(u[s])||0)|0}},113525:function(t,e,n){n.d(e,{Ak:function(){return a},GD:function(){return u},KL:function(){return o},iv:function(){return l},vU:function(){return s}});var r=n(939828),i=1;r.Z.hasGlobalWindow&&(i=Math.max(window.devicePixelRatio||window.screen&&window.screen.deviceXDPI/window.screen.logicalXDPI||1,1));var o=i,a=.4,s="#333",u="#ccc",l="#eee"},992878:function(t,e,n){n.d(e,{m:function(){return o}});var r=n(165322),i=2*Math.PI;function o(t,e,n,o,a,s,u,l,c){if(0===u)return!1;var h=Math.sqrt((l-=t)*l+(c-=e)*c);if(h-u>n||h+u<n)return!1;if(Math.abs(o-a)%i<1e-4)return!0;if(s){var f=o;o=(0,r.m)(a),a=(0,r.m)(f)}else o=(0,r.m)(o),a=(0,r.m)(a);o>a&&(a+=i);var p=Math.atan2(c,l);return p<0&&(p+=i),p>=o&&p<=a||p+i>=o&&p+i<=a}},301211:function(t,e,n){n.d(e,{m:function(){return i}});var r=n(343556);function i(t,e,n,i,o,a,s,u,l,c,h){return 0!==l&&(!(h>e+l)||!(h>i+l)||!(h>a+l)||!(h>u+l))&&(!(h<e-l)||!(h<i-l)||!(h<a-l)||!(h<u-l))&&(!(c>t+l)||!(c>n+l)||!(c>o+l)||!(c>s+l))&&(!(c<t-l)||!(c<n-l)||!(c<o-l)||!(c<s-l))&&r.t1(t,e,n,i,o,a,s,u,c,h,null)<=l/2}},12949:function(t,e,n){n.d(e,{m:function(){return r}});function r(t,e,n,r,i,o,a){if(0===i)return!1;var s=0,u=t;if(a>e+i&&a>r+i||a<e-i&&a<r-i||o>t+i&&o>n+i||o<t-i&&o<n-i)return!1;if(t===n)return Math.abs(o-t)<=i/2;s=(e-r)/(t-n),u=(t*r-n*e)/(t-n);var l=s*o-a+u;return l*l/(s*s+1)<=i/2*i/2}},185341:function(t,e,n){n.d(e,{X:function(){return g},m:function(){return v}});var r=n(351415),i=n(12949),o=n(301211),a=n(763828),s=n(992878),u=n(343556),l=n(529906),c=r.Z.CMD,h=2*Math.PI,f=[-1,-1,-1],p=[-1,-1];function d(t,e,n,r,d){for(var g,v,y=t.data,m=t.len(),_=0,x=0,w=0,S=0,b=0,M=0;M<m;){var T=y[M++],k=1===M;switch(T===c.M&&M>1&&!n&&(_+=(0,l.Z)(x,w,S,b,r,d)),k&&(x=y[M],w=y[M+1],S=x,b=w),T){case c.M:S=y[M++],b=y[M++],x=S,w=b;break;case c.L:if(n){if(i.m(x,w,y[M],y[M+1],e,r,d))return!0}else _+=(0,l.Z)(x,w,y[M],y[M+1],r,d)||0;x=y[M++],w=y[M++];break;case c.C:if(n){if(o.m(x,w,y[M++],y[M++],y[M++],y[M++],y[M],y[M+1],e,r,d))return!0}else _+=function(t,e,n,r,i,o,a,s,l,c){if(c>e&&c>r&&c>o&&c>s||c<e&&c<r&&c<o&&c<s)return 0;var h=u.kD(e,r,o,s,c,f);if(0===h)return 0;for(var d=0,g=-1,v=void 0,y=void 0,m=0;m<h;m++){var _=f[m],x=0===_||1===_?.5:1;if(!(u.af(t,n,i,a,_)<l))g<0&&(g=u.pP(e,r,o,s,p),p[1]<p[0]&&g>1&&!function(){var t=p[0];p[0]=p[1],p[1]=t}(),v=u.af(e,r,o,s,p[0]),g>1&&(y=u.af(e,r,o,s,p[1]))),2===g?_<p[0]?d+=v<e?x:-x:_<p[1]?d+=y<v?x:-x:d+=s<y?x:-x:_<p[0]?d+=v<e?x:-x:d+=s<v?x:-x}return d}(x,w,y[M++],y[M++],y[M++],y[M++],y[M],y[M+1],r,d)||0;x=y[M++],w=y[M++];break;case c.Q:if(n){if(a.m(x,w,y[M++],y[M++],y[M],y[M+1],e,r,d))return!0}else _+=function(t,e,n,r,i,o,a,s){if(s>e&&s>r&&s>o||s<e&&s<r&&s<o)return 0;var l=u.Jz(e,r,o,s,f);if(0===l)return 0;var c=u.QC(e,r,o);if(c>=0&&c<=1){for(var h=0,p=u.Zm(e,r,o,c),d=0;d<l;d++){var g=0===f[d]||1===f[d]?.5:1,v=u.Zm(t,n,i,f[d]);if(!(v<a))f[d]<c?h+=p<e?g:-g:h+=o<p?g:-g}return h}var g=0===f[0]||1===f[0]?.5:1,v=u.Zm(t,n,i,f[0]);return v<a?0:o<e?g:-g}(x,w,y[M++],y[M++],y[M],y[M+1],r,d)||0;x=y[M++],w=y[M++];break;case c.A:var D=y[M++],C=y[M++],I=y[M++],A=y[M++],P=y[M++],Z=y[M++];M+=1;var L=!!(1-y[M++]);g=Math.cos(P)*I+D,v=Math.sin(P)*A+C,k?(S=g,b=v):_+=(0,l.Z)(x,w,g,v,r,d);var O=(r-D)*A/I+D;if(n){if(s.m(D,C,A,P,P+Z,L,e,O,d))return!0}else _+=function(t,e,n,r,i,o,a,s){if((s-=e)>n||s<-n)return 0;var u=Math.sqrt(n*n-s*s);f[0]=-u,f[1]=u;var l=Math.abs(r-i);if(l<1e-4)return 0;if(l>=h-1e-4){r=0,i=h;var c=o?1:-1;return a>=f[0]+t&&a<=f[1]+t?c:0}if(r>i){var p=r;r=i,i=p}r<0&&(r+=h,i+=h);for(var d=0,g=0;g<2;g++){var v=f[g];if(v+t>a){var y=Math.atan2(s,v),c=o?1:-1;y<0&&(y=h+y),(y>=r&&y<=i||y+h>=r&&y+h<=i)&&(y>Math.PI/2&&y<1.5*Math.PI&&(c=-c),d+=c)}}return d}(D,C,A,P,P+Z,L,O,d);x=Math.cos(P+Z)*I+D,w=Math.sin(P+Z)*A+C;break;case c.R:S=x=y[M++],b=w=y[M++];var R=y[M++],N=y[M++];if(g=S+R,v=b+N,n){if(i.m(S,b,g,b,e,r,d)||i.m(g,b,g,v,e,r,d)||i.m(g,v,S,v,e,r,d)||i.m(S,v,S,b,e,r,d))return!0}else _+=(0,l.Z)(g,b,g,v,r,d)+(0,l.Z)(S,v,S,b,r,d);break;case c.Z:if(n){if(i.m(x,w,S,b,e,r,d))return!0}else _+=(0,l.Z)(x,w,S,b,r,d);x=S,w=b}}if(!n&&!(1e-4>Math.abs(w-b)))_+=(0,l.Z)(x,w,S,b,r,d)||0;return 0!==_}function g(t,e,n){return d(t,0,!1,e,n)}function v(t,e,n,r){return d(t,e,!0,n,r)}},763828:function(t,e,n){n.d(e,{m:function(){return i}});var r=n(343556);function i(t,e,n,i,o,a,s,u,l){return 0!==s&&(!(l>e+s)||!(l>i+s)||!(l>a+s))&&(!(l<e-s)||!(l<i-s)||!(l<a-s))&&(!(u>t+s)||!(u>n+s)||!(u>o+s))&&(!(u<t-s)||!(u<n-s)||!(u<o-s))&&(0,r.Wr)(t,e,n,i,o,a,u,l,null)<=s/2}},310123:function(t,e,n){n.d(e,{Dp:function(){return f},GM:function(){return p},M3:function(){return c},dz:function(){return s},lP:function(){return l},mU:function(){return h},wI:function(){return d}});var r=n(339296),i=n(445471),o=n(252919),a={};function s(t,e){var n=a[e=e||o.Uo];!n&&(n=a[e]=new i.ZP(500));var r=n.get(t);return null==r&&(r=o.qW.measureText(t,e).width,n.put(t,r)),r}function u(t,e,n,i){var o=s(t,e),a=f(e),u=c(0,o,n),l=h(0,a,i);return new r.Z(u,l,o,a)}function l(t,e,n,i){var o=((t||"")+"").split("\n");if(1===o.length)return u(o[0],e,n,i);for(var a=new r.Z(0,0,0,0),s=0;s<o.length;s++){var l=u(o[s],e,n,i);0===s?a.copy(l):a.union(l)}return a}function c(t,e,n){return"right"===n?t-=e:"center"===n&&(t-=e/2),t}function h(t,e,n){return"middle"===n?t-=e/2:"bottom"===n&&(t-=e),t}function f(t){return s("国",t)}function p(t,e){if("string"==typeof t)return t.lastIndexOf("%")>=0?parseFloat(t)/100*e:parseFloat(t);return t}function d(t,e,n){var r=e.position||"inside",i=null!=e.distance?e.distance:5,o=n.height,a=n.width,s=o/2,u=n.x,l=n.y,c="left",h="top";if(r instanceof Array)u+=p(r[0],n.width),l+=p(r[1],n.height),c=null,h=null;else switch(r){case"left":u-=i,l+=s,c="right",h="middle";break;case"right":u+=i+a,l+=s,h="middle";break;case"top":u+=a/2,l-=i,c="center",h="bottom";break;case"bottom":u+=a/2,l+=o+i,c="center";break;case"inside":u+=a/2,l+=s,c="center",h="middle";break;case"insideLeft":u+=i,l+=s,h="middle";break;case"insideRight":u+=a-i,l+=s,c="right",h="middle";break;case"insideTop":u+=a/2,l+=i,c="center";break;case"insideBottom":u+=a/2,l+=o-i,c="center",h="bottom";break;case"insideTopLeft":u+=i,l+=i;break;case"insideTopRight":u+=a-i,l+=i,c="right";break;case"insideBottomLeft":u+=i,l+=o-i,h="bottom";break;case"insideBottomRight":u+=a-i,l+=o-i,c="right",h="bottom"}return(t=t||{}).x=u,t.y=l,t.align=c,t.verticalAlign=h,t}},165322:function(t,e,n){n.d(e,{m:function(){return i}});var r=2*Math.PI;function i(t){return(t%=r)<0&&(t+=r),t}},529906:function(t,e,n){n.d(e,{Z:function(){return r}});function r(t,e,n,r,i,o){if(o>e&&o>r||o<e&&o<r||r===e)return 0;var a=(o-e)/(r-e),s=r<e?1:-1;(1===a||0===a)&&(s=r<e?.5:-.5);var u=a*(n-t)+t;return u===i?1/0:u>i?s:0}},339296:function(t,e,n){var r=n(921715),i=n(317080),o=Math.min,a=Math.max,s=new i.Z,u=new i.Z,l=new i.Z,c=new i.Z,h=new i.Z,f=new i.Z,p=function(){function t(t,e,n,r){n<0&&(t+=n,n=-n),r<0&&(e+=r,r=-r),this.x=t,this.y=e,this.width=n,this.height=r}return t.prototype.union=function(t){var e=o(t.x,this.x),n=o(t.y,this.y);isFinite(this.x)&&isFinite(this.width)?this.width=a(t.x+t.width,this.x+this.width)-e:this.width=t.width,isFinite(this.y)&&isFinite(this.height)?this.height=a(t.y+t.height,this.y+this.height)-n:this.height=t.height,this.x=e,this.y=n},t.prototype.applyTransform=function(e){t.applyTransform(this,this,e)},t.prototype.calculateTransform=function(t){var e=t.width/this.width,n=t.height/this.height,i=r.Ue();return r.Iu(i,i,[-this.x,-this.y]),r.bA(i,i,[e,n]),r.Iu(i,i,[t.x,t.y]),i},t.prototype.intersect=function(e,n){if(!e)return!1;!(e instanceof t)&&(e=t.create(e));var r=this.x,o=this.x+this.width,a=this.y,s=this.y+this.height,u=e.x,l=e.x+e.width,c=e.y,p=e.y+e.height,d=!(o<u||l<r||s<c||p<a);if(n){var g=1/0,v=0,y=Math.abs(o-u),m=Math.abs(l-r),_=Math.abs(s-c),x=Math.abs(p-a),w=Math.min(y,m),S=Math.min(_,x);o<u||l<r?w>v&&(v=w,y<m?i.Z.set(f,-y,0):i.Z.set(f,m,0)):w<g&&(g=w,y<m?i.Z.set(h,y,0):i.Z.set(h,-m,0)),s<c||p<a?S>v&&(v=S,_<x?i.Z.set(f,0,-_):i.Z.set(f,0,x)):w<g&&(g=w,_<x?i.Z.set(h,0,_):i.Z.set(h,0,-x))}return n&&i.Z.copy(n,d?h:f),d},t.prototype.contain=function(t,e){return t>=this.x&&t<=this.x+this.width&&e>=this.y&&e<=this.y+this.height},t.prototype.clone=function(){return new t(this.x,this.y,this.width,this.height)},t.prototype.copy=function(e){t.copy(this,e)},t.prototype.plain=function(){return{x:this.x,y:this.y,width:this.width,height:this.height}},t.prototype.isFinite=function(){return isFinite(this.x)&&isFinite(this.y)&&isFinite(this.width)&&isFinite(this.height)},t.prototype.isZero=function(){return 0===this.width||0===this.height},t.create=function(e){return new t(e.x,e.y,e.width,e.height)},t.copy=function(t,e){t.x=e.x,t.y=e.y,t.width=e.width,t.height=e.height},t.applyTransform=function(e,n,r){if(!r){e!==n&&t.copy(e,n);return}if(r[1]<1e-5&&r[1]>-.00001&&r[2]<1e-5&&r[2]>-.00001){var i=r[0],h=r[3],f=r[4],p=r[5];e.x=n.x*i+f,e.y=n.y*h+p,e.width=n.width*i,e.height=n.height*h,e.width<0&&(e.x+=e.width,e.width=-e.width),e.height<0&&(e.y+=e.height,e.height=-e.height);return}s.x=l.x=n.x,s.y=c.y=n.y,u.x=c.x=n.x+n.width,u.y=l.y=n.y+n.height,s.transform(r),c.transform(r),u.transform(r),l.transform(r),e.x=o(s.x,u.x,l.x,c.x),e.y=o(s.y,u.y,l.y,c.y);var d=a(s.x,u.x,l.x,c.x),g=a(s.y,u.y,l.y,c.y);e.width=d-e.x,e.height=g-e.y},t}();e.Z=p},367582:function(t,e){var n=function(){function t(t){t&&(this._$eventProcessor=t)}return t.prototype.on=function(t,e,n,r){!this._$handlers&&(this._$handlers={});var i=this._$handlers;if("function"==typeof e&&(r=n,n=e,e=null),!n||!t)return this;var o=this._$eventProcessor;null!=e&&o&&o.normalizeQuery&&(e=o.normalizeQuery(e)),!i[t]&&(i[t]=[]);for(var a=0;a<i[t].length;a++)if(i[t][a].h===n)return this;var s={h:n,query:e,ctx:r||this,callAtLast:n.zrEventfulCallAtLast},u=i[t].length-1,l=i[t][u];return l&&l.callAtLast?i[t].splice(u,0,s):i[t].push(s),this},t.prototype.isSilent=function(t){var e=this._$handlers;return!e||!e[t]||!e[t].length},t.prototype.off=function(t,e){var n=this._$handlers;if(!n)return this;if(!t)return this._$handlers={},this;if(e){if(n[t]){for(var r=[],i=0,o=n[t].length;i<o;i++)n[t][i].h!==e&&r.push(n[t][i]);n[t]=r}n[t]&&0===n[t].length&&delete n[t]}else delete n[t];return this},t.prototype.trigger=function(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];if(!this._$handlers)return this;var r=this._$handlers[t],i=this._$eventProcessor;if(r){for(var o=e.length,a=r.length,s=0;s<a;s++){var u=r[s];if(!i||!i.filter||null==u.query||!!i.filter(t,u.query))switch(o){case 0:u.h.call(u.ctx);break;case 1:u.h.call(u.ctx,e[0]);break;case 2:u.h.call(u.ctx,e[0],e[1]);break;default:u.h.apply(u.ctx,e)}}}return i&&i.afterTrigger&&i.afterTrigger(t),this},t.prototype.triggerWithContext=function(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];if(!this._$handlers)return this;var r=this._$handlers[t],i=this._$eventProcessor;if(r){for(var o=e.length,a=e[o-1],s=r.length,u=0;u<s;u++){var l=r[u];if(!i||!i.filter||null==l.query||!!i.filter(t,l.query))switch(o){case 0:l.h.call(a);break;case 1:l.h.call(a,e[0]);break;case 2:l.h.call(a,e[0],e[1]);break;default:l.h.apply(a,e.slice(1,o-1))}}}return i&&i.afterTrigger&&i.afterTrigger(t),this},t}();e.Z=n},273515:function(t,e,n){n.d(e,{y:function(){return i}});var r=n(660847),i=function(){function t(){this._track=[]}return t.prototype.recognize=function(t,e,n){return this._doTrack(t,e,n),this._recognize(t)},t.prototype.clear=function(){return this._track.length=0,this},t.prototype._doTrack=function(t,e,n){var i=t.touches;if(!!i){for(var o={points:[],touches:[],target:e,event:t},a=0,s=i.length;a<s;a++){var u=i[a],l=r.eV(n,u,{});o.points.push([l.zrX,l.zrY]),o.touches.push(u)}this._track.push(o)}},t.prototype._recognize=function(t){for(var e in a)if(a.hasOwnProperty(e)){var n=a[e](this._track,t);if(n)return n}},t}();function o(t){var e=t[1][0]-t[0][0],n=t[1][1]-t[0][1];return Math.sqrt(e*e+n*n)}var a={pinch:function(t,e){var n=t.length;if(!!n){var r=(t[n-1]||{}).points,i=(t[n-2]||{}).points||r;if(i&&i.length>1&&r&&r.length>1){var a,s=o(r)/o(i);isFinite(s)||(s=1),e.pinchScale=s;var u=[((a=r)[0][0]+a[1][0])/2,(a[0][1]+a[1][1])/2];return e.pinchX=u[0],e.pinchY=u[1],{type:"pinch",target:t[0].target,event:e}}}}}},445471:function(t,e){var n=function(t){this.value=t},r=function(){function t(){this._len=0}return t.prototype.insert=function(t){var e=new n(t);return this.insertEntry(e),e},t.prototype.insertEntry=function(t){this.head?(this.tail.next=t,t.prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},t.prototype.remove=function(t){var e=t.prev,n=t.next;e?e.next=n:this.head=n,n?n.prev=e:this.tail=e,t.next=t.prev=null,this._len--},t.prototype.len=function(){return this._len},t.prototype.clear=function(){this.head=this.tail=null,this._len=0},t}(),i=function(){function t(t){this._list=new r,this._maxSize=10,this._map={},this._maxSize=t}return t.prototype.put=function(t,e){var r=this._list,i=this._map,o=null;if(null==i[t]){var a=r.len(),s=this._lastRemovedEntry;if(a>=this._maxSize&&a>0){var u=r.head;r.remove(u),delete i[u.key],o=u.value,this._lastRemovedEntry=u}s?s.value=e:s=new n(e),s.key=t,r.insertEntry(s),i[t]=s}return o},t.prototype.get=function(t){var e=this._map[t],n=this._list;if(null!=e)return e!==n.tail&&(n.remove(e),n.insertEntry(e)),e.value},t.prototype.clear=function(){this._list.clear(),this._map={}},t.prototype.len=function(){return this._list.len()},t}();e.ZP=i},660487:function(t,e,n){var r=n(317080),i=[0,0],o=[0,0],a=new r.Z,s=new r.Z,u=function(){function t(t,e){this._corners=[],this._axes=[],this._origin=[0,0];for(var n=0;n<4;n++)this._corners[n]=new r.Z;for(var n=0;n<2;n++)this._axes[n]=new r.Z;t&&this.fromBoundingRect(t,e)}return t.prototype.fromBoundingRect=function(t,e){var n=this._corners,i=this._axes,o=t.x,a=t.y,s=o+t.width,u=a+t.height;if(n[0].set(o,a),n[1].set(s,a),n[2].set(s,u),n[3].set(o,u),e)for(var l=0;l<4;l++)n[l].transform(e);r.Z.sub(i[0],n[1],n[0]),r.Z.sub(i[1],n[3],n[0]),i[0].normalize(),i[1].normalize();for(var l=0;l<2;l++)this._origin[l]=i[l].dot(n[0])},t.prototype.intersect=function(t,e){var n=!0,i=!e;return(a.set(1/0,1/0),s.set(0,0),!this._intersectCheckOneSide(this,t,a,s,i,1)&&(n=!1,i)||!this._intersectCheckOneSide(t,this,a,s,i,-1)&&(n=!1,i))?n:(!i&&r.Z.copy(e,n?a:s),n)},t.prototype._intersectCheckOneSide=function(t,e,n,a,s,u){for(var l=!0,c=0;c<2;c++){var h=this._axes[c];if(this._getProjMinMaxOnAxis(c,t._corners,i),this._getProjMinMaxOnAxis(c,e._corners,o),i[1]<o[0]||i[0]>o[1]){if(l=!1,s)return l;var f=Math.abs(o[0]-i[1]),p=Math.abs(i[0]-o[1]);Math.min(f,p)>a.len()&&(f<p?r.Z.scale(a,h,-f*u):r.Z.scale(a,h,p*u))}else if(n){var f=Math.abs(o[0]-i[1]),p=Math.abs(i[0]-o[1]);Math.min(f,p)<n.len()&&(f<p?r.Z.scale(n,h,f*u):r.Z.scale(n,h,-p*u))}}return l},t.prototype._getProjMinMaxOnAxis=function(t,e,n){for(var r=this._axes[t],i=this._origin,o=e[0].dot(r)+i[t],a=o,s=o,u=1;u<e.length;u++){var l=e[u].dot(r)+i[t];a=Math.min(l,a),s=Math.max(l,s)}n[0]=a,n[1]=s},t}();e.Z=u},351415:function(t,e,n){var r=n(217961),i=n(339296),o=n(113525),a=n(570884),s=n(343556),u={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},l=[],c=[],h=[],f=[],p=[],d=[],g=Math.min,v=Math.max,y=Math.cos,m=Math.sin,_=Math.abs,x=Math.PI,w=2*x,S="undefined"!=typeof Float32Array,b=[];function M(t){return Math.round(t/x*1e8)/1e8%2*x}var T=function(){var t;function e(t){this.dpr=1,this._xi=0,this._yi=0,this._x0=0,this._y0=0,this._len=0,t&&(this._saveData=!1),this._saveData&&(this.data=[])}return e.prototype.increaseVersion=function(){this._version++},e.prototype.getVersion=function(){return this._version},e.prototype.setScale=function(t,e,n){(n=n||0)>0&&(this._ux=_(n/o.KL/t)||0,this._uy=_(n/o.KL/e)||0)},e.prototype.setDPR=function(t){this.dpr=t},e.prototype.setContext=function(t){this._ctx=t},e.prototype.getContext=function(){return this._ctx},e.prototype.beginPath=function(){return this._ctx&&this._ctx.beginPath(),this.reset(),this},e.prototype.reset=function(){this._saveData&&(this._len=0),this._pathSegLen&&(this._pathSegLen=null,this._pathLen=0),this._version++},e.prototype.moveTo=function(t,e){return this._drawPendingPt(),this.addData(u.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},e.prototype.lineTo=function(t,e){var n=_(t-this._xi),r=_(e-this._yi),i=n>this._ux||r>this._uy;if(this.addData(u.L,t,e),this._ctx&&i&&this._ctx.lineTo(t,e),i)this._xi=t,this._yi=e,this._pendingPtDist=0;else{var o=n*n+r*r;o>this._pendingPtDist&&(this._pendingPtX=t,this._pendingPtY=e,this._pendingPtDist=o)}return this},e.prototype.bezierCurveTo=function(t,e,n,r,i,o){return this._drawPendingPt(),this.addData(u.C,t,e,n,r,i,o),this._ctx&&this._ctx.bezierCurveTo(t,e,n,r,i,o),this._xi=i,this._yi=o,this},e.prototype.quadraticCurveTo=function(t,e,n,r){return this._drawPendingPt(),this.addData(u.Q,t,e,n,r),this._ctx&&this._ctx.quadraticCurveTo(t,e,n,r),this._xi=n,this._yi=r,this},e.prototype.arc=function(t,e,n,r,i,o){this._drawPendingPt(),b[0]=r,b[1]=i,a=b,s=o,(l=M(a[0]))<0&&(l+=w),c=l-a[0],h=a[1]+c,!s&&h-l>=w?h=l+w:s&&l-h>=w?h=l-w:!s&&l>h?h=l+(w-M(l-h)):s&&l<h&&(h=l-(w-M(h-l))),a[0]=l,a[1]=h,r=b[0];var a,s,l,c,h,f=(i=b[1])-r;return this.addData(u.A,t,e,n,n,r,f,0,o?0:1),this._ctx&&this._ctx.arc(t,e,n,r,i,o),this._xi=y(i)*n+t,this._yi=m(i)*n+e,this},e.prototype.arcTo=function(t,e,n,r,i){return this._drawPendingPt(),this._ctx&&this._ctx.arcTo(t,e,n,r,i),this},e.prototype.rect=function(t,e,n,r){return this._drawPendingPt(),this._ctx&&this._ctx.rect(t,e,n,r),this.addData(u.R,t,e,n,r),this},e.prototype.closePath=function(){this._drawPendingPt(),this.addData(u.Z);var t=this._ctx,e=this._x0,n=this._y0;return t&&t.closePath(),this._xi=e,this._yi=n,this},e.prototype.fill=function(t){t&&t.fill(),this.toStatic()},e.prototype.stroke=function(t){t&&t.stroke(),this.toStatic()},e.prototype.len=function(){return this._len},e.prototype.setData=function(t){var e=t.length;!(this.data&&this.data.length===e)&&S&&(this.data=new Float32Array(e));for(var n=0;n<e;n++)this.data[n]=t[n];this._len=e},e.prototype.appendPath=function(t){!(t instanceof Array)&&(t=[t]);for(var e=t.length,n=0,r=this._len,i=0;i<e;i++)n+=t[i].len();S&&this.data instanceof Float32Array&&(this.data=new Float32Array(r+n));for(var i=0;i<e;i++){for(var o=t[i].data,a=0;a<o.length;a++)this.data[r++]=o[a]}this._len=r},e.prototype.addData=function(t,e,n,r,i,o,a,s,u){if(!!this._saveData){var l=this.data;this._len+arguments.length>l.length&&(this._expandData(),l=this.data);for(var c=0;c<arguments.length;c++)l[this._len++]=arguments[c]}},e.prototype._drawPendingPt=function(){this._pendingPtDist>0&&(this._ctx&&this._ctx.lineTo(this._pendingPtX,this._pendingPtY),this._pendingPtDist=0)},e.prototype._expandData=function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},e.prototype.toStatic=function(){if(!!this._saveData){this._drawPendingPt();var t=this.data;t instanceof Array&&(t.length=this._len,S&&this._len>11&&(this.data=new Float32Array(t)))}},e.prototype.getBoundingRect=function(){h[0]=h[1]=p[0]=p[1]=Number.MAX_VALUE,f[0]=f[1]=d[0]=d[1]=-Number.MAX_VALUE;var t,e=this.data,n=0,o=0,s=0,l=0;for(t=0;t<this._len;){var c=e[t++],g=1===t;switch(g&&(n=e[t],o=e[t+1],s=n,l=o),c){case u.M:n=s=e[t++],o=l=e[t++],p[0]=s,p[1]=l,d[0]=s,d[1]=l;break;case u.L:(0,a.u4)(n,o,e[t],e[t+1],p,d),n=e[t++],o=e[t++];break;case u.C:(0,a.H9)(n,o,e[t++],e[t++],e[t++],e[t++],e[t],e[t+1],p,d),n=e[t++],o=e[t++];break;case u.Q:(0,a.mJ)(n,o,e[t++],e[t++],e[t],e[t+1],p,d),n=e[t++],o=e[t++];break;case u.A:var v=e[t++],_=e[t++],x=e[t++],w=e[t++],S=e[t++],b=e[t++]+S;t+=1;var M=!e[t++];g&&(s=y(S)*x+v,l=m(S)*w+_),(0,a.qL)(v,_,x,w,S,b,M,p,d),n=y(b)*x+v,o=m(b)*w+_;break;case u.R:s=n=e[t++],l=o=e[t++];var T=e[t++],k=e[t++];(0,a.u4)(s,l,s+T,l+k,p,d);break;case u.Z:n=s,o=l}r.VV(h,h,p),r.Fp(f,f,d)}return 0===t&&(h[0]=h[1]=f[0]=f[1]=0),new i.Z(h[0],h[1],f[0]-h[0],f[1]-h[1])},e.prototype._calculateLength=function(){var t=this.data,e=this._len,n=this._ux,r=this._uy,i=0,o=0,a=0,l=0;!this._pathSegLen&&(this._pathSegLen=[]);for(var c=this._pathSegLen,h=0,f=0,p=0;p<e;){var d=t[p++],x=1===p;x&&(i=t[p],o=t[p+1],a=i,l=o);var S=-1;switch(d){case u.M:i=a=t[p++],o=l=t[p++];break;case u.L:var b=t[p++],M=t[p++],T=b-i,k=M-o;(_(T)>n||_(k)>r||p===e-1)&&(S=Math.sqrt(T*T+k*k),i=b,o=M);break;case u.C:var D=t[p++],C=t[p++],b=t[p++],M=t[p++],I=t[p++],A=t[p++];S=(0,s.Ci)(i,o,D,C,b,M,I,A,10),i=I,o=A;break;case u.Q:var D=t[p++],C=t[p++],b=t[p++],M=t[p++];S=(0,s.wQ)(i,o,D,C,b,M,10),i=b,o=M;break;case u.A:var P=t[p++],Z=t[p++],L=t[p++],O=t[p++],R=t[p++],N=t[p++],E=N+R;p+=1,x&&(a=y(R)*L+P,l=m(R)*O+Z),S=v(L,O)*g(w,Math.abs(N)),i=y(E)*L+P,o=m(E)*O+Z;break;case u.R:a=i=t[p++],l=o=t[p++],S=2*t[p++]+2*t[p++];break;case u.Z:var T=a-i,k=l-o;S=Math.sqrt(T*T+k*k),i=a,o=l}S>=0&&(c[f++]=S,h+=S)}return this._pathLen=h,h},e.prototype.rebuildPath=function(t,e){var n,r,i,o,a,h,f,p,d,x,w=this.data,S=this._ux,b=this._uy,M=this._len,T=e<1,k=0,D=0,C=0;if(!T||(!this._pathSegLen&&this._calculateLength(),f=this._pathSegLen,!!(p=e*this._pathLen)))t:for(var I=0;I<M;){var A=w[I++],P=1===I;switch(P&&(i=w[I],o=w[I+1],n=i,r=o),A!==u.L&&C>0&&(t.lineTo(d,x),C=0),A){case u.M:n=i=w[I++],r=o=w[I++],t.moveTo(i,o);break;case u.L:a=w[I++],h=w[I++];var Z=_(a-i),L=_(h-o);if(Z>S||L>b){if(T){var O=f[D++];if(k+O>p){var R=(p-k)/O;t.lineTo(i*(1-R)+a*R,o*(1-R)+h*R);break t}k+=O}t.lineTo(a,h),i=a,o=h,C=0}else{var N=Z*Z+L*L;N>C&&(d=a,x=h,C=N)}break;case u.C:var E=w[I++],B=w[I++],z=w[I++],F=w[I++],W=w[I++],H=w[I++];if(T){var O=f[D++];if(k+O>p){var R=(p-k)/O;(0,s.Vz)(i,E,z,W,R,l),(0,s.Vz)(o,B,F,H,R,c),t.bezierCurveTo(l[1],c[1],l[2],c[2],l[3],c[3]);break t}k+=O}t.bezierCurveTo(E,B,z,F,W,H),i=W,o=H;break;case u.Q:var E=w[I++],B=w[I++],z=w[I++],F=w[I++];if(T){var O=f[D++];if(k+O>p){var R=(p-k)/O;(0,s.Lx)(i,E,z,R,l),(0,s.Lx)(o,B,F,R,c),t.quadraticCurveTo(l[1],c[1],l[2],c[2]);break t}k+=O}t.quadraticCurveTo(E,B,z,F),i=z,o=F;break;case u.A:var V=w[I++],U=w[I++],G=w[I++],X=w[I++],Y=w[I++],q=w[I++],K=w[I++],j=!w[I++],J=G>X?G:X,Q=_(G-X)>.001,$=Y+q,tt=!1;if(T){var O=f[D++];k+O>p&&($=Y+q*(p-k)/O,tt=!0),k+=O}if(Q&&t.ellipse?t.ellipse(V,U,G,X,K,Y,$,j):t.arc(V,U,J,Y,$,j),tt)break t;P&&(n=y(Y)*G+V,r=m(Y)*X+U),i=y($)*G+V,o=m($)*X+U;break;case u.R:n=i=w[I],r=o=w[I+1],a=w[I++],h=w[I++];var te=w[I++],tn=w[I++];if(T){var O=f[D++];if(k+O>p){var tr=p-k;t.moveTo(a,h),t.lineTo(a+g(tr,te),h),(tr-=te)>0&&t.lineTo(a+te,h+g(tr,tn)),(tr-=tn)>0&&t.lineTo(a+v(te-tr,0),h+tn),(tr-=te)>0&&t.lineTo(a,h+v(tn-tr,0));break t}k+=O}t.rect(a,h,te,tn);break;case u.Z:if(T){var O=f[D++];if(k+O>p){var R=(p-k)/O;t.lineTo(i*(1-R)+n*R,o*(1-R)+r*R);break t}k+=O}t.closePath(),i=n,o=r}}},e.prototype.clone=function(){var t=new e,n=this.data;return t.data=n.slice?n.slice():Array.prototype.slice.call(n),t._len=this._len,t},e.CMD=u,e.initDefaultProps=void((t=e.prototype)._saveData=!0,t._ux=0,t._uy=0,t._pendingPtDist=0,t._version=0),e}();e.Z=T},317080:function(t,e){var n=function(){function t(t,e){this.x=t||0,this.y=e||0}return t.prototype.copy=function(t){return this.x=t.x,this.y=t.y,this},t.prototype.clone=function(){return new t(this.x,this.y)},t.prototype.set=function(t,e){return this.x=t,this.y=e,this},t.prototype.equal=function(t){return t.x===this.x&&t.y===this.y},t.prototype.add=function(t){return this.x+=t.x,this.y+=t.y,this},t.prototype.scale=function(t){this.x*=t,this.y*=t},t.prototype.scaleAndAdd=function(t,e){this.x+=t.x*e,this.y+=t.y*e},t.prototype.sub=function(t){return this.x-=t.x,this.y-=t.y,this},t.prototype.dot=function(t){return this.x*t.x+this.y*t.y},t.prototype.len=function(){return Math.sqrt(this.x*this.x+this.y*this.y)},t.prototype.lenSquare=function(){return this.x*this.x+this.y*this.y},t.prototype.normalize=function(){var t=this.len();return this.x/=t,this.y/=t,this},t.prototype.distance=function(t){var e=this.x-t.x,n=this.y-t.y;return Math.sqrt(e*e+n*n)},t.prototype.distanceSquare=function(t){var e=this.x-t.x,n=this.y-t.y;return e*e+n*n},t.prototype.negate=function(){return this.x=-this.x,this.y=-this.y,this},t.prototype.transform=function(t){if(!!t){var e=this.x,n=this.y;return this.x=t[0]*e+t[2]*n+t[4],this.y=t[1]*e+t[3]*n+t[5],this}},t.prototype.toArray=function(t){return t[0]=this.x,t[1]=this.y,t},t.prototype.fromArray=function(t){this.x=t[0],this.y=t[1]},t.set=function(t,e,n){t.x=e,t.y=n},t.copy=function(t,e){t.x=e.x,t.y=e.y},t.len=function(t){return Math.sqrt(t.x*t.x+t.y*t.y)},t.lenSquare=function(t){return t.x*t.x+t.y*t.y},t.dot=function(t,e){return t.x*e.x+t.y*e.y},t.add=function(t,e,n){t.x=e.x+n.x,t.y=e.y+n.y},t.sub=function(t,e,n){t.x=e.x-n.x,t.y=e.y-n.y},t.scale=function(t,e,n){t.x=e.x*n,t.y=e.y*n},t.scaleAndAdd=function(t,e,n,r){t.x=e.x+n.x*r,t.y=e.y+n.y*r},t.lerp=function(t,e,n,r){var i=1-r;t.x=i*e.x+r*n.x,t.y=i*e.y+r*n.y},t}();e.Z=n},762680:function(t,e,n){n.d(e,{dN:function(){return f},kY:function(){return p}});var r=n(921715),i=n(217961),o=r.yR;function a(t){return t>5e-5||t<-.00005}var s=[],u=[],l=r.Ue(),c=Math.abs,h=function(){var t;function e(){}return e.prototype.getLocalTransform=function(t){return e.getLocalTransform(this,t)},e.prototype.setPosition=function(t){this.x=t[0],this.y=t[1]},e.prototype.setScale=function(t){this.scaleX=t[0],this.scaleY=t[1]},e.prototype.setSkew=function(t){this.skewX=t[0],this.skewY=t[1]},e.prototype.setOrigin=function(t){this.originX=t[0],this.originY=t[1]},e.prototype.needLocalTransform=function(){return a(this.rotation)||a(this.x)||a(this.y)||a(this.scaleX-1)||a(this.scaleY-1)||a(this.skewX)||a(this.skewY)},e.prototype.updateTransform=function(){var t=this.parent&&this.parent.transform,e=this.needLocalTransform(),n=this.transform;if(!(e||t)){n&&(o(n),this.invTransform=null);return}n=n||r.Ue(),e?this.getLocalTransform(n):o(n),t&&(e?r.dC(n,t,n):r.JG(n,t)),this.transform=n,this._resolveGlobalScaleRatio(n)},e.prototype._resolveGlobalScaleRatio=function(t){var e=this.globalScaleRatio;if(null!=e&&1!==e){this.getGlobalScale(s);var n=s[0]<0?-1:1,i=s[1]<0?-1:1,o=((s[0]-n)*e+n)/s[0]||0,a=((s[1]-i)*e+i)/s[1]||0;t[0]*=o,t[1]*=o,t[2]*=a,t[3]*=a}this.invTransform=this.invTransform||r.Ue(),r.U_(this.invTransform,t)},e.prototype.getComputedTransform=function(){for(var t=this,e=[];t;)e.push(t),t=t.parent;for(;t=e.pop();)t.updateTransform();return this.transform},e.prototype.setLocalTransform=function(t){if(!!t){var e=t[0]*t[0]+t[1]*t[1],n=t[2]*t[2]+t[3]*t[3],r=Math.atan2(t[1],t[0]),i=Math.PI/2+r-Math.atan2(t[3],t[2]);n=Math.sqrt(n)*Math.cos(i),e=Math.sqrt(e),this.skewX=i,this.skewY=0,this.rotation=-r,this.x=+t[4],this.y=+t[5],this.scaleX=e,this.scaleY=n,this.originX=0,this.originY=0}},e.prototype.decomposeTransform=function(){if(!!this.transform){var t=this.parent,e=this.transform;t&&t.transform&&(t.invTransform=t.invTransform||r.Ue(),r.dC(u,t.invTransform,e),e=u);var n=this.originX,i=this.originY;(n||i)&&(l[4]=n,l[5]=i,r.dC(u,e,l),u[4]-=n,u[5]-=i,e=u),this.setLocalTransform(e)}},e.prototype.getGlobalScale=function(t){var e=this.transform;return(t=t||[],e)?(t[0]=Math.sqrt(e[0]*e[0]+e[1]*e[1]),t[1]=Math.sqrt(e[2]*e[2]+e[3]*e[3]),e[0]<0&&(t[0]=-t[0]),e[3]<0&&(t[1]=-t[1]),t):(t[0]=1,t[1]=1,t)},e.prototype.transformCoordToLocal=function(t,e){var n=[t,e],r=this.invTransform;return r&&i.Ne(n,n,r),n},e.prototype.transformCoordToGlobal=function(t,e){var n=[t,e],r=this.transform;return r&&i.Ne(n,n,r),n},e.prototype.getLineScale=function(){var t=this.transform;return t&&c(t[0]-1)>1e-10&&c(t[3]-1)>1e-10?Math.sqrt(c(t[0]*t[3]-t[2]*t[1])):1},e.prototype.copyTransform=function(t){p(this,t)},e.getLocalTransform=function(t,e){e=e||[];var n=t.originX||0,i=t.originY||0,o=t.scaleX,a=t.scaleY,s=t.anchorX,u=t.anchorY,l=t.rotation||0,c=t.x,h=t.y,f=t.skewX?Math.tan(t.skewX):0,p=t.skewY?Math.tan(-t.skewY):0;if(n||i||s||u){var d=n+s,g=i+u;e[4]=-d*o-f*g*a,e[5]=-g*a-p*d*o}else e[4]=e[5]=0;return e[0]=o,e[3]=a,e[1]=p*o,e[2]=f*a,l&&r.U1(e,e,l),e[4]+=n+c,e[5]+=i+h,e},e.initDefaultProps=void((t=e.prototype).scaleX=t.scaleY=t.globalScaleRatio=1,t.x=t.y=t.originX=t.originY=t.skewX=t.skewY=t.rotation=t.anchorX=t.anchorY=0),e}(),f=["x","y","originX","originY","anchorX","anchorY","rotation","scaleX","scaleY","skewX","skewY"];function p(t,e){for(var n=0;n<f.length;n++){var r=f[n];t[r]=e[r]}}e.ZP=h},833572:function(t,e){var n=Math.round(9*Math.random()),r="function"==typeof Object.defineProperty,i=function(){function t(){this._id="__ec_inner_"+n++}return t.prototype.get=function(t){return this._guard(t)[this._id]},t.prototype.set=function(t,e){var n=this._guard(t);return r?Object.defineProperty(n,this._id,{value:e,enumerable:!1,configurable:!0}):n[this._id]=e,this},t.prototype.delete=function(t){return!!this.has(t)&&(delete this._guard(t)[this._id],!0)},t.prototype.has=function(t){return!!this._guard(t)[this._id]},t.prototype._guard=function(t){if(t!==Object(t))throw TypeError("Value of WeakMap is not a non-null object.");return t},t}();e.Z=i},570884:function(t,e,n){n.d(e,{H9:function(){return v},mJ:function(){return y},qL:function(){return m},u4:function(){return p}});var r=n(217961),i=n(343556),o=Math.min,a=Math.max,s=Math.sin,u=Math.cos,l=2*Math.PI,c=r.Ue(),h=r.Ue(),f=r.Ue();function p(t,e,n,r,i,s){i[0]=o(t,n),i[1]=o(e,r),s[0]=a(t,n),s[1]=a(e,r)}var d=[],g=[];function v(t,e,n,r,s,u,l,c,h,f){var p=i.pP,v=i.af,y=p(t,n,s,l,d);h[0]=1/0,h[1]=1/0,f[0]=-1/0,f[1]=-1/0;for(var m=0;m<y;m++){var _=v(t,n,s,l,d[m]);h[0]=o(_,h[0]),f[0]=a(_,f[0])}y=p(e,r,u,c,g);for(var m=0;m<y;m++){var x=v(e,r,u,c,g[m]);h[1]=o(x,h[1]),f[1]=a(x,f[1])}h[0]=o(t,h[0]),f[0]=a(t,f[0]),h[0]=o(l,h[0]),f[0]=a(l,f[0]),h[1]=o(e,h[1]),f[1]=a(e,f[1]),h[1]=o(c,h[1]),f[1]=a(c,f[1])}function y(t,e,n,r,s,u,l,c){var h=i.QC,f=i.Zm,p=a(o(h(t,n,s),1),0),d=a(o(h(e,r,u),1),0),g=f(t,n,s,p),v=f(e,r,u,d);l[0]=o(t,s,g),l[1]=o(e,u,v),c[0]=a(t,s,g),c[1]=a(e,u,v)}function m(t,e,n,i,o,a,p,d,g){var v=r.VV,y=r.Fp,m=Math.abs(o-a);if(m%l<1e-4&&m>1e-4){d[0]=t-n,d[1]=e-i,g[0]=t+n,g[1]=e+i;return}if(c[0]=u(o)*n+t,c[1]=s(o)*i+e,h[0]=u(a)*n+t,h[1]=s(a)*i+e,v(d,c,h),y(g,c,h),(o%=l)<0&&(o+=l),(a%=l)<0&&(a+=l),o>a&&!p?a+=l:o<a&&p&&(o+=l),p){var _=a;a=o,o=_}for(var x=0;x<a;x+=Math.PI/2)x>o&&(f[0]=u(x)*n+t,f[1]=s(x)*i+e,v(d,f,d),y(g,f,g))}},343556:function(t,e,n){n.d(e,{AZ:function(){return w},Ci:function(){return _},Jz:function(){return S},Lx:function(){return M},QC:function(){return b},Vz:function(){return y},Wr:function(){return T},X_:function(){return d},Zm:function(){return x},af:function(){return p},kD:function(){return g},pP:function(){return v},t1:function(){return m},wQ:function(){return k}});var r=n(217961),i=Math.pow,o=Math.sqrt,a=o(3),s=1/3,u=(0,r.Ue)(),l=(0,r.Ue)(),c=(0,r.Ue)();function h(t){return t>-.00000001&&t<1e-8}function f(t){return t>1e-8||t<-.00000001}function p(t,e,n,r,i){var o=1-i;return o*o*(o*t+3*i*e)+i*i*(i*r+3*o*n)}function d(t,e,n,r,i){var o=1-i;return 3*(((e-t)*o+2*(n-e)*i)*o+(r-n)*i*i)}function g(t,e,n,r,u,l){var c=r+3*(e-n)-t,f=3*(n-2*e+t),p=3*(e-t),d=t-u,g=f*f-3*c*p,v=f*p-9*c*d,y=p*p-3*f*d,m=0;if(h(g)&&h(v)){if(h(f))l[0]=0;else{var _=-p/f;_>=0&&_<=1&&(l[m++]=_)}}else{var x=v*v-4*g*y;if(h(x)){var w=v/g,_=-f/c+w,S=-w/2;_>=0&&_<=1&&(l[m++]=_),S>=0&&S<=1&&(l[m++]=S)}else if(x>0){var b=o(x),M=g*f+1.5*c*(-v+b),T=g*f+1.5*c*(-v-b);M=M<0?-i(-M,s):i(M,s);var _=(-f-(M+(T=T<0?-i(-T,s):i(T,s))))/(3*c);_>=0&&_<=1&&(l[m++]=_)}else{var k=Math.acos((2*g*f-3*c*v)/(2*o(g*g*g)))/3,D=o(g),C=Math.cos(k),_=(-f-2*D*C)/(3*c),S=(-f+D*(C+a*Math.sin(k)))/(3*c),I=(-f+D*(C-a*Math.sin(k)))/(3*c);_>=0&&_<=1&&(l[m++]=_),S>=0&&S<=1&&(l[m++]=S),I>=0&&I<=1&&(l[m++]=I)}}return m}function v(t,e,n,r,i){var a=6*n-12*e+6*t,s=9*e+3*r-3*t-9*n,u=3*e-3*t,l=0;if(h(s)){if(f(a)){var c=-u/a;c>=0&&c<=1&&(i[l++]=c)}}else{var p=a*a-4*s*u;if(h(p))i[0]=-a/(2*s);else if(p>0){var d=o(p),c=(-a+d)/(2*s),g=(-a-d)/(2*s);c>=0&&c<=1&&(i[l++]=c),g>=0&&g<=1&&(i[l++]=g)}}return l}function y(t,e,n,r,i,o){var a=(e-t)*i+t,s=(n-e)*i+e,u=(r-n)*i+n,l=(s-a)*i+a,c=(u-s)*i+s,h=(c-l)*i+l;o[0]=t,o[1]=a,o[2]=l,o[3]=h,o[4]=h,o[5]=c,o[6]=u,o[7]=r}function m(t,e,n,i,a,s,h,f,d,g,v){var y,m,_,x,w,S=.005,b=1/0;u[0]=d,u[1]=g;for(var M=0;M<1;M+=.05)l[0]=p(t,n,a,h,M),l[1]=p(e,i,s,f,M),(x=(0,r.WU)(u,l))<b&&(y=M,b=x);b=1/0;for(var T=0;T<32&&!(S<1e-4);T++){;m=y-S,_=y+S,l[0]=p(t,n,a,h,m),l[1]=p(e,i,s,f,m),x=(0,r.WU)(l,u),m>=0&&x<b?(y=m,b=x):(c[0]=p(t,n,a,h,_),c[1]=p(e,i,s,f,_),w=(0,r.WU)(c,u),_<=1&&w<b?(y=_,b=w):S*=.5)}return v&&(v[0]=p(t,n,a,h,y),v[1]=p(e,i,s,f,y)),o(b)}function _(t,e,n,r,i,o,a,s,u){for(var l=t,c=e,h=0,f=1/u,d=1;d<=u;d++){var g=d*f,v=p(t,n,i,a,g),y=p(e,r,o,s,g),m=v-l,_=y-c;h+=Math.sqrt(m*m+_*_),l=v,c=y}return h}function x(t,e,n,r){var i=1-r;return i*(i*t+2*r*e)+r*r*n}function w(t,e,n,r){return 2*((1-r)*(e-t)+r*(n-e))}function S(t,e,n,r,i){var a=t-2*e+n,s=2*(e-t),u=t-r,l=0;if(h(a)){if(f(s)){var c=-u/s;c>=0&&c<=1&&(i[l++]=c)}}else{var p=s*s-4*a*u;if(h(p)){var c=-s/(2*a);c>=0&&c<=1&&(i[l++]=c)}else if(p>0){var d=o(p),c=(-s+d)/(2*a),g=(-s-d)/(2*a);c>=0&&c<=1&&(i[l++]=c),g>=0&&g<=1&&(i[l++]=g)}}return l}function b(t,e,n){var r=t+n-2*e;return 0===r?.5:(t-e)/r}function M(t,e,n,r,i){var o=(e-t)*r+t,a=(n-e)*r+e,s=(a-o)*r+o;i[0]=t,i[1]=o,i[2]=s,i[3]=s,i[4]=a,i[5]=n}function T(t,e,n,i,a,s,h,f,p){var d,g=.005,v=1/0;u[0]=h,u[1]=f;for(var y=0;y<1;y+=.05){l[0]=x(t,n,a,y),l[1]=x(e,i,s,y);var m=(0,r.WU)(u,l);m<v&&(d=y,v=m)}v=1/0;for(var _=0;_<32&&!(g<1e-4);_++){;var w=d-g,S=d+g;l[0]=x(t,n,a,w),l[1]=x(e,i,s,w);var m=(0,r.WU)(l,u);if(w>=0&&m<v)d=w,v=m;else{c[0]=x(t,n,a,S),c[1]=x(e,i,s,S);var b=(0,r.WU)(c,u);S<=1&&b<v?(d=S,v=b):g*=.5}}return p&&(p[0]=x(t,n,a,d),p[1]=x(e,i,s,d)),o(v)}function k(t,e,n,r,i,o,a){for(var s=t,u=e,l=0,c=1/a,h=1;h<=a;h++){var f=h*c,p=x(t,n,i,f),d=x(e,r,o,f),g=p-s,v=d-u;l+=Math.sqrt(g*g+v*v),s=p,u=d}return l}},734477:function(t,e,n){n.d(e,{A4:function(){return a},F1:function(){return c},UK:function(){return s}});var r=n(939828),i=n(131426),o="___zrEVENTSAVED";function a(t,e,n,a,u){if(e.getBoundingClientRect&&r.Z.domSupported&&!s(e)){var l=e[o]||(e[o]={}),c=function(t,e,n){for(var r=n?"invTrans":"trans",o=e[r],a=e.srcCoords,s=[],u=[],l=!0,c=0;c<4;c++){var h=t[c].getBoundingClientRect(),f=2*c,p=h.left,d=h.top;s.push(p,d),l=l&&a&&p===a[f]&&d===a[f+1],u.push(t[c].offsetLeft,t[c].offsetTop)}return l&&o?o:(e.srcCoords=s,e[r]=n?(0,i.Q)(u,s):(0,i.Q)(s,u))}(function(t,e){var n=e.markers;if(n)return n;n=e.markers=[];for(var r=["left","right"],i=["top","bottom"],o=0;o<4;o++){var a=document.createElement("div"),s=a.style,u=o%2,l=(o>>1)%2;s.cssText=["position: absolute","visibility: hidden","padding: 0","margin: 0","border-width: 0","user-select: none","width:0","height:0",r[u]+":0",i[l]+":0",r[1-u]+":auto",i[1-l]+":auto",""].join("!important;"),t.appendChild(a),n.push(a)}return n}(e,l),l,u);if(c)return c(t,n,a),!0}return!1}function s(t){return"CANVAS"===t.nodeName.toUpperCase()}var u=/([&<>"'])/g,l={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function c(t){return null==t?"":(t+"").replace(u,function(t,e){return l[e]})}},939828:function(t,e){var n=function(){this.firefox=!1,this.ie=!1,this.edge=!1,this.newEdge=!1,this.weChat=!1},r=new function(){this.browser=new n,this.node=!1,this.wxa=!1,this.worker=!1,this.svgSupported=!1,this.touchEventsSupported=!1,this.pointerEventsSupported=!1,this.domSupported=!1,this.transformSupported=!1,this.transform3dSupported=!1,this.hasGlobalWindow="undefined"!=typeof window};"object"==typeof wx&&"function"==typeof wx.getSystemInfoSync?(r.wxa=!0,r.touchEventsSupported=!0):"undefined"==typeof document&&"undefined"!=typeof self?r.worker=!0:"undefined"==typeof navigator||0===navigator.userAgent.indexOf("Node.js")?(r.node=!0,r.svgSupported=!0):function(t,e){var n=e.browser,r=t.match(/Firefox\/([\d.]+)/),i=t.match(/MSIE\s([\d.]+)/)||t.match(/Trident\/.+?rv:(([\d.]+))/),o=t.match(/Edge?\/([\d.]+)/),a=/micromessenger/i.test(t);r&&(n.firefox=!0,n.version=r[1]),i&&(n.ie=!0,n.version=i[1]),o&&(n.edge=!0,n.version=o[1],n.newEdge=+o[1].split(".")[0]>18),a&&(n.weChat=!0),e.svgSupported="undefined"!=typeof SVGRect,e.touchEventsSupported="ontouchstart"in window&&!n.ie&&!n.edge,e.pointerEventsSupported="onpointerdown"in window&&(n.edge||n.ie&&+n.version>=11),e.domSupported="undefined"!=typeof document;var s=document.documentElement.style;e.transform3dSupported=(n.ie&&"transition"in s||n.edge||"WebKitCSSMatrix"in window&&"m11"in new WebKitCSSMatrix||"MozPerspective"in s)&&!("OTransition"in s),e.transformSupported=e.transform3dSupported||n.ie&&+n.version>=9}(navigator.userAgent,r);e.Z=r},660847:function(t,e,n){n.d(e,{OD:function(){return h},Oo:function(){return f},eV:function(){return u},iP:function(){return c},sT:function(){return d},xg:function(){return p}});var r=n(939828),i=n(734477),o=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,a=[],s=r.Z.browser.firefox&&39>+r.Z.browser.version.split(".")[0];function u(t,e,n,r){return n=n||{},r?l(t,e,n):s&&null!=e.layerX&&e.layerX!==e.offsetX?(n.zrX=e.layerX,n.zrY=e.layerY):null!=e.offsetX?(n.zrX=e.offsetX,n.zrY=e.offsetY):l(t,e,n),n}function l(t,e,n){if(r.Z.domSupported&&t.getBoundingClientRect){var o=e.clientX,s=e.clientY;if((0,i.UK)(t)){var u=t.getBoundingClientRect();n.zrX=o-u.left,n.zrY=s-u.top;return}if((0,i.A4)(a,t,o,s)){n.zrX=a[0],n.zrY=a[1];return}}n.zrX=n.zrY=0}function c(t){return t||window.event}function h(t,e,n){if(null!=(e=c(e)).zrX)return e;var r=e.type;if(r&&r.indexOf("touch")>=0){var i="touchend"!==r?e.targetTouches[0]:e.changedTouches[0];i&&u(t,i,e,n)}else{u(t,e,e,n);var a=function(t){var e=t.wheelDelta;if(e)return e;var n=t.deltaX,r=t.deltaY;if(null==n||null==r)return e;var i=0!==r?Math.abs(r):Math.abs(n);return 3*i*(r>0?-1:r<0?1:n>0?-1:1)}(e);e.zrDelta=a?a/120:-(e.detail||0)/3}var s=e.button;return null==e.which&&void 0!==s&&o.test(e.type)&&(e.which=1&s?1:2&s?3:4&s?2:0),e}function f(t,e,n,r){t.addEventListener(e,n,r)}function p(t,e,n,r){t.removeEventListener(e,n,r)}var d=function(t){t.preventDefault(),t.stopPropagation(),t.cancelBubble=!0}},131426:function(t,e,n){n.d(e,{Q:function(){return o}});var r=Math.log(2);function i(t,e,n,o,a,s){var u=o+"-"+a,l=t.length;if(s.hasOwnProperty(u))return s[u];if(1===e){var c=Math.round(Math.log((1<<l)-1&~a)/r);return t[n][c]}for(var h=o|1<<n,f=n+1;o&1<<f;)f++;for(var p=0,d=0,g=0;d<l;d++){var v=1<<d;!(v&a)&&(p+=(g%2?-1:1)*t[n][d]*i(t,e-1,f,h,a|v,s),g++)}return s[u]=p,p}function o(t,e){var n=[[t[0],t[1],1,0,0,0,-e[0]*t[0],-e[0]*t[1]],[0,0,0,t[0],t[1],1,-e[1]*t[0],-e[1]*t[1]],[t[2],t[3],1,0,0,0,-e[2]*t[2],-e[2]*t[3]],[0,0,0,t[2],t[3],1,-e[3]*t[2],-e[3]*t[3]],[t[4],t[5],1,0,0,0,-e[4]*t[4],-e[4]*t[5]],[0,0,0,t[4],t[5],1,-e[5]*t[4],-e[5]*t[5]],[t[6],t[7],1,0,0,0,-e[6]*t[6],-e[6]*t[7]],[0,0,0,t[6],t[7],1,-e[7]*t[6],-e[7]*t[7]]],r={},o=i(n,8,0,0,0,r);if(0!==o){for(var a=[],s=0;s<8;s++)for(var u=0;u<8;u++)null==a[u]&&(a[u]=0),a[u]+=((s+u)%2?-1:1)*i(n,7,0===s?1:0,1<<s,1<<u,r)/o*e[s];return function(t,e,n){var r=e*a[6]+n*a[7]+1;t[0]=(e*a[0]+n*a[1]+a[2])/r,t[1]=(e*a[3]+n*a[4]+a[5])/r}}}},921715:function(t,e,n){function r(){return[1,0,0,1,0,0]}function i(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t}function o(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t}function a(t,e,n){var r=e[0]*n[0]+e[2]*n[1],i=e[1]*n[0]+e[3]*n[1],o=e[0]*n[2]+e[2]*n[3],a=e[1]*n[2]+e[3]*n[3],s=e[0]*n[4]+e[2]*n[5]+e[4],u=e[1]*n[4]+e[3]*n[5]+e[5];return t[0]=r,t[1]=i,t[2]=o,t[3]=a,t[4]=s,t[5]=u,t}function s(t,e,n){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4]+n[0],t[5]=e[5]+n[1],t}function u(t,e,n,r){void 0===r&&(r=[0,0]);var i=e[0],o=e[2],a=e[4],s=e[1],u=e[3],l=e[5],c=Math.sin(n),h=Math.cos(n);return t[0]=i*h+s*c,t[1]=-i*c+s*h,t[2]=o*h+u*c,t[3]=-o*c+h*u,t[4]=h*(a-r[0])+c*(l-r[1])+r[0],t[5]=h*(l-r[1])-c*(a-r[0])+r[1],t}function l(t,e,n){var r=n[0],i=n[1];return t[0]=e[0]*r,t[1]=e[1]*i,t[2]=e[2]*r,t[3]=e[3]*i,t[4]=e[4]*r,t[5]=e[5]*i,t}function c(t,e){var n=e[0],r=e[2],i=e[4],o=e[1],a=e[3],s=e[5],u=n*a-o*r;return u?(u=1/u,t[0]=a*u,t[1]=-o*u,t[2]=-r*u,t[3]=n*u,t[4]=(r*s-a*i)*u,t[5]=(o*i-n*s)*u,t):null}n.d(e,{Iu:function(){return s},JG:function(){return o},U1:function(){return u},U_:function(){return c},Ue:function(){return r},bA:function(){return l},dC:function(){return a},yR:function(){return i}})},252919:function(t,e,n){n.d(e,{Uo:function(){return s},n5:function(){return o},qW:function(){return l},rk:function(){return a}});var r,i,o=12,a="sans-serif",s="12px "+a,u=function(t){var e={};if("undefined"==typeof JSON)return e;for(var n=0;n<t.length;n++){var r=String.fromCharCode(n+32),i=(t.charCodeAt(n)-20)/100;e[r]=i}return e}("007LLmW'55;N0500LLLLLLLLLL00NNNLzWW\\\\WQb\\0FWLg\\bWb\\WQ\\WrWWQ000CL5LLFLL0LL**F*gLLLL5F0LF\\FFF5.5N");var l={createCanvas:function(){return"undefined"!=typeof document&&document.createElement("canvas")},measureText:function(t,e){if(!r){var n=l.createCanvas();r=n&&n.getContext("2d")}if(r)return i!==e&&(i=r.font=e||s),r.measureText(t);t=t||"",e=e||s;var a=/((?:\d+)?\.?\d*)px/.exec(e),c=a&&+a[1]||o,h=0;if(e.indexOf("mono")>=0)h=c*t.length;else for(var f=0;f<t.length;f++){var p=u[t[f]];h+=null==p?c:p*c}return{width:h}},loadImage:function(t,e,n){var r=new Image;return r.onload=e,r.onerror=n,r.src=t,r}}},128067:function(t,e,n){n.d(e,{Z:function(){return s}});function r(t,e,n,r){var i=e+1;if(i===n)return 1;if(0>r(t[i++],t[e])){for(;i<n&&0>r(t[i],t[i-1]);)i++;(function(t,e,n){for(n--;e<n;){var r=t[e];t[e++]=t[n],t[n--]=r}})(t,e,i)}else for(;i<n&&r(t[i],t[i-1])>=0;)i++;return i-e}function i(t,e,n,r,i){for(r===e&&r++;r<n;r++){for(var o,a=t[r],s=e,u=r;s<u;)0>i(a,t[o=s+u>>>1])?u=o:s=o+1;var l=r-s;switch(l){case 3:t[s+3]=t[s+2];case 2:t[s+2]=t[s+1];case 1:t[s+1]=t[s];break;default:for(;l>0;)t[s+l]=t[s+l-1],l--}t[s]=a}}function o(t,e,n,r,i,o){var a=0,s=0,u=1;if(o(t,e[n+i])>0){for(s=r-i;u<s&&o(t,e[n+i+u])>0;)a=u,(u=(u<<1)+1)<=0&&(u=s);u>s&&(u=s),a+=i,u+=i}else{for(s=i+1;u<s&&0>=o(t,e[n+i-u]);)a=u,(u=(u<<1)+1)<=0&&(u=s);u>s&&(u=s);var l=a;a=i-u,u=i-l}for(a++;a<u;){var c=a+(u-a>>>1);o(t,e[n+c])>0?a=c+1:u=c}return u}function a(t,e,n,r,i,o){var a=0,s=0,u=1;if(0>o(t,e[n+i])){for(s=i+1;u<s&&0>o(t,e[n+i-u]);)a=u,(u=(u<<1)+1)<=0&&(u=s);u>s&&(u=s);var l=a;a=i-u,u=i-l}else{for(s=r-i;u<s&&o(t,e[n+i+u])>=0;)a=u,(u=(u<<1)+1)<=0&&(u=s);u>s&&(u=s),a+=i,u+=i}for(a++;a<u;){var c=a+(u-a>>>1);0>o(t,e[n+c])?u=c:a=c+1}return u}function s(t,e,n,s){!n&&(n=0),!s&&(s=t.length);var u=s-n;if(!(u<2)){var l=0;if(u<32){l=r(t,n,s,e),i(t,n,s,n+l,e);return}var c=function(t,e){var n,r,i=7,s=0,u=[];n=[],r=[];function l(l){var c=n[l],h=r[l],f=n[l+1],p=r[l+1];r[l]=h+p,l===s-3&&(n[l+1]=n[l+2],r[l+1]=r[l+2]),s--;var d=a(t[f],t,c,h,0,e);if(c+=d,0!=(h-=d)&&0!==(p=o(t[c+h-1],t,f,p,p-1,e)))h<=p?function(n,r,s,l){var c,h,f,p=0;for(p=0;p<r;p++)u[p]=t[n+p];var d=0,g=s,v=n;if(t[v++]=t[g++],0==--l){for(p=0;p<r;p++)t[v+p]=u[d+p];return}if(1===r){for(p=0;p<l;p++)t[v+p]=t[g+p];t[v+l]=u[d];return}for(var y=i;;){c=0,h=0,f=!1;do if(0>e(t[g],u[d])){if(t[v++]=t[g++],h++,c=0,0==--l){f=!0;break}}else if(t[v++]=u[d++],c++,h=0,1==--r){f=!0;break}while((c|h)<y);if(f)break;do{if(0!==(c=a(t[g],u,d,r,0,e))){for(p=0;p<c;p++)t[v+p]=u[d+p];if(v+=c,d+=c,(r-=c)<=1){f=!0;break}}if(t[v++]=t[g++],0==--l){f=!0;break}if(0!==(h=o(u[d],t,g,l,0,e))){for(p=0;p<h;p++)t[v+p]=t[g+p];if(v+=h,g+=h,0==(l-=h)){f=!0;break}}if(t[v++]=u[d++],1==--r){f=!0;break}y--}while(c>=7||h>=7);if(f)break;y<0&&(y=0),y+=2}if((i=y)<1&&(i=1),1===r){for(p=0;p<l;p++)t[v+p]=t[g+p];t[v+l]=u[d]}else if(0===r)throw Error();else for(p=0;p<r;p++)t[v+p]=u[d+p]}(c,h,f,p):function(n,r,s,l){var c=0;for(c=0;c<l;c++)u[c]=t[s+c];var h=n+r-1,f=l-1,p=s+l-1,d=0,g=0;if(t[p--]=t[h--],0==--r){for(c=0,d=p-(l-1);c<l;c++)t[d+c]=u[c];return}if(1===l){for(p-=r,h-=r,g=p+1,d=h+1,c=r-1;c>=0;c--)t[g+c]=t[d+c];t[p]=u[f];return}for(var v=i;;){var y=0,m=0,_=!1;do if(0>e(u[f],t[h])){if(t[p--]=t[h--],y++,m=0,0==--r){_=!0;break}}else if(t[p--]=u[f--],m++,y=0,1==--l){_=!0;break}while((y|m)<v);if(_)break;do{if(0!=(y=r-a(u[f],t,n,r,r-1,e))){for(p-=y,h-=y,r-=y,g=p+1,d=h+1,c=y-1;c>=0;c--)t[g+c]=t[d+c];if(0===r){_=!0;break}}if(t[p--]=u[f--],1==--l){_=!0;break}if(0!=(m=l-o(t[h],u,0,l,l-1,e))){for(p-=m,f-=m,l-=m,g=p+1,d=f+1,c=0;c<m;c++)t[g+c]=u[d+c];if(l<=1){_=!0;break}}if(t[p--]=t[h--],0==--r){_=!0;break}v--}while(y>=7||m>=7);if(_)break;v<0&&(v=0),v+=2}if((i=v)<1&&(i=1),1===l){for(p-=r,h-=r,g=p+1,d=h+1,c=r-1;c>=0;c--)t[g+c]=t[d+c];t[p]=u[f]}else if(0===l)throw Error();else for(c=0,d=p-(l-1);c<l;c++)t[d+c]=u[c]}(c,h,f,p)}return{mergeRuns:function(){for(;s>1;){var t=s-2;if(t>=1&&r[t-1]<=r[t]+r[t+1]||t>=2&&r[t-2]<=r[t]+r[t-1])r[t-1]<r[t+1]&&t--;else if(r[t]>r[t+1])break;l(t)}},forceMergeRuns:function(){for(;s>1;){var t=s-2;t>0&&r[t-1]<r[t+1]&&t--,l(t)}},pushRun:function(t,e){n[s]=t,r[s]=e,s+=1}}}(t,e),h=function(t){for(var e=0;t>=32;)e|=1&t,t>>=1;return t+e}(u);do{if((l=r(t,n,s,e))<h){var f=u;f>h&&(f=h),i(t,n,n+f,n+l,e),l=f}c.pushRun(n,l),c.mergeRuns(),u-=l,n+=l}while(0!==u);c.forceMergeRuns()}}},807028:function(t,e,n){n.d(e,{$j:function(){return ta},Bu:function(){return V},H:function(){return y},HD:function(){return O},I3:function(){return tl},Jv:function(){return U},Kn:function(){return E},M8:function(){return v},MY:function(){return q},Qq:function(){return W},R1:function(){return X},RI:function(){return ts},S6:function(){return T},TS:function(){return function t(e,n,r){if(!E(n)||!E(e))return r?m(n):e;for(var i in n)if(n.hasOwnProperty(i)&&i!==d){var o=e[i],a=n[i];!(E(a)&&E(o))||Z(a)||Z(o)||F(a)||F(o)||B(a)||B(o)||$(a)||$(o)?(r||!(i in e))&&(e[i]=m(n[i])):t(o,a,r)}return e}},UI:function(){return k},WA:function(){return P},WW:function(){return ti},XP:function(){return I},XW:function(){return S},ZT:function(){return tu},ak:function(){return A},cd:function(){return R},ce:function(){return x},cq:function(){return w},d9:function(){return m},dL:function(){return H},fU:function(){return z},fy:function(){return j},hX:function(){return C},hj:function(){return N},hu:function(){return K},jB:function(){return b},kJ:function(){return Z},kW:function(){return tr},l7:function(){return _},mf:function(){return L},nW:function(){return to},pD:function(){return G},s7:function(){return Q},tP:function(){return Y},u4:function(){return D},zG:function(){return M}});var r=n(252919),i=D(["Function","RegExp","Date","Error","CanvasGradient","CanvasPattern","Image","Canvas"],function(t,e){return t["[object "+e+"]"]=!0,t},{}),o=D(["Int8","Uint8","Uint8Clamped","Int16","Uint16","Int32","Uint32","Float32","Float64"],function(t,e){return t["[object "+e+"Array]"]=!0,t},{}),a=Object.prototype.toString,s=Array.prototype,u=s.forEach,l=s.filter,c=s.slice,h=s.map,f=(function(){}).constructor,p=f?f.prototype:null,d="__proto__",g=2311;function v(){return g++}function y(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];"undefined"!=typeof console&&console.error.apply(console,t)}function m(t){if(null==t||"object"!=typeof t)return t;var e=t,n=a.call(t);if("[object Array]"===n){if(!function(t){return t[J]}(t)){e=[];for(var r=0,s=t.length;r<s;r++)e[r]=m(t[r])}}else if(o[n]){if(!function(t){return t[J]}(t)){var u=t.constructor;if(u.from)e=u.from(t);else{e=new u(t.length);for(var r=0,s=t.length;r<s;r++)e[r]=t[r]}}}else if(!i[n]&&!function(t){return t[J]}(t)&&!F(t))for(var l in e={},t)t.hasOwnProperty(l)&&l!==d&&(e[l]=m(t[l]));return e}function _(t,e){if(Object.assign)Object.assign(t,e);else for(var n in e)e.hasOwnProperty(n)&&n!==d&&(t[n]=e[n]);return t}function x(t,e,n){for(var r=I(e),i=0;i<r.length;i++){var o=r[i];(n?null!=e[o]:null==t[o])&&(t[o]=e[o])}return t}function w(t,e){if(t){if(t.indexOf)return t.indexOf(e);for(var n=0,r=t.length;n<r;n++)if(t[n]===e)return n}return -1}function S(t,e){var n=t.prototype;function r(){}for(var i in r.prototype=e.prototype,t.prototype=new r,n)n.hasOwnProperty(i)&&(t.prototype[i]=n[i]);t.prototype.constructor=t,t.superClass=e}function b(t,e,n){if(t="prototype"in t?t.prototype:t,e="prototype"in e?e.prototype:e,Object.getOwnPropertyNames){for(var r=Object.getOwnPropertyNames(e),i=0;i<r.length;i++){var o=r[i];"constructor"!==o&&(n?null!=e[o]:null==t[o])&&(t[o]=e[o])}}else x(t,e,n)}function M(t){return!!t&&"string"!=typeof t&&"number"==typeof t.length}function T(t,e,n){if(!!(t&&e))if(t.forEach&&t.forEach===u)t.forEach(e,n);else if(t.length===+t.length)for(var r=0,i=t.length;r<i;r++)e.call(n,t[r],r,t);else for(var o in t)t.hasOwnProperty(o)&&e.call(n,t[o],o,t)}function k(t,e,n){if(!t)return[];if(!e)return Y(t);if(t.map&&t.map===h)return t.map(e,n);for(var r=[],i=0,o=t.length;i<o;i++)r.push(e.call(n,t[i],i,t));return r}function D(t,e,n,r){if(!!(t&&e)){for(var i=0,o=t.length;i<o;i++)n=e.call(r,n,t[i],i,t);return n}}function C(t,e,n){if(!t)return[];if(!e)return Y(t);if(t.filter&&t.filter===l)return t.filter(e,n);for(var r=[],i=0,o=t.length;i<o;i++)e.call(n,t[i],i,t)&&r.push(t[i]);return r}function I(t){if(!t)return[];if(Object.keys)return Object.keys(t);var e=[];for(var n in t)t.hasOwnProperty(n)&&e.push(n);return e}r.qW.createCanvas;var A=p&&L(p.bind)?p.call.bind(p.bind):function(t,e){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];return function(){return t.apply(e,n.concat(c.call(arguments)))}};function P(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];return function(){return t.apply(this,e.concat(c.call(arguments)))}}function Z(t){return Array.isArray?Array.isArray(t):"[object Array]"===a.call(t)}function L(t){return"function"==typeof t}function O(t){return"string"==typeof t}function R(t){return"[object String]"===a.call(t)}function N(t){return"number"==typeof t}function E(t){var e=typeof t;return"function"===e||!!t&&"object"===e}function B(t){return!!i[a.call(t)]}function z(t){return!!o[a.call(t)]}function F(t){return"object"==typeof t&&"number"==typeof t.nodeType&&"object"==typeof t.ownerDocument}function W(t){return null!=t.colorStops}function H(t){return null!=t.image}function V(t){return t!=t}function U(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];for(var n=0,r=t.length;n<r;n++)if(null!=t[n])return t[n]}function G(t,e){return null!=t?t:e}function X(t,e,n){return null!=t?t:null!=e?e:n}function Y(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];return c.apply(t,e)}function q(t){if("number"==typeof t)return[t,t,t,t];var e=t.length;return 2===e?[t[0],t[1],t[0],t[1]]:3===e?[t[0],t[1],t[2],t[1]]:t}function K(t,e){if(!t)throw Error(e)}function j(t){return null==t?null:"function"==typeof t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}var J="__ec_primitive__";function Q(t){t[J]=!0}function $(t){return t[J]}var tt=function(){function t(){this.data={}}return t.prototype.delete=function(t){var e=this.has(t);return e&&delete this.data[t],e},t.prototype.has=function(t){return this.data.hasOwnProperty(t)},t.prototype.get=function(t){return this.data[t]},t.prototype.set=function(t,e){return this.data[t]=e,this},t.prototype.keys=function(){return I(this.data)},t.prototype.forEach=function(t){var e=this.data;for(var n in e)e.hasOwnProperty(n)&&t(e[n],n)},t}(),te="function"==typeof Map,tn=function(){function t(e){var n=Z(e);this.data=te?new Map:new tt;var r=this;function i(t,e){n?r.set(t,e):r.set(e,t)}e instanceof t?e.each(i):e&&T(e,i)}return t.prototype.hasKey=function(t){return this.data.has(t)},t.prototype.get=function(t){return this.data.get(t)},t.prototype.set=function(t,e){return this.data.set(t,e),e},t.prototype.each=function(t,e){this.data.forEach(function(n,r){t.call(e,n,r)})},t.prototype.keys=function(){var t=this.data.keys();return te?Array.from(t):t},t.prototype.removeKey=function(t){this.data.delete(t)},t}();function tr(t){return new tn(t)}function ti(t,e){for(var n=new t.constructor(t.length+e.length),r=0;r<t.length;r++)n[r]=t[r];for(var i=t.length,r=0;r<e.length;r++)n[r+i]=e[r];return n}function to(t,e){var n;if(Object.create)n=Object.create(t);else{var r=function(){};r.prototype=t,n=new r}return e&&_(n,e),n}function ta(t){var e=t.style;e.webkitUserSelect="none",e.userSelect="none",e.webkitTapHighlightColor="rgba(0,0,0,0)",e["-webkit-touch-callout"]="none"}function ts(t,e){return t.hasOwnProperty(e)}function tu(){}var tl=180/Math.PI},217961:function(t,e,n){function r(t,e){return null==t&&(t=0),null==e&&(e=0),[t,e]}function i(t){return[t[0],t[1]]}function o(t,e,n){return t[0]=e[0]+n[0],t[1]=e[1]+n[1],t}function a(t,e,n){return t[0]=e[0]-n[0],t[1]=e[1]-n[1],t}n.d(e,{Fp:function(){return g},Fv:function(){return u},IH:function(){return o},Ne:function(){return p},TE:function(){return l},TK:function(){return c},Ue:function(){return r},VV:function(){return d},WU:function(){return h},bA:function(){return s},d9:function(){return i},lu:function(){return a},t7:function(){return f}});function s(t,e,n){return t[0]=e[0]*n,t[1]=e[1]*n,t}function u(t,e){var n=Math.sqrt(function(t){return t[0]*t[0]+t[1]*t[1]}(e));return 0===n?(t[0]=0,t[1]=0):(t[0]=e[0]/n,t[1]=e[1]/n),t}function l(t,e){return Math.sqrt((t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1]))}var c=l,h=function(t,e){return(t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1])};function f(t,e,n,r){return t[0]=e[0]+r*(n[0]-e[0]),t[1]=e[1]+r*(n[1]-e[1]),t}function p(t,e,n){var r=e[0],i=e[1];return t[0]=n[0]*r+n[2]*i+n[4],t[1]=n[1]*r+n[3]*i+n[5],t}function d(t,e,n){return t[0]=Math.min(e[0],n[0]),t[1]=Math.min(e[1],n[1]),t}function g(t,e,n){return t[0]=Math.max(e[0],n[0]),t[1]=Math.max(e[1],n[1]),t}},829465:function(t,e,n){var r,i,o,a=n(904311),s=n(660847),u=n(807028),l=n(367582),c=n(939828),h=c.Z.domSupported;var f=(r=["click","dblclick","mousewheel","wheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],i={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},o=u.UI(r,function(t){var e=t.replace("mouse","pointer");return i.hasOwnProperty(e)?e:t}),{mouse:r,touch:["touchstart","touchend","touchmove"],pointer:o}),p={mouse:["mousemove","mouseup"],pointer:["pointermove","pointerup"]},d=!1;function g(t){var e=t.pointerType;return"pen"===e||"touch"===e}function v(t){t&&(t.zrByTouch=!0)}function y(t,e){for(var n=e,r=!1;n&&9!==n.nodeType&&!(r=n.domBelongToZr||n!==e&&n===t.painterRoot);)n=n.parentNode;return r}var m=function(t,e){this.stopPropagation=u.ZT,this.stopImmediatePropagation=u.ZT,this.preventDefault=u.ZT,this.type=e.type,this.target=this.currentTarget=t.dom,this.pointerType=e.pointerType,this.clientX=e.clientX,this.clientY=e.clientY},_={mousedown:function(t){t=(0,s.OD)(this.dom,t),this.__mayPointerCapture=[t.zrX,t.zrY],this.trigger("mousedown",t)},mousemove:function(t){t=(0,s.OD)(this.dom,t);var e=this.__mayPointerCapture;e&&(t.zrX!==e[0]||t.zrY!==e[1])&&this.__togglePointerCapture(!0),this.trigger("mousemove",t)},mouseup:function(t){t=(0,s.OD)(this.dom,t),this.__togglePointerCapture(!1),this.trigger("mouseup",t)},mouseout:function(t){!y(this,(t=(0,s.OD)(this.dom,t)).toElement||t.relatedTarget)&&(this.__pointerCapturing&&(t.zrEventControl="no_globalout"),this.trigger("mouseout",t))},wheel:function(t){d=!0,t=(0,s.OD)(this.dom,t),this.trigger("mousewheel",t)},mousewheel:function(t){if(!d)t=(0,s.OD)(this.dom,t),this.trigger("mousewheel",t)},touchstart:function(t){v(t=(0,s.OD)(this.dom,t)),this.__lastTouchMoment=new Date,this.handler.processGesture(t,"start"),_.mousemove.call(this,t),_.mousedown.call(this,t)},touchmove:function(t){v(t=(0,s.OD)(this.dom,t)),this.handler.processGesture(t,"change"),_.mousemove.call(this,t)},touchend:function(t){v(t=(0,s.OD)(this.dom,t)),this.handler.processGesture(t,"end"),_.mouseup.call(this,t),+new Date-+this.__lastTouchMoment<300&&_.click.call(this,t)},pointerdown:function(t){_.mousedown.call(this,t)},pointermove:function(t){!g(t)&&_.mousemove.call(this,t)},pointerup:function(t){_.mouseup.call(this,t)},pointerout:function(t){!g(t)&&_.mouseout.call(this,t)}};u.S6(["click","dblclick","contextmenu"],function(t){_[t]=function(e){e=(0,s.OD)(this.dom,e),this.trigger(t,e)}});var x={pointermove:function(t){!g(t)&&x.mousemove.call(this,t)},pointerup:function(t){x.mouseup.call(this,t)},mousemove:function(t){this.trigger("mousemove",t)},mouseup:function(t){var e=this.__pointerCapturing;this.__togglePointerCapture(!1),this.trigger("mouseup",t),e&&(t.zrEventControl="only_globalout",this.trigger("mouseout",t))}};function w(t,e,n,r){t.mounted[e]=n,t.listenerOpts[e]=r,(0,s.Oo)(t.domTarget,e,n,r)}function S(t){var e=t.mounted;for(var n in e)e.hasOwnProperty(n)&&(0,s.xg)(t.domTarget,n,e[n],t.listenerOpts[n]);t.mounted={}}var b=function(t,e){this.mounted={},this.listenerOpts={},this.touching=!1,this.domTarget=t,this.domHandlers=e},M=function(t){function e(e,n){var r,i,o,a=t.call(this)||this;return a.__pointerCapturing=!1,a.dom=e,a.painterRoot=n,a._localHandlerScope=new b(e,_),h&&(a._globalHandlerScope=new b(document,x)),r=a,o=(i=a._localHandlerScope).domHandlers,c.Z.pointerEventsSupported?u.S6(f.pointer,function(t){w(i,t,function(e){o[t].call(r,e)})}):(c.Z.touchEventsSupported&&u.S6(f.touch,function(t){w(i,t,function(e){var n;o[t].call(r,e),(n=i).touching=!0,null!=n.touchTimer&&(clearTimeout(n.touchTimer),n.touchTimer=null),n.touchTimer=setTimeout(function(){n.touching=!1,n.touchTimer=null},700)})}),u.S6(f.mouse,function(t){w(i,t,function(e){e=(0,s.iP)(e),!i.touching&&o[t].call(r,e)})})),a}return(0,a.ZT)(e,t),e.prototype.dispose=function(){S(this._localHandlerScope),h&&S(this._globalHandlerScope)},e.prototype.setCursor=function(t){this.dom.style&&(this.dom.style.cursor=t||"default")},e.prototype.__togglePointerCapture=function(t){if(this.__mayPointerCapture=null,h&&+this.__pointerCapturing^+t){this.__pointerCapturing=t;var e=this._globalHandlerScope;t?function(t,e){c.Z.pointerEventsSupported?u.S6(p.pointer,n):!c.Z.touchEventsSupported&&u.S6(p.mouse,n);function n(n){w(e,n,function(r){if(!y(t,(r=(0,s.iP)(r)).target)){var i,o;i=t,o=r,r=(0,s.OD)(i.dom,new m(i,o),!0),e.domHandlers[n].call(t,r)}},{capture:!0})}}(this,e):S(e)}},e}(l.Z);e.Z=M},702654:function(t,e,n){var r=n(904311),i=n(894641),o=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="compound",e}return(0,r.ZT)(e,t),e.prototype._updatePathDirty=function(){for(var t=this.shape.paths,e=this.shapeChanged(),n=0;n<t.length;n++)e=e||t[n].shapeChanged();e&&this.dirtyShape()},e.prototype.beforeBrush=function(){this._updatePathDirty();for(var t=this.shape.paths||[],e=this.getGlobalScale(),n=0;n<t.length;n++)!t[n].path&&t[n].createPathProxy(),t[n].path.setScale(e[0],e[1],t[n].segmentIgnoreThreshold)},e.prototype.buildPath=function(t,e){for(var n=e.paths||[],r=0;r<n.length;r++)n[r].buildPath(t,n[r].shape,!0)},e.prototype.afterBrush=function(){for(var t=this.shape.paths||[],e=0;e<t.length;e++)t[e].pathUpdated()},e.prototype.getBoundingRect=function(){return this._updatePathDirty.call(this),i.ZP.prototype.getBoundingRect.call(this)},e}(i.ZP);e.Z=o},783821:function(t,e,n){n.d(e,{ik:function(){return c},tj:function(){return l}});var r=n(904311),i=n(674194),o=n(339296),a=n(807028),s=n(360577),u="__zr_style_"+Math.round(10*Math.random()),l={shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,shadowColor:"#000",opacity:1,blend:"source-over"},c={style:{shadowBlur:!0,shadowOffsetX:!0,shadowOffsetY:!0,shadowColor:!0,opacity:!0}};l[u]=!0;var h=["z","z2","invisible"],f=["invisible"],p=function(t){var e;function n(e){return t.call(this,e)||this}return(0,r.ZT)(n,t),n.prototype._init=function(e){for(var n=(0,a.XP)(e),r=0;r<n.length;r++){var i=n[r];"style"===i?this.useStyle(e[i]):t.prototype.attrKV.call(this,i,e[i])}!this.style&&this.useStyle({})},n.prototype.beforeBrush=function(){},n.prototype.afterBrush=function(){},n.prototype.innerBeforeBrush=function(){},n.prototype.innerAfterBrush=function(){},n.prototype.shouldBePainted=function(t,e,n,r){var i=this.transform;if(this.ignore||this.invisible||0===this.style.opacity||this.culling&&function(t,e,n){return d.copy(t.getBoundingRect()),t.transform&&d.applyTransform(t.transform),g.width=e,g.height=n,!d.intersect(g)}(this,t,e)||i&&!i[0]&&!i[3])return!1;if(n&&this.__clipPaths){for(var o=0;o<this.__clipPaths.length;++o)if(this.__clipPaths[o].isZeroArea())return!1}if(r&&this.parent){for(var a=this.parent;a;){if(a.ignore)return!1;a=a.parent}}return!0},n.prototype.contain=function(t,e){return this.rectContain(t,e)},n.prototype.traverse=function(t,e){t.call(e,this)},n.prototype.rectContain=function(t,e){var n=this.transformCoordToLocal(t,e);return this.getBoundingRect().contain(n[0],n[1])},n.prototype.getPaintRect=function(){var t=this._paintRect;if(!this._paintRect||this.__dirty){var e=this.transform,n=this.getBoundingRect(),r=this.style,i=r.shadowBlur||0,a=r.shadowOffsetX||0,s=r.shadowOffsetY||0;t=this._paintRect||(this._paintRect=new o.Z(0,0,0,0)),e?o.Z.applyTransform(t,n,e):t.copy(n),(i||a||s)&&(t.width+=2*i+Math.abs(a),t.height+=2*i+Math.abs(s),t.x=Math.min(t.x,t.x+a-i),t.y=Math.min(t.y,t.y+s-i));var u=this.dirtyRectTolerance;!t.isZero()&&(t.x=Math.floor(t.x-u),t.y=Math.floor(t.y-u),t.width=Math.ceil(t.width+1+2*u),t.height=Math.ceil(t.height+1+2*u))}return t},n.prototype.setPrevPaintRect=function(t){t?(this._prevPaintRect=this._prevPaintRect||new o.Z(0,0,0,0),this._prevPaintRect.copy(t)):this._prevPaintRect=null},n.prototype.getPrevPaintRect=function(){return this._prevPaintRect},n.prototype.animateStyle=function(t){return this.animate("style",t)},n.prototype.updateDuringAnimation=function(t){"style"===t?this.dirtyStyle():this.markRedraw()},n.prototype.attrKV=function(e,n){"style"!==e?t.prototype.attrKV.call(this,e,n):this.style?this.setStyle(n):this.useStyle(n)},n.prototype.setStyle=function(t,e){return"string"==typeof t?this.style[t]=e:(0,a.l7)(this.style,t),this.dirtyStyle(),this},n.prototype.dirtyStyle=function(t){!t&&this.markRedraw(),this.__dirty|=s.SE,this._rect&&(this._rect=null)},n.prototype.dirty=function(){this.dirtyStyle()},n.prototype.styleChanged=function(){return!!(this.__dirty&s.SE)},n.prototype.styleUpdated=function(){this.__dirty&=~s.SE},n.prototype.createStyle=function(t){return(0,a.nW)(l,t)},n.prototype.useStyle=function(t){!t[u]&&(t=this.createStyle(t)),this.__inHover?this.__hoverStyle=t:this.style=t,this.dirtyStyle()},n.prototype.isStyleObject=function(t){return t[u]},n.prototype._innerSaveToNormal=function(e){t.prototype._innerSaveToNormal.call(this,e);var n=this._normalState;e.style&&!n.style&&(n.style=this._mergeStyle(this.createStyle(),this.style)),this._savePrimaryToNormal(e,n,h)},n.prototype._applyStateObj=function(e,n,r,i,o,s){t.prototype._applyStateObj.call(this,e,n,r,i,o,s);var u,l=!(n&&i);if(n&&n.style?o?i?u=n.style:(u=this._mergeStyle(this.createStyle(),r.style),this._mergeStyle(u,n.style)):(u=this._mergeStyle(this.createStyle(),i?this.style:r.style),this._mergeStyle(u,n.style)):l&&(u=r.style),u){if(o){var c=this.style;if(this.style=this.createStyle(l?{}:c),l){for(var p=(0,a.XP)(c),d=0;d<p.length;d++){var g=p[d];g in u&&(u[g]=u[g],this.style[g]=c[g])}}for(var v=(0,a.XP)(u),d=0;d<v.length;d++){var g=v[d];this.style[g]=this.style[g]}this._transitionState(e,{style:u},s,this.getAnimationStyleProps())}else this.useStyle(u)}for(var y=this.__inHover?f:h,d=0;d<y.length;d++){var g=y[d];n&&null!=n[g]?this[g]=n[g]:l&&null!=r[g]&&(this[g]=r[g])}},n.prototype._mergeStates=function(e){for(var n,r=t.prototype._mergeStates.call(this,e),i=0;i<e.length;i++){var o=e[i];o.style&&(n=n||{},this._mergeStyle(n,o.style))}return n&&(r.style=n),r},n.prototype._mergeStyle=function(t,e){return(0,a.l7)(t,e),t},n.prototype.getAnimationStyleProps=function(){return c},n.initDefaultProps=void((e=n.prototype).type="displayable",e.invisible=!1,e.z=0,e.z2=0,e.zlevel=0,e.culling=!1,e.cursor="pointer",e.rectHover=!1,e.incremental=!1,e._rect=null,e.dirtyRectTolerance=0,e.__dirty=s.YV|s.SE),n}(i.Z),d=new o.Z(0,0,0,0),g=new o.Z(0,0,0,0);e.ZP=p},788869:function(t,e){var n=function(){function t(t){this.colorStops=t||[]}return t.prototype.addColorStop=function(t,e){this.colorStops.push({offset:t,color:e})},t}();e.Z=n},707498:function(t,e,n){var r=n(904311),i=n(807028),o=n(674194),a=n(339296),s=function(t){function e(e){var n=t.call(this)||this;return n.isGroup=!0,n._children=[],n.attr(e),n}return(0,r.ZT)(e,t),e.prototype.childrenRef=function(){return this._children},e.prototype.children=function(){return this._children.slice()},e.prototype.childAt=function(t){return this._children[t]},e.prototype.childOfName=function(t){for(var e=this._children,n=0;n<e.length;n++)if(e[n].name===t)return e[n]},e.prototype.childCount=function(){return this._children.length},e.prototype.add=function(t){return t&&t!==this&&t.parent!==this&&(this._children.push(t),this._doAdd(t)),this},e.prototype.addBefore=function(t,e){if(t&&t!==this&&t.parent!==this&&e&&e.parent===this){var n=this._children,r=n.indexOf(e);r>=0&&(n.splice(r,0,t),this._doAdd(t))}return this},e.prototype.replace=function(t,e){var n=i.cq(this._children,t);return n>=0&&this.replaceAt(e,n),this},e.prototype.replaceAt=function(t,e){var n=this._children,r=n[e];if(t&&t!==this&&t.parent!==this&&t!==r){n[e]=t,r.parent=null;var i=this.__zr;i&&r.removeSelfFromZr(i),this._doAdd(t)}return this},e.prototype._doAdd=function(t){t.parent&&t.parent.remove(t),t.parent=this;var e=this.__zr;e&&e!==t.__zr&&t.addSelfToZr(e),e&&e.refresh()},e.prototype.remove=function(t){var e=this.__zr,n=this._children,r=i.cq(n,t);return r<0?this:(n.splice(r,1),t.parent=null,e&&t.removeSelfFromZr(e),e&&e.refresh(),this)},e.prototype.removeAll=function(){for(var t=this._children,e=this.__zr,n=0;n<t.length;n++){var r=t[n];e&&r.removeSelfFromZr(e),r.parent=null}return t.length=0,this},e.prototype.eachChild=function(t,e){for(var n=this._children,r=0;r<n.length;r++){var i=n[r];t.call(e,i,r)}return this},e.prototype.traverse=function(t,e){for(var n=0;n<this._children.length;n++){var r=this._children[n],i=t.call(e,r);r.isGroup&&!i&&r.traverse(t,e)}return this},e.prototype.addSelfToZr=function(e){t.prototype.addSelfToZr.call(this,e);for(var n=0;n<this._children.length;n++)this._children[n].addSelfToZr(e)},e.prototype.removeSelfFromZr=function(e){t.prototype.removeSelfFromZr.call(this,e);for(var n=0;n<this._children.length;n++)this._children[n].removeSelfFromZr(e)},e.prototype.getBoundingRect=function(t){for(var e=new a.Z(0,0,0,0),n=t||this._children,r=[],i=null,o=0;o<n.length;o++){var s=n[o];if(!s.ignore&&!s.invisible){var u=s.getBoundingRect(),l=s.getLocalTransform(r);l?(a.Z.applyTransform(e,u,l),(i=i||e.clone()).union(e)):(i=i||u.clone()).union(u)}}return i||e},e}(o.Z);s.prototype.type="group",e.Z=s},694923:function(t,e,n){var r=n(904311),i=n(783821),o=n(339296),a=n(807028),s=(0,a.ce)({x:0,y:0},i.tj),u={style:(0,a.ce)({x:!0,y:!0,width:!0,height:!0,sx:!0,sy:!0,sWidth:!0,sHeight:!0},i.ik.style)},l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return(0,r.ZT)(e,t),e.prototype.createStyle=function(t){return(0,a.nW)(s,t)},e.prototype._getSize=function(t){var e,n=this.style,r=n[t];if(null!=r)return r;var i=(e=n.image)&&"string"!=typeof e&&e.width&&e.height?n.image:this.__image;if(!i)return 0;var o="width"===t?"height":"width",a=n[o];return null==a?i[t]:i[t]/i[o]*a},e.prototype.getWidth=function(){return this._getSize("width")},e.prototype.getHeight=function(){return this._getSize("height")},e.prototype.getAnimationStyleProps=function(){return u},e.prototype.getBoundingRect=function(){var t=this.style;return!this._rect&&(this._rect=new o.Z(t.x||0,t.y||0,this.getWidth(),this.getHeight())),this._rect},e}(i.ZP);l.prototype.type="image",e.ZP=l},874543:function(t,e,n){var r=n(904311),i=n(783821),o=n(339296),a=[],s=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.notClear=!0,e.incremental=!0,e._displayables=[],e._temporaryDisplayables=[],e._cursor=0,e}return(0,r.ZT)(e,t),e.prototype.traverse=function(t,e){t.call(e,this)},e.prototype.useStyle=function(){this.style={}},e.prototype.getCursor=function(){return this._cursor},e.prototype.innerAfterBrush=function(){this._cursor=this._displayables.length},e.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.markRedraw(),this.notClear=!1},e.prototype.clearTemporalDisplayables=function(){this._temporaryDisplayables=[]},e.prototype.addDisplayable=function(t,e){e?this._temporaryDisplayables.push(t):this._displayables.push(t),this.markRedraw()},e.prototype.addDisplayables=function(t,e){e=e||!1;for(var n=0;n<t.length;n++)this.addDisplayable(t[n],e)},e.prototype.getDisplayables=function(){return this._displayables},e.prototype.getTemporalDisplayables=function(){return this._temporaryDisplayables},e.prototype.eachPendingDisplayable=function(t){for(var e=this._cursor;e<this._displayables.length;e++)t&&t(this._displayables[e]);for(var e=0;e<this._temporaryDisplayables.length;e++)t&&t(this._temporaryDisplayables[e])},e.prototype.update=function(){this.updateTransform();for(var t=this._cursor;t<this._displayables.length;t++){var e=this._displayables[t];e.parent=this,e.update(),e.parent=null}for(var t=0;t<this._temporaryDisplayables.length;t++){var e=this._temporaryDisplayables[t];e.parent=this,e.update(),e.parent=null}},e.prototype.getBoundingRect=function(){if(!this._rect){for(var t=new o.Z(1/0,1/0,-1/0,-1/0),e=0;e<this._displayables.length;e++){var n=this._displayables[e],r=n.getBoundingRect().clone();n.needLocalTransform()&&r.applyTransform(n.getLocalTransform(a)),t.union(r)}this._rect=t}return this._rect},e.prototype.contain=function(t,e){var n=this.transformCoordToLocal(t,e);if(this.getBoundingRect().contain(n[0],n[1])){for(var r=0;r<this._displayables.length;r++)if(this._displayables[r].contain(t,e))return!0}return!1},e}(i.ZP);e.Z=s},550025:function(t,e,n){var r=n(904311),i=function(t){function e(e,n,r,i,o,a){var s=t.call(this,o)||this;return s.x=null==e?0:e,s.y=null==n?0:n,s.x2=null==r?1:r,s.y2=null==i?0:i,s.type="linear",s.global=a||!1,s}return(0,r.ZT)(e,t),e}(n(788869).Z);e.Z=i},894641:function(t,e,n){n.d(e,{$t:function(){return f}});var r=n(904311),i=n(783821),o=n(351415),a=n(185341),s=n(807028),u=n(529134),l=n(113525),c=n(360577),h=n(762680),f=(0,s.ce)({fill:"#000",stroke:null,strokePercent:1,fillOpacity:1,strokeOpacity:1,lineDashOffset:0,lineWidth:1,lineCap:"butt",miterLimit:10,strokeNoScale:!1,strokeFirst:!1},i.tj),p={style:(0,s.ce)({fill:!0,stroke:!0,strokePercent:!0,fillOpacity:!0,strokeOpacity:!0,lineDashOffset:!0,lineWidth:!0,miterLimit:!0},i.ik.style)},d=h.dN.concat(["invisible","culling","z","z2","zlevel","parent"]),g=function(t){var e;function n(e){return t.call(this,e)||this}return(0,r.ZT)(n,t),n.prototype.update=function(){var e=this;t.prototype.update.call(this);var r=this.style;if(r.decal){var i=this._decalEl=this._decalEl||new n;i.buildPath===n.prototype.buildPath&&(i.buildPath=function(t){e.buildPath(t,e.shape)}),i.silent=!0;var o=i.style;for(var a in r)o[a]!==r[a]&&(o[a]=r[a]);o.fill=r.fill?r.decal:null,o.decal=null,o.shadowColor=null,r.strokeFirst&&(o.stroke=null);for(var s=0;s<d.length;++s)i[d[s]]=this[d[s]];i.__dirty|=c.YV}else this._decalEl&&(this._decalEl=null)},n.prototype.getDecalElement=function(){return this._decalEl},n.prototype._init=function(e){var n=(0,s.XP)(e);this.shape=this.getDefaultShape();var r=this.getDefaultStyle();r&&this.useStyle(r);for(var i=0;i<n.length;i++){var o=n[i],a=e[o];"style"===o?this.style?(0,s.l7)(this.style,a):this.useStyle(a):"shape"===o?(0,s.l7)(this.shape,a):t.prototype.attrKV.call(this,o,a)}!this.style&&this.useStyle({})},n.prototype.getDefaultStyle=function(){return null},n.prototype.getDefaultShape=function(){return{}},n.prototype.canBeInsideText=function(){return this.hasFill()},n.prototype.getInsideTextFill=function(){var t=this.style.fill;if("none"!==t){if((0,s.HD)(t)){var e=(0,u.L0)(t,0);return e>.5?l.vU:e>.2?l.iv:l.GD}if(t)return l.GD}return l.vU},n.prototype.getInsideTextStroke=function(t){var e=this.style.fill;if((0,s.HD)(e)){var n=this.__zr;if(!!(n&&n.isDarkMode())==(0,u.L0)(t,0)<l.Ak)return e}},n.prototype.buildPath=function(t,e,n){},n.prototype.pathUpdated=function(){this.__dirty&=~c.RH},n.prototype.getUpdatedPathProxy=function(t){return this.path||this.createPathProxy(),this.path.beginPath(),this.buildPath(this.path,this.shape,t),this.path},n.prototype.createPathProxy=function(){this.path=new o.Z(!1)},n.prototype.hasStroke=function(){var t=this.style,e=t.stroke;return!(null==e||"none"===e||!(t.lineWidth>0))},n.prototype.hasFill=function(){var t=this.style.fill;return null!=t&&"none"!==t},n.prototype.getBoundingRect=function(){var t=this._rect,e=this.style,n=!t;if(n){var r=!1;!this.path&&(r=!0,this.createPathProxy());var i=this.path;(r||this.__dirty&c.RH)&&(i.beginPath(),this.buildPath(i,this.shape,!1),this.pathUpdated()),t=i.getBoundingRect()}if(this._rect=t,this.hasStroke()&&this.path&&this.path.len()>0){var o=this._rectStroke||(this._rectStroke=t.clone());if(this.__dirty||n){o.copy(t);var a=e.strokeNoScale?this.getLineScale():1,s=e.lineWidth;if(!this.hasFill()){var u=this.strokeContainThreshold;s=Math.max(s,null==u?4:u)}a>1e-10&&(o.width+=s/a,o.height+=s/a,o.x-=s/a/2,o.y-=s/a/2)}return o}return t},n.prototype.contain=function(t,e){var n=this.transformCoordToLocal(t,e),r=this.getBoundingRect(),i=this.style;if(t=n[0],e=n[1],r.contain(t,e)){var o=this.path;if(this.hasStroke()){var s=i.lineWidth,u=i.strokeNoScale?this.getLineScale():1;if(u>1e-10&&(!this.hasFill()&&(s=Math.max(s,this.strokeContainThreshold)),a.m(o,s/u,t,e)))return!0}if(this.hasFill())return a.X(o,t,e)}return!1},n.prototype.dirtyShape=function(){this.__dirty|=c.RH,this._rect&&(this._rect=null),this._decalEl&&this._decalEl.dirtyShape(),this.markRedraw()},n.prototype.dirty=function(){this.dirtyStyle(),this.dirtyShape()},n.prototype.animateShape=function(t){return this.animate("shape",t)},n.prototype.updateDuringAnimation=function(t){"style"===t?this.dirtyStyle():"shape"===t?this.dirtyShape():this.markRedraw()},n.prototype.attrKV=function(e,n){"shape"===e?this.setShape(n):t.prototype.attrKV.call(this,e,n)},n.prototype.setShape=function(t,e){var n=this.shape;return!n&&(n=this.shape={}),"string"==typeof t?n[t]=e:(0,s.l7)(n,t),this.dirtyShape(),this},n.prototype.shapeChanged=function(){return!!(this.__dirty&c.RH)},n.prototype.createStyle=function(t){return(0,s.nW)(f,t)},n.prototype._innerSaveToNormal=function(e){t.prototype._innerSaveToNormal.call(this,e);var n=this._normalState;e.shape&&!n.shape&&(n.shape=(0,s.l7)({},this.shape))},n.prototype._applyStateObj=function(e,n,r,i,o,a){t.prototype._applyStateObj.call(this,e,n,r,i,o,a);var u,l=!(n&&i);if(n&&n.shape?o?i?u=n.shape:(u=(0,s.l7)({},r.shape),(0,s.l7)(u,n.shape)):(u=(0,s.l7)({},i?this.shape:r.shape),(0,s.l7)(u,n.shape)):l&&(u=r.shape),u){if(o){this.shape=(0,s.l7)({},this.shape);for(var c={},h=(0,s.XP)(u),f=0;f<h.length;f++){var p=h[f];"object"==typeof u[p]?this.shape[p]=u[p]:c[p]=u[p]}this._transitionState(e,{shape:c},a)}else this.shape=u,this.dirtyShape()}},n.prototype._mergeStates=function(e){for(var n,r=t.prototype._mergeStates.call(this,e),i=0;i<e.length;i++){var o=e[i];o.shape&&(n=n||{},this._mergeStyle(n,o.shape))}return n&&(r.shape=n),r},n.prototype.getAnimationStyleProps=function(){return p},n.prototype.isZeroArea=function(){return!1},n.extend=function(t){var e=function(e){function n(n){var r=e.call(this,n)||this;return t.init&&t.init.call(r,n),r}return(0,r.ZT)(n,e),n.prototype.getDefaultStyle=function(){return(0,s.d9)(t.style)},n.prototype.getDefaultShape=function(){return(0,s.d9)(t.shape)},n}(n);for(var i in t)"function"==typeof t[i]&&(e.prototype[i]=t[i]);return e},n.initDefaultProps=void((e=n.prototype).type="path",e.strokeContainThreshold=5,e.segmentIgnoreThreshold=0,e.subPixelOptimize=!1,e.autoBatch=!1,e.__dirty=c.YV|c.SE|c.RH),n}(i.ZP);e.ZP=g},938083:function(t,e,n){var r=n(904311),i=function(t){function e(e,n,r,i,o){var a=t.call(this,i)||this;return a.x=null==e?.5:e,a.y=null==n?.5:n,a.r=null==r?.5:r,a.type="radial",a.global=o||!1,a}return(0,r.ZT)(e,t),e}(n(788869).Z);e.Z=i},613410:function(t,e,n){var r=n(904311),i=n(783821),o=n(310123),a=n(894641),s=n(807028),u=n(252919),l=(0,s.ce)({strokeFirst:!0,font:u.Uo,x:0,y:0,textAlign:"left",textBaseline:"top",miterLimit:2},a.$t),c=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return(0,r.ZT)(e,t),e.prototype.hasStroke=function(){var t=this.style,e=t.stroke;return null!=e&&"none"!==e&&t.lineWidth>0},e.prototype.hasFill=function(){var t=this.style.fill;return null!=t&&"none"!==t},e.prototype.createStyle=function(t){return(0,s.nW)(l,t)},e.prototype.setBoundingRect=function(t){this._rect=t},e.prototype.getBoundingRect=function(){var t=this.style;if(!this._rect){var e=t.text;null!=e?e+="":e="";var n=(0,o.lP)(e,t.font,t.textAlign,t.textBaseline);if(n.x+=t.x||0,n.y+=t.y||0,this.hasStroke()){var r=t.lineWidth;n.x-=r/2,n.y-=r/2,n.width+=r,n.height+=r}this._rect=n}return this._rect},e.initDefaultProps=void(e.prototype.dirtyRectTolerance=10),e}(i.ZP);c.prototype.type="tspan",e.Z=c},345262:function(t,e,n){n.d(e,{VG:function(){return _},Y1:function(){return w}});var r=n(904311),i=n(597250),o=n(613410),a=n(807028),s=n(310123),u=n(694923),l=n(406822),c=n(339296),h=n(783821),f=n(252919),p={fill:"#000"},d={style:(0,a.ce)({fill:!0,stroke:!0,fillOpacity:!0,strokeOpacity:!0,lineWidth:!0,fontSize:!0,lineHeight:!0,width:!0,height:!0,textShadowColor:!0,textShadowBlur:!0,textShadowOffsetX:!0,textShadowOffsetY:!0,backgroundColor:!0,padding:!0,borderColor:!0,borderWidth:!0,borderRadius:!0},h.ik.style)},g=function(t){function e(e){var n=t.call(this)||this;return n.type="text",n._children=[],n._defaultStyle=p,n.attr(e),n}return(0,r.ZT)(e,t),e.prototype.childrenRef=function(){return this._children},e.prototype.update=function(){t.prototype.update.call(this),this.styleChanged()&&this._updateSubTexts();for(var e=0;e<this._children.length;e++){var n=this._children[e];n.zlevel=this.zlevel,n.z=this.z,n.z2=this.z2,n.culling=this.culling,n.cursor=this.cursor,n.invisible=this.invisible}},e.prototype.updateTransform=function(){var e=this.innerTransformable;e?(e.updateTransform(),e.transform&&(this.transform=e.transform)):t.prototype.updateTransform.call(this)},e.prototype.getLocalTransform=function(e){var n=this.innerTransformable;return n?n.getLocalTransform(e):t.prototype.getLocalTransform.call(this,e)},e.prototype.getComputedTransform=function(){return this.__hostTarget&&(this.__hostTarget.getComputedTransform(),this.__hostTarget.updateInnerText(!0)),t.prototype.getComputedTransform.call(this)},e.prototype._updateSubTexts=function(){this._childCursor=0,function(t){S(t),(0,a.S6)(t.rich,S)}(this.style),this.style.rich?this._updateRichTexts():this._updatePlainTexts(),this._children.length=this._childCursor,this.styleUpdated()},e.prototype.addSelfToZr=function(e){t.prototype.addSelfToZr.call(this,e);for(var n=0;n<this._children.length;n++)this._children[n].__zr=e},e.prototype.removeSelfFromZr=function(e){t.prototype.removeSelfFromZr.call(this,e);for(var n=0;n<this._children.length;n++)this._children[n].__zr=null},e.prototype.getBoundingRect=function(){if(this.styleChanged()&&this._updateSubTexts(),!this._rect){for(var t=new c.Z(0,0,0,0),e=this._children,n=[],r=null,i=0;i<e.length;i++){var o=e[i],a=o.getBoundingRect(),s=o.getLocalTransform(n);s?(t.copy(a),t.applyTransform(s),(r=r||t.clone()).union(t)):(r=r||a.clone()).union(a)}this._rect=r||t}return this._rect},e.prototype.setDefaultTextStyle=function(t){this._defaultStyle=t||p},e.prototype.setTextContent=function(t){},e.prototype._mergeStyle=function(t,e){if(!e)return t;var n=e.rich,r=t.rich||n&&{};return(0,a.l7)(t,e),n&&r?(this._mergeRich(r,n),t.rich=r):r&&(t.rich=r),t},e.prototype._mergeRich=function(t,e){for(var n=(0,a.XP)(e),r=0;r<n.length;r++){var i=n[r];t[i]=t[i]||{},(0,a.l7)(t[i],e[i])}},e.prototype.getAnimationStyleProps=function(){return d},e.prototype._getOrCreateChild=function(t){var e=this._children[this._childCursor];return(!e||!(e instanceof t))&&(e=new t),this._children[this._childCursor++]=e,e.__zr=this.__zr,e.parent=this,e},e.prototype._updatePlainTexts=function(){var t=this.style,e=t.font||f.Uo,n=t.padding,r=k(t),a=(0,i.NY)(r,t),u=D(t),l=!!t.backgroundColor,h=a.outerHeight,p=a.outerWidth,d=a.contentWidth,g=a.lines,v=a.lineHeight,y=this._defaultStyle,m=t.x||0,_=t.y||0,w=t.align||y.align||"left",S=t.verticalAlign||y.verticalAlign||"top",C=m,I=(0,s.mU)(_,a.contentHeight,S);if(u||n){var A=(0,s.M3)(m,p,w),P=(0,s.mU)(_,h,S);u&&this._renderBackground(t,t,A,P,p,h)}I+=v/2,n&&(C=T(m,w,n),"top"===S?I+=n[0]:"bottom"===S&&(I-=n[2]));for(var Z=0,L=!1,O=M(("fill"in t)?t.fill:(L=!0,y.fill)),R=b(("stroke"in t)?t.stroke:l||y.autoStroke&&!L?null:(Z=2,y.stroke)),N=t.textShadowBlur>0,E=null!=t.width&&("truncate"===t.overflow||"break"===t.overflow||"breakAll"===t.overflow),B=a.calculatedLineHeight,z=0;z<g.length;z++){var F=this._getOrCreateChild(o.Z),W=F.createStyle();F.useStyle(W),W.text=g[z],W.x=C,W.y=I,w&&(W.textAlign=w),W.textBaseline="middle",W.opacity=t.opacity,W.strokeFirst=!0,N&&(W.shadowBlur=t.textShadowBlur||0,W.shadowColor=t.textShadowColor||"transparent",W.shadowOffsetX=t.textShadowOffsetX||0,W.shadowOffsetY=t.textShadowOffsetY||0),W.stroke=R,W.fill=O,R&&(W.lineWidth=t.lineWidth||Z,W.lineDash=t.lineDash,W.lineDashOffset=t.lineDashOffset||0),W.font=e,x(W,t),I+=v,E&&F.setBoundingRect(new c.Z((0,s.M3)(W.x,t.width,W.textAlign),(0,s.mU)(W.y,B,W.textBaseline),d,B))}},e.prototype._updateRichTexts=function(){var t=this.style,e=k(t),n=(0,i.$F)(e,t),r=n.width,o=n.outerWidth,a=n.outerHeight,u=t.padding,l=t.x||0,c=t.y||0,h=this._defaultStyle,f=t.align||h.align,p=t.verticalAlign||h.verticalAlign,d=(0,s.M3)(l,o,f),g=(0,s.mU)(c,a,p),v=d,y=g;u&&(v+=u[3],y+=u[0]);var m=v+r;D(t)&&this._renderBackground(t,t,d,g,o,a);for(var _=!!t.backgroundColor,x=0;x<n.lines.length;x++){for(var w=n.lines[x],S=w.tokens,b=S.length,M=w.lineHeight,T=w.width,C=0,I=v,A=m,P=b-1,Z=void 0;C<b&&(!(Z=S[C]).align||"left"===Z.align);)this._placeToken(Z,t,M,y,I,"left",_),T-=Z.width,I+=Z.width,C++;for(;P>=0&&"right"===(Z=S[P]).align;)this._placeToken(Z,t,M,y,A,"right",_),T-=Z.width,A-=Z.width,P--;for(I+=(r-(I-v)-(m-A)-T)/2;C<=P;)Z=S[C],this._placeToken(Z,t,M,y,I+Z.width/2,"center",_),I+=Z.width,C++;y+=M}},e.prototype._placeToken=function(t,e,n,r,i,u,l){var h=e.rich[t.styleName]||{};h.text=t.text;var p=t.verticalAlign,d=r+n/2;"top"===p?d=r+t.height/2:"bottom"===p&&(d=r+n-t.height/2),!t.isLineHolder&&D(h)&&this._renderBackground(h,e,"right"===u?i-t.width:"center"===u?i-t.width/2:i,d-t.height/2,t.width,t.height);var g=!!h.backgroundColor,v=t.textPadding;v&&(i=T(i,u,v),d-=t.height/2-v[0]-t.innerHeight/2);var y=this._getOrCreateChild(o.Z),m=y.createStyle();y.useStyle(m);var _=this._defaultStyle,w=!1,S=0,k=M("fill"in h?h.fill:"fill"in e?e.fill:(w=!0,_.fill)),C=b("stroke"in h?h.stroke:"stroke"in e?e.stroke:g||l||_.autoStroke&&!w?null:(S=2,_.stroke)),I=h.textShadowBlur>0||e.textShadowBlur>0;m.text=t.text,m.x=i,m.y=d,I&&(m.shadowBlur=h.textShadowBlur||e.textShadowBlur||0,m.shadowColor=h.textShadowColor||e.textShadowColor||"transparent",m.shadowOffsetX=h.textShadowOffsetX||e.textShadowOffsetX||0,m.shadowOffsetY=h.textShadowOffsetY||e.textShadowOffsetY||0),m.textAlign=u,m.textBaseline="middle",m.font=t.font||f.Uo,m.opacity=(0,a.R1)(h.opacity,e.opacity,1),x(m,h),C&&(m.lineWidth=(0,a.R1)(h.lineWidth,e.lineWidth,S),m.lineDash=(0,a.pD)(h.lineDash,e.lineDash),m.lineDashOffset=e.lineDashOffset||0,m.stroke=C),k&&(m.fill=k);var A=t.contentWidth,P=t.contentHeight;y.setBoundingRect(new c.Z((0,s.M3)(m.x,A,m.textAlign),(0,s.mU)(m.y,P,m.textBaseline),A,P))},e.prototype._renderBackground=function(t,e,n,r,i,o){var s,c,h=t.backgroundColor,f=t.borderWidth,p=t.borderColor,d=h&&h.image,g=h&&!d,v=t.borderRadius,y=this;if(g||t.lineHeight||f&&p){(s=this._getOrCreateChild(l.Z)).useStyle(s.createStyle()),s.style.fill=null;var m=s.shape;m.x=n,m.y=r,m.width=i,m.height=o,m.r=v,s.dirtyShape()}if(g){var _=s.style;_.fill=h||null,_.fillOpacity=(0,a.pD)(t.fillOpacity,1)}else if(d){(c=this._getOrCreateChild(u.ZP)).onload=function(){y.dirtyStyle()};var x=c.style;x.image=h.image,x.x=n,x.y=r,x.width=i,x.height=o}if(f&&p){var _=s.style;_.lineWidth=f,_.stroke=p,_.strokeOpacity=(0,a.pD)(t.strokeOpacity,1),_.lineDash=t.borderDash,_.lineDashOffset=t.borderDashOffset||0,s.strokeContainThreshold=0,s.hasFill()&&s.hasStroke()&&(_.strokeFirst=!0,_.lineWidth*=2)}var w=(s||c).style;w.shadowBlur=t.shadowBlur||0,w.shadowColor=t.shadowColor||"transparent",w.shadowOffsetX=t.shadowOffsetX||0,w.shadowOffsetY=t.shadowOffsetY||0,w.opacity=(0,a.R1)(t.opacity,e.opacity,1)},e.makeFont=function(t){var e="";return w(t)&&(e=[t.fontStyle,t.fontWeight,_(t.fontSize),t.fontFamily||"sans-serif"].join(" ")),e&&(0,a.fy)(e)||t.textFont||t.font},e}(h.ZP),v={left:!0,right:1,center:1},y={top:1,bottom:1,middle:1},m=["fontStyle","fontWeight","fontSize","fontFamily"];function _(t){return"string"==typeof t&&(-1!==t.indexOf("px")||-1!==t.indexOf("rem")||-1!==t.indexOf("em"))?t:isNaN(+t)?f.n5+"px":t+"px"}function x(t,e){for(var n=0;n<m.length;n++){var r=m[n],i=e[r];null!=i&&(t[r]=i)}}function w(t){return null!=t.fontSize||t.fontFamily||t.fontWeight}function S(t){if(t){t.font=g.makeFont(t);var e=t.align;"middle"===e&&(e="center"),t.align=null==e||v[e]?e:"left";var n=t.verticalAlign;"center"===n&&(n="middle"),t.verticalAlign=null==n||y[n]?n:"top",t.padding&&(t.padding=(0,a.MY)(t.padding))}}function b(t,e){return null==t||e<=0||"transparent"===t||"none"===t?null:t.image||t.colorStops?"#000":t}function M(t){return null==t||"none"===t?null:t.image||t.colorStops?"#000":t}function T(t,e,n){return"right"===e?t-n[1]:"center"===e?t+n[3]/2-n[1]/2:t+n[3]}function k(t){var e=t.text;return null!=e&&(e+=""),e}function D(t){return!!(t.backgroundColor||t.lineHeight||t.borderWidth&&t.borderColor)}e.ZP=g},360577:function(t,e,n){n.d(e,{RH:function(){return o},SE:function(){return i},YV:function(){return r}});var r=1,i=2,o=4},175873:function(t,e,n){n.d(e,{Gq:function(){return s},ko:function(){return a},v5:function(){return l}});var r=n(445471),i=n(252919),o=new r.ZP(50);function a(t){if("string"!=typeof t)return t;var e=o.get(t);return e&&e.image}function s(t,e,n,r,a){if(!t)return e;if("string"!=typeof t)return t;if(e&&e.__zrImageSrc===t||!n)return e;var s=o.get(t),c={hostEl:n,cb:r,cbPayload:a};return s?l(e=s.image)||s.pending.push(c):((e=i.qW.loadImage(t,u,u)).__zrImageSrc=t,o.put(t,e.__cachedImgObj={image:e,pending:[c]})),e}function u(){var t=this.__cachedImgObj;this.onload=this.onerror=this.__cachedImgObj=null;for(var e=0;e<t.pending.length;e++){var n=t.pending[e],r=n.cb;r&&r(this,n.cbPayload),n.hostEl.dirty()}t.pending.length=0}function l(t){return t&&t.width&&t.height}},597250:function(t,e,n){n.d(e,{$F:function(){return p},NY:function(){return l}});var r=n(175873),i=n(807028),o=n(310123),a=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g;function s(t,e,n,r){r=r||{};var a=(0,i.l7)({},r);a.font=e,n=(0,i.pD)(n,"..."),a.maxIterations=(0,i.pD)(r.maxIterations,2);var s=a.minChar=(0,i.pD)(r.minChar,0);a.cnCharWidth=(0,o.dz)("国",e);var u=a.ascCharWidth=(0,o.dz)("a",e);a.placeholder=(0,i.pD)(r.placeholder,"");for(var l=t=Math.max(0,t-1),c=0;c<s&&l>=u;c++)l-=u;var h=(0,o.dz)(n,e);return h>l&&(n="",h=0),l=t-h,a.ellipsis=n,a.ellipsisWidth=h,a.contentWidth=l,a.containerWidth=t,a}function u(t,e){var n=e.containerWidth,r=e.font,i=e.contentWidth;if(!n)return"";var a=(0,o.dz)(t,r);if(a<=n)return t;for(var s=0;;s++){if(a<=i||s>=e.maxIterations){t+=e.ellipsis;break}var u=0===s?function(t,e,n,r){for(var i=0,o=0,a=t.length;o<a&&i<e;o++){var s=t.charCodeAt(o);i+=0<=s&&s<=127?n:r}return o}(t,i,e.ascCharWidth,e.cnCharWidth):a>0?Math.floor(t.length*i/a):0;t=t.substr(0,u),a=(0,o.dz)(t,r)}return""===t&&(t=e.placeholder),t}function l(t,e){null!=t&&(t+="");var n,r=e.overflow,a=e.padding,l=e.font,c=(0,o.Dp)(l),h=(0,i.pD)(e.lineHeight,c),f=!!e.backgroundColor,p="truncate"===e.lineOverflow,d=e.width,g=(n=null!=d&&("break"===r||"breakAll"===r)?t?v(t,e.font,d,"breakAll"===r,0).lines:[]:t?t.split("\n"):[]).length*h,y=(0,i.pD)(e.height,g);if(g>y&&p){var m=Math.floor(y/h);n=n.slice(0,m)}if(t&&"truncate"===r&&null!=d){for(var _=s(d,l,e.ellipsis,{minChar:e.truncateMinChar,placeholder:e.placeholder}),x=0;x<n.length;x++)n[x]=u(n[x],_)}for(var w=y,S=0,x=0;x<n.length;x++)S=Math.max((0,o.dz)(n[x],l),S);null==d&&(d=S);var b=S;return a&&(w+=a[0]+a[2],b+=a[1]+a[3],d+=a[1]+a[3]),f&&(b=d),{lines:n,height:y,outerWidth:b,outerHeight:w,lineHeight:h,calculatedLineHeight:c,contentWidth:S,contentHeight:g,width:d}}var c=function(){},h=function(t){this.tokens=[],t&&(this.tokens=t)},f=function(){this.width=0,this.height=0,this.contentWidth=0,this.contentHeight=0,this.outerWidth=0,this.outerHeight=0,this.lines=[]};function p(t,e){var n,l=new f;if(null!=t&&(t+=""),!t)return l;for(var c=e.width,h=e.height,p=e.overflow,g=("break"===p||"breakAll"===p)&&null!=c?{width:c,accumWidth:0,breakAll:"breakAll"===p}:null,v=a.lastIndex=0;null!=(n=a.exec(t));){var y=n.index;y>v&&d(l,t.substring(v,y),e,g),d(l,n[2],e,g,n[1]),v=a.lastIndex}v<t.length&&d(l,t.substring(v,t.length),e,g);var m=[],_=0,x=0,w=e.padding,S="truncate"===p,b="truncate"===e.lineOverflow;function M(t,e,n){t.width=e,t.lineHeight=n,_+=n,x=Math.max(x,e)}e:for(var T=0;T<l.lines.length;T++){for(var k=l.lines[T],D=0,C=0,I=0;I<k.tokens.length;I++){var A=k.tokens[I],P=A.styleName&&e.rich[A.styleName]||{},Z=A.textPadding=P.padding,L=Z?Z[1]+Z[3]:0,O=A.font=P.font||e.font;A.contentHeight=(0,o.Dp)(O);var R=(0,i.pD)(P.height,A.contentHeight);if(A.innerHeight=R,Z&&(R+=Z[0]+Z[2]),A.height=R,A.lineHeight=(0,i.R1)(P.lineHeight,e.lineHeight,R),A.align=P&&P.align||e.align,A.verticalAlign=P&&P.verticalAlign||"middle",b&&null!=h&&_+A.lineHeight>h){I>0?(k.tokens=k.tokens.slice(0,I),M(k,C,D),l.lines=l.lines.slice(0,T+1)):l.lines=l.lines.slice(0,T);break e}var N=P.width,E=null==N||"auto"===N;if("string"==typeof N&&"%"===N.charAt(N.length-1))A.percentWidth=N,m.push(A),A.contentWidth=(0,o.dz)(A.text,O);else{if(E){var B=P.backgroundColor,z=B&&B.image;z&&(z=r.ko(z),r.v5(z)&&(A.width=Math.max(A.width,z.width*R/z.height)))}var F=S&&null!=c?c-C:null;null!=F&&F<A.width?!E||F<L?(A.text="",A.width=A.contentWidth=0):(A.text=function(t,e,n,r,i){if(!e)return"";var o=(t+"").split("\n");i=s(e,n,r,i);for(var a=0,l=o.length;a<l;a++)o[a]=u(o[a],i);return o.join("\n")}(A.text,F-L,O,e.ellipsis,{minChar:e.truncateMinChar}),A.width=A.contentWidth=(0,o.dz)(A.text,O)):A.contentWidth=(0,o.dz)(A.text,O)}A.width+=L,C+=A.width,P&&(D=Math.max(D,A.lineHeight))}M(k,C,D)}l.outerWidth=l.width=(0,i.pD)(c,x),l.outerHeight=l.height=(0,i.pD)(h,_),l.contentHeight=_,l.contentWidth=x,w&&(l.outerWidth+=w[1]+w[3],l.outerHeight+=w[0]+w[2]);for(var T=0;T<m.length;T++){var A=m[T],W=A.percentWidth;A.width=parseInt(W,10)/100*l.width}return l}function d(t,e,n,r,i){var a,s,u=""===e,l=i&&n.rich[i]||{},f=t.lines,p=l.font||n.font,d=!1;if(r){var g=l.padding,y=g?g[1]+g[3]:0;if(null!=l.width&&"auto"!==l.width){var m=(0,o.GM)(l.width,r.width)+y;f.length>0&&m+r.accumWidth>r.width&&(a=e.split("\n"),d=!0),r.accumWidth=m}else{var _=v(e,p,r.width,r.breakAll,r.accumWidth);r.accumWidth=_.accumWidth+y,s=_.linesWidths,a=_.lines}}else a=e.split("\n");for(var x=0;x<a.length;x++){var w=a[x],S=new c;if(S.styleName=i,S.text=w,S.isLineHolder=!w&&!u,"number"==typeof l.width?S.width=l.width:S.width=s?s[x]:(0,o.dz)(w,p),x||d)f.push(new h([S]));else{var b=(f[f.length-1]||(f[0]=new h)).tokens,M=b.length;1===M&&b[0].isLineHolder?b[0]=S:(w||!M||u)&&b.push(S)}}}var g=(0,i.u4)(",&?/;] ".split(""),function(t,e){return t[e]=!0,t},{});function v(t,e,n,r,i){for(var a=[],s=[],u="",l="",c=0,h=0,f=0;f<t.length;f++){var p=t.charAt(f);if("\n"===p){l&&(u+=l,h+=c),a.push(u),s.push(h),u="",l="",c=0,h=0;continue}var d=(0,o.dz)(p,e),v=!r&&!function(t){var e;if((e=t.charCodeAt(0))>=32&&e<=591||e>=880&&e<=4351||e>=4608&&e<=5119||e>=7680&&e<=8303)return!!g[t]||!1;return!0}(p);if(a.length?h+d>n:i+h+d>n){h?(u||l)&&(v?(!u&&(u=l,l="",h=c=0),a.push(u),s.push(h-c),l+=p,c+=d,u="",h=c):(l&&(u+=l,l="",c=0),a.push(u),s.push(h),u=p,h=d)):v?(a.push(l),s.push(c),l=p,c=d):(a.push(p),s.push(d));continue}h+=d,v?(l+=p,c+=d):(l&&(u+=l,l="",c=0),u+=p)}return!a.length&&!u&&(u=t,l="",c=0),l&&(u+=l),u&&(a.push(u),s.push(h)),1===a.length&&(h+=i),{accumWidth:h,lines:a,linesWidths:s}}},138092:function(t,e,n){n.d(e,{L:function(){return i}});var r=n(616908);function i(t,e,n){var i=e.smooth,o=e.points;if(o&&o.length>=2){if(i){var a=(0,r.Z)(o,i,n,e.smoothConstraint);t.moveTo(o[0][0],o[0][1]);for(var s=o.length,u=0;u<(n?s:s-1);u++){var l=a[2*u],c=a[2*u+1],h=o[(u+1)%s];t.bezierCurveTo(l[0],l[1],c[0],c[1],h[0],h[1])}}else{t.moveTo(o[0][0],o[0][1]);for(var u=1,f=o.length;u<f;u++)t.lineTo(o[u][0],o[u][1])}n&&t.closePath()}}},504186:function(t,e,n){n.d(e,{L:function(){return r}});function r(t,e){var n,r,i,o,a,s=e.x,u=e.y,l=e.width,c=e.height,h=e.r;l<0&&(s+=l,l=-l),c<0&&(u+=c,c=-c),"number"==typeof h?n=r=i=o=h:h instanceof Array?1===h.length?n=r=i=o=h[0]:2===h.length?(n=i=h[0],r=o=h[1]):3===h.length?(n=h[0],r=o=h[1],i=h[2]):(n=h[0],r=h[1],i=h[2],o=h[3]):n=r=i=o=0,n+r>l&&(a=n+r,n*=l/a,r*=l/a),i+o>l&&(a=i+o,i*=l/a,o*=l/a),r+i>c&&(a=r+i,r*=c/a,i*=c/a),n+o>c&&(a=n+o,n*=c/a,o*=c/a),t.moveTo(s+n,u),t.lineTo(s+l-r,u),0!==r&&t.arc(s+l-r,u+r,r,-Math.PI/2,0),t.lineTo(s+l,u+c-i),0!==i&&t.arc(s+l-i,u+c-i,i,0,Math.PI/2),t.lineTo(s+o,u+c),0!==o&&t.arc(s+o,u+c-o,o,Math.PI/2,Math.PI),t.lineTo(s,u+n),0!==n&&t.arc(s+n,u+n,n,Math.PI,1.5*Math.PI)}},395657:function(t,e,n){n.d(e,{L:function(){return g}});var r=n(807028),i=Math.PI,o=2*i,a=Math.sin,s=Math.cos,u=Math.acos,l=Math.atan2,c=Math.abs,h=Math.sqrt,f=Math.max,p=Math.min;function d(t,e,n,r,i,o,a){var s=t-n,u=e-r,l=(a?o:-o)/h(s*s+u*u),c=l*u,p=-l*s,d=t+c,g=e+p,v=n+c,y=r+p,m=(d+v)/2,_=(g+y)/2,x=v-d,w=y-g,S=x*x+w*w,b=i-o,M=d*y-v*g,T=(w<0?-1:1)*h(f(0,b*b*S-M*M)),k=(M*w-x*T)/S,D=(-M*x-w*T)/S,C=(M*w+x*T)/S,I=(-M*x+w*T)/S,A=k-m,P=D-_,Z=C-m,L=I-_;return A*A+P*P>Z*Z+L*L&&(k=C,D=I),{cx:k,cy:D,x0:-c,y0:-p,x1:k*(i/b-1),y1:D*(i/b-1)}}function g(t,e){var n,g=f(e.r,0),v=f(e.r0||0,0),y=g>0,m=v>0;if(!y&&!m)return;if(!y&&(g=v,v=0),v>g){var _=g;g=v,v=_}var x=e.startAngle,w=e.endAngle;if(!(isNaN(x)||isNaN(w))){var S=e.cx,b=e.cy,M=!!e.clockwise,T=c(w-x),k=T>o&&T%o;if(k>1e-4&&(T=k),g>1e-4){if(T>o-1e-4)t.moveTo(S+g*s(x),b+g*a(x)),t.arc(S,b,g,x,w,!M),v>1e-4&&(t.moveTo(S+v*s(w),b+v*a(w)),t.arc(S,b,v,w,x,M));else{var D=void 0,C=void 0,I=void 0,A=void 0,P=void 0,Z=void 0,L=void 0,O=void 0,R=void 0,N=void 0,E=void 0,B=void 0,z=void 0,F=void 0,W=void 0,H=void 0,V=g*s(x),U=g*a(x),G=v*s(w),X=v*a(w),Y=T>1e-4;if(Y){var q=e.cornerRadius;q&&(D=(n=function(t){var e;if((0,r.kJ)(t)){var n=t.length;if(!n)return t;e=1===n?[t[0],t[0],0,0]:2===n?[t[0],t[0],t[1],t[1]]:3===n?t.concat(t[2]):t}else e=[t,t,t,t];return e}(q))[0],C=n[1],I=n[2],A=n[3]);var K=c(g-v)/2;if(P=p(K,I),Z=p(K,A),L=p(K,D),O=p(K,C),E=R=f(P,Z),B=N=f(L,O),(R>1e-4||N>1e-4)&&(z=g*s(w),F=g*a(w),W=v*s(x),H=v*a(x),T<i)){var j=function(t,e,n,r,i,o,a,s){var u=n-t,l=r-e,c=a-i,h=s-o,f=h*u-c*l;if(!(f*f<1e-4))return f=(c*(e-o)-h*(t-i))/f,[t+f*u,e+f*l]}(V,U,W,H,z,F,G,X);if(j){var J=V-j[0],Q=U-j[1],$=z-j[0],tt=F-j[1],te=1/a(u((J*$+Q*tt)/(h(J*J+Q*Q)*h($*$+tt*tt)))/2),tn=h(j[0]*j[0]+j[1]*j[1]);E=p(R,(g-tn)/(te+1)),B=p(N,(v-tn)/(te-1))}}}if(Y){if(E>1e-4){var tr=p(I,E),ti=p(A,E),to=d(W,H,V,U,g,tr,M),ta=d(z,F,G,X,g,ti,M);t.moveTo(S+to.cx+to.x0,b+to.cy+to.y0),E<R&&tr===ti?t.arc(S+to.cx,b+to.cy,E,l(to.y0,to.x0),l(ta.y0,ta.x0),!M):(tr>0&&t.arc(S+to.cx,b+to.cy,tr,l(to.y0,to.x0),l(to.y1,to.x1),!M),t.arc(S,b,g,l(to.cy+to.y1,to.cx+to.x1),l(ta.cy+ta.y1,ta.cx+ta.x1),!M),ti>0&&t.arc(S+ta.cx,b+ta.cy,ti,l(ta.y1,ta.x1),l(ta.y0,ta.x0),!M))}else t.moveTo(S+V,b+U),t.arc(S,b,g,x,w,!M)}else t.moveTo(S+V,b+U);if(v>1e-4&&Y){if(B>1e-4){var tr=p(D,B),ti=p(C,B),to=d(G,X,z,F,v,-ti,M),ta=d(V,U,W,H,v,-tr,M);t.lineTo(S+to.cx+to.x0,b+to.cy+to.y0),B<N&&tr===ti?t.arc(S+to.cx,b+to.cy,B,l(to.y0,to.x0),l(ta.y0,ta.x0),!M):(ti>0&&t.arc(S+to.cx,b+to.cy,ti,l(to.y0,to.x0),l(to.y1,to.x1),!M),t.arc(S,b,v,l(to.cy+to.y1,to.cx+to.x1),l(ta.cy+ta.y1,ta.cx+ta.x1),M),tr>0&&t.arc(S+ta.cx,b+ta.cy,tr,l(ta.y1,ta.x1),l(ta.y0,ta.x0),!M))}else t.lineTo(S+G,b+X),t.arc(S,b,v,w,x,M)}else t.lineTo(S+G,b+X)}}else t.moveTo(S,b);t.closePath()}}},616908:function(t,e,n){n.d(e,{Z:function(){return i}});var r=n(217961);function i(t,e,n,i){var o,a,s,u,l=[],c=[],h=[],f=[];if(i){s=[1/0,1/0],u=[-1/0,-1/0];for(var p=0,d=t.length;p<d;p++)(0,r.VV)(s,s,t[p]),(0,r.Fp)(u,u,t[p]);(0,r.VV)(s,s,i[0]),(0,r.Fp)(u,u,i[1])}for(var p=0,d=t.length;p<d;p++){var g=t[p];if(n)o=t[p?p-1:d-1],a=t[(p+1)%d];else{if(0===p||p===d-1){l.push((0,r.d9)(t[p]));continue}o=t[p-1],a=t[p+1]}(0,r.lu)(c,a,o),(0,r.bA)(c,c,e);var v=(0,r.TE)(g,o),y=(0,r.TE)(g,a),m=v+y;0!==m&&(v/=m,y/=m),(0,r.bA)(h,c,-v),(0,r.bA)(f,c,y);var _=(0,r.IH)([],g,h),x=(0,r.IH)([],g,f);i&&((0,r.Fp)(_,_,s),(0,r.VV)(_,_,u),(0,r.Fp)(x,x,s),(0,r.VV)(x,x,u)),l.push(_),l.push(x)}return n&&l.push(l.shift()),l}},408432:function(t,e,n){n.d(e,{Pw:function(){return o},_3:function(){return i},vu:function(){return a}});var r=Math.round;function i(t,e,n){if(!!e){var i=e.x1,o=e.x2,s=e.y1,u=e.y2;t.x1=i,t.x2=o,t.y1=s,t.y2=u;var l=n&&n.lineWidth;return l?(r(2*i)===r(2*o)&&(t.x1=t.x2=a(i,l,!0)),r(2*s)===r(2*u)&&(t.y1=t.y2=a(s,l,!0)),t):t}}function o(t,e,n){if(!!e){var r=e.x,i=e.y,o=e.width,s=e.height;t.x=r,t.y=i,t.width=o,t.height=s;var u=n&&n.lineWidth;return u?(t.x=a(r,u,!0),t.y=a(i,u,!0),t.width=Math.max(a(r+o,u,!1)-t.x,0===o?0:1),t.height=Math.max(a(i+s,u,!1)-t.y,0===s?0:1),t):t}}function a(t,e,n){if(!e)return t;var i=r(2*t);return(i+r(e))%2==0?i/2:(i+(n?1:-1))/2}},671466:function(t,e,n){var r=n(904311),i=n(894641),o=function(){this.cx=0,this.cy=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0},a=function(t){function e(e){return t.call(this,e)||this}return(0,r.ZT)(e,t),e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new o},e.prototype.buildPath=function(t,e){var n=e.cx,r=e.cy,i=Math.max(e.r,0),o=e.startAngle,a=e.endAngle,s=e.clockwise,u=Math.cos(o),l=Math.sin(o);t.moveTo(u*i+n,l*i+r),t.arc(n,r,i,o,a,!s)},e}(i.ZP);a.prototype.type="arc",e.Z=a},686475:function(t,e,n){var r=n(904311),i=n(894641),o=n(217961),a=n(343556),s=[],u=function(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.cpx1=0,this.cpy1=0,this.percent=1};function l(t,e,n){var r=t.cpx2,i=t.cpy2;return null!=r||null!=i?[(n?a.X_:a.af)(t.x1,t.cpx1,t.cpx2,t.x2,e),(n?a.X_:a.af)(t.y1,t.cpy1,t.cpy2,t.y2,e)]:[(n?a.AZ:a.Zm)(t.x1,t.cpx1,t.x2,e),(n?a.AZ:a.Zm)(t.y1,t.cpy1,t.y2,e)]}var c=function(t){function e(e){return t.call(this,e)||this}return(0,r.ZT)(e,t),e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new u},e.prototype.buildPath=function(t,e){var n=e.x1,r=e.y1,i=e.x2,o=e.y2,u=e.cpx1,l=e.cpy1,c=e.cpx2,h=e.cpy2,f=e.percent;if(0!==f)t.moveTo(n,r),null==c||null==h?(f<1&&((0,a.Lx)(n,u,i,f,s),u=s[1],i=s[2],(0,a.Lx)(r,l,o,f,s),l=s[1],o=s[2]),t.quadraticCurveTo(u,l,i,o)):(f<1&&((0,a.Vz)(n,u,c,i,f,s),u=s[1],c=s[2],i=s[3],(0,a.Vz)(r,l,h,o,f,s),l=s[1],h=s[2],o=s[3]),t.bezierCurveTo(u,l,c,h,i,o))},e.prototype.pointAt=function(t){return l(this.shape,t,!1)},e.prototype.tangentAt=function(t){var e=l(this.shape,t,!0);return o.Fv(e,e)},e}(i.ZP);c.prototype.type="bezier-curve",e.Z=c},418819:function(t,e,n){var r=n(904311),i=n(894641),o=function(){this.cx=0,this.cy=0,this.r=0},a=function(t){function e(e){return t.call(this,e)||this}return(0,r.ZT)(e,t),e.prototype.getDefaultShape=function(){return new o},e.prototype.buildPath=function(t,e){t.moveTo(e.cx+e.r,e.cy),t.arc(e.cx,e.cy,e.r,0,2*Math.PI)},e}(i.ZP);a.prototype.type="circle",e.Z=a},183859:function(t,e,n){var r=n(904311),i=n(894641),o=function(){this.cx=0,this.cy=0,this.rx=0,this.ry=0},a=function(t){function e(e){return t.call(this,e)||this}return(0,r.ZT)(e,t),e.prototype.getDefaultShape=function(){return new o},e.prototype.buildPath=function(t,e){var n=e.cx,r=e.cy,i=e.rx,o=e.ry,a=.5522848*i,s=.5522848*o;t.moveTo(n-i,r),t.bezierCurveTo(n-i,r-s,n-a,r-o,n,r-o),t.bezierCurveTo(n+a,r-o,n+i,r-s,n+i,r),t.bezierCurveTo(n+i,r+s,n+a,r+o,n,r+o),t.bezierCurveTo(n-a,r+o,n-i,r+s,n-i,r),t.closePath()},e}(i.ZP);a.prototype.type="ellipse",e.Z=a},798383:function(t,e,n){var r=n(904311),i=n(894641),o=n(408432),a={},s=function(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.percent=1},u=function(t){function e(e){return t.call(this,e)||this}return(0,r.ZT)(e,t),e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new s},e.prototype.buildPath=function(t,e){if(this.subPixelOptimize){var n,r,i,s,u=(0,o._3)(a,e,this.style);n=u.x1,r=u.y1,i=u.x2,s=u.y2}else n=e.x1,r=e.y1,i=e.x2,s=e.y2;var l=e.percent;if(0!==l)t.moveTo(n,r),l<1&&(i=n*(1-l)+i*l,s=r*(1-l)+s*l),t.lineTo(i,s)},e.prototype.pointAt=function(t){var e=this.shape;return[e.x1*(1-t)+e.x2*t,e.y1*(1-t)+e.y2*t]},e}(i.ZP);u.prototype.type="line",e.Z=u},638144:function(t,e,n){var r=n(904311),i=n(894641),o=n(138092),a=function(){this.points=null,this.smooth=0,this.smoothConstraint=null},s=function(t){function e(e){return t.call(this,e)||this}return(0,r.ZT)(e,t),e.prototype.getDefaultShape=function(){return new a},e.prototype.buildPath=function(t,e){o.L(t,e,!0)},e}(i.ZP);s.prototype.type="polygon",e.Z=s},714720:function(t,e,n){var r=n(904311),i=n(894641),o=n(138092),a=function(){this.points=null,this.percent=1,this.smooth=0,this.smoothConstraint=null},s=function(t){function e(e){return t.call(this,e)||this}return(0,r.ZT)(e,t),e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new a},e.prototype.buildPath=function(t,e){o.L(t,e,!1)},e}(i.ZP);s.prototype.type="polyline",e.Z=s},406822:function(t,e,n){var r=n(904311),i=n(894641),o=n(504186),a=n(408432),s=function(){this.x=0,this.y=0,this.width=0,this.height=0},u={},l=function(t){function e(e){return t.call(this,e)||this}return(0,r.ZT)(e,t),e.prototype.getDefaultShape=function(){return new s},e.prototype.buildPath=function(t,e){if(this.subPixelOptimize){var n,r,i,s,l=(0,a.Pw)(u,e,this.style);n=l.x,r=l.y,i=l.width,s=l.height,l.r=e.r,e=l}else n=e.x,r=e.y,i=e.width,s=e.height;e.r?o.L(t,e):t.rect(n,r,i,s)},e.prototype.isZeroArea=function(){return!this.shape.width||!this.shape.height},e}(i.ZP);l.prototype.type="rect",e.Z=l},980890:function(t,e,n){var r=n(904311),i=n(894641),o=function(){this.cx=0,this.cy=0,this.r=0,this.r0=0},a=function(t){function e(e){return t.call(this,e)||this}return(0,r.ZT)(e,t),e.prototype.getDefaultShape=function(){return new o},e.prototype.buildPath=function(t,e){var n=e.cx,r=e.cy,i=2*Math.PI;t.moveTo(n+e.r,r),t.arc(n,r,e.r,0,i,!1),t.moveTo(n+e.r0,r),t.arc(n,r,e.r0,0,i,!0)},e}(i.ZP);a.prototype.type="ring",e.Z=a},171093:function(t,e,n){var r=n(904311),i=n(894641),o=n(395657),a=function(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0,this.cornerRadius=0},s=function(t){function e(e){return t.call(this,e)||this}return(0,r.ZT)(e,t),e.prototype.getDefaultShape=function(){return new a},e.prototype.buildPath=function(t,e){o.L(t,e)},e.prototype.isZeroArea=function(){return this.shape.startAngle===this.shape.endAngle||this.shape.r===this.shape.r0},e}(i.ZP);s.prototype.type="sector",e.C=s},684107:function(t,e){var n=function(t,e){this.target=t,this.topTarget=e&&e.topTarget},r=function(){function t(t){this.handler=t,t.on("mousedown",this._dragStart,this),t.on("mousemove",this._drag,this),t.on("mouseup",this._dragEnd,this)}return t.prototype._dragStart=function(t){for(var e=t.target;e&&!e.draggable;)e=e.parent||e.__hostTarget;e&&(this._draggingTarget=e,e.dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.handler.dispatchToElement(new n(e,t),"dragstart",t.event))},t.prototype._drag=function(t){var e=this._draggingTarget;if(e){var r=t.offsetX,i=t.offsetY,o=r-this._x,a=i-this._y;this._x=r,this._y=i,e.drift(o,a,t),this.handler.dispatchToElement(new n(e,t),"drag",t.event);var s=this.handler.findHover(r,i,e).target,u=this._dropTarget;this._dropTarget=s,e!==s&&(u&&s!==u&&this.handler.dispatchToElement(new n(u,t),"dragleave",t.event),s&&s!==u&&this.handler.dispatchToElement(new n(s,t),"dragenter",t.event))}},t.prototype._dragEnd=function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.handler.dispatchToElement(new n(e,t),"dragend",t.event),this._dropTarget&&this.handler.dispatchToElement(new n(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null},t}();e.Z=r},201974:function(t,e,n){n.d(e,{Cv:function(){return v},Gk:function(){return S},H3:function(){return x},I1:function(){return m},Pn:function(){return c},R:function(){return y},gA:function(){return b},gO:function(){return _},i2:function(){return d},jY:function(){return f},m1:function(){return w},mU:function(){return p},n1:function(){return g},oF:function(){return M},qV:function(){return h},ut:function(){return s},zT:function(){return u}});var r=n(807028),i=n(529134),o=n(939828),a=Math.round;function s(t){var e;if(t&&"transparent"!==t){if("string"==typeof t&&t.indexOf("rgba")>-1){var n=(0,i.Qc)(t);n&&(t="rgb("+n[0]+","+n[1]+","+n[2]+")",e=n[3])}}else t="none";return{color:t,opacity:null==e?1:e}}function u(t){return t<1e-4&&t>-.0001}function l(t){return a(1e3*t)/1e3}function c(t){return a(1e4*t)/1e4}function h(t){return"matrix("+l(t[0])+","+l(t[1])+","+l(t[2])+","+l(t[3])+","+c(t[4])+","+c(t[5])+")"}var f={left:"start",right:"end",center:"middle",middle:"middle"};function p(t,e,n){return"top"===n?t+=e/2:"bottom"===n&&(t-=e/2),t}function d(t){return t&&(t.shadowBlur||t.shadowOffsetX||t.shadowOffsetY)}function g(t){var e=t.style,n=t.getGlobalScale();return[e.shadowColor,(e.shadowBlur||0).toFixed(2),(e.shadowOffsetX||0).toFixed(2),(e.shadowOffsetY||0).toFixed(2),n[0],n[1]].join(",")}function v(t){return t&&!!t.image}function y(t){var e;return v(t)||(e=t)&&!!e.svgElement}function m(t){return"linear"===t.type}function _(t){return"radial"===t.type}function x(t){return t&&("linear"===t.type||"radial"===t.type)}function w(t){return"url(#"+t+")"}function S(t){var e=t.getGlobalScale();return Math.max(Math.ceil(Math.log(Math.max(e[0],e[1]))/Math.log(10)),1)}function b(t){var e=t.x||0,n=t.y||0,i=(t.rotation||0)*r.I3,o=(0,r.pD)(t.scaleX,1),s=(0,r.pD)(t.scaleY,1),u=t.skewX||0,l=t.skewY||0,c=[];return(e||n)&&c.push("translate("+e+"px,"+n+"px)"),i&&c.push("rotate("+i+")"),(1!==o||1!==s)&&c.push("scale("+o+","+s+")"),(u||l)&&c.push("skew("+a(u*r.I3)+"deg, "+a(l*r.I3)+"deg)"),c.join(" ")}var M=o.Z.hasGlobalWindow&&(0,r.mf)(window.btoa)?function(t){return window.btoa(unescape(encodeURIComponent(t)))}:"undefined"!=typeof Buffer?function(t){return Buffer.from(t).toString("base64")}:function(t){return null}},529134:function(t,e,n){n.d(e,{L0:function(){return T},Pz:function(){return M},Qc:function(){return y},Uu:function(){return x},fD:function(){return D},m8:function(){return b},ox:function(){return S},t7:function(){return w}});var r=n(445471),i=n(807028),o={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]};function a(t){return(t=Math.round(t))<0?0:t>255?255:t}function s(t){return t<0?0:t>1?1:t}function u(t){return t.length&&"%"===t.charAt(t.length-1)?a(parseFloat(t)/100*255):a(parseInt(t,10))}function l(t){return t.length&&"%"===t.charAt(t.length-1)?s(parseFloat(t)/100):s(parseFloat(t))}function c(t,e,n){return(n<0?n+=1:n>1&&(n-=1),6*n<1)?t+(e-t)*n*6:2*n<1?e:3*n<2?t+(e-t)*(2/3-n)*6:t}function h(t,e,n){return t+(e-t)*n}function f(t,e,n,r,i){return t[0]=e,t[1]=n,t[2]=r,t[3]=i,t}function p(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t}var d=new r.ZP(20),g=null;function v(t,e){g&&p(g,e),g=d.put(t,g||e.slice())}function y(t,e){if(!!t){e=e||[];var n=d.get(t);if(n)return p(e,n);var r=(t+="").replace(/ /g,"").toLowerCase();if(r in o)return p(e,o[r]),v(t,e),e;var i=r.length;if("#"===r.charAt(0)){if(4===i||5===i){var a=parseInt(r.slice(1,4),16);if(!(a>=0&&a<=4095)){f(e,0,0,0,1);return}return f(e,(3840&a)>>4|(3840&a)>>8,240&a|(240&a)>>4,15&a|(15&a)<<4,5===i?parseInt(r.slice(4),16)/15:1),v(t,e),e}if(7===i||9===i){var a=parseInt(r.slice(1,7),16);if(!(a>=0&&a<=16777215)){f(e,0,0,0,1);return}return f(e,(16711680&a)>>16,(65280&a)>>8,255&a,9===i?parseInt(r.slice(7),16)/255:1),v(t,e),e}return}var s=r.indexOf("("),c=r.indexOf(")");if(-1!==s&&c+1===i){var h=r.substr(0,s),g=r.substr(s+1,c-(s+1)).split(","),y=1;switch(h){case"rgba":if(4!==g.length)return 3===g.length?f(e,+g[0],+g[1],+g[2],1):f(e,0,0,0,1);y=l(g.pop());case"rgb":if(g.length>=3)return f(e,u(g[0]),u(g[1]),u(g[2]),3===g.length?y:l(g[3])),v(t,e),e;f(e,0,0,0,1);return;case"hsla":if(4!==g.length){f(e,0,0,0,1);return}return g[3]=l(g[3]),m(g,e),v(t,e),e;case"hsl":if(3!==g.length){f(e,0,0,0,1);return}return m(g,e),v(t,e),e;default:return}}f(e,0,0,0,1)}}function m(t,e){var n=(parseFloat(t[0])%360+360)%360/360,r=l(t[1]),i=l(t[2]),o=i<=.5?i*(r+1):i+r-i*r,s=2*i-o;return f(e=e||[],a(255*c(s,o,n+1/3)),a(255*c(s,o,n)),a(255*c(s,o,n-1/3)),1),4===t.length&&(e[3]=t[3]),e}function _(t,e){var n=y(t);if(n){for(var r=0;r<3;r++)e<0?n[r]=n[r]*(1-e)|0:n[r]=(255-n[r])*e+n[r]|0,n[r]>255?n[r]=255:n[r]<0&&(n[r]=0);return M(n,4===n.length?"rgba":"rgb")}}function x(t,e,n){if(!!(e&&e.length)&&!!(t>=0&&t<=1)){n=n||[];var r,i,o,u,l,c,h,f,p=t*(e.length-1),d=Math.floor(p),g=Math.ceil(p),v=e[d],y=e[g],m=p-d;return n[0]=a((r=v[0],i=y[0],r+(i-r)*m)),n[1]=a((o=v[1],u=y[1],o+(u-o)*m)),n[2]=a((l=v[2],c=y[2],l+(c-l)*m)),n[3]=s((h=v[3],f=y[3],h+(f-h)*m)),n}}function w(t,e,n){if(!!(e&&e.length)&&!!(t>=0&&t<=1)){var r,i,o,u,l,c,h,f,p=t*(e.length-1),d=Math.floor(p),g=Math.ceil(p),v=y(e[d]),m=y(e[g]),_=p-d;var x=M([a((r=v[0],i=m[0],r+(i-r)*_)),a((o=v[1],u=m[1],o+(u-o)*_)),a((l=v[2],c=m[2],l+(c-l)*_)),s((h=v[3],f=m[3],h+(f-h)*_))],"rgba");return n?{color:x,leftIndex:d,rightIndex:g,value:p}:x}}function S(t,e,n,r){var i,o=y(t);if(t){;return o=function(t){if(!!t){var e,n,r=t[0]/255,i=t[1]/255,o=t[2]/255,a=Math.min(r,i,o),s=Math.max(r,i,o),u=s-a,l=(s+a)/2;if(0===u)e=0,n=0;else{n=l<.5?u/(s+a):u/(2-s-a);var c=((s-r)/6+u/2)/u,h=((s-i)/6+u/2)/u,f=((s-o)/6+u/2)/u;r===s?e=f-h:i===s?e=1/3+c-f:o===s&&(e=2/3+h-c),e<0&&(e+=1),e>1&&(e-=1)}var p=[360*e,n,l];return null!=t[3]&&p.push(t[3]),p}}(o),null!=e&&(o[0]=(i=Math.round(i=e))<0?0:i>360?360:i),null!=n&&(o[1]=l(n)),null!=r&&(o[2]=l(r)),M(m(o),"rgba")}}function b(t,e){var n=y(t);if(n&&null!=e)return n[3]=s(e),M(n,"rgba")}function M(t,e){if(!!t&&!!t.length){var n=t[0]+","+t[1]+","+t[2];return("rgba"===e||"hsva"===e||"hsla"===e)&&(n+=","+t[3]),e+"("+n+")"}}function T(t,e){var n=y(t);return n?(.299*n[0]+.587*n[1]+.114*n[2])*n[3]/255+(1-n[3])*e:0}var k=new r.ZP(100);function D(t){if((0,i.HD)(t)){var e=k.get(t);return!e&&(e=_(t,-.1),k.put(t,e)),e}if((0,i.Qq)(t)){var n=(0,i.l7)({},t);return n.colorStops=(0,i.UI)(t.colorStops,function(t){return{offset:t.offset,color:_(t.color,-.1)}}),n}return t}},692606:function(t,e,n){n.d(e,{AA:function(){return b},Pc:function(){return S},iR:function(){return w}});var r=n(904311),i=n(894641),o=n(351415),a=n(232357),s=n(807028),u=Math.sqrt,l=Math.sin,c=Math.cos,h=Math.PI;function f(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])}function p(t,e){return(t[0]*e[0]+t[1]*e[1])/(f(t)*f(e))}function d(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(p(t,e))}function g(t,e,n,r,i,o,a,s,f,g,v){var y=h/180*f,m=c(y)*(t-n)/2+l(y)*(e-r)/2,_=-1*l(y)*(t-n)/2+c(y)*(e-r)/2,x=m*m/(a*a)+_*_/(s*s);x>1&&(a*=u(x),s*=u(x));var w=(i===o?-1:1)*u((a*a*(s*s)-a*a*(_*_)-s*s*(m*m))/(a*a*(_*_)+s*s*(m*m)))||0,S=w*a*_/s,b=-(w*s)*m/a,M=(t+n)/2+c(y)*S-l(y)*b,T=(e+r)/2+l(y)*S+c(y)*b,k=d([1,0],[(m-S)/a,(_-b)/s]),D=[(m-S)/a,(_-b)/s],C=[(-1*m-S)/a,(-1*_-b)/s],I=d(D,C);if(-1>=p(D,C)&&(I=h),p(D,C)>=1&&(I=0),I<0){var A=Math.round(I/h*1e6)/1e6;I=2*h+A%2*h}v.addData(g,M,T,a,s,k,I,y,o)}var v=/([mlvhzcqtsa])([^mlvhzcqtsa]*)/ig,y=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g,m=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return(0,r.ZT)(e,t),e.prototype.applyTransform=function(t){},e}(i.ZP);function _(t){return null!=t.setData}function x(t,e){var n=function(t){var e,n=new o.Z;if(!t)return n;var r=0,i=0,a=0,s=0,u=o.Z.CMD,l=t.match(v);if(!l)return n;for(var c=0;c<l.length;c++){for(var h=l[c],f=h.charAt(0),p=void 0,d=h.match(y)||[],m=d.length,_=0;_<m;_++)d[_]=parseFloat(d[_]);for(var x=0;x<m;){var w=void 0,S=void 0,b=void 0,M=void 0,T=void 0,k=void 0,D=void 0,C=r,I=i,A=void 0,P=void 0;switch(f){case"l":r+=d[x++],i+=d[x++],p=u.L,n.addData(p,r,i);break;case"L":r=d[x++],i=d[x++],p=u.L,n.addData(p,r,i);break;case"m":r+=d[x++],i+=d[x++],p=u.M,n.addData(p,r,i),a=r,s=i,f="l";break;case"M":r=d[x++],i=d[x++],p=u.M,n.addData(p,r,i),a=r,s=i,f="L";break;case"h":r+=d[x++],p=u.L,n.addData(p,r,i);break;case"H":r=d[x++],p=u.L,n.addData(p,r,i);break;case"v":i+=d[x++],p=u.L,n.addData(p,r,i);break;case"V":i=d[x++],p=u.L,n.addData(p,r,i);break;case"C":p=u.C,n.addData(p,d[x++],d[x++],d[x++],d[x++],d[x++],d[x++]),r=d[x-2],i=d[x-1];break;case"c":p=u.C,n.addData(p,d[x++]+r,d[x++]+i,d[x++]+r,d[x++]+i,d[x++]+r,d[x++]+i),r+=d[x-2],i+=d[x-1];break;case"S":w=r,S=i,A=n.len(),P=n.data,e===u.C&&(w+=r-P[A-4],S+=i-P[A-3]),p=u.C,C=d[x++],I=d[x++],r=d[x++],i=d[x++],n.addData(p,w,S,C,I,r,i);break;case"s":w=r,S=i,A=n.len(),P=n.data,e===u.C&&(w+=r-P[A-4],S+=i-P[A-3]),p=u.C,C=r+d[x++],I=i+d[x++],r+=d[x++],i+=d[x++],n.addData(p,w,S,C,I,r,i);break;case"Q":C=d[x++],I=d[x++],r=d[x++],i=d[x++],p=u.Q,n.addData(p,C,I,r,i);break;case"q":C=d[x++]+r,I=d[x++]+i,r+=d[x++],i+=d[x++],p=u.Q,n.addData(p,C,I,r,i);break;case"T":w=r,S=i,A=n.len(),P=n.data,e===u.Q&&(w+=r-P[A-4],S+=i-P[A-3]),r=d[x++],i=d[x++],p=u.Q,n.addData(p,w,S,r,i);break;case"t":w=r,S=i,A=n.len(),P=n.data,e===u.Q&&(w+=r-P[A-4],S+=i-P[A-3]),r+=d[x++],i+=d[x++],p=u.Q,n.addData(p,w,S,r,i);break;case"A":b=d[x++],M=d[x++],T=d[x++],k=d[x++],D=d[x++],C=r,I=i,r=d[x++],i=d[x++],g(C,I,r,i,k,D,b,M,T,p=u.A,n);break;case"a":b=d[x++],M=d[x++],T=d[x++],k=d[x++],D=d[x++],C=r,I=i,r+=d[x++],i+=d[x++],g(C,I,r,i,k,D,b,M,T,p=u.A,n)}}("z"===f||"Z"===f)&&(p=u.Z,n.addData(p),r=a,i=s),e=p}return n.toStatic(),n}(t),r=(0,s.l7)({},e);return r.buildPath=function(t){if(_(t)){t.setData(n.data);var e=t.getContext();e&&t.rebuildPath(e,1)}else{var e=t;n.rebuildPath(e,1)}},r.applyTransform=function(t){(0,a.Z)(n,t),this.dirtyShape()},r}function w(t,e){return new m(x(t,e))}function S(t,e){var n=x(t,e);return function(t){function e(e){var r=t.call(this,e)||this;return r.applyTransform=n.applyTransform,r.buildPath=n.buildPath,r}return(0,r.ZT)(e,t),e}(m)}function b(t,e){for(var n=[],r=t.length,o=0;o<r;o++){var a=t[o];n.push(a.getUpdatedPathProxy(!0))}var s=new i.ZP(e);return s.createPathProxy(),s.buildPath=function(t){if(_(t)){t.appendPath(n);var e=t.getContext();e&&t.rebuildPath(e,1)}},s}},232357:function(t,e,n){n.d(e,{Z:function(){return l}});var r=n(351415),i=n(217961),o=r.Z.CMD,a=[[],[],[]],s=Math.sqrt,u=Math.atan2;function l(t,e){if(!!e){var n,r,l,c,h,f,p=t.data,d=t.len(),g=o.M,v=o.C,y=o.L,m=o.R,_=o.A,x=o.Q;for(l=0,c=0;l<d;){switch(n=p[l++],c=l,r=0,n){case g:case y:r=1;break;case v:r=3;break;case x:r=2;break;case _:var w=e[4],S=e[5],b=s(e[0]*e[0]+e[1]*e[1]),M=s(e[2]*e[2]+e[3]*e[3]),T=u(-e[1]/M,e[0]/b);p[l]*=b,p[l++]+=w,p[l]*=M,p[l++]+=S,p[l++]*=b,p[l++]*=M,p[l++]+=T,p[l++]+=T,l+=2,c=l;break;case m:f[0]=p[l++],f[1]=p[l++],(0,i.Ne)(f,f,e),p[c++]=f[0],p[c++]=f[1],f[0]+=p[l++],f[1]+=p[l++],(0,i.Ne)(f,f,e),p[c++]=f[0],p[c++]=f[1]}for(h=0;h<r;h++){var k=a[h];k[0]=p[l++],k[1]=p[l++],(0,i.Ne)(k,k,e),p[c++]=k[0],p[c++]=k[1]}}t.increaseVersion()}}},305407:function(t,e,n){n.d(e,{EJ:function(){return m},Qq:function(){return _},S1:function(){return v},wm:function(){return y}});var r,i=n(939828),o=n(807028),a=n(265105),s=n(555053),u=n(477367),l=n(829465),c=n(529134),h=n(113525),f=n(707498),p={},d={},g=function(){function t(t,e,n){var r,c=this;this._sleepAfterStill=10,this._stillFrameAccum=0,this._needsRefresh=!0,this._needsRefreshHover=!0,this._darkMode=!1,n=n||{},this.dom=e,this.id=t;var h=new s.Z,f=n.renderer||"canvas";!p[f]&&(f=o.XP(p)[0]);n.useDirtyRect=null!=n.useDirtyRect&&n.useDirtyRect;var d=new p[f](e,h,n,t),g=n.ssr||d.ssrOnly;this.storage=h,this.painter=d;var v=i.Z.node||i.Z.worker||g?null:new l.Z(d.getViewportRoot(),d.root),y=n.useCoarsePointer,m=null==y||"auto"===y?i.Z.touchEventsSupported:!!y;m&&(r=o.pD(n.pointerSize,44)),this.handler=new a.Z(h,d,v,d.root,r),this.animation=new u.Z({stage:{update:g?null:function(){return c._flush(!0)}}}),!g&&this.animation.start()}return t.prototype.add=function(t){if(!this._disposed&&!!t)this.storage.addRoot(t),t.addSelfToZr(this),this.refresh()},t.prototype.remove=function(t){if(!this._disposed&&!!t)this.storage.delRoot(t),t.removeSelfFromZr(this),this.refresh()},t.prototype.configLayer=function(t,e){if(!this._disposed)this.painter.configLayer&&this.painter.configLayer(t,e),this.refresh()},t.prototype.setBackgroundColor=function(t){if(!this._disposed)this.painter.setBackgroundColor&&this.painter.setBackgroundColor(t),this.refresh(),this._backgroundColor=t,this._darkMode=function(t){if(!t)return!1;if("string"==typeof t)return(0,c.L0)(t,1)<h.Ak;if(t.colorStops){for(var e=t.colorStops,n=0,r=e.length,i=0;i<r;i++)n+=(0,c.L0)(e[i].color,1);return(n/=r)<h.Ak}return!1}(t)},t.prototype.getBackgroundColor=function(){return this._backgroundColor},t.prototype.setDarkMode=function(t){this._darkMode=t},t.prototype.isDarkMode=function(){return this._darkMode},t.prototype.refreshImmediately=function(t){if(!this._disposed)!t&&this.animation.update(!0),this._needsRefresh=!1,this.painter.refresh(),this._needsRefresh=!1},t.prototype.refresh=function(){if(!this._disposed)this._needsRefresh=!0,this.animation.start()},t.prototype.flush=function(){if(!this._disposed)this._flush(!1)},t.prototype._flush=function(t){var e,n=(0,u.h)();this._needsRefresh&&(e=!0,this.refreshImmediately(t)),this._needsRefreshHover&&(e=!0,this.refreshHoverImmediately());var r=(0,u.h)();e?(this._stillFrameAccum=0,this.trigger("rendered",{elapsedTime:r-n})):this._sleepAfterStill>0&&(this._stillFrameAccum++,this._stillFrameAccum>this._sleepAfterStill&&this.animation.stop())},t.prototype.setSleepAfterStill=function(t){this._sleepAfterStill=t},t.prototype.wakeUp=function(){if(!this._disposed)this.animation.start(),this._stillFrameAccum=0},t.prototype.refreshHover=function(){this._needsRefreshHover=!0},t.prototype.refreshHoverImmediately=function(){if(!this._disposed)this._needsRefreshHover=!1,this.painter.refreshHover&&"canvas"===this.painter.getType()&&this.painter.refreshHover()},t.prototype.resize=function(t){if(!this._disposed)t=t||{},this.painter.resize(t.width,t.height),this.handler.resize()},t.prototype.clearAnimation=function(){if(!this._disposed)this.animation.clear()},t.prototype.getWidth=function(){if(!this._disposed)return this.painter.getWidth()},t.prototype.getHeight=function(){if(!this._disposed)return this.painter.getHeight()},t.prototype.setCursorStyle=function(t){if(!this._disposed)this.handler.setCursorStyle(t)},t.prototype.findHover=function(t,e){if(!this._disposed)return this.handler.findHover(t,e)},t.prototype.on=function(t,e,n){return!this._disposed&&this.handler.on(t,e,n),this},t.prototype.off=function(t,e){if(!this._disposed)this.handler.off(t,e)},t.prototype.trigger=function(t,e){if(!this._disposed)this.handler.trigger(t,e)},t.prototype.clear=function(){if(!this._disposed){for(var t=this.storage.getRoots(),e=0;e<t.length;e++)t[e]instanceof f.Z&&t[e].removeSelfFromZr(this);this.storage.delAllRoots(),this.painter.clear()}},t.prototype.dispose=function(){var t;if(!this._disposed)this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,this._disposed=!0,t=this.id,delete d[t]},t}();function v(t,e){var n=new g(o.M8(),t,e);return d[n.id]=n,n}function y(t,e){p[t]=e}function m(t){if("function"==typeof r)return r(t)}function _(t){r=t}}}]);