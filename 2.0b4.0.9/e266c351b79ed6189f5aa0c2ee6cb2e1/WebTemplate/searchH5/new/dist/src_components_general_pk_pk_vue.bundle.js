(self.webpackChunkweixin_search=self.webpackChunkweixin_search||[]).push([["src_components_general_pk_pk_vue"],{75:function(t){(function(){var e,i,n,r,o;"undefined"!=typeof performance&&null!==performance&&performance.now?t.exports=function(){return performance.now()}:"undefined"!=typeof process&&null!==process&&process.hrtime?(t.exports=function(){return(e()-o)/1e6},i=process.hrtime,r=(e=function(){var t;return 1e9*(t=i())[0]+t[1]})(),o=r-1e9*process.uptime()):Date.now?(t.exports=function(){return Date.now()-n},n=Date.now()):(t.exports=function(){return new Date().getTime()-n},n=new Date().getTime())}).call(this)},654087:function(t,e,i){for(var n=i(75),r="undefined"==typeof window?i.g:window,o=["moz","webkit"],a="AnimationFrame",s=r["request"+a],c=r["cancel"+a]||r["cancelRequest"+a],u=0;!s&&u<o.length;u++)s=r[o[u]+"Request"+a],c=r[o[u]+"Cancel"+a]||r[o[u]+"CancelRequest"+a];if(!s||!c){var l=0,h=0,f=[],d=1e3/60;s=function(t){if(0===f.length){var e=n(),i=Math.max(0,d-(e-l));l=i+e,setTimeout(function(){var t=f.slice(0);f.length=0;for(var e=0;e<t.length;e++)if(!t[e].cancelled)try{t[e].callback(l)}catch(t){setTimeout(function(){throw t},0)}},Math.round(i))}return f.push({handle:++h,callback:t,cancelled:!1}),h},c=function(t){for(var e=0;e<f.length;e++)f[e].handle===t&&(f[e].cancelled=!0)}}t.exports=function(t){return s.call(r,t)},t.exports.cancel=function(){c.apply(r,arguments)},t.exports.polyfill=function(t){!t&&(t=r),t.requestAnimationFrame=s,t.cancelAnimationFrame=c}},914455:function(t,e,i){"use strict";var n=i(26745),r=i(58711),o=i(551900),a=i(275885),s=(0,o.Z)(r.Z,n.s,n.x,!1,null,null,null);"function"==typeof a.Z&&(0,a.Z)(s),e.Z=s.exports},245342:function(t,e,i){"use strict";function n(t){t.options.__wxs_id="fff54228"}i.d(e,{Z:function(){return n}})},338009:function(t,e,i){"use strict";var n=i(24755),r=i(747015),o=i(551900),a=i(875749),s=(0,o.Z)(r.Z,n.s,n.x,!1,null,"44542d0b",null);"function"==typeof a.Z&&(0,a.Z)(s),e.Z=s.exports},715508:function(t,e,i){"use strict";function n(t){t.options.__wxs_id="02267d3a"}i.d(e,{Z:function(){return n}})},3090:function(t,e,i){"use strict";var n=i(755711),r=i(937132),o=i(551900),a=i(321342),s=(0,o.Z)(r.Z,n.s,n.x,!1,null,"7b6624ca",null);"function"==typeof a.Z&&(0,a.Z)(s),e.Z=s.exports},299248:function(t,e,i){"use strict";function n(t){t.options.__wxs_id="d48edf91"}i.d(e,{Z:function(){return n}})},195301:function(t,e,i){"use strict";var n=i(913968),r=i(243701),o=i(551900),a=i(743718),s=(0,o.Z)(r.Z,n.s,n.x,!1,null,"5dd29dbc",null);"function"==typeof a.Z&&(0,a.Z)(s),e.Z=s.exports},790006:function(t,e,i){"use strict";function n(t){t.options.__wxs_id="e4442ae0"}i.d(e,{Z:function(){return n}})},443027:function(t,e,i){"use strict";var n=i(604245),r=i(740956),o=i(551900),a=i(623316),s=(0,o.Z)(r.Z,n.s,n.x,!1,null,"2e4c28d3",null);"function"==typeof a.Z&&(0,a.Z)(s),e.Z=s.exports},997106:function(t,e,i){"use strict";function n(t){t.options.__wxs_id="b6676a4b"}i.d(e,{Z:function(){return n}})},962019:function(t,e,i){"use strict";var n=i(161503),r=i(63253),o=i(551900),a=i(512954),s=(0,o.Z)(r.Z,n.s,n.x,!1,null,"e843834c",null);"function"==typeof a.Z&&(0,a.Z)(s),e.Z=s.exports},85578:function(t,e,i){"use strict";function n(t){t.options.__wxs_id="a1141f53"}i.d(e,{Z:function(){return n}})},275885:function(t,e,i){"use strict";var n=i(245342);e.Z=n.Z},875749:function(t,e,i){"use strict";var n=i(715508);e.Z=n.Z},321342:function(t,e,i){"use strict";var n=i(299248);e.Z=n.Z},743718:function(t,e,i){"use strict";var n=i(790006);e.Z=n.Z},623316:function(t,e,i){"use strict";var n=i(997106);e.Z=n.Z},512954:function(t,e,i){"use strict";var n=i(85578);e.Z=n.Z},58711:function(t,e,i){"use strict";var n=i(587233);e.Z=n.Z},747015:function(t,e,i){"use strict";var n=i(54889);e.Z=n.Z},937132:function(t,e,i){"use strict";var n=i(332964);e.Z=n.Z},243701:function(t,e,i){"use strict";var n=i(192690);e.Z=n.Z},740956:function(t,e,i){"use strict";var n=i(450748);e.Z=n.Z},63253:function(t,e,i){"use strict";var n=i(560967);e.Z=n.Z},26745:function(t,e,i){"use strict";i.d(e,{s:function(){return n},x:function(){return r}});var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return t.targetAvatarList.length?i("div",{staticClass:"avatar-area",class:{"reverse-avatar-area":t.isRight},style:{height:t.nowHeight+"px",width:t.width+"px"},attrs:{"data-fcn-avatar-area":"","data-fr-157ffbb80":""}},[i("div",{directives:[{name:"active",rawName:"v-active.stop",modifiers:{stop:!0}}],staticClass:"avatar-inner-container active__mask",class:t.isRight?"reverse-container":"forward-container",style:{width:t.innerWidth+"px"},attrs:{"data-cli":""},on:{click:function(e){return e.stopPropagation(),t.onClick(e)}}},[t._l(t.targetAvatarList,function(e,n){return i("ui-image",{key:n,staticClass:"user-avatar",attrs:{size:20,type:"img","no-rpx":"",url:t.M_getAvatar(e),"data-fc-1b010969e":""}})}),i("div",{staticClass:"avatar-more-arrow-wrap",class:{"avatar-more-arrow-reverse":!t.isRight}},[i("ui-image",{staticClass:"arrow-icon",attrs:{height:20,width:10,"no-rpx":"",url:"https://res.wx.qq.com/t/fed_upload/8f1fd8b4-01de-4935-b770-8818bf86cdf0/cur_color_left_arrow.svg","data-fc-1b010969c":""}})],1)],2)]):t._e()},r=[]},24755:function(t,e,i){"use strict";i.d(e,{s:function(){return n},x:function(){return r}});var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"containerRef",staticClass:"pk-side",class:{"pk-side--reverse":"right"===t.position},style:t.style,attrs:{"data-fcn-pk-side":"","data-fr-1dcd03686":""}},[i("trapezium",{attrs:{width:t.width,height:t.height,position:t.position,disable:t.disable,active:!t.disable&&!t.isLoading,"data-report-id":t.M_itemReportId(t.itemInfo.info,t.getItemPos(t.itemInfo.info)),"data-fc-1bd3699d8":""},on:{tap:t.onVoteClick}},[i("div",{class:"pk-side-content__"+t.position},[t.avatar?i("ui-image",{staticClass:"pk-side-icon",class:{"pk-side-icon--rect":t.itemInfo.info.iconRect},attrs:{size:40,type:"img",url:t.avatar,mode:"plain","no-rpx":"","data-fc-1bd3699d6":""}}):t._e(),i("div",{staticClass:"pk-side-info",class:{"pk-side-info--reverse":"right"===t.position}},[i("ui-clamp",{ref:"clamp",staticClass:"pk-side-info__name",attrs:{autoresize:"","max-lines":t.maxLines,"data-fc-1bd3699d4":""},scopedSlots:t._u([{key:"after",fn:function(){return[t.isLoading?i("svg-icon",{staticClass:"loading-icon",class:{"loading-icon-reverse":"right"===t.position},attrs:{name:"loading","data-fc-1bd3699d2":""}}):t.choose?i("ui-image",{staticClass:"tick-icon",class:{"loading-icon-reverse":"right"===t.position},attrs:{size:16,"no-rpx":"",url:"https://res.wx.qq.com/t/fed_upload/6831a4f4-df66-44db-9181-fc81c803664e/check_mark.svg","data-fc-1bd3699d0":""}}):t._e()]},proxy:!0}])},[i("span",[t._v(t._s(t.name))])]),t.showDesc?i("div",{staticClass:"pk-side-info__desc"},[t._v(t._s(t.desc))]):t._e()],1)],1)]),i("avatar-area",{attrs:{"data-report-id":t.M_itemReportId(t.itemInfo.members,t.getItemPos(t.itemInfo.members)),position:t.position,width:t.avatarAreaWidth,"member-list":t.memberList,"data-fc-1bd3699ce":""},on:{tap:t.onAvatarClick}})],1)},r=[]},755711:function(t,e,i){"use strict";i.d(e,{s:function(){return n},x:function(){return r}});var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{directives:[{name:"resize",rawName:"v-resize.value",value:t.onContainerResize,expression:"onContainerResize",modifiers:{value:!0}}],ref:"containerRef",staticClass:"lpl-pk",attrs:{"data-fcn-pk":"","data-fr-10b1becd7":""}},[t.totalWidth?i("pk-side",t._b({attrs:{"is-in-pop":t.isInPop,width:t.leftWidth,height:t.unifyHeight||t.height,line:t.leftLine,"item-info":t.leftInfo,"self-info":t.selfInfo,"cgi-rsp-info":t.cgiRspList[0],disable:t.isRightChosen,"is-loading":t.isLeftLoading,"has-vote":t.hasVote,"can-click":t.canClick,choose:t.isLeftChosen,"report-refix":t.parentItemPos,"parent-item-pos":"",animate:t.animate,"hide-desc-before-vote":t.source.hideDescBeforeVote,"data-fc-15861ae3a":""},on:{clickStart:function(e){return t.onVoteStart("left")},clickEnd:t.onLeftVoteEnd,"click:report":t.onClickReport,pop:t.onPop}},"pk-side",t.$props,!1)):t._e(),t.totalWidth?i("pk-side",t._b({style:t.rightSideStyle,attrs:{position:"right","is-in-pop":t.isInPop,width:t.rightWidth,height:t.unifyHeight||t.height,line:t.rightLine,"item-info":t.rightInfo,"self-info":t.selfInfo,"cgi-rsp-info":t.cgiRspList[1],disable:t.isLeftChosen,choose:t.isRightChosen,"is-loading":t.isRightLoading,"has-vote":t.hasVote,"can-click":t.canClick,"report-refix":t.parentItemPos,"parent-item-pos":"",animate:t.animate,"hide-desc-before-vote":t.source.hideDescBeforeVote,"data-fc-15861ae3b":""},on:{clickStart:function(e){return t.onVoteStart("right")},clickEnd:t.onRightVoteEnd,"click:report":t.onClickReport,pop:t.onPop}},"pk-side",t.$props,!1)):t._e()],1)},r=[]},913968:function(t,e,i){"use strict";i.d(e,{s:function(){return n},x:function(){return r}});var n=function(){var t=this.$createElement,e=this._self._c||t;return e("svg",{attrs:{width:this.width,height:this.height,viewBox:"0 0 "+this.width+" "+this.height,fill:"none",xmlns:"http://www.w3.org/2000/svg","data-fc-1496340dc":"","data-fcn-left-trapezium-svg":"","data-fr-159a7b07f":""}},[e("path",{attrs:{d:"M0 6C0 2.68629 2.68629 0 6 0H"+this.w1+"C"+this.w2+" 0 "+this.w3+" 2.58815 "+this.w4+" 5.15603L"+this.w5+" "+this.h7+"C"+this.w6+" "+this.h1+" "+this.w7+" "+this.h2+" "+this.w8+" "+this.h2+"H5.99999C2.68628 "+this.h2+" 0 "+this.h3+" 0 "+this.h4+"V6Z",fill:"url(#paint0_linear_219_"+this.random+")","data-fc-1496340da":""}}),e("defs",{attrs:{"data-fc-1496340d8":""}},[e("linearGradient",{attrs:{id:"paint0_linear_219_"+this.random,x1:"4",y1:this.h5,x2:"117.724",y2:this.h6,gradientUnits:"userSpaceOnUse","data-fc-1496340d6":""}},[e("stop",{attrs:{"stop-color":this.startColor,"data-fc-1496340d4":""}}),e("stop",{attrs:{offset:"0.659694","stop-color":this.halfColor,"data-fc-1496340d2":""}}),e("stop",{attrs:{offset:"1","stop-color":this.endColor,"data-fc-1496340d0":""}})],1)],1)])},r=[]},604245:function(t,e,i){"use strict";i.d(e,{s:function(){return n},x:function(){return r}});var n=function(){var t=this.$createElement,e=this._self._c||t;return e("svg",{attrs:{width:this.width,height:this.height,viewBox:"0 0 "+this.width+" "+this.height,fill:"none",xmlns:"http://www.w3.org/2000/svg","data-fc-1a69037aa":"","data-fcn-right-trapezium-svg":"","data-fr-1371eb4c6":""}},[e("path",{attrs:{d:"M"+this.width+" 6C"+this.width+" 2.68629 "+this.w1+" 0 "+this.w2+" 0H21.117C19.3493 0 17.7912 1.16036 17.2847 2.85401L0.538822 "+this.h1+"C-0.228411 "+this.h2+" 1.69318 "+this.height+" 4.37115 "+this.height+"H"+this.w2+"C"+this.w1+" "+this.height+" "+this.width+" "+this.h3+" "+this.width+" "+this.h4+"V6Z",fill:"url(#paint0_linear_219_"+this.random+")","data-fc-1a69037a8":""}}),e("defs",{attrs:{"data-fc-1a69037a6":""}},[e("linearGradient",{attrs:{id:"paint0_linear_219_"+this.random,x1:"170",y1:this.h5,x2:"25.1445",y2:this.h6,gradientUnits:"userSpaceOnUse","data-fc-1a69037a4":""}},[e("stop",{attrs:{"stop-color":this.startColor,"data-fc-1a69037a2":""}}),e("stop",{attrs:{offset:"0.52974","stop-color":this.halfColor,"data-fc-1a69037a0":""}}),e("stop",{attrs:{offset:"1","stop-color":this.endColor,"data-fc-1a690379e":""}})],1)],1)])},r=[]},161503:function(t,e,i){"use strict";i.d(e,{s:function(){return n},x:function(){return r}});var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"trapezium-wrapper",style:t.style,attrs:{"data-fcn-trapezium":"","data-fr-1250e2d40":""},on:{touchstart:t.onDown,touchend:t.onUp,mousedown:t.onDown,mouseup:t.onUp,click:function(e){return e.stopPropagation(),t.onClick(e)}}},[i("div",{staticClass:"trapezium--base"},[i(t.trComp,{tag:"component",attrs:{height:t.height,width:t.width,"data-fc-17cb77a5e":""}})],1),i("div",{staticClass:"trapezium--slot"},[t._t("default")],2),t.disable?i("div",{staticClass:"trapezium--disable"},[i(t.trComp,{tag:"component",attrs:{height:t.height,width:t.width,"start-color":"rgba(255,255,255,0.6)","half-color":"rgba(255,255,255,0.6)","end-color":"rgba(255,255,255,0.6)","data-fc-17cb77a5c":""}})],1):t._e(),t.isActivate&&t.active?i("div",{staticClass:"trapezium--hover"},[i(t.trComp,{tag:"component",attrs:{height:t.height,width:t.width,"start-color":"rgba(255,255,255,0.15)","half-color":"rgba(255,255,255,0.15)","end-color":"rgba(255,255,255,0.15)","data-fc-17cb77a5a":""}})],1):t._e()])},r=[]},587233:function(t,e,i){"use strict";var n=i(798509),r=i(382907);e.Z={mixins:[n.uW,n.jB,n.Sx,r.Z],props:{position:{type:String,default:"left"},memberList:{type:Array,default:function(){return[]}},width:{type:Number,default:0}},computed:{targetAvatarList:function(){return this.memberList.slice(0,this.maxNum)},nowHeight:function(){var t,e=Math.ceil(((null===(t=this.targetAvatarList)||void 0===t?void 0:t.length)||0)/(this.maxNum/3));return e<=0?0:20*e+4*(e-1)},isRight:function(){return"right"===this.position},numPerRow:function(){var t=this.width;return t<34?0:Math.floor((t-10)/24)},innerWidth:function(){return 24*Math.min(this.numPerRow,this.targetAvatarList.length)+10},maxNum:function(){return 3*this.numPerRow}},watch:{memberList:{handler:function(){this.refreshMemberList()},immediate:!0}},methods:{refreshMemberList:function(){this.M_getMemberInfoList(this.memberList)},onClick:function(){this.$emit("tap",{items:this.M_getJumpInfos(this.memberList)})}}}},54889:function(t,e,i){"use strict";var n=i(798509),r=i(372973),o=i(677009),a=i(411313),s=i(962019),c=i(914455);function u(t,e,i,n,r,o,a){try{var s=t[o](a),c=s.value}catch(t){i(t);return}s.done?e(c):Promise.resolve(c).then(n,r)}function l(t){return function(){var e=this,i=arguments;return new Promise(function(n,r){var o=t.apply(e,i);function a(t){u(o,n,r,a,s,"next",t)}function s(t){u(o,n,r,a,s,"throw",t)}a(void 0)})}}function h(t,e){var i,n,r,o,a={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return o={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(o){return function(s){return function(o){if(i)throw TypeError("Generator is already executing.");for(;a;)try{if(i=1,n&&(r=2&o[0]?n.return:o[0]?n.throw||((r=n.return)&&r.call(n),0):n.next)&&!(r=r.call(n,o[1])).done)return r;switch(n=0,r&&(o=[2&o[0],r.value]),o[0]){case 0:case 1:r=o;break;case 4:return a.label++,{value:o[1],done:!1};case 5:a.label++,n=o[1],o=[0];continue;case 7:o=a.ops.pop(),a.trys.pop();continue;default:if(!(r=(r=a.trys).length>0&&r[r.length-1])&&(6===o[0]||2===o[0])){a=0;continue}if(3===o[0]&&(!r||o[1]>r[0]&&o[1]<r[3])){a.label=o[1];break}if(6===o[0]&&a.label<r[1]){a.label=r[1],r=o;break}if(r&&a.label<r[2]){a.label=r[2],a.ops.push(o);break}r[2]&&a.ops.pop(),a.trys.pop();continue}o=e.call(t,a)}catch(t){o=[6,t],n=0}finally{i=r=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,s])}}}e.Z={components:{Trapezium:s.Z,AvatarArea:c.Z},mixins:[n.jB,n.uW,o.Z],inject:{onPkMembersChange:{default:function(){return function(){}}}},props:{source:{type:Object,default:function(){return{}}},data:{type:Object,default:function(){return{}}},item:{type:Object,default:function(){return{}}},pos:{type:Number,default:0},typePos:{type:Number,default:0},width:{type:Number,default:175},height:{type:Number,default:64},position:{type:String,default:"left"},disable:{type:Boolean,default:!1},itemInfo:{type:Object,default:function(){return{}}},selfInfo:{type:Object,default:null},line:{type:Number,default:1},choose:{type:Boolean,default:!1},cgiRspInfo:{type:Object,default:function(){return{}}},isLoading:{type:Boolean,default:!1},canClick:{type:Boolean,default:!0},isInPop:{type:Boolean,default:!1},reportRefix:{type:String,default:""},hasVote:{type:Boolean,default:!1},animate:{type:Boolean,default:!1},hideDescBeforeVote:{type:Boolean,default:!1}},computed:{memberList:function(){var t,e,i;return(null===(t=this.itemInfo)||void 0===t?void 0:t.members)?this.M_composeMemberList(null===(i=this.itemInfo)||void 0===i?void 0:null===(e=i.members)||void 0===e?void 0:e.allMembers,this.selfInfo,this.choose):[]},avatar:function(){var t,e;return null===(e=this.itemInfo)||void 0===e?void 0:null===(t=e.info)||void 0===t?void 0:t.iconUrl},name:function(){var t,e;return null===(e=this.itemInfo)||void 0===e?void 0:null===(t=e.info)||void 0===t?void 0:t.title},desc:function(){var t,e,i,n,r=(null===(t=this.cgiRspInfo)||void 0===t?void 0:t.numStr)||(null===(e=this.itemInfo)||void 0===e?void 0:e.numStr);return r?this.hideDescBeforeVote&&!this.hasVote?null===(n=this.itemInfo)||void 0===n?void 0:n.desc:"".concat(r).concat(null===(i=this.itemInfo)||void 0===i?void 0:i.desc):""},style:function(){return{width:"".concat(this.width,"px")}},avatarAreaWidth:function(){return this.width-24},getItemPosPrefix:function(){return"".concat(this.reportRefix).concat(this.reportRefix?"|":"").concat("left"===this.position?1:2)},titleWidth:function(){return(0,r.I)(this.$refs.containerRef).getTextWidth({text:this.name,fontSize:"var(--FONT-M)",fontWeight:500})},maxLines:function(){if(this.line<2||!this.choose&&!this.isLoading)return this.line;var t=this.width-16-40-12-24;return this.titleWidth<=t&&this.titleWidth>=t-2-16?1:this.line},showDesc:function(){return this.animate?this.hasVote&&!!this.desc:!!this.desc}},watch:{memberList:{handler:function(t){var e;null===(e=this.onPkMembersChange)||void 0===e||e.call(this,this.position,t)},immediate:!0},isLoading:{handler:function(){this.updateClamp()}},choose:{handler:function(){this.updateClamp()}}},methods:{updateClamp:function(){var t=this;this.$nextTick(function(){var e,i;null===(i=t.$refs.clamp)||void 0===i||null===(e=i.update)||void 0===e||e.call(i)})},getItemPos:function(t){var e=this.M_getItemType(t);return"".concat(this.getItemPosPrefix,":").concat(e)},onVoteClick:function(){return l(function(){var t,e;return h(this,function(i){var n;return t=this,e=this,this.M_vote({itemInfo:this.itemInfo,cgi:this.cgiRspInfo.cgi,disable:this.disable||!this.canClick,choose:this.choose,reportPrefix:this.getItemPosPrefix,itemPos:this.getItemPos(this.itemInfo.info),onVoteStart:l(function(){return h(this,function(e){return t.$emit("clickStart"),[2]})}),onVoteEnd:(n=l(function(t){return h(this,function(i){switch(i.label){case 0:return e.$emit("clickEnd",t),[4,(0,a._v)(1e3)];case 1:return i.sent(),[2]}})}),function(t){return n.apply(this,arguments)})}),[2]})}).apply(this)},onAvatarClick:function(t){var e,i,n=t.items;this.M_popNewAvatarListDialog({jumpInfos:n,reportPrefix:this.getItemPosPrefix,reportId:this.itemInfo.members.reportId,itemPos:this.getItemPos(this.itemInfo.members),pop:null===(i=this.itemInfo)||void 0===i?void 0:null===(e=i.members)||void 0===e?void 0:e.pop})},goPop:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.M_getClickZonePath(),i=arguments.length>2?arguments[2]:void 0;this.isInPop?this.$emit("pop",t):this.M_serviceSearchGo(t,e,i)},clickReport:function(t){this.isInPop?this.$emit("click:report",t):this.M_clickReport(t)}}}},332964:function(t,e,i){"use strict";var n=i(798509),r=i(372973),o=i(691409),a=i(338009);function s(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=Array(e);i<e;i++)n[i]=t[i];return n}function c(t,e,i,n,r,o,a){try{var s=t[o](a),c=s.value}catch(t){i(t);return}s.done?e(c):Promise.resolve(c).then(n,r)}function u(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{},n=Object.keys(i);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(i).filter(function(t){return Object.getOwnPropertyDescriptor(i,t).enumerable}))),n.forEach(function(e){var n,r,o;n=t,r=e,o=i[e],r in n?Object.defineProperty(n,r,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[r]=o})}return t}function l(t,e){return e=null!=e?e:{},Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):(function(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);i.push.apply(i,n)}return i})(Object(e)).forEach(function(i){Object.defineProperty(t,i,Object.getOwnPropertyDescriptor(e,i))}),t}e.Z={components:{PkSide:a.Z},mixins:[n.jB,n.uW,n.Sx],inject:{onPkCalcHeight:{value:"onPkCalcHeight",default:null}},props:{isInPop:{type:Boolean,default:!1}},data:function(){return{totalWidth:0,leftFlex:1,rightFlex:1,cgiChooseIndex:-1,cgiRspList:[],selfInfo:null,loadingStatus:0,leftLine:1,rightLine:1,unifyHeight:null}},computed:{chooseIndex:function(){return -1===this.cgiChooseIndex?this.source.chooseIndex:this.cgiChooseIndex},leftInfo:function(){var t;return null===(t=this.source.list)||void 0===t?void 0:t[0]},rightInfo:function(){var t;return null===(t=this.source.list)||void 0===t?void 0:t[1]},leftWidth:function(){return(this.totalWidth- -9)*this.leftFlex/(this.leftFlex+this.rightFlex)},rightWidth:function(){return(this.totalWidth- -9)*this.rightFlex/(this.leftFlex+this.rightFlex)},rightSideStyle:function(){return{marginLeft:"".concat(-9,"px")}},height:function(){var t=Math.max(this.leftLine,this.rightLine),e=(0,r.I)(this.$refs.containerRef),i=e.getTextHeight({text:"111",fontSize:"var(--FONT-M)",fontWeight:500}),n=e.getTextHeight({text:"111",fontSize:"var(--FONT-XS)",fontWeight:400});return Math.max(26+i*t+n,64)},isLeftChosen:function(){return 1===this.chooseIndex},isRightChosen:function(){return 2===this.chooseIndex},canClick:function(){return 0===this.loadingStatus},isLeftLoading:function(){return 1===this.loadingStatus},isRightLoading:function(){return 2===this.loadingStatus},hasVote:function(){return 0!==this.chooseIndex},animate:function(){return this.source.animate}},watch:{source:{handler:function(){var t=this;if(this.animate){if(this.hasVote){var e,i,n=this.limitFlexValue((null===(e=this.leftInfo)||void 0===e?void 0:e.num)||0,(null===(i=this.rightInfo)||void 0===i?void 0:i.num)||0),r=n.leftFlex,o=n.rightFlex;this.leftFlex=r,this.rightFlex=o}else this.leftFlex=1,this.rightFlex=1}this.$nextTick(function(){var e,i,n,r,o;t.leftLine=t.getLine(null===(i=t.leftInfo)||void 0===i?void 0:null===(e=i.info)||void 0===e?void 0:e.title,t.leftWidth),t.rightLine=t.getLine(null===(r=t.rightInfo)||void 0===r?void 0:null===(n=r.info)||void 0===n?void 0:n.title,t.rightWidth),null===(o=t.onPkCalcHeight)||void 0===o||o.call(t,t.height)})},immediate:!0},chooseIndex:{handler:function(t){this.$emit("onChooseIndexChange",t)}}},mounted:function(){var t=this;n.Gc.$once("update:pk-height",function(e){t.unifyHeight=e});var e=this.$refs.containerRef.getBoundingClientRect().width;this.totalWidth=e},methods:{onContainerResize:function(t){this.totalWidth=t.width},getLine:function(t,e){if(!t)return 0;var i=(0,r.I)(this.$refs.containerRef);return i.getTextHeight({text:t,fontSize:"var(--FONT-M)",fontWeight:500})!==i.getTextHeight({text:t,fontSize:"var(--FONT-M)",fontWeight:500,width:e-92})?2:1},onVoteStart:function(t){this.loadingStatus="left"===t?1:2},onLeftVoteEnd:function(t){t?this.handleVoteRsp(l(u({},t),{isLeft:!0})):this.loadingStatus=0},onRightVoteEnd:function(t){t?this.handleVoteRsp(l(u({},t),{isLeft:!1})):this.loadingStatus=0},handleVoteRsp:function(t){var e,i=t.data,n=t.isLeft,r=t.isCancel,o=t.result;return(e=function(){var t,e,a,s,c,u;return function(t,e){var i,n,r,o,a={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return o={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(o){return function(s){return function(o){if(i)throw TypeError("Generator is already executing.");for(;a;)try{if(i=1,n&&(r=2&o[0]?n.return:o[0]?n.throw||((r=n.return)&&r.call(n),0):n.next)&&!(r=r.call(n,o[1])).done)return r;switch(n=0,r&&(o=[2&o[0],r.value]),o[0]){case 0:case 1:r=o;break;case 4:return a.label++,{value:o[1],done:!1};case 5:a.label++,n=o[1],o=[0];continue;case 7:o=a.ops.pop(),a.trys.pop();continue;default:if(!(r=(r=a.trys).length>0&&r[r.length-1])&&(6===o[0]||2===o[0])){a=0;continue}if(3===o[0]&&(!r||o[1]>r[0]&&o[1]<r[3])){a.label=o[1];break}if(6===o[0]&&a.label<r[1]){a.label=r[1],r=o;break}if(r&&a.label<r[2]){a.label=r[2],a.ops.push(o);break}r[2]&&a.ops.pop(),a.trys.pop();continue}o=e.call(t,a)}catch(t){o=[6,t],n=0}finally{i=r=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,s])}}}(this,function(l){switch(l.label){case 0:if(o.isFail())return[2];return o.needToHandleRsp()&&(this.cgiRspList=i.list||this.cgiRspList,i.selfInfo&&(this.selfInfo=i.selfInfo)),c="number"==typeof(null==i?void 0:null===(e=i.list)||void 0===e?void 0:null===(t=e[0])||void 0===t?void 0:t.num)?i.list[0].num:this.leftFlex,u="number"==typeof(null==i?void 0:null===(s=i.list)||void 0===s?void 0:null===(a=s[1])||void 0===a?void 0:a.num)?i.list[1].num:this.rightFlex,r&&(this.cgiChooseIndex=0),[4,this.handleFlexChange(r||!this.animate?1:c,r||!this.animate?1:u)];case 1:return l.sent(),this.loadingStatus=0,r?this.cgiChooseIndex=0:this.cgiChooseIndex=n?1:2,[2]}})},function(){var t=this,i=arguments;return new Promise(function(n,r){var o=e.apply(t,i);function a(t){c(o,n,r,a,s,"next",t)}function s(t){c(o,n,r,a,s,"throw",t)}a(void 0)})}).apply(this)},handleFlexChange:function(t,e){var i=this,n=this.limitFlexValue(t,e),r=n.leftFlex,a=n.rightFlex;return this.animate?(0,o.j)({oldValueList:[this.leftFlex,this.rightFlex],newValueList:[r,a],duration:300,handler:function(t){var e,n,r=(n=2,function(t){if(Array.isArray(t))return t}(e=t)||function(t,e){var i,n,r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var o=[],a=!0,s=!1;try{for(r=r.call(t);!(a=(i=r.next()).done)&&(o.push(i.value),!e||o.length!==e);a=!0);}catch(t){s=!0,n=t}finally{try{!a&&null!=r.return&&r.return()}finally{if(s)throw n}}return o}}(e,2)||function(t,e){if(t){if("string"==typeof t)return s(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);if("Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i)return Array.from(i);if("Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return s(t,e)}}(e,n)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),o=r[0],a=r[1];i.leftFlex=o,i.rightFlex=a}}):(this.leftFlex=r,this.rightFlex=a,Promise.resolve())},limitFlexValue:function(t,e){return t/(t+e)<.4?(t=2,e=3):e/(t+e)<.4&&(t=3,e=2),{leftFlex:t,rightFlex:e}},onPop:function(t){this.$emit("pop",t)},onClickReport:function(t){this.$emit("click:report",t)},getCgiData:function(){return{leftFlex:this.leftFlex,rightFlex:this.rightFlex,cgiChooseIndex:this.cgiChooseIndex,selfInfo:this.selfInfo,cgiRspList:this.cgiRspList}},updateCgiData:function(t){var e=this;Object.keys(t).forEach(function(i){e.$set(e,i,t[i])})}}}},192690:function(t,e){"use strict";e.Z={props:{width:{type:Number,default:175},height:{type:Number,default:64},startColor:{type:String,default:"#4582EE"},halfColor:{type:String,default:"#4582ED"},endColor:{type:String,default:"#5691F3"}},data:function(){return{random:(1e5*Math.random()).toFixed(0)}},computed:{w1:function(){return this.calculateX(170.614,270.614)},w2:function(){return this.calculateX(173.296,273.296)},w3:function(){return this.calculateX(175.219,275.219)},w4:function(){return this.calculateX(174.443,274.443)},w5:function(){return this.calculateX(157.538,257.538)},w6:function(){return this.calculateX(157.028,257.028)},w7:function(){return this.calculateX(155.472,255.472)},w8:function(){return this.calculateX(153.708,253.708)},h1:function(){return this.calculateY(62.8446,83.7077)},h2:function(){return this.calculateY(64,85)},h3:function(){return this.calculateY(61.3137,82.3137)},h4:function(){return this.calculateY(58,79)},h5:function(){return this.calculateY(4,5.3125)},h6:function(){return this.calculateY(104.891,99.1813)},h7:function(){return this.calculateY(61.156,81.8866)}},methods:{calculateX:function(t,e){return(e-t)/100*this.width+-((175*e-275*t)/100)},calculateY:function(t,e){return(e-t)/21*this.height+-((64*e-85*t)/21)}}}},450748:function(t,e){"use strict";e.Z={props:{width:{type:Number,default:175},height:{type:Number,default:64},startColor:{type:String,default:"#F24240"},halfColor:{type:String,default:"#FA544A"},endColor:{type:String,default:"#FE7E5E"}},data:function(){return{random:(1e5*Math.random()).toFixed(0)}},computed:{w1:function(){return this.calculateX(172.314,272.314)},w2:function(){return this.calculateX(169,269)},h1:function(){return this.calculateY(58.854,80.1214)},h2:function(){return this.calculateY(61.4197,82.6223)},h3:function(){return this.calculateY(61.3137,82.3137)},h4:function(){return this.calculateY(58,79)},h5:function(){return this.calculateY(58,77.0313)},h6:function(){return this.calculateY(68.8273,85.2032)}},methods:{calculateX:function(t,e){return(e-t)/100*this.width+-((175*e-275*t)/100)},calculateY:function(t,e){return(e-t)/21*this.height+-((64*e-85*t)/21)}}}},560967:function(t,e,i){"use strict";var n=i(195301),r=i(443027);e.Z={components:{LeftTrapezium:n.Z,RightTrapezium:r.Z},props:{width:{type:Number,default:175},height:{type:Number,default:64},position:{type:String,default:"left"},disable:{type:Boolean,default:!1},active:{type:Boolean,default:!0}},data:function(){return{isActivate:!1}},computed:{style:function(){return{width:"".concat(this.width,"px"),height:"".concat(this.height,"px")}},trComp:function(){return"left"===this.position?n.Z:r.Z}},methods:{onDown:function(){this.isActivate=!0},onUp:function(){this.isActivate=!1},onClick:function(){if(this.disable)return console.log("trapezium disable");this.$emit("tap")}}}},691409:function(t,e,i){"use strict";i.d(e,{j:function(){return a}});var n=i(654087),r=i.n(n);function o(t,e,i,n,r,o,a){try{var s=t[o](a),c=s.value}catch(t){i(t);return}s.done?e(c):Promise.resolve(c).then(n,r)}function a(t){return s.apply(this,arguments)}function s(){var t;return t=function(t){var e,i,n,o,a,s;return function(t,e){var i,n,r,o,a={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return o={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(o){return function(s){return function(o){if(i)throw TypeError("Generator is already executing.");for(;a;)try{if(i=1,n&&(r=2&o[0]?n.return:o[0]?n.throw||((r=n.return)&&r.call(n),0):n.next)&&!(r=r.call(n,o[1])).done)return r;switch(n=0,r&&(o=[2&o[0],r.value]),o[0]){case 0:case 1:r=o;break;case 4:return a.label++,{value:o[1],done:!1};case 5:a.label++,n=o[1],o=[0];continue;case 7:o=a.ops.pop(),a.trys.pop();continue;default:if(!(r=(r=a.trys).length>0&&r[r.length-1])&&(6===o[0]||2===o[0])){a=0;continue}if(3===o[0]&&(!r||o[1]>r[0]&&o[1]<r[3])){a.label=o[1];break}if(6===o[0]&&a.label<r[1]){a.label=r[1],r=o;break}if(r&&a.label<r[2]){a.label=r[2],a.ops.push(o);break}r[2]&&a.ops.pop(),a.trys.pop();continue}o=e.call(t,a)}catch(t){o=[6,t],n=0}finally{i=r=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,s])}}}(this,function(c){return(e=t.newValueList,i=t.oldValueList,n=t.duration,o=t.handler,Array.isArray(e)&&Array.isArray(i))?(a=e.map(function(t,e){return t-i[e]}),s=Date.now(),[2,new Promise(function(t){var c=function(){var u=Date.now()-s;o(i.map(function(t,e){return t+a[e]*u/n})),u<n?r()(c):(o(e),t())};c()})]):[2]})},(s=function(){var e=this,i=arguments;return new Promise(function(n,r){var a=t.apply(e,i);function s(t){o(a,n,r,s,c,"next",t)}function c(t){o(a,n,r,s,c,"throw",t)}s(void 0)})}).apply(this,arguments)}}}]);