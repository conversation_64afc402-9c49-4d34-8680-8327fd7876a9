"use strict";(self.webpackChunkweixin_search=self.webpackChunkweixin_search||[]).push([["81-advance-search"],{444839:function(e,t,n){var a=n(808929),r=n(942433),i=n(551900),o=n(928735),c=(0,i.Z)(r.Z,a.s,a.x,!1,null,"1bddad82",null);"function"==typeof o.Z&&(0,o.Z)(c),t.Z=c.exports},874409:function(e,t,n){n.d(t,{Z:function(){return a}});function a(e){e.options.__wxs_id="118d94a3"}},84288:function(e,t,n){var a=n(242187),r=n(352114),i=n(551900),o=n(342405),c=(0,i.Z)(r.Z,a.s,a.x,!1,null,"33cbc6f2",null);"function"==typeof o.Z&&(0,o.Z)(c),t.Z=c.exports},11267:function(e,t,n){n.d(t,{Z:function(){return a}});function a(e){e.options.__wxs_id="c1c98b1d"}},515770:function(e,t,n){n.r(t);var a=n(292379),r=n(991279),i=n(551900),o=n(111181),c=(0,i.Z)(r.Z,a.s,a.x,!1,null,null,null);"function"==typeof o.Z&&(0,o.Z)(c),t.default=c.exports},671064:function(e,t,n){n.d(t,{Z:function(){return a}});function a(e){e.options.__wxs_id="b80508f5"}},902333:function(e,t,n){var a=n(557757),r=n(83487),i=n(551900),o=n(14308),c=(0,i.Z)(r.Z,a.s,a.x,!1,null,"6338a178",null);"function"==typeof o.Z&&(0,o.Z)(c),t.Z=c.exports},178309:function(e,t,n){n.d(t,{Z:function(){return a}});function a(e){e.options.__wxs_id="a4651709"}},858885:function(e,t,n){var a=n(252363),r=n(407880),i=n(551900),o=n(181094),c=(0,i.Z)(r.Z,a.s,a.x,!1,null,"90cf09ce",null);"function"==typeof o.Z&&(0,o.Z)(c),t.Z=c.exports},108912:function(e,t,n){n.d(t,{Z:function(){return a}});function a(e){e.options.__wxs_id="4a0d3757"}},928735:function(e,t,n){var a=n(874409);t.Z=a.Z},342405:function(e,t,n){var a=n(11267);t.Z=a.Z},111181:function(e,t,n){var a=n(671064);t.Z=a.Z},14308:function(e,t,n){var a=n(178309);t.Z=a.Z},181094:function(e,t,n){var a=n(108912);t.Z=a.Z},942433:function(e,t,n){var a=n(140783);t.Z=a.Z},352114:function(e,t,n){var a=n(363396);t.Z=a.Z},991279:function(e,t,n){var a=n(416157);t.Z=a.Z},83487:function(e,t,n){var a=n(343674);t.Z=a.Z},407880:function(e,t,n){var a=n(863037);t.Z=a.Z},808929:function(e,t,n){n.d(t,{s:function(){return a},x:function(){return r}});var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("fieldset",{staticClass:"ui-checkbox-group",attrs:{"data-fcn-component":"","data-fr-115cbf5f1":""}},[e.$slots.title||e.title?n("legend",{staticClass:"ui-checkbox-group__title"},[e.$slots.title?e._t("title"):[e._v(e._s(e.title))]],2):e._e(),e.column?n("ui-column",{attrs:{list:e.options,col:e.column,"data-fc-1b89a6f40":""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.item,r=t.index;return[n("ui-checkbox",e._b({directives:[{name:"show",rawName:"v-show",value:e.maxDisplayNum<=0||r<e.maxDisplayNum,expression:"maxDisplayNum <= 0 || index < maxDisplayNum"}],attrs:{"data-fc-1b89a6f3e":""},on:{click:function(t){return e.onClick(r,a)},change:function(t){return e.onChange(r,t)}},model:{value:a[e._keyMap.checked],callback:function(t){e.$set(a,e._keyMap.checked,t)},expression:"item[_keyMap.checked]"}},"ui-checkbox",e._getProps(r,a),!1),[e._t("option",null,{option:a,index:r})],2)]}}],null,!0)}):n("div",e._l(e.options,function(t,a){return n("ui-checkbox",e._b({directives:[{name:"show",rawName:"v-show",value:e.maxDisplayNum<=0||a<e.maxDisplayNum,expression:"maxDisplayNum <= 0 || index < maxDisplayNum"}],key:t[e._keyMap.value],attrs:{"data-fc-1b89a6f3c":""},on:{click:function(n){return e.onClick(a,t)},change:function(t){return e.onChange(a,t)}},model:{value:t[e._keyMap.checked],callback:function(n){e.$set(t,e._keyMap.checked,n)},expression:"item[_keyMap.checked]"}},"ui-checkbox",e._getProps(a,t),!1),[e._t("option",null,{option:t,index:a})],2)}),1)],1)},r=[]},242187:function(e,t,n){n.d(t,{s:function(){return a},x:function(){return r}});var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("label",e._b({directives:[{name:"active",rawName:"v-active.stop",modifiers:{stop:!0}}],staticClass:"ui-checkbox",class:"ui-checkbox-"+e.theme,attrs:{"data-cli":"","data-fcn-component":"","data-fr-1737a6202":""}},"label",e.domProps,!1),[n("input",{staticClass:"ui-checkbox__input",attrs:{"aria-hidden":"true",type:"checkbox",name:e.name,disabled:e.disabled||e.readonly,readonly:e.readonly},domProps:{value:e.value,checked:!!e.checked},on:{change:e.onChange}}),n("span",{staticClass:"ui-checkbox__label",on:{click:function(t){return e.$emit("click")},touchmove:e.onMove,mousewheel:e.onMove}},[e.$slots.default?e._t("default"):[e._v(e._s(e.label))]],2)])},r=[]},292379:function(e,t,n){n.d(t,{s:function(){return a},x:function(){return r}});var a=function(){var e=this.$createElement,t=this._self._c||e;return this.hasAdvanceSearch?t(this.component,{tag:"component",attrs:{"data-fc-142a00db3":"","data-fcn-advance-search":"","data-fr-1f3718b84":""}}):this._e()},r=[]},557757:function(e,t,n){n.d(t,{s:function(){return a},x:function(){return r}});var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{directives:[{name:"active",rawName:"v-active",value:!e.titleTapAlone,expression:"!titleTapAlone"}],staticClass:"advance-search-bar",class:{active__item:!e.titleTapAlone},attrs:{"data-cli":"","data-fcn-advance-search-bar":"","data-fr-1733cb17c":""},on:{click:function(t){e.titleTapAlone||e.$emit("tap")},touchmove:e.onTouchMove,mousewheel:e.onTouchMove}},[n("div",{directives:[{name:"active",rawName:"v-active.stop",value:e.titleTapAlone,expression:"titleTapAlone",modifiers:{stop:!0}}],staticClass:"advance-search-bar-title",class:{active__opacity:e.titleTapAlone},attrs:{"data-cli":""},on:{click:function(t){e.titleTapAlone&&e.$emit("tap")}}},[n("div",{staticClass:"advance-search-bar-title-text"},[e._v(e._s(e.title))]),n("ui-arrow",{staticClass:"advance-search-bar-arrow",attrs:{direction:e.arrow,align:"flex",size:"big","data-fc-1174d0396":""}})],1),n("div",{directives:[{name:"active",rawName:"v-active.stop",modifiers:{stop:!0}}],staticClass:"advance-search-bar-clear active__mask",attrs:{"data-cli":""},on:{click:function(t){return t.stopPropagation(),e.$emit("clear")},touchmove:e.onTouchMove,mousewheel:e.onTouchMove}},[e._v("\n      "+e._s(e._f("L")("清空"))+"\n    ")])])},r=[]},252363:function(e,t,n){n.d(t,{s:function(){return a},x:function(){return r}});var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"advance-search",style:{top:e.top},attrs:{"data-p-0bfpe":"","data-fcn-index":"","data-fr-1443cfc82":""}},[e.showAdvanceSearchBar&&e.advanceSearch.title?n("advance-search-bar",{staticClass:"advance-search-bar-occupy",attrs:{arrow:"down",title:e.advanceSearch.title,"title-tap-alone":e.os.pc,"data-fc-14362935c":""},on:{tap:e.onTapFilterBar,clear:e.onTapClear}}):e._e(),n("div",{staticClass:"advance-search-popup",class:{shadow:e.showHalfScreen&&e.os.pc}},[n("height-animate",{directives:[{name:"show",rawName:"v-show",value:e.showHalfScreen,expression:"showHalfScreen"}],attrs:{"data-fc-14362935a":""},nativeOn:{touchmove:function(e){e.preventDefault()},mousewheel:function(e){e.preventDefault()}}},[n("table",{staticClass:"advance-search-options",attrs:{cellspacing:"0",cellpadding:"0"}},e._l(e.filters,function(t){return n("tr",{key:t.paramKey},[(e.forceTitleInline||e.titleInline)&&t.title?n("td",{staticClass:"pop-filter-title"},[e._v(e._s(t.title))]):e._e(),n("td",{staticClass:"pop-filter-options"},[n("ui-checkbox-group",{attrs:{single:t.type!==e.filterMode.multi,title:e.forceTitleInline||e.titleInline?"":t.title,"first-as-mutex":0==t.options[0].paramValue,"key-map":{value:"paramValue",label:"title",checked:"selected"},name:t.paramKey,column:t.column||2,theme:e.M_os("primary","default"),"prevent-scroll":"","data-fc-143629358":""},on:{click:e.onTapOption,change:e.onOptionChange},model:{value:t.options,callback:function(n){e.$set(t,"options",n)},expression:"filter.options"}})],1)])}),0),e.os.mobile&&e.showAdvanceSearchBar&&!e.advanceSearch.title?n("advance-search-bar",{attrs:{arrow:"up",title:e._f("L")("收起"),"prevent-scroll":"","data-fc-143629356":""},on:{tap:e.onTapFilterBar,clear:e.onTapClear}}):e._e(),e.os.pc&&e.showAdvanceSearchBar?n("advance-search-bar",{attrs:{arrow:"up",title:e._f("L")(e.advanceSearch.title||"收起"),"prevent-scroll":"","data-fc-143629354":""},on:{tap:e.onTapFilterBar,clear:e.onTapClear}}):e._e()],1),e.os.mobile&&e.showAdvanceSearchBar&&e.advanceSearch.title?n("advance-search-bar",{style:e.M_os("margin-top: 1px"),attrs:{arrow:e.showHalfScreen?"up":"down",title:e.advanceSearch.title,"prevent-scroll":e.showHalfScreen,"data-fc-143629352":""},on:{tap:e.onTapFilterBar,clear:e.onTapClear}}):e._e()],1),n("div",{directives:[{name:"show",rawName:"v-show",value:e.showMask,expression:"showMask"}],staticClass:"ui-mask",class:e.M_os(e.showHalfScreen?"weui-animate-fade-in":"weui-animate-fade-out"),style:{top:e.M_os(e.top,0)},on:{click:e.onTapMask,touchmove:function(e){e.preventDefault()},mousewheel:function(e){e.preventDefault()}}})],1)},r=[]},140783:function(e,t,n){var a=n(705568),r=n(802712);function i(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=Array(t);n<t;n++)a[n]=e[n];return a}function o(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function c(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),a.forEach(function(t){o(e,t,n[t])})}return e}t.Z={name:"UiCheckboxGroup",components:{UiCheckbox:a.Z,UiColumn:r.Z},model:{prop:"options",event:"change"},props:{options:Array,keyMap:Object,title:String,column:Number,name:String,single:Boolean,max:Number,preventScroll:Boolean,allowEmpty:Boolean,firstAsMutex:Boolean,firstAsAll:Boolean,maxDisplayNum:{type:Number,default:0},theme:String},computed:{_keyMap:function(){return c({label:"label",value:"value",disabled:"disabled",checked:"checked",name:"name"},this.keyMap)},_readonly:function(){var e=this;if(!this.allowEmpty){var t=function(t){return t[e._keyMap.checked]&&!t[e._keyMap.disabled]},n=this.options.filter(t);return this.options.map(function(e){return!!t(e)&&1===n.length||void 0})}return[]},_reachMax:function(){var e=this;return!(this.single||isNaN(this.max))&&!!(this.max>=0)&&this.options.filter(function(t){return t[e._keyMap.checked]}).length>=this.max}},methods:{_getProps:function(e,t){var n=t[this._keyMap.checked]||!this._reachMax;return{name:this.name||t[this._keyMap.name],label:t[this._keyMap.label],value:t[this._keyMap.value],disabled:t[this._keyMap.disabled],readonly:this._readonly[e]||!n,preventScroll:this.preventScroll,domProps:t.domProps,allowEmpty:this.allowEmpty,theme:this.theme}},_updateChecked:function(e){var t=this;this.$emit("change",this.options.map(function(n,a){var r,i;return r=c({},n),i=null!=(i=o({},t._keyMap.checked,e(n,a)))?i:{},Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(i)):(function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);n.push.apply(n,a)}return n})(Object(i)).forEach(function(e){Object.defineProperty(r,e,Object.getOwnPropertyDescriptor(i,e))}),r}))},onChange:function(e,t){var n=this;if(this.firstAsAll&&!this.single){if(0===e)this._updateChecked(function(){return t});else{this.$set(this.options[e],this._keyMap.checked,t);var a,r=function(e){if(Array.isArray(e))return e}(a=this.options)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(a)||function(e,t){if(e){if("string"==typeof e)return i(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return i(e,t)}}(a)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),o=(r[0],r.slice(1)).every(function(e){return e[n._keyMap.checked]});this._updateChecked(function(e,t){return 0===t?o:e[n._keyMap.checked]})}}else this.firstAsMutex&&!this.single&&t?0===e?this._updateChecked(function(t,n){return n===e}):this._updateChecked(function(a,r){return 0!==r&&(r===e?t:a[n._keyMap.checked])}):this._updateChecked(function(a,r){return n.single?r===e&&t:r===e?t:a[n._keyMap.checked]});this.$emit("change-by",e)},onClick:function(e,t){this.$emit("click",e,t),!t[this._keyMap.checked]&&this._reachMax&&this.$emit("max-limit")}}}},363396:function(e,t){t.Z={name:"UiCheckbox",model:{prop:"checked",event:"change"},props:{label:String,value:[String,Number],disabled:Boolean,name:String,checked:[Boolean,Number],domProps:{type:Object,default:function(){return{}}},preventScroll:Boolean,readonly:Boolean,theme:{type:String,default:"primary"}},methods:{onChange:function(e){this.$emit("change",e.target.checked)},onMove:function(e){this.preventScroll&&(e.preventDefault(),e.stopPropagation())}}}},416157:function(e,t,n){var a=n(858885),r=n(798509);t.Z={components:{dropdown:a.Z},computed:{component:function(){return({3:"dropdown",5:"dropdown"})[this.$store.state.result.sift.advanceSearch.showType]||"dropdown"},hasAdvanceSearch:function(){return!r.Zr.isObjectEmpty(this.$store.state.result.sift.advanceSearch)}}}},343674:function(e,t,n){var a=n(151285);t.Z={mixins:[a.ZP],props:{arrow:String,title:String,preventScroll:Boolean,titleTapAlone:Boolean},methods:{onTouchMove:function(e){this.preventScroll&&e.preventDefault()}}}},863037:function(e,t,n){var a=n(984928),r=n(798509),i=n(142862),o=n(902333),c=n(690286),s=n(915794);function l(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),a.forEach(function(t){var a,r,i;a=e,r=t,i=n[t],r in a?Object.defineProperty(a,r,{value:i,enumerable:!0,configurable:!0,writable:!0}):a[r]=i})}return e}function u(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):(function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);n.push.apply(n,a)}return n})(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}var p={tab:1,mask:2,filter:3},f=null;t.Z={components:{advanceSearchBar:o.Z,UiCheckboxGroup:s.Z},mixins:[c.Z],data:function(){return{showHalfScreen:!1,showHalfScreenBottom:!1,showHalfScreenBottomTimer:0,showMask:!1,titleInline:"en"!==r.xB.queryString.lang}},computed:{forceTitleInline:function(){return 5==this.advanceSearch.showType},showAdvanceSearchBar:function(){return 5!=this.advanceSearch.showType}},watch:{$route:function(){this.hideHalfScreenFilter()},showHalfScreen:function(e){var t=this;r.Gc.$emit(r.U3.TOGGLE_ADVANCE_SEARCH_SHOW,e),clearTimeout(this.showHalfScreenBottomTimer),e?this.showHalfScreenBottom=!0:this.advanceSearch.title?this.showHalfScreenBottomTimer=setTimeout(function(){t.showHalfScreenBottom=!1},300):this.showHalfScreenBottom=!1}},mounted:function(){r.Gc.$on(r.U3.UNIT_FILTER_CLICK,this.onTapUnitFilter),r.Gc.$on(r.U3.TAB_SWITCH,this.onTabSwitch)},destroyed:function(){r.Gc.$emit(r.U3.TOGGLE_ADVANCE_SEARCH_SHOW,!1),r.Gc.$off(r.U3.UNIT_FILTER_CLICK,this.onTapUnitFilter),r.Gc.$off(r.U3.TAB_SWITCH,this.onTabSwitch)},methods:{showHalfScreenFilter:function(){this.showHalfScreen=!0,this.updateStickyTop(),clearTimeout(f),this.showMask=!0,this.clickId=this.M_genClickId(),this.onFiltersExpose()},hideHalfScreenFilter:function(e){var t=this;this.showHalfScreen=!1,f=setTimeout(function(){t.showMask=!1},300),void 0!==e&&this.makeReport25032({actionType:a.At.CLICK_FOLD},{clickDetail:e})},onTapMask:function(){this.initData(),this.hideHalfScreenFilter(p.mask)},onTabSwitch:function(){this.hideHalfScreenFilter()},onFiltersExpose:function(){var e=this;this.filters.forEach(function(t){e.makeReport25032({actionType:a.At.EXPOSE_ITEM,docInfos:0===r.xB.type?[]:t._docInfos,itemInfos:t.options.map(function(e){return e.reportId}),itemPoses:t.options.map(function(e){return e._itemPos})})})},onTapOption:function(e,t){var n;this.makeReport25032({docInfos:0===r.xB.type?[]:this.getFilterByOption(t)._docInfos,itemInfos:[t.reportId],itemPoses:[t._itemPos],actionType:a.At.ALL_SEARCH},{docinfo:this.currentSelected.map(function(e){return"".concat(e.paramKey,":").concat(e.paramKey,"_").concat(e.paramValue)}).join(";")}),r.yG.reportJumpType({businessType:10000001,subType:2,pageType:a.Hq[null===(n=this.$route)||void 0===n?void 0:n.name],clickId:this.clickId,clickType:2,newSearchType:a.NK.FILTER}),5==this.advanceSearch.showType&&this.hideHalfScreenFilter()},onTapUnitFilter:r.Zr.throttle(function(){this.showHalfScreen?this.hideHalfScreenFilter(p.tab):(this.showHalfScreenFilter(),this.makeReport25032({actionType:a.At.CLICK_UNFOLD,businessType:1e7}))}),onTapFilterBar:function(){this.showHalfScreen?this.hideHalfScreenFilter(p.filter):(this.showHalfScreenFilter(),this.makeReport25032({actionType:a.At.CLICK_UNFOLD,resultSubType:3,businessType:1e7,docInfos:[this.advanceSearch.title]}))},onTapClear:function(){var e;this.makeReport25032({actionType:a.At.CLICK_CLEAR,resultSubType:3,businessType:1e7,docInfos:[this.advanceSearch.title]}),r.yG.reportJumpType({businessType:1e7,subType:3,pageType:a.Hq[null===(e=this.$route)||void 0===e?void 0:e.name],clickId:this.clickId,clickType:2,newSearchType:a.NK.FILTER}),this.$set(this.advanceSearch,"filters",this.filters.map(function(e){return u(l({},e),{options:e.options.map(function(e,t){return u(l({},e),{selected:0===t})})})})),this.checkRules(),this.$store.commit({type:"clearFilterExt"}),this.doSearchOperation(),this.hideHalfScreenFilter()},makeReport25032:function(e,t){(0,i.Z)(l(u(l({},r.xB.getBase()),{reqBusinessType:r.xB.type,resultSubType:2,businessType:10000001,extInfo:JSON.stringify(l({currentTab:r.xB.type,clickId:this.clickId},t))}),e))}}}},915794:function(e,t,n){var a=n(444839);a.Z.install=function(e){e.component(a.Z.name,a.Z)},t.Z=a.Z},705568:function(e,t,n){var a=n(84288);a.Z.install=function(e){e.component(a.Z.name,a.Z)},t.Z=a.Z},690286:function(e,t,n){var a=n(420629),r=n(984928),i=n(798509),o=n(151285),c=n(142862),s=n(411795);function l(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function u(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),a.forEach(function(t){l(e,t,n[t])})}return e}function p(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):(function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);n.push.apply(n,a)}return n})(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}var f={single:1,multi:2};t.Z={components:{heightAnimate:s.Z},mixins:[o.ZP],data:function(){return{advanceSearch:{},clickId:"",top:0,filterMode:f}},computed:p(u({},(0,a.rn)({storeData:function(e){return e.result.sift.advanceSearch}}),(0,a.Se)({getExtReqParams:"getExtReqParams"})),{filters:function(){return this.advanceSearch.filters||[]},currentSelected:function(){return this.filters.map(function(e){var t=e.options;return(void 0===t?[]:t).filter(function(e){return e.selected&&!e.disabled}).map(function(e){return{paramKey:e.paramKey,paramValue:e.paramValue}})}).flat()},isImageSearch:function(){return+i.xB.chatSearch===r.LL.IMAGE},rules:function(){if(this.advanceSearch.rules)try{return JSON.parse(this.advanceSearch.rules).map(function(e){var t=e.selected,n=void 0===t?[]:t;return n=n.reduce(function(e,t){var n=e.findIndex(function(e){return e.paramKey===t.paramKey});return n>=0?e[n].paramValue.push(t.paramValue):e.push({paramKey:t.paramKey,paramValue:[t.paramValue]}),e},[]),p(u({},e),{selected:n})})}catch(e){console.error("advanceSearch rules验证失败",this.advanceSearch.rules,e)}return[]}}),watch:{storeData:{handler:function(e){!i.Zr.isObjectEmpty(e)&&this.initData()},immediate:!0},getExtReqParams:function(e){this.filters&&this.filters.forEach(function(t){var n=e.find(function(e){return e.key==t.paramKey});if(n)try{var a=JSON.parse(n.textValue)||[];t.options.forEach(function(e){e.selected=a.includes(e.paramValue)})}catch(e){console.error("advance-search parse extReqParams err",e)}})}},mounted:function(){this.updateStickyTop(),i.Gc.$on(i.U3.BACK_BUTTON_CLICK,this.removeFilterExt)},destroyed:function(){i.Gc.$off(i.U3.BACK_BUTTON_CLICK,this.removeFilterExt)},methods:{updateStickyTop:function(){var e=document.querySelector(".unit__outer");if(e){var t=e.getBoundingClientRect();this.top="".concat(t.top+t.height,"px")}},initData:function(){var e=this;this.advanceSearch=JSON.parse(JSON.stringify(this.storeData)),this.checkRules(),this.advanceSearch=p(u({},this.advanceSearch),{filters:(this.advanceSearch.filters||[]).map(function(t,n){return p(u({},t),{_docInfos:["".concat(t.paramKey,":").concat(n+1)],options:(t.options||[]).map(function(t,n){return p(u({},t),{_itemPos:"".concat(n+1,":").concat(e.M_getItemType(t.reportId))})})})})})},onOptionChange:function(){this.checkRules(),this.$store.commit({type:"result/sift/updateAdvanceSearch",advanceSearch:JSON.parse(JSON.stringify(this.advanceSearch))}),this.doSearchOperation()},checkRules:function(){var e=this;if(this.rules.length){this.filters.forEach(function(t,n){e.$set(e.advanceSearch.filters[n],"options",t.options.map(function(e){return delete e.disabled,e}))});var t=function(t){for(var n=0;n<t.length;n++){var a,r=function(n){var a=t[n],r=a.paramKey,i=a.paramValue;if(!e.currentSelected.find(function(e){return e.paramKey===r&&e.paramValue===i}))return{v:!1}}(n);if("object"==((a=r)&&"undefined"!=typeof Symbol&&a.constructor===Symbol?"symbol":typeof a))return r.v}return!0};this.rules.forEach(function(n){var a=n.condition,r=n.disabled,i=n.selected;t(void 0===a?[]:a)&&((void 0===r?[]:r).forEach(function(t){var n=e.filters.findIndex(function(e){return e.paramKey===t.paramKey});if(n>=0){var a=e.filters[n].options.findIndex(function(e){return e.paramValue===t.paramValue});a>=0&&e.$set(e.advanceSearch.filters[n].options[a],"disabled",!0)}}),(void 0===i?[]:i).forEach(function(t){var n=e.filters.findIndex(function(e){return e.paramKey===t.paramKey});if(n>=0){var a=e.filters[n].options;e.$set(e.advanceSearch.filters[n],"options",a.map(function(e){return e.selected=t.paramValue.includes(e.paramValue),e}))}}))})}},getFilterByOption:function(e){return this.filters.find(function(t){return t.paramKey===e.paramKey})||{}},removeFilterExt:function(e){0!==e.type&&this.$store.commit({type:"clearFilterExt"})},doSearchOperation:function(){this.$store.commit({type:"clearFilterExt"}),this.showToast=!1;var e=this.filters.map(function(e){return{key:e.paramKey,textValue:e.options.filter(function(e){return e.selected&&!e.disabled}).map(function(e){return e.paramValue})}}).filter(function(e){return e.textValue.length});i.Gc.$emit(i.U3.setQuery,{page:r.kO.RESULT,searchId:"",extReqParams:{key:"parentSearchID",textValue:this.M_composeParentSid({t:r.X$.FILTER,s:i.xB.searchId,did:"filter",rid:this.$store.state.result.previousRid,kv:e.reduce(function(e,t){return p(u({},e),l({},t.key,{textValue:t.textValue.join("_")}))},{})})},filter:e.length?e.map(function(e){return{key:e.key,textValue:JSON.stringify(e.textValue)}}):void 0})},makeReport25032:function(e,t){(0,c.Z)(u(p(u({},i.xB.getBase()),{reqBusinessType:i.xB.type,resultSubType:1,businessType:1e7,extInfo:JSON.stringify(u({currentTab:i.xB.type},t))}),e))}}}}}]);