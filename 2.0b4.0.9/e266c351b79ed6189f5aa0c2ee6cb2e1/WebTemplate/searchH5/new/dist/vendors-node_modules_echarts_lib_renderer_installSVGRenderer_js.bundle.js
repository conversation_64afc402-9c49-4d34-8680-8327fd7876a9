"use strict";(self.webpackChunkweixin_search=self.webpackChunkweixin_search||[]).push([["vendors-node_modules_echarts_lib_renderer_installSVGRenderer_js"],{942622:function(t,e,n){n.d(e,{N:function(){return r}});var i=n(146962);function r(t){t.registerPainter("svg",i.Z)}},146962:function(t,e,n){var i=n(133479),r=n(754623),o=n(201974),a=n(807028),s=n(344773),u=n(638687),l=0,h=function(){function t(t,e,n){if(this.type="svg",this.refreshHover=f("refreshHover"),this.configLayer=f("configLayer"),this.storage=e,this._opts=n=(0,a.l7)({},n),this.root=t,this._id="zr"+l++,this._oldVNode=(0,r.Wu)(n.width,n.height),t&&!n.ssr){var i=this._viewport=document.createElement("div");i.style.cssText="position:relative;overflow:hidden";var o=this._svgDom=this._oldVNode.elm=(0,r.az)("svg");(0,s.G)(null,this._oldVNode),i.appendChild(o),t.appendChild(i)}this.resize(n.width,n.height)}return t.prototype.getType=function(){return this.type},t.prototype.getViewportRoot=function(){return this._viewport},t.prototype.getViewportRootOffset=function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},t.prototype.getSvgDom=function(){return this._svgDom},t.prototype.refresh=function(){if(this.root){var t=this.renderToVNode({willUpdate:!0});t.attrs.style="position:absolute;left:0;top:0;user-select:none",(0,s.Z)(this._oldVNode,t),this._oldVNode=t}},t.prototype.renderOneToVNode=function(t){return(0,i.Dm)(t,(0,r.So)(this._id))},t.prototype.renderToVNode=function(t){t=t||{};var e=this.storage.getDisplayList(!0),n=this._width,s=this._height,u=(0,r.So)(this._id);u.animation=t.animation,u.willUpdate=t.willUpdate,u.compress=t.compress,u.emphasis=t.emphasis;var l=[],h=this._bgVNode=function(t,e,n,s){var u;if(n&&"none"!==n){if(u=(0,r.Wm)("rect","bg",{width:t,height:e,x:"0",y:"0"}),(0,o.H3)(n))(0,i.Qo)({fill:n},u.attrs,"fill",s);else if((0,o.R)(n))(0,i.YU)({style:{fill:n},dirty:a.ZT,getBoundingRect:function(){return{width:t,height:e}}},u.attrs,"fill",s);else{var l=(0,o.ut)(n),h=l.color,f=l.opacity;u.attrs.fill=h,f<1&&(u.attrs["fill-opacity"]=f)}}return u}(n,s,this._backgroundColor,u);h&&l.push(h);var f=t.compress?null:this._mainVNode=(0,r.Wm)("g","main",{},[]);this._paintList(e,u,f?f.children:l),f&&l.push(f);var c=(0,a.UI)((0,a.XP)(u.defs),function(t){return u.defs[t]});if(c.length&&l.push((0,r.Wm)("defs","defs",{},c)),t.animation){var d=(0,r.wU)(u.cssNodes,u.cssAnims,{newline:!0});if(d){var p=(0,r.Wm)("style","stl",{},[],d);l.push(p)}}return(0,r.Wu)(n,s,l,t.useViewBox)},t.prototype.renderToString=function(t){return t=t||{},(0,r.HO)(this.renderToVNode({animation:(0,a.pD)(t.cssAnimation,!0),emphasis:(0,a.pD)(t.cssEmphasis,!0),willUpdate:!1,compress:!0,useViewBox:(0,a.pD)(t.useViewBox,!0)}),{newline:!0})},t.prototype.setBackgroundColor=function(t){this._backgroundColor=t},t.prototype.getSvgRoot=function(){return this._mainVNode&&this._mainVNode.elm},t.prototype._paintList=function(t,e,n){for(var o,a,s=t.length,u=[],l=0,h=0,f=0;f<s;f++){var c=t[f];if(!c.invisible){var d=c.__clipPaths,p=d&&d.length||0,v=a&&a.length||0,g=void 0;for(g=Math.max(p-1,v-1);g>=0&&(!d||!a||d[g]!==a[g]);g--);for(var m=v-1;m>g;m--)o=u[--l-1];for(var y=g+1;y<p;y++){var _={};(0,i.qk)(d[y],_,e);var w=(0,r.Wm)("g","clip-g-"+h++,_,[]);(o?o.children:n).push(w),u[l++]=w,o=w}a=d;var x=(0,i.Dm)(c,e);x&&(o?o.children:n).push(x)}}},t.prototype.resize=function(t,e){var n=this._opts,i=this.root,r=this._viewport;if(null!=t&&(n.width=t),null!=e&&(n.height=e),i&&r&&(r.style.display="none",t=(0,u.ap)(i,0,n),e=(0,u.ap)(i,1,n),r.style.display=""),this._width!==t||this._height!==e){if(this._width=t,this._height=e,r){var a=r.style;a.width=t+"px",a.height=e+"px"}if((0,o.R)(this._backgroundColor))this.refresh();else{var s=this._svgDom;s&&(s.setAttribute("width",t),s.setAttribute("height",e));var l=this._bgVNode&&this._bgVNode.elm;l&&(l.setAttribute("width",t),l.setAttribute("height",e))}}},t.prototype.getWidth=function(){return this._width},t.prototype.getHeight=function(){return this._height},t.prototype.dispose=function(){this.root&&(this.root.innerHTML=""),this._svgDom=this._viewport=this.storage=this._oldVNode=this._bgVNode=this._mainVNode=null},t.prototype.clear=function(){this._svgDom&&(this._svgDom.innerHTML=null),this._oldVNode=null},t.prototype.toDataURL=function(t){var e=this.renderToString(),n="data:image/svg+xml;";return t?(e=(0,o.oF)(e))&&n+"base64,"+e:n+"charset=UTF-8,"+encodeURIComponent(e)},t}();function f(t){return function(){}}e.Z=h},577472:function(t,e,n){var i=n(201974),r=Math.sin,o=Math.cos,a=Math.PI,s=2*Math.PI,u=180/a,l=function(){function t(){}return t.prototype.reset=function(t){this._start=!0,this._d=[],this._str="",this._p=Math.pow(10,t||4)},t.prototype.moveTo=function(t,e){this._add("M",t,e)},t.prototype.lineTo=function(t,e){this._add("L",t,e)},t.prototype.bezierCurveTo=function(t,e,n,i,r,o){this._add("C",t,e,n,i,r,o)},t.prototype.quadraticCurveTo=function(t,e,n,i){this._add("Q",t,e,n,i)},t.prototype.arc=function(t,e,n,i,r,o){this.ellipse(t,e,n,n,0,i,r,o)},t.prototype.ellipse=function(t,e,n,l,h,f,c,d){var p=c-f,v=!d,g=Math.abs(p),m=(0,i.zT)(g-s)||(v?p>=s:-p>=s),y=p>0?p%s:p%s+s,_=!1;_=!!m||!(0,i.zT)(g)&&y>=a==!!v;var w=t+n*o(f),x=e+l*r(f);this._start&&this._add("M",w,x);var k=Math.round(h*u);if(m){var b=1/this._p,I=(v?1:-1)*(s-b);this._add("A",n,l,k,1,+v,t+n*o(f+I),e+l*r(f+I)),b>.01&&this._add("A",n,l,k,0,+v,w,x)}else{var N=t+n*o(c),V=e+l*r(c);this._add("A",n,l,k,+_,+v,N,V)}},t.prototype.rect=function(t,e,n,i){this._add("M",t,e),this._add("l",n,0),this._add("l",0,i),this._add("l",-n,0),this._add("Z")},t.prototype.closePath=function(){this._d.length>0&&this._add("Z")},t.prototype._add=function(t,e,n,i,r,o,a,s,u){for(var l=[],h=this._p,f=1;f<arguments.length;f++){var c=arguments[f];if(isNaN(c)){this._invalid=!0;return}l.push(Math.round(c*h)/h)}this._d.push(t+l.join(" ")),this._start="Z"===t},t.prototype.generateStr=function(){this._str=this._invalid?"":this._d.join(""),this._d=[]},t.prototype.getStr=function(){return this._str},t}();e.Z=l},754623:function(t,e,n){n.d(e,{C5:function(){return u},HO:function(){return c},So:function(){return p},Wm:function(){return f},Wu:function(){return v},ar:function(){return a},az:function(){return h},qt:function(){return l},rv:function(){return s},wU:function(){return d}});var i=n(807028),r=n(734477),o="http://www.w3.org/2000/svg",a="http://www.w3.org/1999/xlink",s="http://www.w3.org/2000/xmlns/",u="http://www.w3.org/XML/1998/namespace",l="ecmeta_";function h(t){return document.createElementNS(o,t)}function f(t,e,n,i,r){return{tag:t,attrs:n||{},children:i,text:r,key:e}}function c(t,e){var n=(e=e||{}).newline?"\n":"";return function t(e){var o=e.children,a=e.tag,s=e.attrs,u=e.text;return function(t,e){var n=[];if(e)for(var i in e){var r=e[i],o=i;if(!1!==r){!0!==r&&null!=r&&(o+='="'+r+'"');n.push(o)}}return"<"+t+" "+n.join(" ")+">"}(a,s)+("style"!==a?(0,r.F1)(u):u||"")+(o?""+n+(0,i.UI)(o,function(e){return t(e)}).join(n)+n:"")+("</"+a)+">"}(t)}function d(t,e,n){var r=(n=n||{}).newline?"\n":"",o=" {"+r,a=r+"}",s=(0,i.UI)((0,i.XP)(t),function(e){return e+o+(0,i.UI)((0,i.XP)(t[e]),function(n){return n+":"+t[e][n]+";"}).join(r)+a}).join(r),u=(0,i.UI)((0,i.XP)(e),function(t){return"@keyframes "+t+o+(0,i.UI)((0,i.XP)(e[t]),function(n){return n+o+(0,i.UI)((0,i.XP)(e[t][n]),function(i){var r=e[t][n][i];return"d"===i&&(r='path("'+r+'")'),i+":"+r+";"}).join(r)+a}).join(r)+a}).join(r);return s||u?["<![CDATA[",s,u,"]]>"].join(r):""}function p(t){return{zrId:t,shadowCache:{},patternCache:{},gradientCache:{},clipPathCache:{},defs:{},cssNodes:{},cssAnims:{},cssStyleCache:{},cssAnimIdx:0,shadowIdx:0,gradientIdx:0,patternIdx:0,clipPathIdx:0}}function v(t,e,n,i){return f("svg","root",{width:t,height:e,xmlns:o,"xmlns:xlink":a,version:"1.1",baseProfile:"full",viewBox:!!i&&"0 0 "+t+" "+e},n)}},390447:function(t,e,n){n.d(e,{gH:function(){return m}});var i=n(762680),r=n(754623),o=n(577472),a=n(351415),s=n(201974),u=n(807028),l=n(702654),h=n(973298),f=n(226228),c={cubicIn:"0.32,0,0.67,0",cubicOut:"0.33,1,0.68,1",cubicInOut:"0.65,0,0.35,1",quadraticIn:"0.11,0,0.5,0",quadraticOut:"0.5,1,0.89,1",quadraticInOut:"0.45,0,0.55,1",quarticIn:"0.5,0,0.75,0",quarticOut:"0.25,1,0.5,1",quarticInOut:"0.76,0,0.24,1",quinticIn:"0.64,0,0.78,0",quinticOut:"0.22,1,0.36,1",quinticInOut:"0.83,0,0.17,1",sinusoidalIn:"0.12,0,0.39,0",sinusoidalOut:"0.61,1,0.88,1",sinusoidalInOut:"0.37,0,0.63,1",exponentialIn:"0.7,0,0.84,0",exponentialOut:"0.16,1,0.3,1",exponentialInOut:"0.87,0,0.13,1",circularIn:"0.55,0,1,0.45",circularOut:"0,0.55,0.45,1",circularInOut:"0.85,0,0.15,1"},d="transform-origin",p={fill:"fill",opacity:"opacity",lineWidth:"stroke-width",lineDashOffset:"stroke-dashoffset"};function v(t,e){var n=e.zrId+"-ani-"+e.cssAnimIdx++;return e.cssAnims[n]=t,n}function g(t){return(0,u.HD)(t)?c[t]?"cubic-bezier("+c[t]+")":(0,h.H)(t)?t:"":""}function m(t,e,n,h){var c=t.animators,y=c.length,_=[];if(t instanceof l.Z){var w=function(t,e,n){var i,o,a=t.shape.paths,s={};if((0,u.S6)(a,function(t){var e=(0,r.So)(n.zrId);e.animation=!0,m(t,{},e,!0);var a=e.cssAnims,l=e.cssNodes,h=(0,u.XP)(a),f=h.length;if(!!f){var c=a[o=h[f-1]];for(var d in c){var p=c[d];s[d]=s[d]||{d:""},s[d].d+=p.d||""}for(var v in l){var g=l[v].animation;g.indexOf(o)>=0&&(i=g)}}}),!!i){e.d=!1;var l=v(s,n);return i.replace(o,l)}}(t,e,n);if(w)_.push(w);else if(!y)return}else if(!y)return;for(var x={},k=0;k<y;k++){var b=c[k],I=[b.getMaxTime()/1e3+"s"],N=g(b.getClip().easing),V=b.getDelay();N?I.push(N):I.push("linear"),V&&I.push(V/1e3+"s"),b.getLoop()&&I.push("infinite");var P=I.join(" ");x[P]=x[P]||[P,[]],x[P][1].push(b)}for(var S in x){var w=function(r){var l,f,c=r[1],m=c.length,y={},_={},w={},x="animation-timing-function";function k(t,e,n){for(var i=t.getTracks(),r=t.getMaxTime(),o=0;o<i.length;o++){var a=i[o];if(a.needsAnimate()){var s=a.keyframes,l=a.propName;if(n&&(l=n(l)),l)for(var h=0;h<s.length;h++){var f=s[h],c=Math.round(f.time/r*100)+"%",d=g(f.easing),p=f.rawValue;((0,u.HD)(p)||(0,u.hj)(p))&&(e[c]=e[c]||{},e[c][l]=f.rawValue,d&&(e[c][x]=d))}}}}for(var b=0;b<m;b++){var I=c[b],N=I.targetName;N?"shape"===N&&k(I,_):h||k(I,y)}for(var V in y){var P={};(0,i.kY)(P,t),(0,u.l7)(P,y[V]);var S=(0,s.gA)(P),C=y[V][x];w[V]=S?{transform:S}:{},!function(t,e){var n=e.originX,i=e.originY;(n||i)&&(t[d]=n+"px "+i+"px")}(w[V],P),C&&(w[V][x]=C)}var D=!0;for(var V in _){w[V]=w[V]||{};var A=!l,C=_[V][x];A&&(l=new a.Z);var O=l.len();l.reset(),w[V].d=function(t,e,n){var i=(0,u.l7)({},t.shape);(0,u.l7)(i,e),t.buildPath(n,i);var r=new o.Z;return r.reset((0,s.Gk)(t)),n.rebuildPath(r,1),r.generateStr(),r.getStr()}(t,_[V],l);var T=l.len();if(!A&&O!==T){D=!1;break}C&&(w[V][x]=C)}if(!D)for(var V in w)delete w[V].d;if(!h)for(var b=0;b<m;b++){var I=c[b],N=I.targetName;"style"===N&&k(I,w,function(t){return p[t]})}for(var U=(0,u.XP)(w),W=!0,b=1;b<U.length;b++){var z=U[b-1],H=U[b];if(w[z][d]!==w[H][d]){W=!1;break}f=w[z][d]}if(W&&f){for(var V in w)w[V][d]&&delete w[V][d];e[d]=f}if((0,u.hX)(U,function(t){return(0,u.XP)(w[t]).length>0}).length)return v(w,n)+" "+r[0]+" both"}(x[S]);w&&_.push(w)}if(_.length){var C=n.zrId+"-cls-"+(0,f.y)();n.cssNodes["."+C]={animation:_.join(",")},e.class=C}}},226228:function(t,e,n){n.d(e,{y:function(){return r}});var i=0;function r(){return i++}},492712:function(t,e,n){n.d(e,{y:function(){return o}});var i=n(529134),r=n(226228);function o(t,e,n){if(!t.ignore){if(t.isSilent()){var r={"pointer-events":"none"};a(r,e,n,!0)}else{var o=t.states.emphasis&&t.states.emphasis.style?t.states.emphasis.style:{},s=o.fill;if(!s){var u=t.style&&t.style.fill,l=t.states.select&&t.states.select.style&&t.states.select.style.fill,h=t.currentStates.indexOf("select")>=0&&l||u;h&&(s=(0,i.fD)(h))}var f=o.lineWidth;f&&(f/=!o.strokeNoScale&&t.transform?t.transform[0]:1);var r={cursor:"pointer"};s&&(r.fill=s),o.stroke&&(r.stroke=o.stroke),f&&(r["stroke-width"]=f),a(r,e,n,!0)}}}function a(t,e,n,i){var o=JSON.stringify(t),a=n.cssStyleCache[o];!a&&(a=n.zrId+"-cls-"+(0,r.y)(),n.cssStyleCache[o]=a,n.cssNodes["."+a+(i?":hover":"")]=t),e.class=e.class?e.class+" "+a:a}},584379:function(t,e,n){function i(t){return document.createTextNode(t)}function r(t,e,n){t.insertBefore(e,n)}function o(t,e){t.removeChild(e)}function a(t,e){t.appendChild(e)}function s(t){return t.parentNode}function u(t){return t.nextSibling}function l(t,e){t.textContent=e}n.d(e,{AH:function(){return u},D5:function(){return l},Eg:function(){return i},Vt:function(){return r},hK:function(){return s},hr:function(){return o},jG:function(){return a}})},133479:function(t,e,n){n.d(e,{Dm:function(){return A},Qo:function(){return O},YU:function(){return T},qk:function(){return U}});var i,r,o=n(201974),a=n(894641),s=n(694923),u=n(310123),l=n(613410),h=n(577472),f=n(704990),c=n(754623),d=n(807028),p=n(175873),v=n(390447),g=n(345262),m=n(252919),y=n(492712),_=n(305407),w=Math.round;function x(t){return t&&(0,d.HD)(t.src)}function k(t){return t&&(0,d.mf)(t.toDataURL)}function b(t,e,n,i){(0,f.Z)(function(r,a){var s="fill"===r||"stroke"===r;s&&(0,o.H3)(a)?O(e,t,r,i):s&&(0,o.R)(a)?T(n,t,r,i):s&&"none"===a?t[r]="transparent":t[r]=a},e,n,!1),function(t,e,n){var i=t.style;if((0,o.i2)(i)){var r=(0,o.n1)(t),a=n.shadowCache,s=a[r];if(!s){var u=t.getGlobalScale(),l=u[0],h=u[1];if(!l||!h)return;var f=i.shadowOffsetX||0,d=i.shadowOffsetY||0,p=i.shadowBlur,v=(0,o.ut)(i.shadowColor),g=v.opacity,m=v.color;s=n.zrId+"-s"+n.shadowIdx++,n.defs[s]=(0,c.Wm)("filter",s,{id:s,x:"-100%",y:"-100%",width:"300%",height:"300%"},[(0,c.Wm)("feDropShadow","",{dx:f/l,dy:d/h,stdDeviation:p/2/l+" "+p/2/h,"flood-color":m,"flood-opacity":g})]),a[r]=s}e.filter=(0,o.m1)(s)}}(n,t,i)}function I(t,e){var n=(0,_.EJ)(e);n&&(n.each(function(e,n){null!=e&&(t[(c.qt+n).toLowerCase()]=e+"")}),e.isSilent()&&(t[c.qt+"silent"]="true"))}function N(t){return(0,o.zT)(t[0]-1)&&(0,o.zT)(t[1])&&(0,o.zT)(t[2])&&(0,o.zT)(t[3]-1)}function V(t,e,n){var i;if(e&&(i=e,!((0,o.zT)(i[4])&&(0,o.zT)(i[5])&&N(e)))){var r=n?10:1e4;t.transform=N(e)?"translate("+w(e[4]*r)/r+" "+w(e[5]*r)/r+")":(0,o.qV)(e)}}function P(t,e,n){for(var i=t.points,r=[],o=0;o<i.length;o++)r.push(w(i[o][0]*n)/n),r.push(w(i[o][1]*n)/n);e.points=r.join(" ")}function S(t){return!t.smooth}var C={circle:[(i=["cx","cy","r"],r=(0,d.UI)(i,function(t){return"string"==typeof t?[t,t]:t}),function(t,e,n){for(var i=0;i<r.length;i++){var o=r[i],a=t[o[0]];null!=a&&(e[o[1]]=w(a*n)/n)}})],polyline:[P,S],polygon:[P,S]};function D(t,e){var n=t.style,i=t.shape,r=C[t.type],a={},s=e.animation,u="path",l=t.style.strokePercent,f=e.compress&&(0,o.Gk)(t)||4;if(!r||e.willUpdate||r[1]&&!r[1](i)||s&&function(t){for(var e=t.animators,n=0;n<e.length;n++)if("shape"===e[n].targetName)return!0;return!1}(t)||l<1){var d=!t.path||t.shapeChanged();!t.path&&t.createPathProxy();var p=t.path;d&&(p.beginPath(),t.buildPath(p,t.shape),t.pathUpdated());var g=p.getVersion(),m=t.__svgPathBuilder;(t.__svgPathVersion!==g||!m||l!==t.__svgPathStrokePercent)&&(!m&&(m=t.__svgPathBuilder=new h.Z),m.reset(f),p.rebuildPath(m,l),m.generateStr(),t.__svgPathVersion=g,t.__svgPathStrokePercent=l),a.d=m.getStr()}else{u=t.type;var _=Math.pow(10,f);r[0](i,a,_)}return V(a,t.transform),b(a,n,t,e),I(a,t),e.animation&&(0,v.gH)(t,a,e),e.emphasis&&(0,y.y)(t,a,e),(0,c.Wm)(u,t.id+"",a)}function A(t,e){if(t instanceof a.ZP)return D(t,e);if(t instanceof s.ZP)return function(t,e){var n=t.style,i=n.image;if(i&&!(0,d.HD)(i)&&(x(i)?i=i.src:k(i)&&(i=i.toDataURL())),!!i){var r=n.x||0,o=n.y||0,a={href:i,width:n.width,height:n.height};return r&&(a.x=r),o&&(a.y=o),V(a,t.transform),b(a,n,t,e),I(a,t),e.animation&&(0,v.gH)(t,a,e),(0,c.Wm)("image",t.id+"",a)}}(t,e);if(t instanceof l.Z)return function(t,e){var n=t.style,i=n.text;if(null!=i&&(i+=""),!(!i||isNaN(n.x)||isNaN(n.y))){var r=n.font||m.Uo,a=n.x||0,s=(0,o.mU)(n.y||0,(0,u.Dp)(r),n.textBaseline),l={"dominant-baseline":"central","text-anchor":o.jY[n.textAlign]||n.textAlign};if((0,g.Y1)(n)){var h="",f=n.fontStyle,d=(0,g.VG)(n.fontSize);if(!parseFloat(d))return;var p=n.fontFamily||m.rk,y=n.fontWeight;h+="font-size:"+d+";font-family:"+p+";",f&&"normal"!==f&&(h+="font-style:"+f+";"),y&&"normal"!==y&&(h+="font-weight:"+y+";"),l.style=h}else l.style="font: "+r;return i.match(/\s/)&&(l["xml:space"]="preserve"),a&&(l.x=a),s&&(l.y=s),V(l,t.transform),b(l,n,t,e),I(l,t),e.animation&&(0,v.gH)(t,l,e),(0,c.Wm)("text",t.id+"",l,void 0,i)}}(t,e)}function O(t,e,n,i){var r,a=t[n],s={gradientUnits:a.global?"userSpaceOnUse":"objectBoundingBox"};if((0,o.I1)(a))r="linearGradient",s.x1=a.x,s.y1=a.y,s.x2=a.x2,s.y2=a.y2;else{if(!(0,o.gO)(a))return;r="radialGradient",s.cx=(0,d.pD)(a.x,.5),s.cy=(0,d.pD)(a.y,.5),s.r=(0,d.pD)(a.r,.5)}for(var u=a.colorStops,l=[],h=0,f=u.length;h<f;++h){var p=100*(0,o.Pn)(u[h].offset)+"%",v=u[h].color,g=(0,o.ut)(v),m=g.color,y=g.opacity,_={offset:p};_["stop-color"]=m,y<1&&(_["stop-opacity"]=y),l.push((0,c.Wm)("stop",h+"",_))}var w=(0,c.Wm)(r,"",s,l),x=(0,c.HO)(w),k=i.gradientCache,b=k[x];!b&&(b=i.zrId+"-g"+i.gradientIdx++,k[x]=b,s.id=b,i.defs[b]=(0,c.Wm)(r,b,s,l)),e[n]=(0,o.m1)(b)}function T(t,e,n,i){var r,a,s,u=t.style[n],l=t.getBoundingRect(),h={},f=u.repeat,v="no-repeat"===f,g="repeat-x"===f,m="repeat-y"===f;if((0,o.Cv)(u)){var y=u.imageWidth,_=u.imageHeight,w=void 0,b=u.image;if((0,d.HD)(b)?w=b:x(b)?w=b.src:k(b)&&(w=b.toDataURL()),"undefined"==typeof Image){var I="Image width/height must been given explictly in svg-ssr renderer.";(0,d.hu)(y,I),(0,d.hu)(_,I)}else if(null==y||null==_){var N=function(t,e){if(t){var n=t.elm,i=y||e.width,r=_||e.height;"pattern"===t.tag&&(g?(r=1,i/=l.width):m&&(i=1,r/=l.height)),t.attrs.width=i,t.attrs.height=r,n&&(n.setAttribute("width",i),n.setAttribute("height",r))}},V=(0,p.Gq)(w,null,t,function(t){v||N(S,t),N(r,t)});V&&V.width&&V.height&&(y=y||V.width,_=_||V.height)}r=(0,c.Wm)("image","img",{href:w,width:y,height:_}),h.width=y,h.height=_}else u.svgElement&&(r=(0,d.d9)(u.svgElement),h.width=u.svgWidth,h.height=u.svgHeight);if(!!r){v?a=s=1:g?(s=1,a=h.width/l.width):m?(a=1,s=h.height/l.height):h.patternUnits="userSpaceOnUse",null!=a&&!isNaN(a)&&(h.width=a),null!=s&&!isNaN(s)&&(h.height=s);var P=(0,o.gA)(u);P&&(h.patternTransform=P);var S=(0,c.Wm)("pattern","",h,[r]),C=(0,c.HO)(S),D=i.patternCache,A=D[C];!A&&(A=i.zrId+"-p"+i.patternIdx++,D[C]=A,h.id=A,S=i.defs[A]=(0,c.Wm)("pattern",A,h,[r])),e[n]=(0,o.m1)(A)}}function U(t,e,n){var i=n.clipPathCache,r=n.defs,a=i[t.id];if(!a){var s={id:a=n.zrId+"-c"+n.clipPathIdx++};i[t.id]=a,r[a]=(0,c.Wm)("clipPath",a,s,[D(t,n)])}e["clip-path"]=(0,o.m1)(a)}},704990:function(t,e,n){n.d(e,{Z:function(){return c}});var i=n(894641),r=n(694923),o=n(861996),a=n(807028),s=n(201974),u="none",l=Math.round,h=["lineCap","miterLimit","lineJoin"],f=(0,a.UI)(h,function(t){return"stroke-"+t.toLowerCase()});function c(t,e,n,a){var c,d,p=null==e.opacity?1:e.opacity;if(n instanceof r.ZP){t("opacity",p);return}if(null!=(c=e.fill)&&c!==u){var v=(0,s.ut)(e.fill);t("fill",v.color);var g=null!=e.fillOpacity?e.fillOpacity*v.opacity*p:v.opacity*p;(a||g<1)&&t("fill-opacity",g)}else t("fill",u);if(null!=(d=e.stroke)&&d!==u){var m=(0,s.ut)(e.stroke);t("stroke",m.color);var y=e.strokeNoScale?n.getLineScale():1,_=y?(e.lineWidth||0)/y:0,w=null!=e.strokeOpacity?e.strokeOpacity*m.opacity*p:m.opacity*p,x=e.strokeFirst;if((a||1!==_)&&t("stroke-width",_),(a||x)&&t("paint-order",x?"stroke":"fill"),(a||w<1)&&t("stroke-opacity",w),e.lineDash){var k=(0,o.a)(n),b=k[0],I=k[1];b&&(I=l(I||0),t("stroke-dasharray",b.join(",")),(I||a)&&t("stroke-dashoffset",I))}else a&&t("stroke-dasharray",u);for(var N=0;N<h.length;N++){var V=h[N];if(a||e[V]!==i.$t[V]){var P=e[V]||i.$t[V];P&&t(f[N],P)}}}else a&&t("stroke",u)}},344773:function(t,e,n){n.d(e,{G:function(){return d},Z:function(){return v}});var i=n(807028),r=n(754623),o=n(584379),a=(0,r.Wm)("","");function s(t){return void 0===t}function u(t){return void 0!==t}function l(t,e){var n=t.key===e.key;return t.tag===e.tag&&n}function h(t){var e,n=t.children,s=t.tag;if(u(s)){var l=t.elm=(0,r.az)(s);if(d(a,t),(0,i.kJ)(n))for(e=0;e<n.length;++e){var f=n[e];null!=f&&o.jG(l,h(f))}else u(t.text)&&!(0,i.Kn)(t.text)&&o.jG(l,o.Eg(t.text))}else t.elm=o.Eg(t.text);return t.elm}function f(t,e,n,i,r){for(;i<=r;++i){var a=n[i];null!=a&&o.Vt(t,h(a),e)}}function c(t,e,n,i){for(;n<=i;++n){var r=e[n];if(null!=r){if(u(r.tag)){var a=o.hK(r.elm);o.hr(a,r.elm)}else o.hr(t,r.elm)}}}function d(t,e){var n,i=e.elm,o=t&&t.attrs||{},a=e.attrs||{};if(o!==a){for(n in a){var s=a[n];o[n]!==s&&(!0===s?i.setAttribute(n,""):!1===s?i.removeAttribute(n):"style"===n?i.style.cssText=s:120!==n.charCodeAt(0)?i.setAttribute(n,s):"xmlns:xlink"===n||"xmlns"===n?i.setAttributeNS(r.rv,n,s):58===n.charCodeAt(3)?i.setAttributeNS(r.C5,n,s):58===n.charCodeAt(5)?i.setAttributeNS(r.ar,n,s):i.setAttribute(n,s))}for(n in o)!(n in a)&&i.removeAttribute(n)}}function p(t,e){var n=e.elm=t.elm,i=t.children,r=e.children;if(t!==e)d(t,e),s(e.text)?u(i)&&u(r)?i!==r&&!function(t,e,n){for(var i,r,a,u=0,d=0,v=e.length-1,g=e[0],m=e[v],y=n.length-1,_=n[0],w=n[y];u<=v&&d<=y;)null==g?g=e[++u]:null==m?m=e[--v]:null==_?_=n[++d]:null==w?w=n[--y]:l(g,_)?(p(g,_),g=e[++u],_=n[++d]):l(m,w)?(p(m,w),m=e[--v],w=n[--y]):l(g,w)?(p(g,w),o.Vt(t,g.elm,o.AH(m.elm)),g=e[++u],w=n[--y]):(l(m,_)?(p(m,_),o.Vt(t,m.elm,g.elm),m=e[--v]):(s(i)&&(i=function(t,e,n){for(var i={},r=e;r<=n;++r){var o=t[r].key;void 0!==o&&(i[o]=r)}return i}(e,u,v)),s(r=i[_.key])?o.Vt(t,h(_),g.elm):(a=e[r]).tag!==_.tag?o.Vt(t,h(_),g.elm):(p(a,_),e[r]=void 0,o.Vt(t,a.elm,g.elm))),_=n[++d]);(u<=v||d<=y)&&(u>v?f(t,null==n[y+1]?null:n[y+1].elm,n,d,y):c(t,e,u,v))}(n,i,r):u(r)?(u(t.text)&&o.D5(n,""),f(n,null,r,0,r.length-1)):u(i)?c(n,i,0,i.length-1):u(t.text)&&o.D5(n,""):t.text!==e.text&&(u(i)&&c(n,i,0,i.length-1),o.D5(n,e.text))}function v(t,e){if(l(t,e))p(t,e);else{var n=t.elm,i=o.hK(n);h(e),null!==i&&(o.Vt(i,e.elm,o.AH(n)),c(i,[t],0,0))}return e}}}]);