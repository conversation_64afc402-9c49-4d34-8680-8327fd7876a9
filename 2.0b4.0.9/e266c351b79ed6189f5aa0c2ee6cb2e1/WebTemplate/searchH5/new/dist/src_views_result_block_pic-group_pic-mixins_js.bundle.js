"use strict";(self.webpackChunkweixin_search=self.webpackChunkweixin_search||[]).push([["src_views_result_block_pic-group_pic-mixins_js"],{813880:function(t,e,r){var n=r(984928),i=r(136525),o=r(578248),u=r(798509);function c(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function a(t,e,r,n,i,o,u){try{var c=t[o](u),a=c.value}catch(t){r(t);return}c.done?e(a):Promise.resolve(a).then(n,i)}function s(t){return function(){var e=this,r=arguments;return new Promise(function(n,i){var o=t.apply(e,r);function u(t){a(o,n,i,u,c,"next",t)}function c(t){a(o,n,i,u,c,"throw",t)}u(void 0)})}}function l(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(t){return Object.getOwnPropertyDescriptor(r,t).enumerable}))),n.forEach(function(e){var n,i,o;n=t,i=e,o=r[e],i in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o})}return t}function p(t,e){return e=null!=e?e:{},Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):(function(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);r.push.apply(r,n)}return r})(Object(e)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(e,r))}),t}function f(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r,n,i=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=i){var o=[],u=!0,c=!1;try{for(i=i.call(t);!(u=(r=i.next()).done)&&(o.push(r.value),!e||o.length!==e);u=!0);}catch(t){c=!0,n=t}finally{try{!u&&null!=i.return&&i.return()}finally{if(c)throw n}}return o}}(t,e)||function(t,e){if(t){if("string"==typeof t)return c(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(r);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return c(t,e)}}(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function h(t,e){var r,n,i,o,u={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function c(o){return function(c){return function(o){if(r)throw TypeError("Generator is already executing.");for(;u;)try{if(r=1,n&&(i=2&o[0]?n.return:o[0]?n.throw||((i=n.return)&&i.call(n),0):n.next)&&!(i=i.call(n,o[1])).done)return i;switch(n=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return u.label++,{value:o[1],done:!1};case 5:u.label++,n=o[1],o=[0];continue;case 7:o=u.ops.pop(),u.trys.pop();continue;default:if(!(i=(i=u.trys).length>0&&i[i.length-1])&&(6===o[0]||2===o[0])){u=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){u.label=o[1];break}if(6===o[0]&&u.label<i[1]){u.label=i[1],i=o;break}if(i&&u.label<i[2]){u.label=i[2],u.ops.push(o);break}i[2]&&u.ops.pop(),u.trys.pop();continue}o=e.call(t,u)}catch(t){o=[6,t],n=0}finally{r=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,c])}}}var d=function(t){if(!t)return!1;var e=t||{},r=e.retCode,n=e.err_code,i=e.err_msg;return 0===r||0===n||/[:：]\s*(?:ok|success)/.test(i)};e.Z={methods:{slicePicItem:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.list,r=t.index,n=t.total,i=void 0===n?11:n,o=Math.floor(i/2),u=e.length;return r<o?e.slice(0,i):r>u-(o+1)?e.slice(u-i):e.slice(r-o,r+o+1)},mergeQuery:function(t){var e=new URLSearchParams(t.split("?")[1]||""),r={},n=!0,i=!1,o=void 0;try{for(var u,c=e.entries()[Symbol.iterator]();!(n=(u=c.next()).done);n=!0){var a=f(u.value,2),s=a[0],l=a[1];r[s]=l||""}}catch(t){i=!0,o=t}finally{try{!n&&null!=c.return&&c.return()}finally{if(i)throw o}}return r},getPicBoxJumpInfo:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.items,r=t.target||{},i=r.jumpUrl,o=r.extField||{},u=o.groupIndex,c=o.docId,a=Array.isArray(e)&&e.filter(function(t){return t.extField}).map(function(t){return t.extField});return{appId:n.tv.PIC_LITE_APPID,jumpType:n.sn.LITE_APP,page:"pages/detail",query:p(l({},this.mergeQuery(i)),{source:n.Zc.PIC_BOX,groupIndex:void 0===u?0:u,docId:void 0===c?"":c,list:a})}},getPicTabJumpInfo:function(t){var e=t.items,r=t.target||{},o=r.sgDocID,u=r.jumpUrl,c=r.extField.groupIndex,a=0,s=99999,f=e.filter(function(t){return t.type===i.jJ.picGroupList}).map(function(t,e){var r=t.items[0],n=r.sgDocID,i=void 0===n?"":n,u=r.title,c=r.height,p=r.width,f=r.picUrl,h=r.extField;return i===o&&(a=e),t.totalCount&&(s=t.totalCount),l({docId:i,title:u,height:c,width:p,picUrl:f},h||{})}),h=this.slicePicItem({list:f,index:a});return{jumpInfo:{appId:n.tv.PIC_LITE_APPID,jumpType:n.sn.LITE_APP,page:"pages/detail",query:p(l({},this.mergeQuery(u)),{source:n.Zc.PIC_TAB,list:h,groupIndex:c,docId:o})},list:f,firstTransList:h,groupIndex:c,totalNum:s}},checkLiteAppStoreAlive:function(){return s(function(){return h(this,function(t){return[2,new Promise(function(t){var e=window.setTimeout(s(function(){var r,i;return h(this,function(u){switch(u.label){case 0:return clearTimeout(e),[4,o.ZP.callLiteAppConnectEvent({method:"checkLiteAppStoreAlive",appId:n.tv.PIC_LITE_APPID})];case 1:if(console.log("===checkRes",r=u.sent()),d(r))return[2,t(!0)];return[4,o.ZP.callLiteAppConnectEvent({method:"ensureLiteAppStoreAlive",appId:n.tv.PIC_LITE_APPID})];case 2:return console.log("===ensureRes",i=u.sent()),[2,t(d(i))]}})}),100)})]})})()},subscribeLiteAppStore:function(){return s(function(){var t;return h(this,function(e){switch(e.label){case 0:return[4,o.ZP.callLiteAppConnectEvent({method:"subscribeLiteAppStore",config:n.Sn,option:{type:"other",appId:n.tv.PIC_LITE_APPID}})];case 1:return t=e.sent(),o.ZP.log({key:"liteapp-store",msg:"subscribe:".concat(JSON.stringify(t))}),console.log("===subscribeRes",t),[2,d(t)]}})})()},dispatchLiteAppAction:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return s(function(){var e;return h(this,function(r){switch(r.label){case 0:return e=t.liteInfo,[4,o.ZP.dispatchLiteAppAction({actionName:"onSyncDataList",data:{list:Object.entries(e).map(function(t){var e=f(t,2);return{key:e[0],value:e[1]}})},option:{type:"other",appId:n.tv.PIC_LITE_APPID}})];case 1:return[2,r.sent()]}})})()},goLiteAppPicDetail:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.items,r=t.target,i=t.source;return s(function(){var t,o,u,c,a,l;return h(this,function(p){var f;if(i===n.Zc.PIC_BOX)return this.M_go(this.getPicBoxJumpInfo({items:e,target:r})),[2,!1];return o=(t=this.getPicTabJumpInfo({items:e,target:r})).jumpInfo,u=t.list,c=t.groupIndex,a=t.totalNum,l=this,this.M_go(o,(f=s(function(t){return h(this,function(e){switch(e.label){case 0:if(u.length<=11)return[2,!1];if(!d(t))return l.M_startDetail(r),[2,!1];return[4,l.checkLiteAppStoreAlive()];case 1:if(!e.sent())return[2,!1];return[4,l.subscribeLiteAppStore()];case 2:if(!e.sent())return[2,!1];return[4,l.dispatchLiteAppAction({liteInfo:{picData:{list:u,currPicIndex:c},totalNum:a}})];case 3:return console.log("===dispatchRes",e.sent()),[2]}})}),function(t){return f.apply(this,arguments)})),[2]})}).apply(this)},getActionType:function(t){var e;return(null==t?void 0:null===(e=t.extField)||void 0===e?void 0:e.isNewData)===1&&u.xB.isSupportLiteappStoreJsapi?n.At.LITE_APP:n.At.H5},goPicDetailPage:function(t){var e,r=t.items,n=t.target,i=t.source;this.os.pc?this.M_startDetail(n):(null==n?void 0:null===(e=n.extField)||void 0===e?void 0:e.isNewData)===1&&u.xB.isSupportLiteappStoreJsapi?this.goLiteAppPicDetail({items:r,target:n,source:i}):this.M_startDetail(n)}}}}}]);