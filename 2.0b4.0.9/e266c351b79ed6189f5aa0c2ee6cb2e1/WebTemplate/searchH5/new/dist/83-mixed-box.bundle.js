"use strict";(self.webpackChunkweixin_search=self.webpackChunkweixin_search||[]).push([["83-mixed-box"],{983257:function(t,e,o){o.r(e);var n=o(540873),i=o(59435),r=o(551900),a=o(834032),s=(0,r.Z)(i.Z,n.s,n.x,!1,null,"b75bd684",null);"function"==typeof a.Z&&(0,a.Z)(s),e.default=s.exports},891808:function(t,e,o){o.d(e,{Z:function(){return n}});function n(t){t.options.__wxs_id="d6c1036f"}},834032:function(t,e,o){var n=o(891808);e.Z=n.Z},59435:function(t,e,o){var n=o(139328);e.Z=n.Z},540873:function(t,e,o){o.d(e,{s:function(){return n},x:function(){return i}});var n=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("div",{staticClass:"mixed-box",class:{"finder-search":t.isFinderSearch},style:t.boxStyle,attrs:{"data-mixed-box":t.typePos,"data-fcn-mixed-box":"","data-fr-147014fd8":""}},[t.d.boxExtraInfo&&t.d.boxExtraInfo.header?o("block-title",{attrs:{d:t.d.boxExtraInfo.header,"type-pos":t.typePos,"data-fc-1cdaea94e":""}}):t._e(),t.d.boxExtraInfo&&t.d.boxExtraInfo.tabs?o("tabs",{attrs:{d:t.d,"type-pos":t.typePos,tabs:t.d.boxExtraInfo&&t.d.boxExtraInfo.tabs,filters:t.d.boxExtraInfo&&t.d.boxExtraInfo.filters,"data-fc-1cdaea94c":""}}):t._e(),o("div",{staticClass:"mixed-box__bd"},t._l(t.subBoxes,function(e,n){return o("div",{key:e.boxID+n,staticClass:"mixed-box__item",style:t.subBoxStyle},[!t.isFinderInnerSearch||t.M_isPreComponentMounted(n,t.subBoxes)?o(t.componentMap[e.type],{tag:"component",staticClass:"mixed-box__item-render",attrs:{"cn-props":e,d:Object.assign({},e,{mixedPos:t.genMixedPos(n)}),"type-pos":t.typePos,"data-box":"","data-tracker-uniqueId":t.getTrackerUniqueId(e),"data-component-boxid":e.boxID,"data-fc-1cdaea94a":"","data-single":""},on:{"hook:mounted":function(o){return t.M_finishComponentMounted(n,t.subBoxes,t.componentMap[e.type])}}}):t._e()],1)}),0)],1)},i=[]},139328:function(t,e,o){var n,i=o(116746),r=o(136525),a=o(798509),s=o(368795),c=o(91543),u=o(868841),d=o(830875),f=o(632014),x=o(736490),p=o(794587),b=o(311363),l=o(392935),h=o(588671);function m(t,e,o){return e in t?Object.defineProperty(t,e,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[e]=o,t}var y=new i.J((m(n={},r.jJ.DOUBLE_CARD_CHANNEL_ACTIVITY,"RichMediaBox"),m(n,r.jJ.CHANNEL_ACTIVITY_CROSS_LINE,"RichMediaBox"),m(n,r.jJ.AUCTION_AD_ZONE,"VideoAdBox"),m(n,r.jJ.LIVE,"LiveBox"),m(n,r.jJ.NEW_RELATED_SEARCH,"RelatedBox"),m(n,r.jJ.NEW_LIFE,"NewLifeBox"),m(n,r.jJ.SHOPPING_PRODUCT_CARD,"ProductCardBox"),m(n,r.jJ.PEDIA,"PediaBox"),n));e.Z={components:{tabs:s.Z,RichMediaBox:c.Z,VideoAdBox:u.Z,LiveBox:d.Z,RelatedBox:b.Z,NewLifeBox:f.Z,ProductCardBox:x.P,PediaBox:p.Z,BlockTitle:h.default},mixins:[l.Z],props:{d:{type:Object,default:function(){}},typePos:{type:Number,default:0}},data:function(){return{componentMap:y}},computed:{subBoxes:function(){return this.d.subBoxes.filter(function(t){return!!t.items&&t.items.length>0})},isFinderSearch:function(){return a.xB.isFinderSearch||7==a.xB.type},isFinderInnerSearch:function(){return a.xB.isFinderSearch},column:function(){return a.xB.resultType===r.jJ.VERTICAL_VIDEO_SEARCH?3:2},isFlexLayoutSubBox:function(){return 33554464==a.xB.type},isSingleSubBox:function(){return 1==this.subBoxes.length},boxStyle:function(){return this.isFlexLayoutSubBox?{flex:"0 ".concat(100/(this.isSingleSubBox?this.column:1),"%")}:{}},subBoxStyle:function(){return{flex:"0 ".concat(100/(this.isFlexLayoutSubBox&&this.isSingleSubBox?1:this.column),"%")}}},watch:{d:{immediate:!0,handler:function(t){var e=(t.subBoxes||[]).reduce(function(t,e){return t||!!(e.items&&e.items[0]&&e.items[0].bottomRightX)},!1),o=function(t){var e;return t.bottomRightX-t.topLeftX>0&&t.bottomRightY-t.topLeftY>0?(t.bottomRightY-t.topLeftY)/(t.bottomRightX-t.topLeftX):(null===(e=t.imageData)||void 0===e?void 0:e.width)?t.imageData.height/t.imageData.width:0};if(e){for(var n=t.subBoxes.slice();;){var i=n.shift(),a=n.shift(),s=null==i?void 0:i.items[0],c=null==a?void 0:a.items[0];if(s&&(s.videoAspectRatio=o(s)),c&&(c.videoAspectRatio=o(c)),!s||!c)break;if(i.type==r.jJ.NEW_RELATED_SEARCH||a.type==r.jJ.NEW_RELATED_SEARCH){s.videoAspectRatio=4/3,c.videoAspectRatio=4/3;continue}var u=o(s),d=o(c);s.videoAspectRatio=Math.max(u,d),c.videoAspectRatio=Math.max(u,d)}}}}},methods:{getTrackerUniqueId:function(t){var e=t.subType,o=t.real_type,n=t.resultType;return"".concat(o,"-").concat(void 0===e?"":e,"-").concat(void 0===n?0:n)},genMixedPos:function(t){var e=Math.floor(t/2)+1;return"".concat(e,"-").concat(t%2+1)}}}},736490:function(t,e,o){o.d(e,{P:function(){return a}});var n,i=o(798509),r=o(275667);var a=(n=r.default,{props:{d:{type:Object,default:function(){return{}}},typePos:{type:Number,default:0}},mixins:[i.uW,i.jB],render:function(t){var e,o;return t(n,{on:this.$listeners,attrs:(e=function(t){for(var e=1;e<arguments.length;e++){var o=null!=arguments[e]?arguments[e]:{},n=Object.keys(o);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(o).filter(function(t){return Object.getOwnPropertyDescriptor(o,t).enumerable}))),n.forEach(function(e){var n,i,r;n=t,i=e,r=o[e],i in n?Object.defineProperty(n,i,{value:r,enumerable:!0,configurable:!0,writable:!0}):n[i]=r})}return t}({},this.$attrs),o=(o={"data-back-report-id":this.M_genDocReportId({data:this.d,typePos:this.typePos,item:this.d.items&&this.d.items[0],pos:1})},o),Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):(function(t,e){var o=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);o.push.apply(o,n)}return o})(Object(o)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}),e),props:{data:this.d,item:this.d.items&&this.d.items[0],pos:1,typePos:this.typePos,boxStyle:!0}})}})}}]);