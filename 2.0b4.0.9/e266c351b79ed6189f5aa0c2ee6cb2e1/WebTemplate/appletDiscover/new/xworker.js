(()=>{"use strict";var t={6729:t=>{var e=Object.prototype.hasOwnProperty,n="~";function r(){}function o(t,e,n){this.fn=t,this.context=e,this.once=n||!1}function i(t,e,r,i,a){if("function"!=typeof r)throw new TypeError("The listener must be a function");var c=new o(r,i||t,a),s=n?n+e:e;return t._events[s]?t._events[s].fn?t._events[s]=[t._events[s],c]:t._events[s].push(c):(t._events[s]=c,t._eventsCount++),t}function a(t,e){0==--t._eventsCount?t._events=new r:delete t._events[e]}function c(){this._events=new r,this._eventsCount=0}Object.create&&(r.prototype=Object.create(null),(new r).__proto__||(n=!1)),c.prototype.eventNames=function(){var t,r,o=[];if(0===this._eventsCount)return o;for(r in t=this._events)e.call(t,r)&&o.push(n?r.slice(1):r);return Object.getOwnPropertySymbols?o.concat(Object.getOwnPropertySymbols(t)):o},c.prototype.listeners=function(t){var e=n?n+t:t,r=this._events[e];if(!r)return[];if(r.fn)return[r.fn];for(var o=0,i=r.length,a=new Array(i);o<i;o++)a[o]=r[o].fn;return a},c.prototype.listenerCount=function(t){var e=n?n+t:t,r=this._events[e];return r?r.fn?1:r.length:0},c.prototype.emit=function(t,e,r,o,i,a){var c=n?n+t:t;if(!this._events[c])return!1;var s,l,u=this._events[c],f=arguments.length;if(u.fn){switch(u.once&&this.removeListener(t,u.fn,void 0,!0),f){case 1:return u.fn.call(u.context),!0;case 2:return u.fn.call(u.context,e),!0;case 3:return u.fn.call(u.context,e,r),!0;case 4:return u.fn.call(u.context,e,r,o),!0;case 5:return u.fn.call(u.context,e,r,o,i),!0;case 6:return u.fn.call(u.context,e,r,o,i,a),!0}for(l=1,s=new Array(f-1);l<f;l++)s[l-1]=arguments[l];u.fn.apply(u.context,s)}else{var p,h=u.length;for(l=0;l<h;l++)switch(u[l].once&&this.removeListener(t,u[l].fn,void 0,!0),f){case 1:u[l].fn.call(u[l].context);break;case 2:u[l].fn.call(u[l].context,e);break;case 3:u[l].fn.call(u[l].context,e,r);break;case 4:u[l].fn.call(u[l].context,e,r,o);break;default:if(!s)for(p=1,s=new Array(f-1);p<f;p++)s[p-1]=arguments[p];u[l].fn.apply(u[l].context,s)}}return!0},c.prototype.on=function(t,e,n){return i(this,t,e,n,!1)},c.prototype.once=function(t,e,n){return i(this,t,e,n,!0)},c.prototype.removeListener=function(t,e,r,o){var i=n?n+t:t;if(!this._events[i])return this;if(!e)return a(this,i),this;var c=this._events[i];if(c.fn)c.fn!==e||o&&!c.once||r&&c.context!==r||a(this,i);else{for(var s=0,l=[],u=c.length;s<u;s++)(c[s].fn!==e||o&&!c[s].once||r&&c[s].context!==r)&&l.push(c[s]);l.length?this._events[i]=1===l.length?l[0]:l:a(this,i)}return this},c.prototype.removeAllListeners=function(t){var e;return t?(e=n?n+t:t,this._events[e]&&a(this,e)):(this._events=new r,this._eventsCount=0),this},c.prototype.off=c.prototype.removeListener,c.prototype.addListener=c.prototype.on,c.prefixed=n,c.EventEmitter=c,t.exports=c}},e={};function n(r){var o=e[r];if(void 0!==o)return o.exports;var i=e[r]={exports:{}};return t[r](i,i.exports,n),i.exports}n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),(()=>{const t=n(6729);var e,r=(e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])},e(t,n)},function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}),o=function(){return o=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},o.apply(this,arguments)},i=n.g,a="[XWebCoreBridge]";const c=function(t){function e(){var e=t.call(this)||this;return e._cbs=[],e._id=1,e._callbacks={},i.handleApiCallback=function(t){console.log(a,"on callbackHandler",t);var n=t.__callback_id,r=t.__params;void 0!==e._cbs[n]&&(0,e._cbs[n].resolve)(r)},i.onXWorkerEventCall=function(t){var n,r,o=JSON.parse(t);switch(console.log(a,"on onXWorkerEventCall",Date.now(),o),o.__event_name){case"onClick":return void e.emit("onClick",{msg:JSON.stringify((null===(n=o.__params)||void 0===n?void 0:n.msg)||"{}")});case"onClientSyncCall":var i=JSON.parse((null===(r=null==o?void 0:o.__params)||void 0===r?void 0:r.msg)||"{}"),c=e._callbacks[i.type];return"function"==typeof c?JSON.stringify(c(i.data)):void console.warn("onClientSyncCall msg type ".concat(i.type," no callbacks!"));default:return}},e}return r(e,t),e.prototype.invoke=function(t,e){return n=this,r=void 0,c=function(){var n,r,a=this;return function(t,e){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function c(c){return function(s){return function(c){if(n)throw new TypeError("Generator is already executing.");for(;i&&(i=0,c[0]&&(a=0)),a;)try{if(n=1,r&&(o=2&c[0]?r.return:c[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,c[1])).done)return o;switch(r=0,o&&(c=[2&c[0],o.value]),c[0]){case 0:case 1:o=c;break;case 4:return a.label++,{value:c[1],done:!1};case 5:a.label++,r=c[1],c=[0];continue;case 7:c=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==c[0]&&2!==c[0])){a=0;continue}if(3===c[0]&&(!o||c[1]>o[0]&&c[1]<o[3])){a.label=c[1];break}if(6===c[0]&&a.label<o[1]){a.label=o[1],o=c;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(c);break}o[2]&&a.ops.pop(),a.trys.pop();continue}c=e.call(t,a)}catch(t){c=[6,t],r=0}finally{n=o=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}([c,s])}}}(this,(function(c){return n=String(this._id++),r=JSON.stringify(o(o({},e),{callback_id:n,url:"weixin://resourceid/AppletDiscover/app.html",api_tpye:"setresult",api:"SCENE_FETCHQUEUE",msg:JSON.stringify({__msg_type:"call",__callback_id:n,func:t,params:e})})),i.xwebCore.invoke(t,r,n),[2,new Promise((function(t,e){a._cbs[n]={resolve:t,reject:e}}))]}))},new((a=void 0)||(a=Promise))((function(t,e){function o(t){try{s(c.next(t))}catch(t){e(t)}}function i(t){try{s(c.throw(t))}catch(t){e(t)}}function s(e){var n;e.done?t(e.value):(n=e.value,n instanceof a?n:new a((function(t){t(n)}))).then(o,i)}s((c=c.apply(n,r||[])).next())}));var n,r,a,c},e.prototype.registerCallback=function(t,e){this._callbacks[t]=e},e}(t);var s=new(function(){function t(){var t=this;this.xwebBridge=new c,this._firstScreenData={},console.log("register PCAppBoardXWorker, buildTime=".concat(new Date(1730207237800).toString())),this.xwebBridge.on("onClick",(function(){t.cacheFirstScreenData()})),this.xwebBridge.registerCallback("fetchFirstScreenData",(function(e){return t._firstScreenData[e.key]}))}return t.prototype.cacheFirstScreenData=function(){var t=this;this.xwebBridge.invoke("PCMsgChannel",{MsgData:JSON.stringify({api_name:"getpcrecommcardlistnew",req_data:JSON.stringify({category_id_list:[1,2]})})}).then((function(e){t._firstScreenData.CategoryList=e})),this.xwebBridge.invoke("PCMsgChannel",{MsgData:JSON.stringify({api_name:"getwxausageapp",req_data:JSON.stringify({type:3,max_update_time:0,count:30})})}).then((function(e){t._firstScreenData.StarAndUsageAppList=e}))},t}());globalThis.core=s})()})();