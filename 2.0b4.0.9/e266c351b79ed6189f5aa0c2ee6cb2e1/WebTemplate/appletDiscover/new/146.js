/*! For license information please see 146.js.LICENSE.txt */
"use strict";(self.webpackChunkpc_discover=self.webpackChunkpc_discover||[]).push([[146],{6146:(e,t,n)=>{n.r(t),n.d(t,{default:()=>jt});let r=function(e,t){return new r.fn.init(e,t,o)};try{var i,o,a=window.document,s=window.location,u={},c=[],l=c.concat,f=c.push,d=c.slice,p=c.indexOf,h=u.toString,g=u.hasOwnProperty,m="1.9.1".trim,v=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,y=/\S+/g,b=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,x=/^(?:(<[\w\W]+>)[^>]*|#([\w-]*))$/,w=/^<(\w+)\s*\/?>(?:<\/\1>|)$/,_=/^[\],:{}\s]*$/,T=/(?:^|:|,)(?:\s*\[)+/g,k=/\\(?:["\\\/bfnrt]|u[\da-fA-F]{4})/g,E=/"[^"\\\r\n]*"|true|false|null|-?(?:\d+\.|)\d+(?:[eE][+-]?\d+|)/g,S=/^-ms-/,C=/-([\da-z])/gi,A=function(e,t){return t.toUpperCase()},N=function(e){(a.addEventListener||"load"===e.type||"complete"===a.readyState)&&(R(),r.ready())},R=function(){a.addEventListener?(a.removeEventListener("DOMContentLoaded",N,!1),window.removeEventListener("load",N,!1)):(a.detachEvent("onreadystatechange",N),window.detachEvent("onload",N))};function xr(e){var t=e.length,n=r.type(e);return!r.isWindow(e)&&(!(1!==e.nodeType||!t)||"array"===n||"function"!==n&&(0===t||"number"==typeof t&&t>0&&t-1 in e))}r.fn=r.prototype={jquery:"1.9.1",constructor:r,init:function(e,t,n){var i,o;if(!e)return this;if("string"==typeof e){if(!(i="<"===e.charAt(0)&&">"===e.charAt(e.length-1)&&e.length>=3?[null,e,null]:x.exec(e))||!i[1]&&t)return!t||t.jquery?(t||n).find(e):this.constructor(t).find(e);if(i[1]){if(t=t instanceof r?t[0]:t,r.merge(this,r.parseHTML(i[1],t&&t.nodeType?t.ownerDocument||t:a,!0)),w.test(i[1])&&r.isPlainObject(t))for(i in t)r.isFunction(this[i])?this[i](t[i]):this.attr(i,t[i]);return this}if((o=a.getElementById(i[2]))&&o.parentNode){if(o.id!==i[2])return n.find(e);this.length=1,this[0]=o}return this.context=a,this.selector=e,this}return e.nodeType?(this.context=this[0]=e,this.length=1,this):r.isFunction(e)?n.ready(e):(void 0!==e.selector&&(this.selector=e.selector,this.context=e.context),r.makeArray(e,this))},selector:"",length:0,size:function(){return this.length},toArray:function(){return d.call(this)},get:function(e){return null==e?this.toArray():e<0?this[this.length+e]:this[e]},pushStack:function(e){var t=r.merge(this.constructor(),e);return t.prevObject=this,t.context=this.context,t},each:function(e,t){return r.each(this,e,t)},ready:function(e){return r.ready.promise().done(e),this},slice:function(){return this.pushStack(d.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},eq:function(e){var t=this.length,n=+e+(e<0?t:0);return this.pushStack(n>=0&&n<t?[this[n]]:[])},map:function(e){return this.pushStack(r.map(this,(function(t,n){return e.call(t,n,t)})))},end:function(){return this.prevObject||this.constructor(null)},push:f,sort:[].sort,splice:[].splice},r.fn.init.prototype=r.fn,r.extend=r.fn.extend=function(){var e,t,n,i,o,a,s=arguments[0]||{},u=1,c=arguments.length,l=!1;for("boolean"==typeof s&&(l=s,s=arguments[1]||{},u=2),"object"==typeof s||r.isFunction(s)||(s={}),c===u&&(s=this,--u);u<c;u++)if(null!=(o=arguments[u]))for(i in o)e=s[i],s!==(n=o[i])&&(l&&n&&(r.isPlainObject(n)||(t=r.isArray(n)))?(t?(t=!1,a=e&&r.isArray(e)?e:[]):a=e&&r.isPlainObject(e)?e:{},s[i]=r.extend(l,a,n)):void 0!==n&&(s[i]=n));return s},r.extend({isReady:!1,readyWait:1,holdReady:function(e){e?r.readyWait++:r.ready(!0)},ready:function(e){if(!(!0===e?--r.readyWait:r.isReady)){if(!a.body)return setTimeout(r.ready);r.isReady=!0,!0!==e&&--r.readyWait>0||(i.resolveWith(a,[r]),r.fn.trigger&&r(a).trigger("ready").off("ready"))}},isFunction:function(e){return"function"===r.type(e)},isArray:Array.isArray||function(e){return"array"===r.type(e)},isWindow:function(e){return null!=e&&e==e.window},isNumeric:function(e){return!isNaN(parseFloat(e))&&isFinite(e)},type:function(e){return null==e?String(e):"object"==typeof e||"function"==typeof e?u[h.call(e)]||"object":typeof e},isPlainObject:function(e){if(!e||"object"!==r.type(e)||e.nodeType||r.isWindow(e))return!1;try{if(e.constructor&&!g.call(e,"constructor")&&!g.call(e.constructor.prototype,"isPrototypeOf"))return!1}catch(e){return!1}var t;for(t in e);return void 0===t||g.call(e,t)},isEmptyObject:function(e){var t;for(t in e)return!1;return!0},error:function(e){throw new Error(e)},parseHTML:function(e,t,n){if(!e||"string"!=typeof e)return null;"boolean"==typeof t&&(n=t,t=!1),t=t||a;var i=w.exec(e),o=!n&&[];return i?[t.createElement(i[1])]:(i=r.buildFragment([e],t,o),o&&r(o).remove(),r.merge([],i.childNodes))},parseJSON:function(e){return window.JSON&&window.JSON.parse?window.JSON.parse(e):null===e?e:"string"==typeof e&&(e=r.trim(e))&&_.test(e.replace(k,"@").replace(E,"]").replace(T,""))?new Function("return "+e)():void r.error("Invalid JSON: "+e)},parseXML:function(e){var t;if(!e||"string"!=typeof e)return null;try{window.DOMParser?t=(new DOMParser).parseFromString(e,"text/xml"):((t=new ActiveXObject("Microsoft.XMLDOM")).async="false",t.loadXML(e))}catch(e){t=void 0}return t&&t.documentElement&&!t.getElementsByTagName("parsererror").length||r.error("Invalid XML: "+e),t},noop:function(){},globalEval:function(e){e&&r.trim(e)&&(window.execScript||function(e){window.eval.call(window,e)})(e)},camelCase:function(e){return e.replace(S,"ms-").replace(C,A)},nodeName:function(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()},each:function(e,t,n){var r=0,i=e.length,o=xr(e);if(n){if(o)for(;r<i&&!1!==t.apply(e[r],n);r++);else for(r in e)if(!1===t.apply(e[r],n))break}else if(o)for(;r<i&&!1!==t.call(e[r],r,e[r]);r++);else for(r in e)if(!1===t.call(e[r],r,e[r]))break;return e},trim:m&&!m.call("\ufeff ")?function(e){return null==e?"":m.call(e)}:function(e){return null==e?"":(e+"").replace(b,"")},makeArray:function(e,t){var n=t||[];return null!=e&&(xr(Object(e))?r.merge(n,"string"==typeof e?[e]:e):f.call(n,e)),n},inArray:function(e,t,n){var r;if(t){if(p)return p.call(t,e,n);for(r=t.length,n=n?n<0?Math.max(0,r+n):n:0;n<r;n++)if(n in t&&t[n]===e)return n}return-1},merge:function(e,t){var n=t.length,r=e.length,i=0;if("number"==typeof n)for(;i<n;i++)e[r++]=t[i];else for(;void 0!==t[i];)e[r++]=t[i++];return e.length=r,e},grep:function(e,t,n){var r=[],i=0,o=e.length;for(n=!!n;i<o;i++)n!==!!t(e[i],i)&&r.push(e[i]);return r},map:function(e,t,n){var r,i=0,o=e.length,a=[];if(xr(e))for(;i<o;i++)null!=(r=t(e[i],i,n))&&(a[a.length]=r);else for(i in e)null!=(r=t(e[i],i,n))&&(a[a.length]=r);return l.apply([],a)},guid:1,proxy:function(e,t){var n,i,o;if("string"==typeof t&&(o=e[t],t=e,e=o),r.isFunction(e))return n=d.call(arguments,2),i=function(){return e.apply(t||this,n.concat(d.call(arguments)))},i.guid=e.guid=e.guid||r.guid++,i},access:function(e,t,n,i,o,a,s){var u=0,c=e.length,l=null==n;if("object"===r.type(n))for(u in o=!0,n)r.access(e,t,u,n[u],!0,a,s);else if(void 0!==i&&(o=!0,r.isFunction(i)||(s=!0),l&&(s?(t.call(e,i),t=null):(l=t,t=function(e,t,n){return l.call(r(e),n)})),t))for(;u<c;u++)t(e[u],n,s?i:i.call(e[u],u,t(e[u],n)));return o?e:l?t.call(e):c?t(e[0],n):a},now:function(){return(new Date).getTime()}}),r.ready.promise=function(e){if(!i)if(i=r.Deferred(),"complete"===a.readyState)setTimeout(r.ready);else if(a.addEventListener)a.addEventListener("DOMContentLoaded",N,!1),window.addEventListener("load",N,!1);else{a.attachEvent("onreadystatechange",N),window.attachEvent("onload",N);var t=!1;try{t=null==window.frameElement&&a.documentElement}catch(e){}t&&t.doScroll&&function e(){if(!r.isReady){try{t.doScroll("left")}catch(t){return setTimeout(e,50)}R(),r.ready()}}()}return i.promise(e)},r.each("Boolean Number String Function Array Date RegExp Object Error".split(" "),(function(e,t){u["[object "+t+"]"]=t.toLowerCase()})),o=r(a);var D={};r.Callbacks=function(e){e="string"==typeof e?D[e]||function(e){var t=D[e]={};return r.each(e.match(y)||[],(function(e,n){t[n]=!0})),t}(e):r.extend({},e);var t,n,i,o,a,s,u=[],c=!e.once&&[],l=function(r){for(n=e.memory&&r,i=!0,a=s||0,s=0,o=u.length,t=!0;u&&a<o;a++)if(!1===u[a].apply(r[0],r[1])&&e.stopOnFalse){n=!1;break}t=!1,u&&(c?c.length&&l(c.shift()):n?u=[]:f.disable())},f={add:function(){if(u){var i=u.length;!function t(n){r.each(n,(function(n,i){var o=r.type(i);"function"===o?e.unique&&f.has(i)||u.push(i):i&&i.length&&"string"!==o&&t(i)}))}(arguments),t?o=u.length:n&&(s=i,l(n))}return this},remove:function(){return u&&r.each(arguments,(function(e,n){for(var i;(i=r.inArray(n,u,i))>-1;)u.splice(i,1),t&&(i<=o&&o--,i<=a&&a--)})),this},has:function(e){return e?r.inArray(e,u)>-1:!(!u||!u.length)},empty:function(){return u=[],this},disable:function(){return u=c=n=void 0,this},disabled:function(){return!u},lock:function(){return c=void 0,n||f.disable(),this},locked:function(){return!c},fireWith:function(e,n){return n=[e,(n=n||[]).slice?n.slice():n],!u||i&&!c||(t?c.push(n):l(n)),this},fire:function(){return f.fireWith(this,arguments),this},fired:function(){return!!i}};return f},r.extend({Deferred:function(e){var t=[["resolve","done",r.Callbacks("once memory"),"resolved"],["reject","fail",r.Callbacks("once memory"),"rejected"],["notify","progress",r.Callbacks("memory")]],n="pending",i={state:function(){return n},always:function(){return o.done(arguments).fail(arguments),this},then:function(){var e=arguments;return r.Deferred((function(n){r.each(t,(function(t,a){var s=a[0],u=r.isFunction(e[t])&&e[t];o[a[1]]((function(){var e=u&&u.apply(this,arguments);e&&r.isFunction(e.promise)?e.promise().done(n.resolve).fail(n.reject).progress(n.notify):n[s+"With"](this===i?n.promise():this,u?[e]:arguments)}))})),e=null})).promise()},promise:function(e){return null!=e?r.extend(e,i):i}},o={};return i.pipe=i.then,r.each(t,(function(e,r){var a=r[2],s=r[3];i[r[1]]=a.add,s&&a.add((function(){n=s}),t[1^e][2].disable,t[2][2].lock),o[r[0]]=function(){return o[r[0]+"With"](this===o?i:this,arguments),this},o[r[0]+"With"]=a.fireWith})),i.promise(o),e&&e.call(o,o),o},when:function(e){var t,n,i,o=0,a=d.call(arguments),s=a.length,u=1!==s||e&&r.isFunction(e.promise)?s:0,c=1===u?e:r.Deferred(),l=function(e,n,r){return function(i){n[e]=this,r[e]=arguments.length>1?d.call(arguments):i,r===t?c.notifyWith(n,r):--u||c.resolveWith(n,r)}};if(s>1)for(t=new Array(s),n=new Array(s),i=new Array(s);o<s;o++)a[o]&&r.isFunction(a[o].promise)?a[o].promise().done(l(o,i,a)).fail(c.reject).progress(l(o,n,t)):--u;return u||c.resolveWith(i,a),c.promise()}}),r.support=function(){var e,t,n,i,o,s,u,c,l,f,d=a.createElement("div");if(d.setAttribute("className","t"),d.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>",t=d.getElementsByTagName("*"),n=d.getElementsByTagName("a")[0],!t||!n||!t.length)return{};u=(o=a.createElement("select")).appendChild(a.createElement("option")),i=d.getElementsByTagName("input")[0],n.style.cssText="top:1px;float:left;opacity:.5",e={getSetAttribute:"t"!==d.className,leadingWhitespace:3===d.firstChild.nodeType,tbody:!d.getElementsByTagName("tbody").length,htmlSerialize:!!d.getElementsByTagName("link").length,style:/top/.test(n.getAttribute("style")),hrefNormalized:"/a"===n.getAttribute("href"),opacity:/^0.5/.test(n.style.opacity),cssFloat:!!n.style.cssFloat,checkOn:!!i.value,optSelected:u.selected,enctype:!!a.createElement("form").enctype,html5Clone:"<:nav></:nav>"!==a.createElement("nav").cloneNode(!0).outerHTML,boxModel:"CSS1Compat"===a.compatMode,deleteExpando:!0,noCloneEvent:!0,inlineBlockNeedsLayout:!1,shrinkWrapBlocks:!1,reliableMarginRight:!0,boxSizingReliable:!0,pixelPosition:!1},i.checked=!0,e.noCloneChecked=i.cloneNode(!0).checked,o.disabled=!0,e.optDisabled=!u.disabled;try{delete d.test}catch(t){e.deleteExpando=!1}for(f in(i=a.createElement("input")).setAttribute("value",""),e.input=""===i.getAttribute("value"),i.value="t",i.setAttribute("type","radio"),e.radioValue="t"===i.value,i.setAttribute("checked","t"),i.setAttribute("name","t"),(s=a.createDocumentFragment()).appendChild(i),e.appendChecked=i.checked,e.checkClone=s.cloneNode(!0).cloneNode(!0).lastChild.checked,d.attachEvent&&(d.attachEvent("onclick",(function(){e.noCloneEvent=!1})),d.cloneNode(!0).click()),{submit:!0,change:!0,focusin:!0})d.setAttribute(c="on"+f,"t"),e[f+"Bubbles"]=c in window||!1===d.attributes[c].expando;return d.style.backgroundClip="content-box",d.cloneNode(!0).style.backgroundClip="",e.clearCloneStyle="content-box"===d.style.backgroundClip,r((function(){var t,n,r,i="padding:0;margin:0;border:0;display:block;box-sizing:content-box;-moz-box-sizing:content-box;-webkit-box-sizing:content-box;",o=a.getElementsByTagName("body")[0];o&&((t=a.createElement("div")).style.cssText="border:0;width:0;height:0;position:absolute;top:0;left:-9999px;margin-top:1px",o.appendChild(t).appendChild(d),d.innerHTML="<table><tr><td></td><td>t</td></tr></table>",(r=d.getElementsByTagName("td"))[0].style.cssText="padding:0;margin:0;border:0;display:none",l=0===r[0].offsetHeight,r[0].style.display="",r[1].style.display="none",e.reliableHiddenOffsets=l&&0===r[0].offsetHeight,d.innerHTML="",d.style.cssText="box-sizing:border-box;-moz-box-sizing:border-box;-webkit-box-sizing:border-box;padding:1px;border:1px;display:block;width:4px;margin-top:1%;position:absolute;top:1%;",e.boxSizing=4===d.offsetWidth,e.doesNotIncludeMarginInBodyOffset=1!==o.offsetTop,window.getComputedStyle&&(e.pixelPosition="1%"!==(window.getComputedStyle(d,null)||{}).top,e.boxSizingReliable="4px"===(window.getComputedStyle(d,null)||{width:"4px"}).width,(n=d.appendChild(a.createElement("div"))).style.cssText=d.style.cssText=i,n.style.marginRight=n.style.width="0",d.style.width="1px",e.reliableMarginRight=!parseFloat((window.getComputedStyle(n,null)||{}).marginRight)),void 0!==d.style.zoom&&(d.innerHTML="",d.style.cssText=i+"width:1px;padding:1px;display:inline;zoom:1",e.inlineBlockNeedsLayout=3===d.offsetWidth,d.style.display="block",d.innerHTML="<div></div>",d.firstChild.style.width="5px",e.shrinkWrapBlocks=3!==d.offsetWidth,e.inlineBlockNeedsLayout&&(o.style.zoom=1)),o.removeChild(t),t=d=r=n=null)})),t=o=s=u=n=i=null,e}();var F=/(?:\{[\s\S]*\}|\[[\s\S]*\])$/,L=/([A-Z])/g;function wr(e,t,n,i){if(r.acceptData(e)){var o,a,s=r.expando,u="string"==typeof t,l=e.nodeType,f=l?r.cache:e,d=l?e[s]:e[s]&&s;if(d&&f[d]&&(i||f[d].data)||!u||void 0!==n)return d||(l?e[s]=d=c.pop()||r.guid++:d=s),f[d]||(f[d]={},l||(f[d].toJSON=r.noop)),"object"!=typeof t&&"function"!=typeof t||(i?f[d]=r.extend(f[d],t):f[d].data=r.extend(f[d].data,t)),o=f[d],i||(o.data||(o.data={}),o=o.data),void 0!==n&&(o[r.camelCase(t)]=n),u?null==(a=o[t])&&(a=o[r.camelCase(t)]):a=o,a}}function _r(e,t,n){if(r.acceptData(e)){var i,o,a,s=e.nodeType,u=s?r.cache:e,c=s?e[r.expando]:r.expando;if(u[c]){if(t&&(a=n?u[c]:u[c].data)){for((i=0,o=(t=r.isArray(t)?t.concat(r.map(t,r.camelCase)):t in a||(t=r.camelCase(t))in a?[t]:t.split(" ")).length);i<o;i++)delete a[t[i]];if(!(n?kr:r.isEmptyObject)(a))return}(n||(delete u[c].data,kr(u[c])))&&(s?r.cleanData([e],!0):r.support.deleteExpando||u!=u.window?delete u[c]:u[c]=null)}}}function Tr(e,t,n){if(void 0===n&&1===e.nodeType){var i="data-"+t.replace(L,"-$1").toLowerCase();if("string"==typeof(n=e.getAttribute(i))){try{n="true"===n||"false"!==n&&("null"===n?null:+n+""===n?+n:F.test(n)?r.parseJSON(n):n)}catch(e){}r.data(e,t,n)}else n=void 0}return n}function kr(e){var t;for(t in e)if(("data"!==t||!r.isEmptyObject(e[t]))&&"toJSON"!==t)return!1;return!0}r.extend({cache:{},expando:"jQuery"+("1.9.1"+Math.random()).replace(/\D/g,""),noData:{embed:!0,object:"clsid:D27CDB6E-AE6D-11cf-96B8-************",applet:!0},hasData:function(e){return!!(e=e.nodeType?r.cache[e[r.expando]]:e[r.expando])&&!kr(e)},data:function(e,t,n){return wr(e,t,n)},removeData:function(e,t){return _r(e,t)},_data:function(e,t,n){return wr(e,t,n,!0)},_removeData:function(e,t){return _r(e,t,!0)},acceptData:function(e){if(e.nodeType&&1!==e.nodeType&&9!==e.nodeType)return!1;var t=e.nodeName&&r.noData[e.nodeName.toLowerCase()];return!t||!0!==t&&e.getAttribute("classid")===t}}),r.fn.extend({data:function(e,t){var n,i,o=this[0],a=0,s=null;if(void 0===e){if(this.length&&(s=r.data(o),1===o.nodeType&&!r._data(o,"parsedAttrs"))){for(n=o.attributes;a<n.length;a++)(i=n[a].name).indexOf("data-")||(i=r.camelCase(i.slice(5)),Tr(o,i,s[i]));r._data(o,"parsedAttrs",!0)}return s}return"object"==typeof e?this.each((function(){r.data(this,e)})):r.access(this,(function(t){if(void 0===t)return o?Tr(o,e,r.data(o,e)):null;this.each((function(){r.data(this,e,t)}))}),null,t,arguments.length>1,null,!0)},removeData:function(e){return this.each((function(){r.removeData(this,e)}))}}),r.extend({queue:function(e,t,n){var i;if(e)return t=(t||"fx")+"queue",i=r._data(e,t),n&&(!i||r.isArray(n)?i=r._data(e,t,r.makeArray(n)):i.push(n)),i||[]},dequeue:function(e,t){t=t||"fx";var n=r.queue(e,t),i=n.length,o=n.shift(),a=r._queueHooks(e,t);"inprogress"===o&&(o=n.shift(),i--),a.cur=o,o&&("fx"===t&&n.unshift("inprogress"),delete a.stop,o.call(e,(function(){r.dequeue(e,t)}),a)),!i&&a&&a.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return r._data(e,n)||r._data(e,n,{empty:r.Callbacks("once memory").add((function(){r._removeData(e,t+"queue"),r._removeData(e,n)}))})}}),r.fn.extend({queue:function(e,t){var n=2;return"string"!=typeof e&&(t=e,e="fx",n--),arguments.length<n?r.queue(this[0],e):void 0===t?this:this.each((function(){var n=r.queue(this,e,t);r._queueHooks(this,e),"fx"===e&&"inprogress"!==n[0]&&r.dequeue(this,e)}))},dequeue:function(e){return this.each((function(){r.dequeue(this,e)}))},delay:function(e,t){return e=r.fx&&r.fx.speeds[e]||e,t=t||"fx",this.queue(t,(function(t,n){var r=setTimeout(t,e);n.stop=function(){clearTimeout(r)}}))},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var n,i=1,o=r.Deferred(),a=this,s=this.length,u=function(){--i||o.resolveWith(a,[a])};for("string"!=typeof e&&(t=e,e=void 0),e=e||"fx";s--;)(n=r._data(a[s],e+"queueHooks"))&&n.empty&&(i++,n.empty.add(u));return u(),o.promise(t)}});var O,H,j=/[\t\r\n]/g,q=/\r/g,I=/^(?:input|select|textarea|button|object)$/i,M=/^(?:a|area)$/i,B=/^(?:checked|selected|autofocus|autoplay|async|controls|defer|disabled|hidden|loop|multiple|open|readonly|required|scoped)$/i,P=/^(?:checked|selected)$/i,U=r.support.getSetAttribute,z=r.support.input;r.fn.extend({attr:function(e,t){return r.access(this,r.attr,e,t,arguments.length>1)},removeAttr:function(e){return this.each((function(){r.removeAttr(this,e)}))},prop:function(e,t){return r.access(this,r.prop,e,t,arguments.length>1)},removeProp:function(e){return e=r.propFix[e]||e,this.each((function(){try{this[e]=void 0,delete this[e]}catch(e){}}))},addClass:function(e){var t,n,i,o,a,s=0,u=this.length,c="string"==typeof e&&e;if(r.isFunction(e))return this.each((function(t){r(this).addClass(e.call(this,t,this.className))}));if(c)for(t=(e||"").match(y)||[];s<u;s++)if(i=1===(n=this[s]).nodeType&&(n.className?(" "+n.className+" ").replace(j," "):" ")){for(a=0;o=t[a++];)i.indexOf(" "+o+" ")<0&&(i+=o+" ");n.className=r.trim(i)}return this},removeClass:function(e){var t,n,i,o,a,s=0,u=this.length,c=0===arguments.length||"string"==typeof e&&e;if(r.isFunction(e))return this.each((function(t){r(this).removeClass(e.call(this,t,this.className))}));if(c)for(t=(e||"").match(y)||[];s<u;s++)if(i=1===(n=this[s]).nodeType&&(n.className?(" "+n.className+" ").replace(j," "):"")){for(a=0;o=t[a++];)for(;i.indexOf(" "+o+" ")>=0;)i=i.replace(" "+o+" "," ");n.className=e?r.trim(i):""}return this},toggleClass:function(e,t){var n=typeof e,i="boolean"==typeof t;return r.isFunction(e)?this.each((function(n){r(this).toggleClass(e.call(this,n,this.className,t),t)})):this.each((function(){if("string"===n)for(var o,a=0,s=r(this),u=t,c=e.match(y)||[];o=c[a++];)u=i?u:!s.hasClass(o),s[u?"addClass":"removeClass"](o);else"undefined"!==n&&"boolean"!==n||(this.className&&r._data(this,"__className__",this.className),this.className=this.className||!1===e?"":r._data(this,"__className__")||"")}))},hasClass:function(e){for(var t=" "+e+" ",n=0,r=this.length;n<r;n++)if(1===this[n].nodeType&&(" "+this[n].className+" ").replace(j," ").indexOf(t)>=0)return!0;return!1},val:function(e){var t,n,i,o=this[0];return arguments.length?(i=r.isFunction(e),this.each((function(t){var o,a=r(this);1===this.nodeType&&(null==(o=i?e.call(this,t,a.val()):e)?o="":"number"==typeof o?o+="":r.isArray(o)&&(o=r.map(o,(function(e){return null==e?"":e+""}))),(n=r.valHooks[this.type]||r.valHooks[this.nodeName.toLowerCase()])&&"set"in n&&void 0!==n.set(this,o,"value")||(this.value=o))}))):o?(n=r.valHooks[o.type]||r.valHooks[o.nodeName.toLowerCase()])&&"get"in n&&void 0!==(t=n.get(o,"value"))?t:"string"==typeof(t=o.value)?t.replace(q,""):null==t?"":t:void 0}}),r.extend({valHooks:{option:{get:function(e){var t=e.attributes.value;return!t||t.specified?e.value:e.text}},select:{get:function(e){for(var t,n,i=e.options,o=e.selectedIndex,a="select-one"===e.type||o<0,s=a?null:[],u=a?o+1:i.length,c=o<0?u:a?o:0;c<u;c++)if(((n=i[c]).selected||c===o)&&(r.support.optDisabled?!n.disabled:null===n.getAttribute("disabled"))&&(!n.parentNode.disabled||!r.nodeName(n.parentNode,"optgroup"))){if(t=r(n).val(),a)return t;s.push(t)}return s},set:function(e,t){var n=r.makeArray(t);return r(e).find("option").each((function(){this.selected=r.inArray(r(this).val(),n)>=0})),n.length||(e.selectedIndex=-1),n}}},attr:function(e,t,n){var i,o,a,s=e.nodeType;if(e&&3!==s&&8!==s&&2!==s)return void 0===e.getAttribute?r.prop(e,t,n):((o=1!==s||!r.isXMLDoc(e))&&(t=t.toLowerCase(),i=r.attrHooks[t]||(B.test(t)?H:O)),void 0===n?i&&o&&"get"in i&&null!==(a=i.get(e,t))?a:(void 0!==e.getAttribute&&(a=e.getAttribute(t)),null==a?void 0:a):null!==n?i&&o&&"set"in i&&void 0!==(a=i.set(e,n,t))?a:(e.setAttribute(t,n+""),n):void r.removeAttr(e,t))},removeAttr:function(e,t){var n,i,o=0,a=t&&t.match(y);if(a&&1===e.nodeType)for(;n=a[o++];)i=r.propFix[n]||n,B.test(n)?!U&&P.test(n)?e[r.camelCase("default-"+n)]=e[i]=!1:e[i]=!1:r.attr(e,n,""),e.removeAttribute(U?n:i)},attrHooks:{type:{set:function(e,t){if(!r.support.radioValue&&"radio"===t&&r.nodeName(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}}},propFix:{tabindex:"tabIndex",readonly:"readOnly",for:"htmlFor",class:"className",maxlength:"maxLength",cellspacing:"cellSpacing",cellpadding:"cellPadding",rowspan:"rowSpan",colspan:"colSpan",usemap:"useMap",frameborder:"frameBorder",contenteditable:"contentEditable"},prop:function(e,t,n){var i,o,a=e.nodeType;if(e&&3!==a&&8!==a&&2!==a)return(1!==a||!r.isXMLDoc(e))&&(t=r.propFix[t]||t,o=r.propHooks[t]),void 0!==n?o&&"set"in o&&void 0!==(i=o.set(e,n,t))?i:e[t]=n:o&&"get"in o&&null!==(i=o.get(e,t))?i:e[t]},propHooks:{tabIndex:{get:function(e){var t=e.getAttributeNode("tabindex");return t&&t.specified?parseInt(t.value,10):I.test(e.nodeName)||M.test(e.nodeName)&&e.href?0:void 0}}}}),H={get:function(e,t){var n=r.prop(e,t),i="boolean"==typeof n&&e.getAttribute(t),o="boolean"==typeof n?z&&U?null!=i:P.test(t)?e[r.camelCase("default-"+t)]:!!i:e.getAttributeNode(t);return o&&!1!==o.value?t.toLowerCase():void 0},set:function(e,t,n){return!1===t?r.removeAttr(e,n):z&&U||!P.test(n)?e.setAttribute(!U&&r.propFix[n]||n,n):e[r.camelCase("default-"+n)]=e[n]=!0,n}},z&&U||(r.attrHooks.value={get:function(e,t){var n=e.getAttributeNode(t);return r.nodeName(e,"input")?e.defaultValue:n&&n.specified?n.value:void 0},set:function(e,t,n){if(!r.nodeName(e,"input"))return O&&O.set(e,t,n);e.defaultValue=t}}),U||(O=r.valHooks.button={get:function(e,t){var n=e.getAttributeNode(t);return n&&("id"===t||"name"===t||"coords"===t?""!==n.value:n.specified)?n.value:void 0},set:function(e,t,n){var r=e.getAttributeNode(n);return r||e.setAttributeNode(r=e.ownerDocument.createAttribute(n)),r.value=t+="","value"===n||t===e.getAttribute(n)?t:void 0}},r.attrHooks.contenteditable={get:O.get,set:function(e,t,n){O.set(e,""!==t&&t,n)}},r.each(["width","height"],(function(e,t){r.attrHooks[t]=r.extend(r.attrHooks[t],{set:function(e,n){if(""===n)return e.setAttribute(t,"auto"),n}})}))),r.support.hrefNormalized||(r.each(["href","src","width","height"],(function(e,t){r.attrHooks[t]=r.extend(r.attrHooks[t],{get:function(e){var n=e.getAttribute(t,2);return null==n?void 0:n}})})),r.each(["href","src"],(function(e,t){r.propHooks[t]={get:function(e){return e.getAttribute(t,4)}}}))),r.support.style||(r.attrHooks.style={get:function(e){return e.style.cssText||void 0},set:function(e,t){return e.style.cssText=t+""}}),r.support.optSelected||(r.propHooks.selected=r.extend(r.propHooks.selected,{get:function(e){var t=e.parentNode;return t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex),null}})),r.support.enctype||(r.propFix.enctype="encoding"),r.support.checkOn||r.each(["radio","checkbox"],(function(){r.valHooks[this]={get:function(e){return null===e.getAttribute("value")?"on":e.value}}})),r.each(["radio","checkbox"],(function(){r.valHooks[this]=r.extend(r.valHooks[this],{set:function(e,t){if(r.isArray(t))return e.checked=r.inArray(r(e).val(),t)>=0}})}));var $=/^(?:input|select|textarea)$/i,W=/^key/,V=/^(?:mouse|contextmenu)|click/,X=/^(?:focusinfocus|focusoutblur)$/,Q=/^([^.]*)(?:\.(.+)|)$/;function Er(){return!0}function Sr(){return!1}r.event={global:{},add:function(e,t,n,i,o){var a,s,u,c,l,f,d,p,h,g,m,v=r._data(e);if(v){for(n.handler&&(n=(c=n).handler,o=c.selector),n.guid||(n.guid=r.guid++),(s=v.events)||(s=v.events={}),(f=v.handle)||(f=v.handle=function(e){return void 0===r||e&&r.event.triggered===e.type?void 0:r.event.dispatch.apply(f.elem,arguments)},f.elem=e),u=(t=(t||"").match(y)||[""]).length;u--;)h=m=(a=Q.exec(t[u])||[])[1],g=(a[2]||"").split(".").sort(),l=r.event.special[h]||{},h=(o?l.delegateType:l.bindType)||h,l=r.event.special[h]||{},d=r.extend({type:h,origType:m,data:i,handler:n,guid:n.guid,selector:o,needsContext:o&&r.expr.match.needsContext.test(o),namespace:g.join(".")},c),(p=s[h])||((p=s[h]=[]).delegateCount=0,l.setup&&!1!==l.setup.call(e,i,g,f)||(e.addEventListener?e.addEventListener(h,f,!1):e.attachEvent&&e.attachEvent("on"+h,f))),l.add&&(l.add.call(e,d),d.handler.guid||(d.handler.guid=n.guid)),o?p.splice(p.delegateCount++,0,d):p.push(d),r.event.global[h]=!0;e=null}},remove:function(e,t,n,i,o){var a,s,u,c,l,f,d,p,h,g,m,v=r.hasData(e)&&r._data(e);if(v&&(f=v.events)){for(l=(t=(t||"").match(y)||[""]).length;l--;)if(h=m=(u=Q.exec(t[l])||[])[1],g=(u[2]||"").split(".").sort(),h){for(d=r.event.special[h]||{},p=f[h=(i?d.delegateType:d.bindType)||h]||[],u=u[2]&&new RegExp("(^|\\.)"+g.join("\\.(?:.*\\.|)")+"(\\.|$)"),c=a=p.length;a--;)s=p[a],!o&&m!==s.origType||n&&n.guid!==s.guid||u&&!u.test(s.namespace)||i&&i!==s.selector&&("**"!==i||!s.selector)||(p.splice(a,1),s.selector&&p.delegateCount--,d.remove&&d.remove.call(e,s));c&&!p.length&&(d.teardown&&!1!==d.teardown.call(e,g,v.handle)||r.removeEvent(e,h,v.handle),delete f[h])}else for(h in f)r.event.remove(e,h+t[l],n,i,!0);r.isEmptyObject(f)&&(delete v.handle,r._removeData(e,"events"))}},trigger:function(e,t,n,i){var o,s,u,c,l,f,d,p=[n||a],h=g.call(e,"type")?e.type:e,m=g.call(e,"namespace")?e.namespace.split("."):[];if(u=f=n=n||a,3!==n.nodeType&&8!==n.nodeType&&!X.test(h+r.event.triggered)&&(h.indexOf(".")>=0&&(m=h.split("."),h=m.shift(),m.sort()),s=h.indexOf(":")<0&&"on"+h,(e=e[r.expando]?e:new r.Event(h,"object"==typeof e&&e)).isTrigger=!0,e.namespace=m.join("."),e.namespace_re=e.namespace?new RegExp("(^|\\.)"+m.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=n),t=null==t?[e]:r.makeArray(t,[e]),l=r.event.special[h]||{},i||!l.trigger||!1!==l.trigger.apply(n,t))){if(!i&&!l.noBubble&&!r.isWindow(n)){for(c=l.delegateType||h,X.test(c+h)||(u=u.parentNode);u;u=u.parentNode)p.push(u),f=u;f===(n.ownerDocument||a)&&p.push(f.defaultView||f.parentWindow||window)}for(d=0;(u=p[d++])&&!e.isPropagationStopped();)e.type=d>1?c:l.bindType||h,(o=(r._data(u,"events")||{})[e.type]&&r._data(u,"handle"))&&o.apply(u,t),(o=s&&u[s])&&r.acceptData(u)&&o.apply&&!1===o.apply(u,t)&&e.preventDefault();if(e.type=h,!i&&!e.isDefaultPrevented()&&(!l._default||!1===l._default.apply(n.ownerDocument,t))&&("click"!==h||!r.nodeName(n,"a"))&&r.acceptData(n)&&s&&n[h]&&!r.isWindow(n)){(f=n[s])&&(n[s]=null),r.event.triggered=h;try{n[h]()}catch(e){}r.event.triggered=void 0,f&&(n[s]=f)}return e.result}},dispatch:function(e){e=r.event.fix(e);var t,n,i,o,a,s=[],u=d.call(arguments),c=(r._data(this,"events")||{})[e.type]||[],l=r.event.special[e.type]||{};if(u[0]=e,e.delegateTarget=this,!l.preDispatch||!1!==l.preDispatch.call(this,e)){for(s=r.event.handlers.call(this,e,c),t=0;(o=s[t++])&&!e.isPropagationStopped();)for(e.currentTarget=o.elem,a=0;(i=o.handlers[a++])&&!e.isImmediatePropagationStopped();)e.namespace_re&&!e.namespace_re.test(i.namespace)||(e.handleObj=i,e.data=i.data,void 0!==(n=((r.event.special[i.origType]||{}).handle||i.handler).apply(o.elem,u))&&!1===(e.result=n)&&(e.preventDefault(),e.stopPropagation()));return l.postDispatch&&l.postDispatch.call(this,e),e.result}},handlers:function(e,t){var n,i,o,a,s=[],u=t.delegateCount,c=e.target;if(u&&c.nodeType&&(!e.button||"click"!==e.type))for(;c!=this;c=c.parentNode||this)if(1===c.nodeType&&(!0!==c.disabled||"click"!==e.type)){for(o=[],a=0;a<u;a++)void 0===o[n=(i=t[a]).selector+" "]&&(o[n]=i.needsContext?r(n,this).index(c)>=0:r.find(n,this,null,[c]).length),o[n]&&o.push(i);o.length&&s.push({elem:c,handlers:o})}return u<t.length&&s.push({elem:this,handlers:t.slice(u)}),s},fix:function(e){if(e[r.expando])return e;var t,n,i,o=e.type,s=e,u=this.fixHooks[o];for(u||(this.fixHooks[o]=u=V.test(o)?this.mouseHooks:W.test(o)?this.keyHooks:{}),i=u.props?this.props.concat(u.props):this.props,e=new r.Event(s),t=i.length;t--;)e[n=i[t]]=s[n];return e.target||(e.target=s.srcElement||a),3===e.target.nodeType&&(e.target=e.target.parentNode),e.metaKey=!!e.metaKey,u.filter?u.filter(e,s):e},props:"altKey bubbles cancelable ctrlKey currentTarget eventPhase metaKey relatedTarget shiftKey target timeStamp view which".split(" "),fixHooks:{},keyHooks:{props:"char charCode key keyCode".split(" "),filter:function(e,t){return null==e.which&&(e.which=null!=t.charCode?t.charCode:t.keyCode),e}},mouseHooks:{props:"button buttons clientX clientY fromElement offsetX offsetY pageX pageY screenX screenY toElement".split(" "),filter:function(e,t){var n,r,i,o=t.button,s=t.fromElement;return null==e.pageX&&null!=t.clientX&&(i=(r=e.target.ownerDocument||a).documentElement,n=r.body,e.pageX=t.clientX+(i&&i.scrollLeft||n&&n.scrollLeft||0)-(i&&i.clientLeft||n&&n.clientLeft||0),e.pageY=t.clientY+(i&&i.scrollTop||n&&n.scrollTop||0)-(i&&i.clientTop||n&&n.clientTop||0)),!e.relatedTarget&&s&&(e.relatedTarget=s===e.target?t.toElement:s),e.which||void 0===o||(e.which=1&o?1:2&o?3:4&o?2:0),e}},special:{load:{noBubble:!0},click:{trigger:function(){if(r.nodeName(this,"input")&&"checkbox"===this.type&&this.click)return this.click(),!1}},focus:{trigger:function(){if(this!==a.activeElement&&this.focus)try{return this.focus(),!1}catch(e){}},delegateType:"focusin"},blur:{trigger:function(){if(this===a.activeElement&&this.blur)return this.blur(),!1},delegateType:"focusout"},beforeunload:{postDispatch:function(e){void 0!==e.result&&(e.originalEvent.returnValue=e.result)}}},simulate:function(e,t,n,i){var o=r.extend(new r.Event,n,{type:e,isSimulated:!0,originalEvent:{}});i?r.event.trigger(o,null,t):r.event.dispatch.call(t,o),o.isDefaultPrevented()&&n.preventDefault()}},r.removeEvent=a.removeEventListener?function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n,!1)}:function(e,t,n){var r="on"+t;e.detachEvent&&(void 0===e[r]&&(e[r]=null),e.detachEvent(r,n))},r.Event=function(e,t){if(!(this instanceof r.Event))return new r.Event(e,t);e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||!1===e.returnValue||e.getPreventDefault&&e.getPreventDefault()?Er:Sr):this.type=e,t&&r.extend(this,t),this.timeStamp=e&&e.timeStamp||r.now(),this[r.expando]=!0},r.Event.prototype={isDefaultPrevented:Sr,isPropagationStopped:Sr,isImmediatePropagationStopped:Sr,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=Er,e&&(e.preventDefault?e.preventDefault():e.returnValue=!1)},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=Er,e&&(e.stopPropagation&&e.stopPropagation(),e.cancelBubble=!0)},stopImmediatePropagation:function(){this.isImmediatePropagationStopped=Er,this.stopPropagation()}},r.each({mouseenter:"mouseover",mouseleave:"mouseout"},(function(e,t){r.event.special[e]={delegateType:t,bindType:t,handle:function(e){var n,i=e.relatedTarget,o=e.handleObj;return i&&(i===this||r.contains(this,i))||(e.type=o.origType,n=o.handler.apply(this,arguments),e.type=t),n}}})),r.support.submitBubbles||(r.event.special.submit={setup:function(){if(r.nodeName(this,"form"))return!1;r.event.add(this,"click._submit keypress._submit",(function(e){var t=e.target,n=r.nodeName(t,"input")||r.nodeName(t,"button")?t.form:void 0;n&&!r._data(n,"submitBubbles")&&(r.event.add(n,"submit._submit",(function(e){e._submit_bubble=!0})),r._data(n,"submitBubbles",!0))}))},postDispatch:function(e){e._submit_bubble&&(delete e._submit_bubble,this.parentNode&&!e.isTrigger&&r.event.simulate("submit",this.parentNode,e,!0))},teardown:function(){if(r.nodeName(this,"form"))return!1;r.event.remove(this,"._submit")}}),r.support.changeBubbles||(r.event.special.change={setup:function(){if($.test(this.nodeName))return"checkbox"!==this.type&&"radio"!==this.type||(r.event.add(this,"propertychange._change",(function(e){"checked"===e.originalEvent.propertyName&&(this._just_changed=!0)})),r.event.add(this,"click._change",(function(e){this._just_changed&&!e.isTrigger&&(this._just_changed=!1),r.event.simulate("change",this,e,!0)}))),!1;r.event.add(this,"beforeactivate._change",(function(e){var t=e.target;$.test(t.nodeName)&&!r._data(t,"changeBubbles")&&(r.event.add(t,"change._change",(function(e){!this.parentNode||e.isSimulated||e.isTrigger||r.event.simulate("change",this.parentNode,e,!0)})),r._data(t,"changeBubbles",!0))}))},handle:function(e){var t=e.target;if(this!==t||e.isSimulated||e.isTrigger||"radio"!==t.type&&"checkbox"!==t.type)return e.handleObj.handler.apply(this,arguments)},teardown:function(){return r.event.remove(this,"._change"),!$.test(this.nodeName)}}),r.support.focusinBubbles||r.each({focus:"focusin",blur:"focusout"},(function(e,t){var n=0,i=function(e){r.event.simulate(t,e.target,r.event.fix(e),!0)};r.event.special[t]={setup:function(){0==n++&&a.addEventListener(e,i,!0)},teardown:function(){0==--n&&a.removeEventListener(e,i,!0)}}})),r.fn.extend({on:function(e,t,n,i,o){var a,s;if("object"==typeof e){for(a in"string"!=typeof t&&(n=n||t,t=void 0),e)this.on(a,t,n,e[a],o);return this}if(null==n&&null==i?(i=t,n=t=void 0):null==i&&("string"==typeof t?(i=n,n=void 0):(i=n,n=t,t=void 0)),!1===i)i=Sr;else if(!i)return this;return 1===o&&(s=i,i=function(e){return r().off(e),s.apply(this,arguments)},i.guid=s.guid||(s.guid=r.guid++)),this.each((function(){r.event.add(this,e,i,n,t)}))},one:function(e,t,n,r){return this.on(e,t,n,r,1)},off:function(e,t,n){var i,o;if(e&&e.preventDefault&&e.handleObj)return i=e.handleObj,r(e.delegateTarget).off(i.namespace?i.origType+"."+i.namespace:i.origType,i.selector,i.handler),this;if("object"==typeof e){for(o in e)this.off(o,t,e[o]);return this}return!1!==t&&"function"!=typeof t||(n=t,t=void 0),!1===n&&(n=Sr),this.each((function(){r.event.remove(this,e,n,t)}))},bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,r){return this.on(t,e,n,r)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)},trigger:function(e,t){return this.each((function(){r.event.trigger(e,t,this)}))},triggerHandler:function(e,t){var n=this[0];if(n)return r.event.trigger(e,t,n,!0)}}),function(e,t){var n,i,o,a,s,u,c,l,f,d,p,h,g,m,v,y,b,x="sizzle"+-new Date,w=e.document,_={},T=0,k=0,E=ne(),S=ne(),C=ne(),A="undefined",N=1<<31,R=[],D=R.pop,F=R.push,L=R.slice,O=R.indexOf||function(e){for(var t=0,n=this.length;t<n;t++)if(this[t]===e)return t;return-1},H="[\\x20\\t\\r\\n\\f]",j="(?:\\\\.|[\\w-]|[^\\x00-\\xa0])+",q=j.replace("w","w#"),I="\\[[\\x20\\t\\r\\n\\f]*("+j+")"+H+"*(?:([*^$|!~]?=)"+H+"*(?:(['\"])((?:\\\\.|[^\\\\])*?)\\3|("+q+")|)|)"+H+"*\\]",M=":("+j+")(?:\\(((['\"])((?:\\\\.|[^\\\\])*?)\\3|((?:\\\\.|[^\\\\()[\\]]|"+I.replace(3,8)+")*)|.*)\\)|)",B=new RegExp("^[\\x20\\t\\r\\n\\f]+|((?:^|[^\\\\])(?:\\\\.)*)[\\x20\\t\\r\\n\\f]+$","g"),P=new RegExp("^[\\x20\\t\\r\\n\\f]*,[\\x20\\t\\r\\n\\f]*"),U=new RegExp("^[\\x20\\t\\r\\n\\f]*([\\x20\\t\\r\\n\\f>+~])[\\x20\\t\\r\\n\\f]*"),z=new RegExp(M),$=new RegExp("^"+q+"$"),W={ID:new RegExp("^#("+j+")"),CLASS:new RegExp("^\\.("+j+")"),NAME:new RegExp("^\\[name=['\"]?("+j+")['\"]?\\]"),TAG:new RegExp("^("+j.replace("w","w*")+")"),ATTR:new RegExp("^"+I),PSEUDO:new RegExp("^"+M),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\([\\x20\\t\\r\\n\\f]*(even|odd|(([+-]|)(\\d*)n|)[\\x20\\t\\r\\n\\f]*(?:([+-]|)[\\x20\\t\\r\\n\\f]*(\\d+)|))[\\x20\\t\\r\\n\\f]*\\)|)","i"),needsContext:new RegExp("^[\\x20\\t\\r\\n\\f]*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\([\\x20\\t\\r\\n\\f]*((?:-\\d)?\\d*)[\\x20\\t\\r\\n\\f]*\\)|)(?=[^-]|$)","i")},V=/[\x20\t\r\n\f]*[+~]/,X=/^[^{]+\{\s*\[native code/,Q=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,G=/^(?:input|select|textarea|button)$/i,J=/^h\d$/i,Y=/'|\\/g,K=/\=[\x20\t\r\n\f]*([^'"\]]*)[\x20\t\r\n\f]*\]/g,Z=/\\([\da-fA-F]{1,6}[\x20\t\r\n\f]?|.)/g,ee=function(e,t){var n="0x"+t-65536;return n!=n?t:n<0?String.fromCharCode(n+65536):String.fromCharCode(n>>10|55296,1023&n|56320)};try{L.call(w.documentElement.childNodes,0)[0].nodeType}catch(e){L=function(e){for(var t,n=[];t=this[e++];)n.push(t);return n}}function te(e){return X.test(e+"")}function ne(){var e,t=[];return e=function(n,r){return t.push(n+=" ")>o.cacheLength&&delete e[t.shift()],e[n]=r}}function re(e){return e[x]=!0,e}function ie(e){var t=d.createElement("div");try{return e(t)}catch(e){return!1}finally{t=null}}function oe(e,t,n,r){var i,a,s,c,l,p,m,v,b,T;if((t?t.ownerDocument||t:w)!==d&&f(t),n=n||[],!e||"string"!=typeof e)return n;if(1!==(c=(t=t||d).nodeType)&&9!==c)return[];if(!h&&!r){if(i=Q.exec(e))if(s=i[1]){if(9===c){if(!(a=t.getElementById(s))||!a.parentNode)return n;if(a.id===s)return n.push(a),n}else if(t.ownerDocument&&(a=t.ownerDocument.getElementById(s))&&y(t,a)&&a.id===s)return n.push(a),n}else{if(i[2])return F.apply(n,L.call(t.getElementsByTagName(e),0)),n;if((s=i[3])&&_.getByClassName&&t.getElementsByClassName)return F.apply(n,L.call(t.getElementsByClassName(s),0)),n}if(_.qsa&&!g.test(e)){if(m=!0,v=x,b=t,T=9===c&&e,1===c&&"object"!==t.nodeName.toLowerCase()){for(p=le(e),(m=t.getAttribute("id"))?v=m.replace(Y,"\\$&"):t.setAttribute("id",v),v="[id='"+v+"'] ",l=p.length;l--;)p[l]=v+fe(p[l]);b=V.test(e)&&t.parentNode||t,T=p.join(",")}if(T)try{return F.apply(n,L.call(b.querySelectorAll(T),0)),n}catch(e){}finally{m||t.removeAttribute("id")}}}return function(e,t,n,r){var i,a,s,c,l,f=le(e);if(!r&&1===f.length){if((a=f[0]=f[0].slice(0)).length>2&&"ID"===(s=a[0]).type&&9===t.nodeType&&!h&&o.relative[a[1].type]){if(!(t=o.find.ID(s.matches[0].replace(Z,ee),t)[0]))return n;e=e.slice(a.shift().value.length)}for(i=W.needsContext.test(e)?0:a.length;i--&&(s=a[i],!o.relative[c=s.type]);)if((l=o.find[c])&&(r=l(s.matches[0].replace(Z,ee),V.test(a[0].type)&&t.parentNode||t))){if(a.splice(i,1),!(e=r.length&&fe(a)))return F.apply(n,L.call(r,0)),n;break}}return u(e,f)(r,t,h,n,V.test(e)),n}(e.replace(B,"$1"),t,n,r)}function ae(e,t){var n=t&&e,r=n&&(~t.sourceIndex||N)-(~e.sourceIndex||N);if(r)return r;if(n)for(;n=n.nextSibling;)if(n===t)return-1;return e?1:-1}function se(e){return function(t){return"input"===t.nodeName.toLowerCase()&&t.type===e}}function ue(e){return function(t){var n=t.nodeName.toLowerCase();return("input"===n||"button"===n)&&t.type===e}}function ce(e){return re((function(t){return t=+t,re((function(n,r){for(var i,o=e([],n.length,t),a=o.length;a--;)n[i=o[a]]&&(n[i]=!(r[i]=n[i]))}))}))}for(n in s=oe.isXML=function(e){var t=e&&(e.ownerDocument||e).documentElement;return!!t&&"HTML"!==t.nodeName},f=oe.setDocument=function(e){var t=e?e.ownerDocument||e:w;return t!==d&&9===t.nodeType&&t.documentElement?(d=t,p=t.documentElement,h=s(t),_.tagNameNoComments=ie((function(e){return e.appendChild(t.createComment("")),!e.getElementsByTagName("*").length})),_.attributes=ie((function(e){e.innerHTML="<select></select>";var t=typeof e.lastChild.getAttribute("multiple");return"boolean"!==t&&"string"!==t})),_.getByClassName=ie((function(e){return e.innerHTML="<div class='hidden e'></div><div class='hidden'></div>",!(!e.getElementsByClassName||!e.getElementsByClassName("e").length)&&(e.lastChild.className="e",2===e.getElementsByClassName("e").length)})),_.getByName=ie((function(e){e.id=x+0,e.innerHTML="<a name='"+x+"'></a><div name='"+x+"'></div>",p.insertBefore(e,p.firstChild);var n=t.getElementsByName&&t.getElementsByName(x).length===2+t.getElementsByName(x+0).length;return _.getIdNotName=!t.getElementById(x),p.removeChild(e),n})),o.attrHandle=ie((function(e){return e.innerHTML="<a href='#'></a>",e.firstChild&&typeof e.firstChild.getAttribute!==A&&"#"===e.firstChild.getAttribute("href")}))?{}:{href:function(e){return e.getAttribute("href",2)},type:function(e){return e.getAttribute("type")}},_.getIdNotName?(o.find.ID=function(e,t){if(typeof t.getElementById!==A&&!h){var n=t.getElementById(e);return n&&n.parentNode?[n]:[]}},o.filter.ID=function(e){var t=e.replace(Z,ee);return function(e){return e.getAttribute("id")===t}}):(o.find.ID=function(e,t){if(typeof t.getElementById!==A&&!h){var n=t.getElementById(e);return n?n.id===e||typeof n.getAttributeNode!==A&&n.getAttributeNode("id").value===e?[n]:void 0:[]}},o.filter.ID=function(e){var t=e.replace(Z,ee);return function(e){var n=typeof e.getAttributeNode!==A&&e.getAttributeNode("id");return n&&n.value===t}}),o.find.TAG=_.tagNameNoComments?function(e,t){if(typeof t.getElementsByTagName!==A)return t.getElementsByTagName(e)}:function(e,t){var n,r=[],i=0,o=t.getElementsByTagName(e);if("*"===e){for(;n=o[i++];)1===n.nodeType&&r.push(n);return r}return o},o.find.NAME=_.getByName&&function(e,t){if(typeof t.getElementsByName!==A)return t.getElementsByName(name)},o.find.CLASS=_.getByClassName&&function(e,t){if(typeof t.getElementsByClassName!==A&&!h)return t.getElementsByClassName(e)},m=[],g=[":focus"],(_.qsa=te(t.querySelectorAll))&&(ie((function(e){e.innerHTML="<select><option selected=''></option></select>",e.querySelectorAll("[selected]").length||g.push("\\[[\\x20\\t\\r\\n\\f]*(?:checked|disabled|ismap|multiple|readonly|selected|value)"),e.querySelectorAll(":checked").length||g.push(":checked")})),ie((function(e){e.innerHTML="<input type='hidden' i=''/>",e.querySelectorAll("[i^='']").length&&g.push("[*^$]=[\\x20\\t\\r\\n\\f]*(?:\"\"|'')"),e.querySelectorAll(":enabled").length||g.push(":enabled",":disabled"),e.querySelectorAll("*,:x"),g.push(",.*:")}))),(_.matchesSelector=te(v=p.matchesSelector||p.mozMatchesSelector||p.webkitMatchesSelector||p.oMatchesSelector||p.msMatchesSelector))&&ie((function(e){_.disconnectedMatch=v.call(e,"div"),v.call(e,"[s!='']:x"),m.push("!=",M)})),g=new RegExp(g.join("|")),m=new RegExp(m.join("|")),y=te(p.contains)||p.compareDocumentPosition?function(e,t){var n=9===e.nodeType?e.documentElement:e,r=t&&t.parentNode;return e===r||!(!r||1!==r.nodeType||!(n.contains?n.contains(r):e.compareDocumentPosition&&16&e.compareDocumentPosition(r)))}:function(e,t){if(t)for(;t=t.parentNode;)if(t===e)return!0;return!1},b=p.compareDocumentPosition?function(e,n){var r;return e===n?(c=!0,0):(r=n.compareDocumentPosition&&e.compareDocumentPosition&&e.compareDocumentPosition(n))?1&r||e.parentNode&&11===e.parentNode.nodeType?e===t||y(w,e)?-1:n===t||y(w,n)?1:0:4&r?-1:1:e.compareDocumentPosition?-1:1}:function(e,n){var r,i=0,o=e.parentNode,a=n.parentNode,s=[e],u=[n];if(e===n)return c=!0,0;if(!o||!a)return e===t?-1:n===t?1:o?-1:a?1:0;if(o===a)return ae(e,n);for(r=e;r=r.parentNode;)s.unshift(r);for(r=n;r=r.parentNode;)u.unshift(r);for(;s[i]===u[i];)i++;return i?ae(s[i],u[i]):s[i]===w?-1:u[i]===w?1:0},c=!1,[0,0].sort(b),_.detectDuplicates=c,d):d},oe.matches=function(e,t){return oe(e,null,null,t)},oe.matchesSelector=function(e,t){if((e.ownerDocument||e)!==d&&f(e),t=t.replace(K,"='$1']"),_.matchesSelector&&!h&&(!m||!m.test(t))&&!g.test(t))try{var n=v.call(e,t);if(n||_.disconnectedMatch||e.document&&11!==e.document.nodeType)return n}catch(e){}return oe(t,d,null,[e]).length>0},oe.contains=function(e,t){return(e.ownerDocument||e)!==d&&f(e),y(e,t)},oe.attr=function(e,t){var n;return(e.ownerDocument||e)!==d&&f(e),h||(t=t.toLowerCase()),(n=o.attrHandle[t])?n(e):h||_.attributes?e.getAttribute(t):((n=e.getAttributeNode(t))||e.getAttribute(t))&&!0===e[t]?t:n&&n.specified?n.value:null},oe.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},oe.uniqueSort=function(e){var t,n=[],r=1,i=0;if(c=!_.detectDuplicates,e.sort(b),c){for(;t=e[r];r++)t===e[r-1]&&(i=n.push(r));for(;i--;)e.splice(n[i],1)}return e},a=oe.getText=function(e){var t,n="",r=0,i=e.nodeType;if(i){if(1===i||9===i||11===i){if("string"==typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=a(e)}else if(3===i||4===i)return e.nodeValue}else for(;t=e[r];r++)n+=a(t);return n},o=oe.selectors={cacheLength:50,createPseudo:re,match:W,find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(Z,ee),e[3]=(e[4]||e[5]||"").replace(Z,ee),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||oe.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&oe.error(e[0]),e},PSEUDO:function(e){var t,n=!e[5]&&e[2];return W.CHILD.test(e[0])?null:(e[4]?e[2]=e[4]:n&&z.test(n)&&(t=le(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){return"*"===e?function(){return!0}:(e=e.replace(Z,ee).toLowerCase(),function(t){return t.nodeName&&t.nodeName.toLowerCase()===e})},CLASS:function(e){var t=E[e+" "];return t||(t=new RegExp("(^|[\\x20\\t\\r\\n\\f])"+e+"("+H+"|$)"))&&E(e,(function(e){return t.test(e.className||typeof e.getAttribute!==A&&e.getAttribute("class")||"")}))},ATTR:function(e,t,n){return function(r){var i=oe.attr(r,e);return null==i?"!="===t:!t||(i+="","="===t?i===n:"!="===t?i!==n:"^="===t?n&&0===i.indexOf(n):"*="===t?n&&i.indexOf(n)>-1:"$="===t?n&&i.slice(-n.length)===n:"~="===t?(" "+i+" ").indexOf(n)>-1:"|="===t&&(i===n||i.slice(0,n.length+1)===n+"-"))}},CHILD:function(e,t,n,r,i){var o="nth"!==e.slice(0,3),a="last"!==e.slice(-4),s="of-type"===t;return 1===r&&0===i?function(e){return!!e.parentNode}:function(t,n,u){var c,l,f,d,p,h,g=o!==a?"nextSibling":"previousSibling",m=t.parentNode,v=s&&t.nodeName.toLowerCase(),y=!u&&!s;if(m){if(o){for(;g;){for(f=t;f=f[g];)if(s?f.nodeName.toLowerCase()===v:1===f.nodeType)return!1;h=g="only"===e&&!h&&"nextSibling"}return!0}if(h=[a?m.firstChild:m.lastChild],a&&y){for(p=(c=(l=m[x]||(m[x]={}))[e]||[])[0]===T&&c[1],d=c[0]===T&&c[2],f=p&&m.childNodes[p];f=++p&&f&&f[g]||(d=p=0)||h.pop();)if(1===f.nodeType&&++d&&f===t){l[e]=[T,p,d];break}}else if(y&&(c=(t[x]||(t[x]={}))[e])&&c[0]===T)d=c[1];else for(;(f=++p&&f&&f[g]||(d=p=0)||h.pop())&&((s?f.nodeName.toLowerCase()!==v:1!==f.nodeType)||!++d||(y&&((f[x]||(f[x]={}))[e]=[T,d]),f!==t)););return(d-=i)===r||d%r==0&&d/r>=0}}},PSEUDO:function(e,t){var n,r=o.pseudos[e]||o.setFilters[e.toLowerCase()]||oe.error("unsupported pseudo: "+e);return r[x]?r(t):r.length>1?(n=[e,e,"",t],o.setFilters.hasOwnProperty(e.toLowerCase())?re((function(e,n){for(var i,o=r(e,t),a=o.length;a--;)e[i=O.call(e,o[a])]=!(n[i]=o[a])})):function(e){return r(e,0,n)}):r}},pseudos:{not:re((function(e){var t=[],n=[],r=u(e.replace(B,"$1"));return r[x]?re((function(e,t,n,i){for(var o,a=r(e,null,i,[]),s=e.length;s--;)(o=a[s])&&(e[s]=!(t[s]=o))})):function(e,i,o){return t[0]=e,r(t,null,o,n),!n.pop()}})),has:re((function(e){return function(t){return oe(e,t).length>0}})),contains:re((function(e){return function(t){return(t.textContent||t.innerText||a(t)).indexOf(e)>-1}})),lang:re((function(e){return $.test(e||"")||oe.error("unsupported lang: "+e),e=e.replace(Z,ee).toLowerCase(),function(t){var n;do{if(n=h?t.getAttribute("xml:lang")||t.getAttribute("lang"):t.lang)return(n=n.toLowerCase())===e||0===n.indexOf(e+"-")}while((t=t.parentNode)&&1===t.nodeType);return!1}})),target:function(t){var n=e.location&&e.location.hash;return n&&n.slice(1)===t.id},root:function(e){return e===p},focus:function(e){return e===d.activeElement&&(!d.hasFocus||d.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:function(e){return!1===e.disabled},disabled:function(e){return!0===e.disabled},checked:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeName>"@"||3===e.nodeType||4===e.nodeType)return!1;return!0},parent:function(e){return!o.pseudos.empty(e)},header:function(e){return J.test(e.nodeName)},input:function(e){return G.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},text:function(e){var t;return"input"===e.nodeName.toLowerCase()&&"text"===e.type&&(null==(t=e.getAttribute("type"))||t.toLowerCase()===e.type)},first:ce((function(){return[0]})),last:ce((function(e,t){return[t-1]})),eq:ce((function(e,t,n){return[n<0?n+t:n]})),even:ce((function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e})),odd:ce((function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e})),lt:ce((function(e,t,n){for(var r=n<0?n+t:n;--r>=0;)e.push(r);return e})),gt:ce((function(e,t,n){for(var r=n<0?n+t:n;++r<t;)e.push(r);return e}))}},{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})o.pseudos[n]=se(n);for(n in{submit:!0,reset:!0})o.pseudos[n]=ue(n);function le(e,t){var n,r,i,a,s,u,c,l=S[e+" "];if(l)return t?0:l.slice(0);for(s=e,u=[],c=o.preFilter;s;){for(a in n&&!(r=P.exec(s))||(r&&(s=s.slice(r[0].length)||s),u.push(i=[])),n=!1,(r=U.exec(s))&&(n=r.shift(),i.push({value:n,type:r[0].replace(B," ")}),s=s.slice(n.length)),o.filter)!(r=W[a].exec(s))||c[a]&&!(r=c[a](r))||(n=r.shift(),i.push({value:n,type:a,matches:r}),s=s.slice(n.length));if(!n)break}return t?s.length:s?oe.error(e):S(e,u).slice(0)}function fe(e){for(var t=0,n=e.length,r="";t<n;t++)r+=e[t].value;return r}function de(e,t,n){var r=t.dir,o=n&&"parentNode"===r,a=k++;return t.first?function(t,n,i){for(;t=t[r];)if(1===t.nodeType||o)return e(t,n,i)}:function(t,n,s){var u,c,l,f=T+" "+a;if(s){for(;t=t[r];)if((1===t.nodeType||o)&&e(t,n,s))return!0}else for(;t=t[r];)if(1===t.nodeType||o)if((c=(l=t[x]||(t[x]={}))[r])&&c[0]===f){if(!0===(u=c[1])||u===i)return!0===u}else if((c=l[r]=[f])[1]=e(t,n,s)||i,!0===c[1])return!0}}function pe(e){return e.length>1?function(t,n,r){for(var i=e.length;i--;)if(!e[i](t,n,r))return!1;return!0}:e[0]}function he(e,t,n,r,i){for(var o,a=[],s=0,u=e.length,c=null!=t;s<u;s++)(o=e[s])&&(n&&!n(o,r,i)||(a.push(o),c&&t.push(s)));return a}function ge(e,t,n,r,i,o){return r&&!r[x]&&(r=ge(r)),i&&!i[x]&&(i=ge(i,o)),re((function(o,a,s,u){var c,l,f,d=[],p=[],h=a.length,g=o||function(e,t,n){for(var r=0,i=t.length;r<i;r++)oe(e,t[r],n);return n}(t||"*",s.nodeType?[s]:s,[]),m=!e||!o&&t?g:he(g,d,e,s,u),v=n?i||(o?e:h||r)?[]:a:m;if(n&&n(m,v,s,u),r)for(c=he(v,p),r(c,[],s,u),l=c.length;l--;)(f=c[l])&&(v[p[l]]=!(m[p[l]]=f));if(o){if(i||e){if(i){for(c=[],l=v.length;l--;)(f=v[l])&&c.push(m[l]=f);i(null,v=[],c,u)}for(l=v.length;l--;)(f=v[l])&&(c=i?O.call(o,f):d[l])>-1&&(o[c]=!(a[c]=f))}}else v=he(v===a?v.splice(h,v.length):v),i?i(null,a,v,u):F.apply(a,v)}))}function me(e){for(var t,n,r,i=e.length,a=o.relative[e[0].type],s=a||o.relative[" "],u=a?1:0,c=de((function(e){return e===t}),s,!0),f=de((function(e){return O.call(t,e)>-1}),s,!0),d=[function(e,n,r){return!a&&(r||n!==l)||((t=n).nodeType?c(e,n,r):f(e,n,r))}];u<i;u++)if(n=o.relative[e[u].type])d=[de(pe(d),n)];else{if((n=o.filter[e[u].type].apply(null,e[u].matches))[x]){for(r=++u;r<i&&!o.relative[e[r].type];r++);return ge(u>1&&pe(d),u>1&&fe(e.slice(0,u-1)).replace(B,"$1"),n,u<r&&me(e.slice(u,r)),r<i&&me(e=e.slice(r)),r<i&&fe(e))}d.push(n)}return pe(d)}function ve(){}u=oe.compile=function(e,t){var n,r=[],a=[],s=C[e+" "];if(!s){for(t||(t=le(e)),n=t.length;n--;)(s=me(t[n]))[x]?r.push(s):a.push(s);s=C(e,function(e,t){var n=0,r=t.length>0,a=e.length>0,s=function(s,u,c,f,p){var h,g,m,v=[],y=0,b="0",x=s&&[],w=null!=p,_=l,k=s||a&&o.find.TAG("*",p&&u.parentNode||u),E=T+=null==_?1:Math.random()||.1;for(w&&(l=u!==d&&u,i=n);null!=(h=k[b]);b++){if(a&&h){for(g=0;m=e[g++];)if(m(h,u,c)){f.push(h);break}w&&(T=E,i=++n)}r&&((h=!m&&h)&&y--,s&&x.push(h))}if(y+=b,r&&b!==y){for(g=0;m=t[g++];)m(x,v,u,c);if(s){if(y>0)for(;b--;)x[b]||v[b]||(v[b]=D.call(f));v=he(v)}F.apply(f,v),w&&!s&&v.length>0&&y+t.length>1&&oe.uniqueSort(f)}return w&&(T=E,l=_),x};return r?re(s):s}(a,r))}return s},o.pseudos.nth=o.pseudos.eq,o.filters=ve.prototype=o.pseudos,o.setFilters=new ve,f(),oe.attr=r.attr,r.find=oe,r.expr=oe.selectors,r.expr[":"]=r.expr.pseudos,r.unique=oe.uniqueSort,r.text=oe.getText,r.isXMLDoc=oe.isXML,r.contains=oe.contains}(window);var G=/Until$/,J=/^(?:parents|prev(?:Until|All))/,Y=/^.[^:#\[\.,]*$/,K=r.expr.match.needsContext,Z={children:!0,contents:!0,next:!0,prev:!0};function Cr(e,t){do{e=e[t]}while(e&&1!==e.nodeType);return e}function Ar(e,t,n){if(t=t||0,r.isFunction(t))return r.grep(e,(function(e,r){return!!t.call(e,r,e)===n}));if(t.nodeType)return r.grep(e,(function(e){return e===t===n}));if("string"==typeof t){var i=r.grep(e,(function(e){return 1===e.nodeType}));if(Y.test(t))return r.filter(t,i,!n);t=r.filter(t,i)}return r.grep(e,(function(e){return r.inArray(e,t)>=0===n}))}function Nr(e){var t=ee.split("|"),n=e.createDocumentFragment();if(n.createElement)for(;t.length;)n.createElement(t.pop());return n}r.fn.extend({find:function(e){var t,n,i,o=this.length;if("string"!=typeof e)return i=this,this.pushStack(r(e).filter((function(){for(t=0;t<o;t++)if(r.contains(i[t],this))return!0})));for(n=[],t=0;t<o;t++)r.find(e,this[t],n);return(n=this.pushStack(o>1?r.unique(n):n)).selector=(this.selector?this.selector+" ":"")+e,n},has:function(e){var t,n=r(e,this),i=n.length;return this.filter((function(){for(t=0;t<i;t++)if(r.contains(this,n[t]))return!0}))},not:function(e){return this.pushStack(Ar(this,e,!1))},filter:function(e){return this.pushStack(Ar(this,e,!0))},is:function(e){return!!e&&("string"==typeof e?K.test(e)?r(e,this.context).index(this[0])>=0:r.filter(e,this).length>0:this.filter(e).length>0)},closest:function(e,t){for(var n,i=0,o=this.length,a=[],s=K.test(e)||"string"!=typeof e?r(e,t||this.context):0;i<o;i++)for(n=this[i];n&&n.ownerDocument&&n!==t&&11!==n.nodeType;){if(s?s.index(n)>-1:r.find.matchesSelector(n,e)){a.push(n);break}n=n.parentNode}return this.pushStack(a.length>1?r.unique(a):a)},index:function(e){return e?"string"==typeof e?r.inArray(this[0],r(e)):r.inArray(e.jquery?e[0]:e,this):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){var n="string"==typeof e?r(e,t):r.makeArray(e&&e.nodeType?[e]:e),i=r.merge(this.get(),n);return this.pushStack(r.unique(i))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),r.fn.andSelf=r.fn.addBack,r.each({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return r.dir(e,"parentNode")},parentsUntil:function(e,t,n){return r.dir(e,"parentNode",n)},next:function(e){return Cr(e,"nextSibling")},prev:function(e){return Cr(e,"previousSibling")},nextAll:function(e){return r.dir(e,"nextSibling")},prevAll:function(e){return r.dir(e,"previousSibling")},nextUntil:function(e,t,n){return r.dir(e,"nextSibling",n)},prevUntil:function(e,t,n){return r.dir(e,"previousSibling",n)},siblings:function(e){return r.sibling((e.parentNode||{}).firstChild,e)},children:function(e){return r.sibling(e.firstChild)},contents:function(e){return r.nodeName(e,"iframe")?e.contentDocument||e.contentWindow.document:r.merge([],e.childNodes)}},(function(e,t){r.fn[e]=function(n,i){var o=r.map(this,t,n);return G.test(e)||(i=n),i&&"string"==typeof i&&(o=r.filter(i,o)),o=this.length>1&&!Z[e]?r.unique(o):o,this.length>1&&J.test(e)&&(o=o.reverse()),this.pushStack(o)}})),r.extend({filter:function(e,t,n){return n&&(e=":not("+e+")"),1===t.length?r.find.matchesSelector(t[0],e)?[t[0]]:[]:r.find.matches(e,t)},dir:function(e,t,n){for(var i=[],o=e[t];o&&9!==o.nodeType&&(void 0===n||1!==o.nodeType||!r(o).is(n));)1===o.nodeType&&i.push(o),o=o[t];return i},sibling:function(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n}});var ee="abbr|article|aside|audio|bdi|canvas|data|datalist|details|figcaption|figure|footer|header|hgroup|mark|meter|nav|output|progress|section|summary|time|video",te=/ jQuery\d+="(?:null|\d+)"/g,ne=new RegExp("<(?:"+ee+")[\\s/>]","i"),re=/^\s+/,ie=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\w:]+)[^>]*)\/>/gi,oe=/<([\w:]+)/,ae=/<tbody/i,se=/<|&#?\w+;/,ue=/<(?:script|style|link)/i,ce=/^(?:checkbox|radio)$/i,le=/checked\s*(?:[^=]|=\s*.checked.)/i,fe=/^$|\/(?:java|ecma)script/i,de=/^true\/(.*)/,pe=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g,he={option:[1,"<select multiple='multiple'>","</select>"],legend:[1,"<fieldset>","</fieldset>"],area:[1,"<map>","</map>"],param:[1,"<object>","</object>"],thead:[1,"<table>","</table>"],tr:[2,"<table><tbody>","</tbody></table>"],col:[2,"<table><tbody></tbody><colgroup>","</colgroup></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:r.support.htmlSerialize?[0,"",""]:[1,"X<div>","</div>"]},ge=Nr(a).appendChild(a.createElement("div"));function Rr(e){var t=e.getAttributeNode("type");return e.type=(t&&t.specified)+"/"+e.type,e}function Dr(e){var t=de.exec(e.type);return t?e.type=t[1]:e.removeAttribute("type"),e}function Fr(e,t){for(var n,i=0;null!=(n=e[i]);i++)r._data(n,"globalEval",!t||r._data(t[i],"globalEval"))}function Lr(e,t){if(1===t.nodeType&&r.hasData(e)){var n,i,o,a=r._data(e),s=r._data(t,a),u=a.events;if(u)for(n in delete s.handle,s.events={},u)for(i=0,o=u[n].length;i<o;i++)r.event.add(t,n,u[n][i]);s.data&&(s.data=r.extend({},s.data))}}function Or(e,t){var n,i,o;if(1===t.nodeType){if(n=t.nodeName.toLowerCase(),!r.support.noCloneEvent&&t[r.expando]){for(i in(o=r._data(t)).events)r.removeEvent(t,i,o.handle);t.removeAttribute(r.expando)}"script"===n&&t.text!==e.text?(Rr(t).text=e.text,Dr(t)):"object"===n?(t.parentNode&&(t.outerHTML=e.outerHTML),r.support.html5Clone&&e.innerHTML&&!r.trim(t.innerHTML)&&(t.innerHTML=e.innerHTML)):"input"===n&&ce.test(e.type)?(t.defaultChecked=t.checked=e.checked,t.value!==e.value&&(t.value=e.value)):"option"===n?t.defaultSelected=t.selected=e.defaultSelected:"input"!==n&&"textarea"!==n||(t.defaultValue=e.defaultValue)}}function Hr(e,t){var n,i,o=0,a=void 0!==e.getElementsByTagName?e.getElementsByTagName(t||"*"):void 0!==e.querySelectorAll?e.querySelectorAll(t||"*"):void 0;if(!a)for(a=[],n=e.childNodes||e;null!=(i=n[o]);o++)!t||r.nodeName(i,t)?a.push(i):r.merge(a,Hr(i,t));return void 0===t||t&&r.nodeName(e,t)?r.merge([e],a):a}function jr(e){ce.test(e.type)&&(e.defaultChecked=e.checked)}he.optgroup=he.option,he.tbody=he.tfoot=he.colgroup=he.caption=he.thead,he.th=he.td,r.fn.extend({text:function(e){return r.access(this,(function(e){return void 0===e?r.text(this):this.empty().append((this[0]&&this[0].ownerDocument||a).createTextNode(e))}),null,e,arguments.length)},wrapAll:function(e){if(r.isFunction(e))return this.each((function(t){r(this).wrapAll(e.call(this,t))}));if(this[0]){var t=r(e,this[0].ownerDocument).eq(0).clone(!0);this[0].parentNode&&t.insertBefore(this[0]),t.map((function(){for(var e=this;e.firstChild&&1===e.firstChild.nodeType;)e=e.firstChild;return e})).append(this)}return this},wrapInner:function(e){return r.isFunction(e)?this.each((function(t){r(this).wrapInner(e.call(this,t))})):this.each((function(){var t=r(this),n=t.contents();n.length?n.wrapAll(e):t.append(e)}))},wrap:function(e){var t=r.isFunction(e);return this.each((function(n){r(this).wrapAll(t?e.call(this,n):e)}))},unwrap:function(){return this.parent().each((function(){r.nodeName(this,"body")||r(this).replaceWith(this.childNodes)})).end()},append:function(){return this.domManip(arguments,!0,(function(e){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||this.appendChild(e)}))},prepend:function(){return this.domManip(arguments,!0,(function(e){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||this.insertBefore(e,this.firstChild)}))},before:function(){return this.domManip(arguments,!1,(function(e){this.parentNode&&this.parentNode.insertBefore(e,this)}))},after:function(){return this.domManip(arguments,!1,(function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)}))},remove:function(e,t){for(var n,i=0;null!=(n=this[i]);i++)(!e||r.filter(e,[n]).length>0)&&(t||1!==n.nodeType||r.cleanData(Hr(n)),n.parentNode&&(t&&r.contains(n.ownerDocument,n)&&Fr(Hr(n,"script")),n.parentNode.removeChild(n)));return this},empty:function(){for(var e,t=0;null!=(e=this[t]);t++){for(1===e.nodeType&&r.cleanData(Hr(e,!1));e.firstChild;)e.removeChild(e.firstChild);e.options&&r.nodeName(e,"select")&&(e.options.length=0)}return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map((function(){return r.clone(this,e,t)}))},html:function(e){return r.access(this,(function(e){var t=this[0]||{},n=0,i=this.length;if(void 0===e)return 1===t.nodeType?t.innerHTML.replace(te,""):void 0;if("string"==typeof e&&!ue.test(e)&&(r.support.htmlSerialize||!ne.test(e))&&(r.support.leadingWhitespace||!re.test(e))&&!he[(oe.exec(e)||["",""])[1].toLowerCase()]){e=e.replace(ie,"<$1></$2>");try{for(;n<i;n++)1===(t=this[n]||{}).nodeType&&(r.cleanData(Hr(t,!1)),t.innerHTML=e);t=0}catch(e){}}t&&this.empty().append(e)}),null,e,arguments.length)},replaceWith:function(e){return r.isFunction(e)||"string"==typeof e||(e=r(e).not(this).detach()),this.domManip([e],!0,(function(e){var t=this.nextSibling,n=this.parentNode;n&&(r(this).remove(),n.insertBefore(e,t))}))},detach:function(e){return this.remove(e,!0)},domManip:function(e,t,n){e=l.apply([],e);var i,o,a,s,u,c,f,d,p=0,h=this.length,g=this,m=h-1,v=e[0],y=r.isFunction(v);if(y||!(h<=1||"string"!=typeof v||r.support.checkClone)&&le.test(v))return this.each((function(r){var i=g.eq(r);y&&(e[0]=v.call(this,r,t?i.html():void 0)),i.domManip(e,t,n)}));if(h&&(i=(c=r.buildFragment(e,this[0].ownerDocument,!1,this)).firstChild,1===c.childNodes.length&&(c=i),i)){for(t=t&&r.nodeName(i,"tr"),a=(s=r.map(Hr(c,"script"),Rr)).length;p<h;p++)o=c,p!==m&&(o=r.clone(o,!0,!0),a&&r.merge(s,Hr(o,"script"))),n.call(t&&r.nodeName(this[p],"table")?(d="tbody",(f=this[p]).getElementsByTagName(d)[0]||f.appendChild(f.ownerDocument.createElement(d))):this[p],o,p);if(a)for(u=s[s.length-1].ownerDocument,r.map(s,Dr),p=0;p<a;p++)o=s[p],fe.test(o.type||"")&&!r._data(o,"globalEval")&&r.contains(u,o)&&(o.src?r.ajax({url:o.src,type:"GET",dataType:"script",async:!1,global:!1,throws:!0}):r.globalEval((o.text||o.textContent||o.innerHTML||"").replace(pe,"")));c=i=null}return this}}),r.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},(function(e,t){r.fn[e]=function(e){for(var n,i=0,o=[],a=r(e),s=a.length-1;i<=s;i++)n=i===s?this:this.clone(!0),r(a[i])[t](n),f.apply(o,n.get());return this.pushStack(o)}})),r.extend({clone:function(e,t,n){var i,o,a,s,u,c=r.contains(e.ownerDocument,e);if(r.support.html5Clone||r.isXMLDoc(e)||!ne.test("<"+e.nodeName+">")?a=e.cloneNode(!0):(ge.innerHTML=e.outerHTML,ge.removeChild(a=ge.firstChild)),!(r.support.noCloneEvent&&r.support.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||r.isXMLDoc(e)))for(i=Hr(a),u=Hr(e),s=0;null!=(o=u[s]);++s)i[s]&&Or(o,i[s]);if(t)if(n)for(u=u||Hr(e),i=i||Hr(a),s=0;null!=(o=u[s]);s++)Lr(o,i[s]);else Lr(e,a);return(i=Hr(a,"script")).length>0&&Fr(i,!c&&Hr(e,"script")),i=u=o=null,a},buildFragment:function(e,t,n,i){for(var o,a,s,u,c,l,f,d=e.length,p=Nr(t),h=[],g=0;g<d;g++)if((a=e[g])||0===a)if("object"===r.type(a))r.merge(h,a.nodeType?[a]:a);else if(se.test(a)){for(u=u||p.appendChild(t.createElement("div")),c=(oe.exec(a)||["",""])[1].toLowerCase(),f=he[c]||he._default,u.innerHTML=f[1]+a.replace(ie,"<$1></$2>")+f[2],o=f[0];o--;)u=u.lastChild;if(!r.support.leadingWhitespace&&re.test(a)&&h.push(t.createTextNode(re.exec(a)[0])),!r.support.tbody)for(o=(a="table"!==c||ae.test(a)?"<table>"!==f[1]||ae.test(a)?0:u:u.firstChild)&&a.childNodes.length;o--;)r.nodeName(l=a.childNodes[o],"tbody")&&!l.childNodes.length&&a.removeChild(l);for(r.merge(h,u.childNodes),u.textContent="";u.firstChild;)u.removeChild(u.firstChild);u=p.lastChild}else h.push(t.createTextNode(a));for(u&&p.removeChild(u),r.support.appendChecked||r.grep(Hr(h,"input"),jr),g=0;a=h[g++];)if((!i||-1===r.inArray(a,i))&&(s=r.contains(a.ownerDocument,a),u=Hr(p.appendChild(a),"script"),s&&Fr(u),n))for(o=0;a=u[o++];)fe.test(a.type||"")&&n.push(a);return u=null,p},cleanData:function(e,t){for(var n,i,o,a,s=0,u=r.expando,l=r.cache,f=r.support.deleteExpando,d=r.event.special;null!=(n=e[s]);s++)if((t||r.acceptData(n))&&(a=(o=n[u])&&l[o])){if(a.events)for(i in a.events)d[i]?r.event.remove(n,i):r.removeEvent(n,i,a.handle);l[o]&&(delete l[o],f?delete n[u]:void 0!==n.removeAttribute?n.removeAttribute(u):n[u]=null,c.push(o))}}});var me,ve,ye,be=/alpha\([^)]*\)/i,xe=/opacity\s*=\s*([^)]*)/,we=/^(top|right|bottom|left)$/,_e=/^(none|table(?!-c[ea]).+)/,Te=/^margin/,ke=new RegExp("^("+v+")(.*)$","i"),Ee=new RegExp("^("+v+")(?!px)[a-z%]+$","i"),Se=new RegExp("^([+-])=("+v+")","i"),Ce={BODY:"block"},Ae={position:"absolute",visibility:"hidden",display:"block"},Ne={letterSpacing:0,fontWeight:400},Re=["Top","Right","Bottom","Left"],De=["Webkit","O","Moz","ms"];function qr(e,t){if(t in e)return t;for(var n=t.charAt(0).toUpperCase()+t.slice(1),r=t,i=De.length;i--;)if((t=De[i]+n)in e)return t;return r}function Ir(e,t){return e=t||e,"none"===r.css(e,"display")||!r.contains(e.ownerDocument,e)}function Mr(e,t){for(var n,i,o,a=[],s=0,u=e.length;s<u;s++)(i=e[s]).style&&(a[s]=r._data(i,"olddisplay"),n=i.style.display,t?(a[s]||"none"!==n||(i.style.display=""),""===i.style.display&&Ir(i)&&(a[s]=r._data(i,"olddisplay",zr(i.nodeName)))):a[s]||(o=Ir(i),(n&&"none"!==n||!o)&&r._data(i,"olddisplay",o?n:r.css(i,"display"))));for(s=0;s<u;s++)(i=e[s]).style&&(t&&"none"!==i.style.display&&""!==i.style.display||(i.style.display=t?a[s]||"":"none"));return e}function Br(e,t,n){var r=ke.exec(t);return r?Math.max(0,r[1]-(n||0))+(r[2]||"px"):t}function Pr(e,t,n,i,o){for(var a=n===(i?"border":"content")?4:"width"===t?1:0,s=0;a<4;a+=2)"margin"===n&&(s+=r.css(e,n+Re[a],!0,o)),i?("content"===n&&(s-=r.css(e,"padding"+Re[a],!0,o)),"margin"!==n&&(s-=r.css(e,"border"+Re[a]+"Width",!0,o))):(s+=r.css(e,"padding"+Re[a],!0,o),"padding"!==n&&(s+=r.css(e,"border"+Re[a]+"Width",!0,o)));return s}function Ur(e,t,n){var i=!0,o="width"===t?e.offsetWidth:e.offsetHeight,a=ve(e),s=r.support.boxSizing&&"border-box"===r.css(e,"boxSizing",!1,a);if(o<=0||null==o){if(((o=ye(e,t,a))<0||null==o)&&(o=e.style[t]),Ee.test(o))return o;i=s&&(r.support.boxSizingReliable||o===e.style[t]),o=parseFloat(o)||0}return o+Pr(e,t,n||(s?"border":"content"),i,a)+"px"}function zr(e){var t=a,n=Ce[e];return n||("none"!==(n=$r(e,t))&&n||((t=((me=(me||r("<iframe frameborder='0' width='0' height='0'/>").css("cssText","display:block !important")).appendTo(t.documentElement))[0].contentWindow||me[0].contentDocument).document).write("<!doctype html><html><body>"),t.close(),n=$r(e,t),me.detach()),Ce[e]=n),n}function $r(e,t){var n=r(t.createElement(e)).appendTo(t.body),i=r.css(n[0],"display");return n.remove(),i}r.fn.extend({css:function(e,t){return r.access(this,(function(e,t,n){var i,o,a={},s=0;if(r.isArray(t)){for(o=ve(e),i=t.length;s<i;s++)a[t[s]]=r.css(e,t[s],!1,o);return a}return void 0!==n?r.style(e,t,n):r.css(e,t)}),e,t,arguments.length>1)},show:function(){return Mr(this,!0)},hide:function(){return Mr(this)},toggle:function(e){var t="boolean"==typeof e;return this.each((function(){(t?e:Ir(this))?r(this).show():r(this).hide()}))}}),r.extend({cssHooks:{opacity:{get:function(e,t){if(t){var n=ye(e,"opacity");return""===n?"1":n}}}},cssNumber:{columnCount:!0,fillOpacity:!0,fontWeight:!0,lineHeight:!0,opacity:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{float:r.support.cssFloat?"cssFloat":"styleFloat"},style:function(e,t,n,i){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var o,a,s,u=r.camelCase(t),c=e.style;if(t=r.cssProps[u]||(r.cssProps[u]=qr(c,u)),s=r.cssHooks[t]||r.cssHooks[u],void 0===n)return s&&"get"in s&&void 0!==(o=s.get(e,!1,i))?o:c[t];if("string"==(a=typeof n)&&(o=Se.exec(n))&&(n=(o[1]+1)*o[2]+parseFloat(r.css(e,t)),a="number"),!(null==n||"number"===a&&isNaN(n)||("number"!==a||r.cssNumber[u]||(n+="px"),r.support.clearCloneStyle||""!==n||0!==t.indexOf("background")||(c[t]="inherit"),s&&"set"in s&&void 0===(n=s.set(e,n,i)))))try{c[t]=n}catch(e){}}},css:function(e,t,n,i){var o,a,s,u=r.camelCase(t);return t=r.cssProps[u]||(r.cssProps[u]=qr(e.style,u)),(s=r.cssHooks[t]||r.cssHooks[u])&&"get"in s&&(a=s.get(e,!0,n)),void 0===a&&(a=ye(e,t,i)),"normal"===a&&t in Ne&&(a=Ne[t]),""===n||n?(o=parseFloat(a),!0===n||r.isNumeric(o)?o||0:a):a},swap:function(e,t,n,r){var i,o,a={};for(o in t)a[o]=e.style[o],e.style[o]=t[o];for(o in i=n.apply(e,r||[]),t)e.style[o]=a[o];return i}}),window.getComputedStyle?(ve=function(e){return window.getComputedStyle(e,null)},ye=function(e,t,n){var i,o,a,s=n||ve(e),u=s?s.getPropertyValue(t)||s[t]:void 0,c=e.style;return s&&(""!==u||r.contains(e.ownerDocument,e)||(u=r.style(e,t)),Ee.test(u)&&Te.test(t)&&(i=c.width,o=c.minWidth,a=c.maxWidth,c.minWidth=c.maxWidth=c.width=u,u=s.width,c.width=i,c.minWidth=o,c.maxWidth=a)),u}):a.documentElement.currentStyle&&(ve=function(e){return e.currentStyle},ye=function(e,t,n){var r,i,o,a=n||ve(e),s=a?a[t]:void 0,u=e.style;return null==s&&u&&u[t]&&(s=u[t]),Ee.test(s)&&!we.test(t)&&(r=u.left,(o=(i=e.runtimeStyle)&&i.left)&&(i.left=e.currentStyle.left),u.left="fontSize"===t?"1em":s,s=u.pixelLeft+"px",u.left=r,o&&(i.left=o)),""===s?"auto":s}),r.each(["height","width"],(function(e,t){r.cssHooks[t]={get:function(e,n,i){if(n)return 0===e.offsetWidth&&_e.test(r.css(e,"display"))?r.swap(e,Ae,(function(){return Ur(e,t,i)})):Ur(e,t,i)},set:function(e,n,i){var o=i&&ve(e);return Br(0,n,i?Pr(e,t,i,r.support.boxSizing&&"border-box"===r.css(e,"boxSizing",!1,o),o):0)}}})),r.support.opacity||(r.cssHooks.opacity={get:function(e,t){return xe.test((t&&e.currentStyle?e.currentStyle.filter:e.style.filter)||"")?.01*parseFloat(RegExp.$1)+"":t?"1":""},set:function(e,t){var n=e.style,i=e.currentStyle,o=r.isNumeric(t)?"alpha(opacity="+100*t+")":"",a=i&&i.filter||n.filter||"";n.zoom=1,(t>=1||""===t)&&""===r.trim(a.replace(be,""))&&n.removeAttribute&&(n.removeAttribute("filter"),""===t||i&&!i.filter)||(n.filter=be.test(a)?a.replace(be,o):a+" "+o)}}),r((function(){r.support.reliableMarginRight||(r.cssHooks.marginRight={get:function(e,t){if(t)return r.swap(e,{display:"inline-block"},ye,[e,"marginRight"])}}),!r.support.pixelPosition&&r.fn.position&&r.each(["top","left"],(function(e,t){r.cssHooks[t]={get:function(e,n){if(n)return n=ye(e,t),Ee.test(n)?r(e).position()[t]+"px":n}}}))})),r.expr&&r.expr.filters&&(r.expr.filters.hidden=function(e){return e.offsetWidth<=0&&e.offsetHeight<=0||!r.support.reliableHiddenOffsets&&"none"===(e.style&&e.style.display||r.css(e,"display"))},r.expr.filters.visible=function(e){return!r.expr.filters.hidden(e)}),r.each({margin:"",padding:"",border:"Width"},(function(e,t){r.cssHooks[e+t]={expand:function(n){for(var r=0,i={},o="string"==typeof n?n.split(" "):[n];r<4;r++)i[e+Re[r]+t]=o[r]||o[r-2]||o[0];return i}},Te.test(e)||(r.cssHooks[e+t].set=Br)}));var Fe=/%20/g,Le=/\[\]$/,Oe=/\r?\n/g,He=/^(?:submit|button|image|reset|file)$/i,je=/^(?:input|select|textarea|keygen)/i;function Wr(e,t,n,i){var o;if(r.isArray(t))r.each(t,(function(t,r){n||Le.test(e)?i(e,r):Wr(e+"["+("object"==typeof r?t:"")+"]",r,n,i)}));else if(n||"object"!==r.type(t))i(e,t);else for(o in t)Wr(e+"["+o+"]",t[o],n,i)}r.fn.extend({serialize:function(){return r.param(this.serializeArray())},serializeArray:function(){return this.map((function(){var e=r.prop(this,"elements");return e?r.makeArray(e):this})).filter((function(){var e=this.type;return this.name&&!r(this).is(":disabled")&&je.test(this.nodeName)&&!He.test(e)&&(this.checked||!ce.test(e))})).map((function(e,t){var n=r(this).val();return null==n?null:r.isArray(n)?r.map(n,(function(e){return{name:t.name,value:e.replace(Oe,"\r\n")}})):{name:t.name,value:n.replace(Oe,"\r\n")}})).get()}}),r.param=function(e,t){var n,i=[],o=function(e,t){t=r.isFunction(t)?t():null==t?"":t,i[i.length]=encodeURIComponent(e)+"="+encodeURIComponent(t)};if(void 0===t&&(t=r.ajaxSettings&&r.ajaxSettings.traditional),r.isArray(e)||e.jquery&&!r.isPlainObject(e))r.each(e,(function(){o(this.name,this.value)}));else for(n in e)Wr(n,e[n],t,o);return i.join("&").replace(Fe,"+")},r.each("blur focus focusin focusout load resize scroll unload click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup error contextmenu".split(" "),(function(e,t){r.fn[t]=function(e,n){return arguments.length>0?this.on(t,null,e,n):this.trigger(t)}})),r.fn.hover=function(e,t){return this.mouseenter(e).mouseleave(t||e)};var qe,Ie,Me=r.now(),Be=/\?/,Pe=/#.*$/,Ue=/([?&])_=[^&]*/,ze=/^(.*?):[ \t]*([^\r\n]*)\r?$/gm,$e=/^(?:GET|HEAD)$/,We=/^\/\//,Ve=/^([\w.+-]+:)(?:\/\/([^\/?#:]*)(?::(\d+)|)|)/,Xe=r.fn.load,Qe={},Ge={},Je="*/".concat("*");try{Ie=s.href}catch(ti){(Ie=a.createElement("a")).href="",Ie=Ie.href}function Vr(e){return function(t,n){"string"!=typeof t&&(n=t,t="*");var i,o=0,a=t.toLowerCase().match(y)||[];if(r.isFunction(n))for(;i=a[o++];)"+"===i[0]?(i=i.slice(1)||"*",(e[i]=e[i]||[]).unshift(n)):(e[i]=e[i]||[]).push(n)}}function Xr(e,t,n,i){var o={},a=e===Ge;function s(u){var c;return o[u]=!0,r.each(e[u]||[],(function(e,r){var u=r(t,n,i);return"string"!=typeof u||a||o[u]?a?!(c=u):void 0:(t.dataTypes.unshift(u),s(u),!1)})),c}return s(t.dataTypes[0])||!o["*"]&&s("*")}function Qr(e,t){var n,i,o=r.ajaxSettings.flatOptions||{};for(i in t)void 0!==t[i]&&((o[i]?e:n||(n={}))[i]=t[i]);return n&&r.extend(!0,e,n),e}qe=Ve.exec(Ie.toLowerCase())||[],r.fn.load=function(e,t,n){if("string"!=typeof e&&Xe)return Xe.apply(this,arguments);var i,o,a,s=this,u=e.indexOf(" ");return u>=0&&(i=e.slice(u,e.length),e=e.slice(0,u)),r.isFunction(t)?(n=t,t=void 0):t&&"object"==typeof t&&(a="POST"),s.length>0&&r.ajax({url:e,type:a,dataType:"html",data:t}).done((function(e){o=arguments,s.html(i?r("<div>").append(r.parseHTML(e)).find(i):e)})).complete(n&&function(e,t){s.each(n,o||[e.responseText,t,e])}),this},r.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],(function(e,t){r.fn[t]=function(e){return this.on(t,e)}})),r.each(["get","post"],(function(e,t){r[t]=function(e,n,i,o){return r.isFunction(n)&&(o=o||i,i=n,n=void 0),r.ajax({url:e,type:t,dataType:o,data:n,success:i})}})),r.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:Ie,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(qe[1]),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Je,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/xml/,html:/html/,json:/json/},responseFields:{xml:"responseXML",text:"responseText"},converters:{"* text":window.String,"text html":!0,"text json":r.parseJSON,"text xml":r.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?Qr(Qr(e,r.ajaxSettings),t):Qr(r.ajaxSettings,e)},ajaxPrefilter:Vr(Qe),ajaxTransport:Vr(Ge),ajax:function(e,t){"object"==typeof e&&(t=e,e=void 0),t=t||{};var n,i,o,a,s,u,c,l,f=r.ajaxSetup({},t),d=f.context||f,p=f.context&&(d.nodeType||d.jquery)?r(d):r.event,h=r.Deferred(),g=r.Callbacks("once memory"),m=f.statusCode||{},v={},b={},x=0,w="canceled",_={readyState:0,getResponseHeader:function(e){var t;if(2===x){if(!l)for(l={};t=ze.exec(a);)l[t[1].toLowerCase()]=t[2];t=l[e.toLowerCase()]}return null==t?null:t},getAllResponseHeaders:function(){return 2===x?a:null},setRequestHeader:function(e,t){var n=e.toLowerCase();return x||(e=b[n]=b[n]||e,v[e]=t),this},overrideMimeType:function(e){return x||(f.mimeType=e),this},statusCode:function(e){var t;if(e)if(x<2)for(t in e)m[t]=[m[t],e[t]];else _.always(e[_.status]);return this},abort:function(e){var t=e||w;return c&&c.abort(t),T(0,t),this}};if(h.promise(_).complete=g.add,_.success=_.done,_.error=_.fail,f.url=((e||f.url||Ie)+"").replace(Pe,"").replace(We,qe[1]+"//"),f.type=t.method||t.type||f.method||f.type,f.dataTypes=r.trim(f.dataType||"*").toLowerCase().match(y)||[""],null==f.crossDomain&&(n=Ve.exec(f.url.toLowerCase()),f.crossDomain=!(!n||n[1]===qe[1]&&n[2]===qe[2]&&(n[3]||("http:"===n[1]?80:443))==(qe[3]||("http:"===qe[1]?80:443)))),f.data&&f.processData&&"string"!=typeof f.data&&(f.data=r.param(f.data,f.traditional)),Xr(Qe,f,t,_),2===x)return _;for(i in(u=f.global)&&0==r.active++&&r.event.trigger("ajaxStart"),f.type=f.type.toUpperCase(),f.hasContent=!$e.test(f.type),o=f.url,f.hasContent||(f.data&&(o=f.url+=(Be.test(o)?"&":"?")+f.data,delete f.data),!1===f.cache&&(f.url=Ue.test(o)?o.replace(Ue,"$1_="+Me++):o+(Be.test(o)?"&":"?")+"_="+Me++)),f.ifModified&&(r.lastModified[o]&&_.setRequestHeader("If-Modified-Since",r.lastModified[o]),r.etag[o]&&_.setRequestHeader("If-None-Match",r.etag[o])),(f.data&&f.hasContent&&!1!==f.contentType||t.contentType)&&_.setRequestHeader("Content-Type",f.contentType),_.setRequestHeader("Accept",f.dataTypes[0]&&f.accepts[f.dataTypes[0]]?f.accepts[f.dataTypes[0]]+("*"!==f.dataTypes[0]?", "+Je+"; q=0.01":""):f.accepts["*"]),f.headers)_.setRequestHeader(i,f.headers[i]);if(f.beforeSend&&(!1===f.beforeSend.call(d,_,f)||2===x))return _.abort();for(i in w="abort",{success:1,error:1,complete:1})_[i](f[i]);if(c=Xr(Ge,f,t,_)){_.readyState=1,u&&p.trigger("ajaxSend",[_,f]),f.async&&f.timeout>0&&(s=setTimeout((function(){_.abort("timeout")}),f.timeout));try{x=1,c.send(v,T)}catch(e){if(!(x<2))throw e;T(-1,e)}}else T(-1,"No Transport");function T(e,t,n,i){var l,v,y,b,w,T=t;2!==x&&(x=2,s&&clearTimeout(s),c=void 0,a=i||"",_.readyState=e>0?4:0,n&&(b=function(e,t,n){var r,i,o,a,s=e.contents,u=e.dataTypes,c=e.responseFields;for(a in c)a in n&&(t[c[a]]=n[a]);for(;"*"===u[0];)u.shift(),void 0===i&&(i=e.mimeType||t.getResponseHeader("Content-Type"));if(i)for(a in s)if(s[a]&&s[a].test(i)){u.unshift(a);break}if(u[0]in n)o=u[0];else{for(a in n){if(!u[0]||e.converters[a+" "+u[0]]){o=a;break}r||(r=a)}o=o||r}if(o)return o!==u[0]&&u.unshift(o),n[o]}(f,_,n)),e>=200&&e<300||304===e?(f.ifModified&&((w=_.getResponseHeader("Last-Modified"))&&(r.lastModified[o]=w),(w=_.getResponseHeader("etag"))&&(r.etag[o]=w)),204===e?(l=!0,T="nocontent"):304===e?(l=!0,T="notmodified"):(l=function(e,t){var n,r,i,o,a={},s=0,u=e.dataTypes.slice(),c=u[0];if(e.dataFilter&&(t=e.dataFilter(t,e.dataType)),u[1])for(i in e.converters)a[i.toLowerCase()]=e.converters[i];for(;r=u[++s];)if("*"!==r){if("*"!==c&&c!==r){if(!(i=a[c+" "+r]||a["* "+r]))for(n in a)if((o=n.split(" "))[1]===r&&(i=a[c+" "+o[0]]||a["* "+o[0]])){!0===i?i=a[n]:!0!==a[n]&&(r=o[0],u.splice(s--,0,r));break}if(!0!==i)if(i&&e.throws)t=i(t);else try{t=i(t)}catch(e){return{state:"parsererror",error:i?e:"No conversion from "+c+" to "+r}}}c=r}return{state:"success",data:t}}(f,b),T=l.state,v=l.data,l=!(y=l.error))):(y=T,!e&&T||(T="error",e<0&&(e=0))),_.status=e,_.statusText=(t||T)+"",l?h.resolveWith(d,[v,T,_]):h.rejectWith(d,[_,T,y]),_.statusCode(m),m=void 0,u&&p.trigger(l?"ajaxSuccess":"ajaxError",[_,f,l?v:y]),g.fireWith(d,[_,T]),u&&(p.trigger("ajaxComplete",[_,f]),--r.active||r.event.trigger("ajaxStop")))}return _},getScript:function(e,t){return r.get(e,void 0,t,"script")},getJSON:function(e,t,n){return r.get(e,t,n,"json")}}),r.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/(?:java|ecma)script/},converters:{"text script":function(e){return r.globalEval(e),e}}}),r.ajaxPrefilter("script",(function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET",e.global=!1)})),r.ajaxTransport("script",(function(e){if(e.crossDomain){var t,n=a.head||r("head")[0]||a.documentElement;return{send:function(r,i){(t=a.createElement("script")).async=!0,e.scriptCharset&&(t.charset=e.scriptCharset),t.src=e.url,t.onload=t.onreadystatechange=function(e,n){(n||!t.readyState||/loaded|complete/.test(t.readyState))&&(t.onload=t.onreadystatechange=null,t.parentNode&&t.parentNode.removeChild(t),t=null,n||i(200,"success"))},n.insertBefore(t,n.firstChild)},abort:function(){t&&t.onload(void 0,!0)}}}}));var Ye=[],Ke=/(=)\?(?=&|$)|\?\?/;r.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=Ye.pop()||r.expando+"_"+Me++;return this[e]=!0,e}}),r.ajaxPrefilter("json jsonp",(function(e,t,n){var i,o,a,s=!1!==e.jsonp&&(Ke.test(e.url)?"url":"string"==typeof e.data&&!(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&Ke.test(e.data)&&"data");if(s||"jsonp"===e.dataTypes[0])return i=e.jsonpCallback=r.isFunction(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,s?e[s]=e[s].replace(Ke,"$1"+i):!1!==e.jsonp&&(e.url+=(Be.test(e.url)?"&":"?")+e.jsonp+"="+i),e.converters["script json"]=function(){return a||r.error(i+" was not called"),a[0]},e.dataTypes[0]="json",o=window[i],window[i]=function(){a=arguments},n.always((function(){window[i]=o,e[i]&&(e.jsonpCallback=t.jsonpCallback,Ye.push(i)),a&&r.isFunction(o)&&o(a[0]),a=o=void 0})),"script"}));var Ze,et,tt=0,nt=window.ActiveXObject&&function(){var e;for(e in Ze)Ze[e](void 0,!0)};function Gr(){try{return new window.XMLHttpRequest}catch(e){}}r.ajaxSettings.xhr=window.ActiveXObject?function(){return!this.isLocal&&Gr()||function(){try{return new window.ActiveXObject("Microsoft.XMLHTTP")}catch(e){}}()}:Gr,et=r.ajaxSettings.xhr(),r.support.cors=!!et&&"withCredentials"in et,(et=r.support.ajax=!!et)&&r.ajaxTransport((function(e){var t;if(!e.crossDomain||r.support.cors)return{send:function(n,i){var o,a,s=e.xhr();if(e.username?s.open(e.type,e.url,e.async,e.username,e.password):s.open(e.type,e.url,e.async),e.xhrFields)for(a in e.xhrFields)s[a]=e.xhrFields[a];e.mimeType&&s.overrideMimeType&&s.overrideMimeType(e.mimeType),e.crossDomain||n["X-Requested-With"]||(n["X-Requested-With"]="XMLHttpRequest");try{for(a in n)s.setRequestHeader(a,n[a])}catch(e){}s.send(e.hasContent&&e.data||null),t=function(n,a){var u,c,l,f;try{if(t&&(a||4===s.readyState))if(t=void 0,o&&(s.onreadystatechange=r.noop,nt&&delete Ze[o]),a)4!==s.readyState&&s.abort();else{f={},u=s.status,c=s.getAllResponseHeaders(),"string"==typeof s.responseText&&(f.text=s.responseText);try{l=s.statusText}catch(e){l=""}u||!e.isLocal||e.crossDomain?1223===u&&(u=204):u=f.text?200:404}}catch(e){a||i(-1,e)}f&&i(u,l,f,c)},e.async?4===s.readyState?setTimeout(t):(o=++tt,nt&&(Ze||(Ze={},r(window).unload(nt)),Ze[o]=t),s.onreadystatechange=t):t()},abort:function(){t&&t(void 0,!0)}}}));var rt,it,ot=/^(?:toggle|show|hide)$/,at=new RegExp("^(?:([+-])=|)("+v+")([a-z%]*)$","i"),st=/queueHooks$/,ut=[function(e,t,n){var i,o,a,s,u,c,l,f,d,p=this,h=e.style,g={},m=[],v=e.nodeType&&Ir(e);for(o in n.queue||(null==(f=r._queueHooks(e,"fx")).unqueued&&(f.unqueued=0,d=f.empty.fire,f.empty.fire=function(){f.unqueued||d()}),f.unqueued++,p.always((function(){p.always((function(){f.unqueued--,r.queue(e,"fx").length||f.empty.fire()}))}))),1===e.nodeType&&("height"in t||"width"in t)&&(n.overflow=[h.overflow,h.overflowX,h.overflowY],"inline"===r.css(e,"display")&&"none"===r.css(e,"float")&&(r.support.inlineBlockNeedsLayout&&"inline"!==zr(e.nodeName)?h.zoom=1:h.display="inline-block")),n.overflow&&(h.overflow="hidden",r.support.shrinkWrapBlocks||p.always((function(){h.overflow=n.overflow[0],h.overflowX=n.overflow[1],h.overflowY=n.overflow[2]}))),t)if(s=t[o],ot.exec(s)){if(delete t[o],c=c||"toggle"===s,s===(v?"hide":"show"))continue;m.push(o)}if(a=m.length){"hidden"in(u=r._data(e,"fxshow")||r._data(e,"fxshow",{}))&&(v=u.hidden),c&&(u.hidden=!v),v?r(e).show():p.done((function(){r(e).hide()})),p.done((function(){var t;for(t in r._removeData(e,"fxshow"),g)r.style(e,t,g[t])}));for(o=0;o<a;o++)i=m[o],l=p.createTween(i,v?u[i]:0),g[i]=u[i]||r.style(e,i),i in u||(u[i]=l.start,v&&(l.end=l.start,l.start="width"===i||"height"===i?1:0))}}],ct={"*":[function(e,t){var n,i,o=this.createTween(e,t),a=at.exec(t),s=o.cur(),u=+s||0,c=1,l=20;if(a){if(n=+a[2],"px"!==(i=a[3]||(r.cssNumber[e]?"":"px"))&&u){u=r.css(o.elem,e,!0)||n||1;do{u/=c=c||".5",r.style(o.elem,e,u+i)}while(c!==(c=o.cur()/s)&&1!==c&&--l)}o.unit=i,o.start=u,o.end=a[1]?u+(a[1]+1)*n:n}return o}]};function Jr(){return setTimeout((function(){rt=void 0})),rt=r.now()}function Yr(e,t,n){var i,o,a=0,s=ut.length,u=r.Deferred().always((function(){delete c.elem})),c=function(){if(o)return!1;for(var t=rt||Jr(),n=Math.max(0,l.startTime+l.duration-t),r=1-(n/l.duration||0),i=0,a=l.tweens.length;i<a;i++)l.tweens[i].run(r);return u.notifyWith(e,[l,r,n]),r<1&&a?n:(u.resolveWith(e,[l]),!1)},l=u.promise({elem:e,props:r.extend({},t),opts:r.extend(!0,{specialEasing:{}},n),originalProperties:t,originalOptions:n,startTime:rt||Jr(),duration:n.duration,tweens:[],createTween:function(t,n){var i=r.Tween(e,l.opts,t,n,l.opts.specialEasing[t]||l.opts.easing);return l.tweens.push(i),i},stop:function(t){var n=0,r=t?l.tweens.length:0;if(o)return this;for(o=!0;n<r;n++)l.tweens[n].run(1);return t?u.resolveWith(e,[l,t]):u.rejectWith(e,[l,t]),this}}),f=l.props;for(function(e,t){var n,i,o,a,s;for(o in e)if(a=t[i=r.camelCase(o)],n=e[o],r.isArray(n)&&(a=n[1],n=e[o]=n[0]),o!==i&&(e[i]=n,delete e[o]),(s=r.cssHooks[i])&&"expand"in s)for(o in n=s.expand(n),delete e[i],n)o in e||(e[o]=n[o],t[o]=a);else t[i]=a}(f,l.opts.specialEasing);a<s;a++)if(i=ut[a].call(l,e,f,l.opts))return i;return function(e,t){r.each(t,(function(t,n){for(var r=(ct[t]||[]).concat(ct["*"]),i=0,o=r.length;i<o;i++)if(r[i].call(e,t,n))return}))}(l,f),r.isFunction(l.opts.start)&&l.opts.start.call(e,l),r.fx.timer(r.extend(c,{elem:e,anim:l,queue:l.opts.queue})),l.progress(l.opts.progress).done(l.opts.done,l.opts.complete).fail(l.opts.fail).always(l.opts.always)}function Kr(e,t,n,r,i){return new Kr.prototype.init(e,t,n,r,i)}function Zr(e,t){var n,r={height:e},i=0;for(t=t?1:0;i<4;i+=2-t)r["margin"+(n=Re[i])]=r["padding"+n]=e;return t&&(r.opacity=r.width=e),r}function ei(e){return r.isWindow(e)?e:9===e.nodeType&&(e.defaultView||e.parentWindow)}r.Animation=r.extend(Yr,{tweener:function(e,t){r.isFunction(e)?(t=e,e=["*"]):e=e.split(" ");for(var n,i=0,o=e.length;i<o;i++)n=e[i],ct[n]=ct[n]||[],ct[n].unshift(t)},prefilter:function(e,t){t?ut.unshift(e):ut.push(e)}}),r.Tween=Kr,Kr.prototype={constructor:Kr,init:function(e,t,n,i,o,a){this.elem=e,this.prop=n,this.easing=o||"swing",this.options=t,this.start=this.now=this.cur(),this.end=i,this.unit=a||(r.cssNumber[n]?"":"px")},cur:function(){var e=Kr.propHooks[this.prop];return e&&e.get?e.get(this):Kr.propHooks._default.get(this)},run:function(e){var t,n=Kr.propHooks[this.prop];return this.options.duration?this.pos=t=r.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=t=e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):Kr.propHooks._default.set(this),this}},Kr.prototype.init.prototype=Kr.prototype,Kr.propHooks={_default:{get:function(e){var t;return null==e.elem[e.prop]||e.elem.style&&null!=e.elem.style[e.prop]?(t=r.css(e.elem,e.prop,""))&&"auto"!==t?t:0:e.elem[e.prop]},set:function(e){r.fx.step[e.prop]?r.fx.step[e.prop](e):e.elem.style&&(null!=e.elem.style[r.cssProps[e.prop]]||r.cssHooks[e.prop])?r.style(e.elem,e.prop,e.now+e.unit):e.elem[e.prop]=e.now}}},Kr.propHooks.scrollTop=Kr.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},r.each(["toggle","show","hide"],(function(e,t){var n=r.fn[t];r.fn[t]=function(e,r,i){return null==e||"boolean"==typeof e?n.apply(this,arguments):this.animate(Zr(t,!0),e,r,i)}})),r.fn.extend({fadeTo:function(e,t,n,r){return this.filter(Ir).css("opacity",0).show().end().animate({opacity:t},e,n,r)},animate:function(e,t,n,i){var o=r.isEmptyObject(e),a=r.speed(t,n,i),s=function(){var t=Yr(this,r.extend({},e),a);s.finish=function(){t.stop(!0)},(o||r._data(this,"finish"))&&t.stop(!0)};return s.finish=s,o||!1===a.queue?this.each(s):this.queue(a.queue,s)},stop:function(e,t,n){var i=function(e){var t=e.stop;delete e.stop,t(n)};return"string"!=typeof e&&(n=t,t=e,e=void 0),t&&!1!==e&&this.queue(e||"fx",[]),this.each((function(){var t=!0,o=null!=e&&e+"queueHooks",a=r.timers,s=r._data(this);if(o)s[o]&&s[o].stop&&i(s[o]);else for(o in s)s[o]&&s[o].stop&&st.test(o)&&i(s[o]);for(o=a.length;o--;)a[o].elem!==this||null!=e&&a[o].queue!==e||(a[o].anim.stop(n),t=!1,a.splice(o,1));!t&&n||r.dequeue(this,e)}))},finish:function(e){return!1!==e&&(e=e||"fx"),this.each((function(){var t,n=r._data(this),i=n[e+"queue"],o=n[e+"queueHooks"],a=r.timers,s=i?i.length:0;for(n.finish=!0,r.queue(this,e,[]),o&&o.cur&&o.cur.finish&&o.cur.finish.call(this),t=a.length;t--;)a[t].elem===this&&a[t].queue===e&&(a[t].anim.stop(!0),a.splice(t,1));for(t=0;t<s;t++)i[t]&&i[t].finish&&i[t].finish.call(this);delete n.finish}))}}),r.each({slideDown:Zr("show"),slideUp:Zr("hide"),slideToggle:Zr("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},(function(e,t){r.fn[e]=function(e,n,r){return this.animate(t,e,n,r)}})),r.speed=function(e,t,n){var i=e&&"object"==typeof e?r.extend({},e):{complete:n||!n&&t||r.isFunction(e)&&e,duration:e,easing:n&&t||t&&!r.isFunction(t)&&t};return i.duration=r.fx.off?0:"number"==typeof i.duration?i.duration:i.duration in r.fx.speeds?r.fx.speeds[i.duration]:r.fx.speeds._default,null!=i.queue&&!0!==i.queue||(i.queue="fx"),i.old=i.complete,i.complete=function(){r.isFunction(i.old)&&i.old.call(this),i.queue&&r.dequeue(this,i.queue)},i},r.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2}},r.timers=[],r.fx=Kr.prototype.init,r.fx.tick=function(){var e,t=r.timers,n=0;for(rt=r.now();n<t.length;n++)(e=t[n])()||t[n]!==e||t.splice(n--,1);t.length||r.fx.stop(),rt=void 0},r.fx.timer=function(e){e()&&r.timers.push(e)&&r.fx.start()},r.fx.interval=13,r.fx.start=function(){it||(it=setInterval(r.fx.tick,r.fx.interval))},r.fx.stop=function(){clearInterval(it),it=null},r.fx.speeds={slow:600,fast:200,_default:400},r.fx.step={},r.expr&&r.expr.filters&&(r.expr.filters.animated=function(e){return r.grep(r.timers,(function(t){return e===t.elem})).length}),r.fn.offset=function(e){if(arguments.length)return void 0===e?this:this.each((function(t){r.offset.setOffset(this,e,t)}));var t,n,i={top:0,left:0},o=this[0],a=o&&o.ownerDocument;return a?(t=a.documentElement,r.contains(t,o)?(void 0!==o.getBoundingClientRect&&(i=o.getBoundingClientRect()),n=ei(a),{top:i.top+(n.pageYOffset||t.scrollTop)-(t.clientTop||0),left:i.left+(n.pageXOffset||t.scrollLeft)-(t.clientLeft||0)}):i):void 0},r.offset={setOffset:function(e,t,n){var i=r.css(e,"position");"static"===i&&(e.style.position="relative");var o,a,s=r(e),u=s.offset(),c=r.css(e,"top"),l=r.css(e,"left"),f={},d={};("absolute"===i||"fixed"===i)&&r.inArray("auto",[c,l])>-1?(o=(d=s.position()).top,a=d.left):(o=parseFloat(c)||0,a=parseFloat(l)||0),r.isFunction(t)&&(t=t.call(e,n,u)),null!=t.top&&(f.top=t.top-u.top+o),null!=t.left&&(f.left=t.left-u.left+a),"using"in t?t.using.call(e,f):s.css(f)}},r.fn.extend({position:function(){if(this[0]){var e,t,n={top:0,left:0},i=this[0];return"fixed"===r.css(i,"position")?t=i.getBoundingClientRect():(e=this.offsetParent(),t=this.offset(),r.nodeName(e[0],"html")||(n=e.offset()),n.top+=r.css(e[0],"borderTopWidth",!0),n.left+=r.css(e[0],"borderLeftWidth",!0)),{top:t.top-n.top-r.css(i,"marginTop",!0),left:t.left-n.left-r.css(i,"marginLeft",!0)}}},offsetParent:function(){return this.map((function(){for(var e=this.offsetParent||a.documentElement;e&&!r.nodeName(e,"html")&&"static"===r.css(e,"position");)e=e.offsetParent;return e||a.documentElement}))}}),r.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},(function(e,t){var n=/Y/.test(t);r.fn[e]=function(i){return r.access(this,(function(e,i,o){var a=ei(e);if(void 0===o)return a?t in a?a[t]:a.document.documentElement[i]:e[i];a?a.scrollTo(n?r(a).scrollLeft():o,n?o:r(a).scrollTop()):e[i]=o}),e,i,arguments.length,null)}})),r.each({Height:"height",Width:"width"},(function(e,t){r.each({padding:"inner"+e,content:t,"":"outer"+e},(function(n,i){r.fn[i]=function(i,o){var a=arguments.length&&(n||"boolean"!=typeof i),s=n||(!0===i||!0===o?"margin":"border");return r.access(this,(function(t,n,i){var o;return r.isWindow(t)?t.document.documentElement["client"+e]:9===t.nodeType?(o=t.documentElement,Math.max(t.body["scroll"+e],o["scroll"+e],t.body["offset"+e],o["offset"+e],o["client"+e])):void 0===i?r.css(t,n,s):r.style(t,n,i,s)}),t,a?i:void 0,a,null)}}))}))}catch(ni){}var lt,ft=r,dt={Deferred:ft.Deferred,when:ft.when,isPromise:function(e){return e&&"function"==typeof e.then}},pt=[].slice,ht=/\s+/;function gt(e,t,n,r){return ft.grep(e,(function(e){return e&&(!t||e.e===t)&&(!n||e.cb===n||e.cb._cb===n)&&(!r||e.ctx===r)}))}function mt(e,t,n){ft.each((e||"").split(ht),(function(e,r){n(r,t)}))}function vt(e,t){for(var n,r=!1,i=-1,o=e.length;++i<o;)if(!1===(n=e[i]).cb.apply(n.ctx2,t)){r=!0;break}return!r}lt={on:function(e,t,n){var r,i=this;return t?(r=this._events||(this._events=[]),mt(e,t,(function(e,t){var o={e};o.cb=t,o.ctx=n,o.ctx2=n||i,o.id=r.length,r.push(o)})),this):this},once:function(e,t,n){var r=this;return t?(mt(e,t,(function(e,t){var i=function(){return r.off(e,i),t.apply(n||r,arguments)};i._cb=t,r.on(e,i,n)})),r):r},off:function(e,t,n){var r=this._events;return r?e||t||n?(mt(e,t,(function(e,t){ft.each(gt(r,e,t,n),(function(){delete r[this.id]}))})),this):(this._events=[],this):this},trigger:function(e){var t,n,r;return this._events&&e?(t=pt.call(arguments,1),n=gt(this._events,e),r=gt(this._events,"all"),vt(n,t)&&vt(r,arguments)):this}};var yt=ft.extend({installTo:function(e){return ft.extend(e,lt)}},lt),bt=0,xt=/\.([^.]+)$/,wt={};function _t(e){this.name=e.name||"Untitled",this.size=e.size||0,this.type=e.type||"application/octet-stream",this.lastModifiedDate=e.lastModifiedDate||1*new Date,this.id="WU_FILE_"+bt++,this.ext=xt.exec(this.name)?RegExp.$1:"",this.statusText="",wt[this.id]=_t.Status.INITED,this.source=e,this.loaded=0,this.on("error",(function(e){this.setStatus(_t.Status.ERROR,e)}))}ft.extend(_t.prototype,{setStatus:function(e,t){var n=wt[this.id];void 0!==t&&(this.statusText=t),e!==n&&(wt[this.id]=e,this.trigger("statuschange",e,n))},getStatus:function(){return wt[this.id]},getSource:function(){return this.source},destroy:function(){this.off(),delete wt[this.id]}}),yt.installTo(_t.prototype),_t.Status={INITED:"inited",QUEUED:"queued",PROGRESS:"progress",ERROR:"error",COMPLETE:"complete",CANCELLED:"cancelled",INTERRUPT:"interrupt",INVALID:"invalid"};var Tt=function(){},kt=Function.call;function Et(e,t){return function(){return e.apply(t,arguments)}}var St,Ct,At,Nt,Rt,Dt,Ft,Lt,Ot,Ht,jt={version:"@version@",$:ft,Deferred:dt.Deferred,isPromise:dt.isPromise,WUFILE:_t,when:dt.when,browser:(At=navigator.userAgent,Nt={},Rt=At.match(/WebKit\/([\d.]+)/),Dt=At.match(/Chrome\/([\d.]+)/)||At.match(/CriOS\/([\d.]+)/),Ft=At.match(/MSIE\s([\d\.]+)/)||At.match(/(?:trident)(?:.*rv:([\w.]+))?/i),Lt=At.match(/Firefox\/([\d.]+)/),Ot=At.match(/Safari\/([\d.]+)/),Ht=At.match(/OPR\/([\d.]+)/),Rt&&(Nt.webkit=parseFloat(Rt[1])),Dt&&(Nt.chrome=parseFloat(Dt[1])),Ft&&(Nt.ie=parseFloat(Ft[1])),Lt&&(Nt.firefox=parseFloat(Lt[1])),Ot&&(Nt.safari=parseFloat(Ot[1])),Ht&&(Nt.opera=parseFloat(Ht[1])),Nt),os:function(e){var t={},n=e.match(/(?:Android);?[\s\/]+([\d.]+)?/),r=e.match(/(?:iPad|iPod|iPhone).*OS\s([\d_]+)/);return n&&(t.android=parseFloat(n[1])),r&&(t.ios=parseFloat(r[1].replace(/_/g,"."))),t}(navigator.userAgent),inherits:function(e,t,n){var r,i,o;return"function"==typeof t?(r=t,t=null):r=t&&t.hasOwnProperty("constructor")?t.constructor:function(){return e.apply(this,arguments)},ft.extend(!0,r,e,n||{}),r.__super__=e.prototype,r.prototype=(i=e.prototype,Object.create?Object.create(i):((o=function(){}).prototype=i,new o)),t&&ft.extend(!0,r.prototype,t),r},noop:Tt,bindFn:Et,log:window.console?Et(console.log,console):Tt,nextTick:function(e){setTimeout(e,1)},slice:(Ct=[].slice,function(){return kt.apply(Ct,arguments)}),guid:(St=0,function(e){for(var t=(+new Date).toString(32),n=0;n<5;n++)t+=Math.floor(65535*Math.random()).toString(32);return(e||"wu_")+t+(St++).toString(32)}),formatSize:function(e,t,n){var r;for(n=n||["B","K","M","G","TB"];(r=n.shift())&&e>1024;)e/=1024;return("B"===r?e:e.toFixed(t||2))+r}},qt=jt.$;function It(e){this.options=qt.extend(!0,{},It.options,e),this._init(this.options)}It.options={},yt.installTo(It.prototype),qt.each({upload:"start-upload",stop:"stop-upload",getFile:"get-file",getFiles:"get-files",addFile:"add-file",addFiles:"add-file",sort:"sort-files",removeFile:"remove-file",cancelFile:"cancel-file",skipFile:"skip-file",retry:"retry",isInProgress:"is-in-progress",makeThumb:"make-thumb",md5File:"md5-file",getDimension:"get-dimension",addButton:"add-btn",predictRuntimeType:"predict-runtime-type",refresh:"refresh",disable:"disable",enable:"enable",reset:"reset"},(function(e,t){It.prototype[e]=function(){return this.request(t,arguments)}})),qt.extend(It.prototype,{state:"pending",_init:function(e){var t=this;t.request("init",e,(function(){t.state="ready",t.trigger("ready")}))},option:function(e,t){var n=this.options;if(!(arguments.length>1))return e?n[e]:n;qt.isPlainObject(t)&&qt.isPlainObject(n[e])?qt.extend(n[e],t):n[e]=t},getStats:function(){var e=this.request("get-stats");return e?{successNum:e.numOfSuccess,progressNum:e.numOfProgress,cancelNum:e.numOfCancel,invalidNum:e.numOfInvalid,uploadFailNum:e.numOfUploadFailed,queueNum:e.numOfQueue,interruptNum:e.numOfInterrupt}:{}},trigger:function(e){var t=[].slice.call(arguments,1),n=this.options,r="on"+e.substring(0,1).toUpperCase()+e.substring(1);return!(!1===yt.trigger.apply(this,arguments)||qt.isFunction(n[r])&&!1===n[r].apply(this,t)||qt.isFunction(this[r])&&!1===this[r].apply(this,t)||!1===yt.trigger.apply(yt,[this,e].concat(t)))},destroy:function(){this.request("destroy",arguments),this.off()},request:jt.noop}),jt.create=It.create=function(e){return new It(e)},jt.Uploader=It;var Mt,Bt,Pt=jt.$,Ut={},zt=function(e){for(var t in e)if(e.hasOwnProperty(t))return t;return null};function $t(e){this.options=Pt.extend({container:document.body},e),this.uid=jt.guid("rt_")}function Wt(e,t){var n,r,i=jt.Deferred();this.uid=jt.guid("client_"),this.runtimeReady=function(e){return i.done(e)},this.connectRuntime=function(e,r){if(n)throw new Error("already connected!");return i.done(r),"string"==typeof e&&Mt.get(e)&&(n=Mt.get(e)),(n=n||Mt.get(null,t))?(jt.$.extend(n.options,e),n.__promise.then(i.resolve),n.__client++):((n=$t.create(e,e.runtimeOrder)).__promise=i.promise(),n.once("ready",i.resolve),n.init(),Mt.add(n),n.__client=1),t&&(n.__standalone=t),n},this.getRuntime=function(){return n},this.disconnectRuntime=function(){n&&(n.__client--,n.__client<=0&&(Mt.remove(n),delete n.__promise,n.destroy()),n=null)},this.exec=function(){if(n){var t=jt.slice(arguments);return e&&t.unshift(e),n.exec.apply(this,t)}},this.getRuid=function(){return n&&n.uid},this.destroy=(r=this.destroy,function(){r&&r.apply(this,arguments),this.trigger("destroy"),this.off(),this.exec("destroy"),this.disconnectRuntime()})}Pt.extend($t.prototype,{getContainer:function(){var e,t,n=this.options;return this._container?this._container:(e=Pt(n.container||document.body),(t=Pt(document.createElement("div"))).attr("id","rt_"+this.uid),t.css({position:"absolute",top:"0px",left:"0px",width:"1px",height:"1px",overflow:"hidden"}),e.append(t),e.addClass("webuploader-container"),this._container=t,this._parent=e,t)},init:jt.noop,exec:jt.noop,destroy:function(){this._container&&this._container.remove(),this._parent&&this._parent.removeClass("webuploader-container"),this.off()}}),$t.orders="html5,flash",$t.addRuntime=function(e,t){Ut[e]=t},$t.hasRuntime=function(e){return!!(e?Ut[e]:zt(Ut))},$t.create=function(e,t){var n;if(t=t||$t.orders,Pt.each(t.split(/\s*,\s*/g),(function(){if(Ut[this])return n=this,!1})),!(n=n||zt(Ut)))throw new Error("Runtime Error");return new Ut[n](e)},yt.installTo($t.prototype),Bt={},Mt={add:function(e){Bt[e.uid]=e},get:function(e,t){var n;if(e)return Bt[e];for(n in Bt)if(!t||!Bt[n].__standalone)return Bt[n];return null},remove:function(e){delete Bt[e.uid]}},yt.installTo(Wt.prototype);var Vt=jt.$;function Xt(e){(e=this.options=Vt.extend({},Xt.options,e)).container=Vt(e.container),e.container.length&&Wt.call(this,"DragAndDrop")}Xt.options={accept:null,disableGlobalDnd:!1},jt.inherits(Wt,{constructor:Xt,init:function(){var e=this;e.connectRuntime(e.options,(function(){e.exec("init"),e.trigger("ready")}))}}),yt.installTo(Xt.prototype);var Qt=jt.$,Gt=It.prototype._init,Jt=It.prototype.destroy,Yt={},Kt=[];function Zt(e){this.owner=e,this.options=e.options}Qt.extend(Zt.prototype,{init:jt.noop,invoke:function(e,t){var n=this.responseMap;return n&&e in n&&n[e]in this&&Qt.isFunction(this[n[e]])?this[n[e]].apply(this,t):Yt},request:function(){return this.owner.request.apply(this.owner,arguments)}}),Qt.extend(It.prototype,{_init:function(){var e=this,t=e._widgets=[],n=e.options.disableWidgets||"";return Qt.each(Kt,(function(r,i){(!n||!~n.indexOf(i._name))&&t.push(new i(e))})),Gt.apply(e,arguments)},request:function(e,t,n){var r,i,o,a=0,s=this._widgets,u=s&&s.length,c=[],l=[];for(t=function(e){if(!e)return!1;var t=e.length,n=Qt.type(e);return!(1!==e.nodeType||!t)||"array"===n||"function"!==n&&"string"!==n&&(0===t||"number"==typeof t&&t>0&&t-1 in e)}(t)?t:[t];a<u;a++)(r=s[a].invoke(e,t))!==Yt&&(jt.isPromise(r)?l.push(r):c.push(r));return n||l.length?(i=jt.when.apply(jt,l))[o=i.pipe?"pipe":"then"]((function(){var e=jt.Deferred(),t=arguments;return 1===t.length&&(t=t[0]),setTimeout((function(){e.resolve(t)}),1),e.promise()}))[n?o:"done"](n||jt.noop):c[0]},destroy:function(){Jt.apply(this,arguments),this._widgets=null}}),It.register=Zt.register=function(e,t){var n,r={init:"init",destroy:"destroy",name:"anonymous"};return 1===arguments.length?(t=e,Qt.each(t,(function(e){"_"!==e[0]&&"name"!==e?r[e.replace(/[A-Z]/g,"-$&").toLowerCase()]=e:"name"===e&&(r.name=t.name)}))):r=Qt.extend(r,e),t.responseMap=r,(n=jt.inherits(Zt,t))._name=r.name,Kt.push(n),n},It.unRegister=Zt.unRegister=function(e){if(e&&"anonymous"!==e)for(var t=Kt.length;t--;)Kt[t]._name===e&&Kt.splice(t,1)};var en=jt.$;It.options.dnd="",It.register({name:"dnd",init:function(e){if(e.dnd&&"html5"===this.request("predict-runtime-type")){var t,n=this,r=jt.Deferred(),i=en.extend({},{disableGlobalDnd:e.disableGlobalDnd,container:e.dnd,accept:e.accept});return this.dnd=t=new Xt(i),t.once("ready",r.resolve),t.on("drop",(function(e){n.request("add-file",[e])})),t.on("accept",(function(e){return n.owner.trigger("dndAccept",e)})),t.on("dragOver",(function(){return n.owner.trigger("dndDragOver")})),t.on("dragLeave",(function(){return n.owner.trigger("dndDragLeave")})),t.init(),r.promise()}},destroy:function(){this.dnd&&this.dnd.destroy()}});var tn=jt.$;function nn(e){(e=this.options=tn.extend({},e)).container=tn(e.container||document.body),Wt.call(this,"FilePaste")}jt.inherits(Wt,{constructor:nn,init:function(){var e=this;e.connectRuntime(e.options,(function(){e.exec("init"),e.trigger("ready")}))}}),yt.installTo(nn.prototype);var rn=jt.$;function on(e,t){var n=this;n.source=t,n.ruid=e,this.size=t.size||0,!t.type&&this.ext&&~"jpg,jpeg,png,gif,bmp".indexOf(this.ext)?this.type="image/"+("jpg"===this.ext?"jpeg":this.ext):this.type=t.type||"application/octet-stream",Wt.call(n,"Blob"),this.uid=t.uid||this.uid,e&&n.connectRuntime(e)}It.register({name:"paste",init:function(e){if(e.paste&&"html5"===this.request("predict-runtime-type")){var t,n=this,r=jt.Deferred(),i=rn.extend({},{container:e.paste,accept:e.accept});return this.paste=t=new nn(i),t.once("ready",r.resolve),t.on("paste",(function(e){n.owner.request("add-file",[e])})),t.init(),r.promise()}},destroy:function(){this.paste&&this.paste.destroy()}}),jt.inherits(Wt,{constructor:on,slice:function(e,t){return this.exec("slice",e,t)},getSource:function(){return this.source}});var an=1,sn=/\.([^.]+)$/,un=jt.inherits(on,(function(e,t){var n;this.name=t.name||"untitled"+an++,!(n=sn.exec(t.name)?RegExp.$1.toLowerCase():"")&&t.type&&(n=/\/(jpg|jpeg|png|gif|bmp)$/i.exec(t.type)?RegExp.$1.toLowerCase():"",this.name+="."+n),this.ext=n,this.lastModifiedDate=t.lastModifiedDate||(new Date).toLocaleString(),on.apply(this,arguments)})),cn=jt.$;function ln(e){if((e=this.options=cn.extend({},ln.options,e)).container=cn(e.id),!e.container.length)throw new Error("按钮指定错误");e.innerHTML=e.innerHTML||e.label||e.container.html()||"",e.button=cn(e.button||document.createElement("div")),e.button.html(e.innerHTML),e.container.html(e.button),Wt.call(this,"FilePicker",!0)}ln.options={button:null,container:null,label:null,innerHTML:null,multiple:!0,accept:null,name:"file",style:"webuploader-pick"},jt.inherits(Wt,{constructor:ln,init:function(){var e=this,t=e.options,n=t.button,r=t.style;r&&n.addClass("webuploader-pick"),e.on("all",(function(i){var o;switch(i){case"mouseenter":r&&n.addClass("webuploader-pick-hover");break;case"mouseleave":r&&n.removeClass("webuploader-pick-hover");break;case"change":o=e.exec("getFiles"),e.trigger("select",cn.map(o,(function(n){return(n=new un(e.getRuid(),n))._refer=t.container,n})),t.container)}})),e.connectRuntime(t,(function(){e.refresh(),e.exec("init",t),e.trigger("ready")})),this._resizeHandler=jt.bindFn(this.refresh,this),cn(window).on("resize",this._resizeHandler)},refresh:function(){var e=this.getRuntime().getContainer(),t=this.options.button,n=t.outerWidth?t.outerWidth():t.width(),r=t.outerHeight?t.outerHeight():t.height(),i=t.offset();n&&r&&e.css({bottom:"auto",right:"auto",width:n+"px",height:r+"px"}).offset(i)},enable:function(){this.options.button.removeClass("webuploader-pick-disable"),this.refresh()},disable:function(){var e=this.options.button;this.getRuntime().getContainer().css({top:"-99999px"}),e.addClass("webuploader-pick-disable")},destroy:function(){var e=this.options.button;cn(window).off("resize",this._resizeHandler),e.removeClass("webuploader-pick-disable webuploader-pick-hover webuploader-pick")}});var fn=jt.$;fn.extend(It.options,{pick:null,accept:null}),It.register({name:"picker",init:function(e){return this.pickers=[],e.pick&&this.addBtn(e.pick)},refresh:function(){fn.each(this.pickers,(function(){this.refresh()}))},addBtn:function(e){var t=this,n=t.options,r=n.accept,i=[];if(e)return fn.isPlainObject(e)||(e={id:e}),fn(e.id).each((function(){var o,a,s;s=jt.Deferred(),o=fn.extend({},e,{accept:fn.isPlainObject(r)?[r]:r,swf:n.swf,runtimeOrder:n.runtimeOrder,id:this}),(a=new ln(o)).once("ready",s.resolve),a.on("select",(function(e){t.owner.request("add-file",[e])})),a.on("dialogopen",(function(){t.owner.trigger("dialogOpen",a.button)})),a.init(),t.pickers.push(a),i.push(s.promise())})),jt.when.apply(jt,i)},disable:function(){fn.each(this.pickers,(function(){this.disable()}))},enable:function(){fn.each(this.pickers,(function(){this.enable()}))},destroy:function(){fn.each(this.pickers,(function(){this.destroy()})),this.pickers=null}});var dn=jt.$;function pn(e){this.options=dn.extend({},pn.options,e),Wt.call(this,"Image"),this.on("load",(function(){this._info=this.exec("info"),this._meta=this.exec("meta")}))}pn.options={quality:90,crop:!1,preserveHeaders:!1,allowMagnify:!1},jt.inherits(Wt,{constructor:pn,info:function(e){return e?(this._info=e,this):this._info},meta:function(e){return e?(this._meta=e,this):this._meta},loadFromBlob:function(e){var t=this,n=e.getRuid();this.connectRuntime(n,(function(){t.exec("init",t.options),t.exec("loadFromBlob",e)}))},resize:function(){var e=jt.slice(arguments);return this.exec.apply(this,["resize"].concat(e))},crop:function(){var e=jt.slice(arguments);return this.exec.apply(this,["crop"].concat(e))},getAsDataUrl:function(e){return this.exec("getAsDataUrl",e)},getAsBlob:function(e){var t=this.exec("getAsBlob",e);return new on(this.getRuid(),t)}});var hn,gn,mn,vn,yn=jt.$;gn=0,mn=[],vn=function(){for(var e;mn.length&&gn<5242880;)e=mn.shift(),gn+=e[0],e[1]()},hn=function(e,t,n){mn.push([t,n]),e.once("destroy",(function(){gn-=t,setTimeout(vn,1)})),setTimeout(vn,1)},yn.extend(It.options,{thumb:{width:110,height:110,quality:70,allowMagnify:!0,crop:!0,preserveHeaders:!1,type:"image/jpeg"},compress:{width:1600,height:1600,quality:90,allowMagnify:!1,crop:!1,preserveHeaders:!0}}),It.register({name:"image",makeThumb:function(e,t,n,r){var i,o;(e=this.request("get-file",e)).type.match(/^image/)?(i=yn.extend({},this.options.thumb),yn.isPlainObject(n)&&(i=yn.extend(i,n),n=null),n=n||i.width,r=r||i.height,(o=new pn(i)).once("load",(function(){e._info=e._info||o.info(),e._meta=e._meta||o.meta(),n<=1&&n>0&&(n=e._info.width*n),r<=1&&r>0&&(r=e._info.height*r),o.resize(n,r)})),o.once("complete",(function(){t(!1,o.getAsDataUrl(i.type)),o.destroy()})),o.once("error",(function(e){t(e||!0),o.destroy()})),hn(o,e.source.size,(function(){e._info&&o.info(e._info),e._meta&&o.meta(e._meta),o.loadFromBlob(e.source)}))):t(!0)},beforeSendFile:function(e){var t,n,r=this.options.compress||this.options.resize,i=r&&r.compressSize||0,o=r&&r.noCompressIfLarger||!1;if(e=this.request("get-file",e),r&&~"image/jpeg,image/jpg".indexOf(e.type)&&!(e.size<i)&&!e._compressed)return r=yn.extend({},r),n=jt.Deferred(),t=new pn(r),n.always((function(){t.destroy(),t=null})),t.once("error",n.reject),t.once("load",(function(){var n=r.width,i=r.height;e._info=e._info||t.info(),e._meta=e._meta||t.meta(),n<=1&&n>0&&(n=e._info.width*n),i<=1&&i>0&&(i=e._info.height*i),t.resize(n,i)})),t.once("complete",(function(){var i,a;try{i=t.getAsBlob(r.type),a=e.size,(!o||i.size<a)&&(e.source=i,e.size=i.size,e.trigger("resize",i.size,a)),e._compressed=!0,n.resolve()}catch(i){n.resolve()}})),e._info&&t.info(e._info),e._meta&&t.meta(e._meta),t.loadFromBlob(e.source),n.promise()}});var bn=jt.$,xn=_t.Status;function wn(){this.stats={numOfQueue:0,numOfSuccess:0,numOfCancel:0,numOfProgress:0,numOfUploadFailed:0,numOfInvalid:0,numOfDeleted:0,numOfInterrupt:0},this._queue=[],this._map={}}bn.extend(wn.prototype,{append:function(e){return this._queue.push(e),this._fileAdded(e),this},prepend:function(e){return this._queue.unshift(e),this._fileAdded(e),this},getFile:function(e){return"string"!=typeof e?e:this._map[e]},fetch:function(e){var t,n,r=this._queue.length;for(e=e||xn.QUEUED,t=0;t<r;t++)if(e===(n=this._queue[t]).getStatus())return n;return null},sort:function(e){"function"==typeof e&&this._queue.sort(e)},getFiles:function(){for(var e,t=[].slice.call(arguments,0),n=[],r=0,i=this._queue.length;r<i;r++)e=this._queue[r],t.length&&!~bn.inArray(e.getStatus(),t)||n.push(e);return n},removeFile:function(e){this._map[e.id]&&(delete this._map[e.id],this._delFile(e),e.destroy(),this.stats.numOfDeleted++)},_fileAdded:function(e){var t=this;this._map[e.id]||(this._map[e.id]=e,e.on("statuschange",(function(e,n){t._onFileStatusChange(e,n)})))},_delFile:function(e){for(var t=this._queue.length-1;t>=0;t--)if(this._queue[t]==e){this._queue.splice(t,1);break}},_onFileStatusChange:function(e,t){var n=this.stats;switch(t){case xn.PROGRESS:n.numOfProgress--;break;case xn.QUEUED:n.numOfQueue--;break;case xn.ERROR:n.numOfUploadFailed--;break;case xn.INVALID:n.numOfInvalid--;break;case xn.INTERRUPT:n.numOfInterrupt--}switch(e){case xn.QUEUED:n.numOfQueue++;break;case xn.PROGRESS:n.numOfProgress++;break;case xn.ERROR:n.numOfUploadFailed++;break;case xn.COMPLETE:n.numOfSuccess++;break;case xn.CANCELLED:n.numOfCancel++;break;case xn.INVALID:n.numOfInvalid++;break;case xn.INTERRUPT:n.numOfInterrupt++}}}),yt.installTo(wn.prototype);var _n=jt.$,Tn=/\.\w+$/,kn=_t.Status;It.register({name:"queue",init:function(e){var t,n,r,i,o,a,s,u=this;if(_n.isPlainObject(e.accept)&&(e.accept=[e.accept]),e.accept){for(o=[],r=0,n=e.accept.length;r<n;r++)(i=e.accept[r].extensions)&&o.push(i);o.length&&(a="\\."+o.join(",").replace(/,/g,"$|\\.").replace(/\*/g,".*")+"$"),u.accept=new RegExp(a,"i")}if(u.queue=new wn,u.stats=u.queue.stats,"html5"===this.request("predict-runtime-type"))return t=jt.Deferred(),this.placeholder=s=new Wt("Placeholder"),s.connectRuntime({runtimeOrder:"html5"},(function(){u._ruid=s.getRuid(),t.resolve()})),t.promise()},_wrapFile:function(e){if(!(e instanceof _t)){if(!(e instanceof un)){if(!this._ruid)throw new Error("Can't add external files.");e=new un(this._ruid,e)}e=new _t(e)}return e},acceptFile:function(e){return!(!e||!e.size||this.accept&&Tn.exec(e.name)&&!this.accept.test(e.name))},_addFile:function(e){var t=this;if(e=t._wrapFile(e),t.owner.trigger("beforeFileQueued",e)){if(t.acceptFile(e))return t.queue.append(e),t.owner.trigger("fileQueued",e),e;t.owner.trigger("error","Q_TYPE_DENIED",e)}},getFile:function(e){return this.queue.getFile(e)},addFile:function(e){var t=this;e.length||(e=[e]),(e=_n.map(e,(function(e){return t._addFile(e)}))).length&&(t.owner.trigger("filesQueued",e),t.options.auto&&setTimeout((function(){t.request("start-upload")}),20))},getStats:function(){return this.stats},removeFile:function(e,t){e=e.id?e:this.queue.getFile(e),this.request("cancel-file",e),t&&this.queue.removeFile(e)},getFiles:function(){return this.queue.getFiles.apply(this.queue,arguments)},fetchFile:function(){return this.queue.fetch.apply(this.queue,arguments)},retry:function(e,t){var n,r,i,o=this;if(e)return(e=e.id?e:o.queue.getFile(e)).setStatus(kn.QUEUED),void(t||o.request("start-upload"));for(r=0,i=(n=o.queue.getFiles(kn.ERROR)).length;r<i;r++)(e=n[r]).setStatus(kn.QUEUED);o.request("start-upload")},sortFiles:function(){return this.queue.sort.apply(this.queue,arguments)},reset:function(){this.owner.trigger("reset"),this.queue=new wn,this.stats=this.queue.stats},destroy:function(){this.reset(),this.placeholder&&this.placeholder.destroy()}}),It.support=function(){return $t.hasRuntime.apply($t,arguments)},It.register({name:"runtime",init:function(){if(!this.predictRuntimeType())throw Error("Runtime Error")},predictRuntimeType:function(){var e,t,n=this.options.runtimeOrder||$t.orders,r=this.type;if(!r)for(e=0,t=(n=n.split(/\s*,\s*/g)).length;e<t;e++)if($t.hasRuntime(n[e])){this.type=r=n[e];break}return r}});var En=jt.$;function Sn(e){var t=this;e=t.options=En.extend(!0,{},Sn.options,e||{}),Wt.call(this,"Transport"),this._blob=null,this._formData=e.formData||{},this._headers=e.headers||{},this.on("progress",this._timeout),this.on("load error",(function(){t.trigger("progress",1),clearTimeout(t._timer)}))}Sn.options={server:"",method:"POST",withCredentials:!1,fileVal:"file",timeout:12e4,formData:{},headers:{},sendAsBinary:!1},En.extend(Sn.prototype,{appendBlob:function(e,t,n){var r=this,i=r.options;r.getRuid()&&r.disconnectRuntime(),r.connectRuntime(t.ruid,(function(){r.exec("init")})),r._blob=t,i.fileVal=e||i.fileVal,i.filename=n||i.filename},append:function(e,t){"object"==typeof e?En.extend(this._formData,e):this._formData[e]=t},setRequestHeader:function(e,t){"object"==typeof e?En.extend(this._headers,e):this._headers[e]=t},send:function(e){this.exec("send",e),this._timeout()},abort:function(){return clearTimeout(this._timer),this.exec("abort")},destroy:function(){this.trigger("destroy"),this.off(),this.exec("destroy"),this.disconnectRuntime()},getResponseHeaders:function(){return this.exec("getResponseHeaders")},getResponse:function(){return this.exec("getResponse")},getResponseAsJson:function(){return this.exec("getResponseAsJson")},getStatus:function(){return this.exec("getStatus")},_timeout:function(){var e=this,t=e.options.timeout;t&&(clearTimeout(e._timer),e._timer=setTimeout((function(){e.abort(),e.trigger("error","timeout")}),t))}}),yt.installTo(Sn.prototype);var Cn=jt.$,An=jt.isPromise,Nn=_t.Status;Cn.extend(It.options,{prepareNextFile:!1,chunked:!1,chunkSize:5242880,chunkRetry:2,chunkRetryDelay:1e3,threads:3,formData:{}}),It.register({name:"upload",init:function(){var e=this.owner,t=this;this.runing=!1,this.progress=!1,e.on("startUpload",(function(){t.progress=!0})).on("uploadFinished",(function(){t.progress=!1})),this.pool=[],this.stack=[],this.pending=[],this.remaning=0,this.__tick=jt.bindFn(this._tick,this),e.on("uploadComplete",(function(e){e.blocks&&Cn.each(e.blocks,(function(e,t){t.transport&&(t.transport.abort(),t.transport.destroy()),delete t.transport})),delete e.blocks,delete e.remaning}))},reset:function(){this.request("stop-upload",!0),this.runing=!1,this.pool=[],this.stack=[],this.pending=[],this.remaning=0,this._trigged=!1,this._promise=null},startUpload:function(e){var t=this;if(Cn.each(t.request("get-files",Nn.INVALID),(function(){t.request("remove-file",this)})),e?(e=e.id?e:t.request("get-file",e)).getStatus()===Nn.INTERRUPT?(e.setStatus(Nn.QUEUED),Cn.each(t.pool,(function(t,n){n.file===e&&(n.transport&&n.transport.send(),e.setStatus(Nn.PROGRESS))}))):e.getStatus()!==Nn.PROGRESS&&e.setStatus(Nn.QUEUED):Cn.each(t.request("get-files",[Nn.INITED]),(function(){this.setStatus(Nn.QUEUED)})),t.runing)return t.owner.trigger("startUpload",e),jt.nextTick(t.__tick);t.runing=!0;var n=[];e||Cn.each(t.pool,(function(e,r){var i=r.file;i.getStatus()===Nn.INTERRUPT&&(t._trigged=!1,n.push(i),r.transport&&r.transport.send())})),Cn.each(n,(function(){this.setStatus(Nn.PROGRESS)})),e||Cn.each(t.request("get-files",Nn.INTERRUPT),(function(){this.setStatus(Nn.PROGRESS)})),t._trigged=!1,jt.nextTick(t.__tick),t.owner.trigger("startUpload")},stopUpload:function(e,t){var n=this;if(!0===e&&(t=e,e=null),!1!==n.runing){if(e){if((e=e.id?e:n.request("get-file",e)).getStatus()!==Nn.PROGRESS&&e.getStatus()!==Nn.QUEUED)return;return e.setStatus(Nn.INTERRUPT),Cn.each(n.pool,(function(r,i){i.file===e&&(i.transport&&i.transport.abort(),t&&(n._putback(i),n._popBlock(i)))})),n.owner.trigger("stopUpload",e),jt.nextTick(n.__tick)}n.runing=!1,this._promise&&this._promise.file&&this._promise.file.setStatus(Nn.INTERRUPT),t&&Cn.each(n.pool,(function(e,t){t.transport&&t.transport.abort(),t.file.setStatus(Nn.INTERRUPT)})),n.owner.trigger("stopUpload")}},cancelFile:function(e){(e=e.id?e:this.request("get-file",e)).blocks&&Cn.each(e.blocks,(function(e,t){var n=t.transport;n&&(n.abort(),n.destroy(),delete t.transport)})),e.setStatus(Nn.CANCELLED),this.owner.trigger("fileDequeued",e)},isInProgress:function(){return!!this.progress},_getStats:function(){return this.request("get-stats")},skipFile:function(e,t){(e=e.id?e:this.request("get-file",e)).setStatus(t||Nn.COMPLETE),e.skipped=!0,e.blocks&&Cn.each(e.blocks,(function(e,t){var n=t.transport;n&&(n.abort(),n.destroy(),delete t.transport)})),this.owner.trigger("uploadSkip",e)},_tick:function(){var e,t,n=this,r=n.options;if(n._promise)return n._promise.always(n.__tick);n.pool.length<r.threads&&(t=n._nextBlock())?(n._trigged=!1,e=function(e){n._promise=null,e&&e.file&&n._startSend(e),jt.nextTick(n.__tick)},n._promise=An(t)?t.always(e):e(t)):n.remaning||n._getStats().numOfQueue||n._getStats().numOfInterrupt||(n.runing=!1,n._trigged||jt.nextTick((function(){n.owner.trigger("uploadFinished")})),n._trigged=!0)},_putback:function(e){e.cuted.unshift(e),~this.stack.indexOf(e.cuted)||this.stack.unshift(e.cuted)},_getStack:function(){for(var e,t=0;e=this.stack[t++];){if(e.has()&&e.file.getStatus()===Nn.PROGRESS)return e;(!e.has()||e.file.getStatus()!==Nn.PROGRESS&&e.file.getStatus()!==Nn.INTERRUPT)&&this.stack.splice(--t,1)}return null},_nextBlock:function(){var e,t,n,r,i=this,o=i.options;return(e=this._getStack())?(o.prepareNextFile&&!i.pending.length&&i._prepareNextFile(),e.shift()):i.runing?(!i.pending.length&&i._getStats()&&i._getStats().numOfQueue&&i._prepareNextFile(),t=i.pending.shift(),n=function(t){return t?(e=function(e,t){var n,r,i=[],o=e.source.size,a=t?Math.ceil(o/t):1,s=0,u=0;for(r={file:e,has:function(){return!!i.length},shift:function(){return i.shift()},unshift:function(e){i.unshift(e)}};u<a;)n=Math.min(t,o-s),i.push({file:e,start:s,end:t?s+n:o,total:o,chunks:a,chunk:u++,cuted:r}),s+=n;return e.blocks=i.concat(),e.remaning=i.length,r}(t,o.chunked?o.chunkSize:0),i.stack.push(e),e.shift()):null},An(t)?(r=t.file,(t=t[t.pipe?"pipe":"then"](n)).file=r,t):n(t)):void 0},_prepareNextFile:function(){var e,t=this,n=t.request("fetch-file"),r=t.pending;n&&(e=t.request("before-send-file",n,(function(){return n.getStatus()===Nn.PROGRESS||n.getStatus()===Nn.INTERRUPT?n:t._finishFile(n)})),t.owner.trigger("uploadStart",n),n.setStatus(Nn.PROGRESS),e.file=n,e.done((function(){var t=Cn.inArray(e,r);~t&&r.splice(t,1,n)})),e.fail((function(e){n.setStatus(Nn.ERROR,e),t.owner.trigger("uploadError",n,e),t.owner.trigger("uploadComplete",n)})),r.push(e))},_popBlock:function(e){var t=Cn.inArray(e,this.pool);this.pool.splice(t,1),e.file.remaning--,this.remaning--},_startSend:function(e){var t=this,n=e.file;n.getStatus()===Nn.PROGRESS?(t.pool.push(e),t.remaning++,e.blob=1===e.chunks?n.source:n.source.slice(e.start,e.end),t.request("before-send",e,(function(){n.getStatus()===Nn.PROGRESS?t._doSend(e):(t._popBlock(e),jt.nextTick(t.__tick))})).fail((function(){1===n.remaning?t._finishFile(n).always((function(){e.percentage=1,t._popBlock(e),t.owner.trigger("uploadComplete",n),jt.nextTick(t.__tick)})):(e.percentage=1,t.updateFileProgress(n),t._popBlock(e),jt.nextTick(t.__tick))}))):n.getStatus()===Nn.INTERRUPT&&t._putback(e)},_doSend:function(e){var t,n,r=this,i=r.owner,o=Cn.extend({},r.options,e.options),a=e.file,s=new Sn(o),u=Cn.extend({},o.formData),c=Cn.extend({},o.headers);e.transport=s,s.on("destroy",(function(){delete e.transport,r._popBlock(e),jt.nextTick(r.__tick)})),s.on("progress",(function(t){e.percentage=t,r.updateFileProgress(a)})),t=function(t){var r;return(n=s.getResponseAsJson()||{})._raw=s.getResponse(),n._headers=s.getResponseHeaders(),e.response=n,r=function(e){t=e},i.trigger("uploadAccept",e,n,r)||(t=t||"server"),t},s.on("error",(function(n,u){e.retried=e.retried||0,e.chunks>1&&~"http,abort,server".indexOf(n.replace(/-.*/,""))&&e.retried<o.chunkRetry?(e.retried++,r.retryTimer=setTimeout((function(){s.send()}),o.chunkRetryDelay||1e3)):(u||"server"!==n||(n=t(n)),a.setStatus(Nn.ERROR,n),i.trigger("uploadError",a,n),i.trigger("uploadComplete",a))})),s.on("load",(function(){var e;(e=t())?s.trigger("error",e,!0):1===a.remaning?r._finishFile(a,n):s.destroy()})),u=Cn.extend(u,{id:a.id,name:a.name,type:a.type,lastModifiedDate:a.lastModifiedDate,size:a.size}),e.chunks>1&&Cn.extend(u,{chunks:e.chunks,chunk:e.chunk}),i.trigger("uploadBeforeSend",e,u,c),s.appendBlob(o.fileVal,e.blob,a.name),s.append(u),s.setRequestHeader(c),s.send()},_finishFile:function(e,t,n){var r=this.owner;return r.request("after-send-file",arguments,(function(){e.setStatus(Nn.COMPLETE),r.trigger("uploadSuccess",e,t,n)})).fail((function(t){e.getStatus()===Nn.PROGRESS&&e.setStatus(Nn.ERROR,t),r.trigger("uploadError",e,t)})).always((function(){r.trigger("uploadComplete",e)}))},updateFileProgress:function(e){var t,n=0;e.blocks&&(Cn.each(e.blocks,(function(e,t){n+=(t.percentage||0)*(t.end-t.start)})),t=n/e.size,this.owner.trigger("uploadProgress",e,t||0))},destroy:function(){clearTimeout(this.retryTimer)}});var Rn,Dn=jt.$,Fn={};function Ln(){Wt.call(this,"Md5")}function On(e,t){this.owner=e,this.options=e.options,this.getRuntime=function(){return t},this.getRuid=function(){return t.uid},this.trigger=function(){return e.trigger.apply(e,arguments)}}Rn={addValidator:function(e,t){Fn[e]=t},removeValidator:function(e){delete Fn[e]}},It.register({name:"validator",init:function(){var e=this;jt.nextTick((function(){Dn.each(Fn,(function(){this.call(e.owner)}))}))}}),Rn.addValidator("fileNumLimit",(function(){var e=this,t=e.options,n=0,r=parseInt(t.fileNumLimit,10),i=!0;r&&(e.on("beforeFileQueued",(function(e){return!(!this.trigger("beforeFileQueuedCheckfileNumLimit",e,n)||(n>=r&&i&&(i=!1,this.trigger("error","Q_EXCEED_NUM_LIMIT",r,e),setTimeout((function(){i=!0}),1)),n>=r))})),e.on("fileQueued",(function(){n++})),e.on("fileDequeued",(function(){n--})),e.on("reset",(function(){n=0})))})),Rn.addValidator("fileSizeLimit",(function(){var e=this,t=e.options,n=0,r=parseInt(t.fileSizeLimit,10),i=!0;r&&(e.on("beforeFileQueued",(function(e){var t=n+e.size>r;return t&&i&&(i=!1,this.trigger("error","Q_EXCEED_SIZE_LIMIT",r,e),setTimeout((function(){i=!0}),1)),!t})),e.on("fileQueued",(function(e){n+=e.size})),e.on("fileDequeued",(function(e){n-=e.size})),e.on("reset",(function(){n=0})))})),Rn.addValidator("fileSingleSizeLimit",(function(){var e=this.options.fileSingleSizeLimit;e&&this.on("beforeFileQueued",(function(t){if(t.size>e)return t.setStatus(_t.Status.INVALID,"exceed_size"),this.trigger("error","F_EXCEED_SIZE",e,t),!1}))})),Rn.addValidator("duplicate",(function(){var e=this,t=e.options,n={};t.duplicate||(e.on("beforeFileQueued",(function(e){var t=e.__hash||(e.__hash=function(e){for(var t=0,n=0,r=e.length;n<r;n++)t=e.charCodeAt(n)+(t<<6)+(t<<16)-t;return t}(e.name+e.size+e.lastModifiedDate));if(n[t])return this.trigger("error","F_DUPLICATE",e),!1})),e.on("fileQueued",(function(e){var t=e.__hash;t&&(n[t]=!0)})),e.on("fileDequeued",(function(e){var t=e.__hash;t&&delete n[t]})),e.on("reset",(function(){n={}})))})),yt.installTo(Ln.prototype),Ln.prototype.loadFromBlob=function(e){var t=this;t.getRuid()&&t.disconnectRuntime(),t.connectRuntime(e.ruid,(function(){t.exec("init"),t.exec("loadFromBlob",e)}))},Ln.prototype.getResult=function(){return this.exec("getResult")},It.register({name:"md5",md5File:function(e,t,n){var r=new Ln,i=jt.Deferred(),o=e instanceof on?e:this.request("get-file",e).source;return r.on("progress load",(function(e){e=e||{},i.notify(e.total?e.loaded/e.total:1)})),r.on("complete",(function(){i.resolve(r.getResult())})),r.on("error",(function(e){i.reject(e)})),arguments.length>1&&(n=n||0,(t=t||0)<0&&(t=o.size+t),n<0&&(n=o.size+n),n=Math.min(n,o.size),o=o.slice(t,n)),r.loadFromBlob(o),i.promise()}});var Hn={};function jn(){var e={},t=this,n=this.destroy;$t.apply(t,arguments),t.type="html5",t.exec=function(n,r){var i,o=this.uid,a=jt.slice(arguments,2);if(Hn[n]&&(i=e[o]=e[o]||new Hn[n](this,t))[r])return i[r].apply(i,a)},t.destroy=function(){return n&&n.apply(this,arguments)}}jt.inherits($t,{constructor:jn,init:function(){var e=this;setTimeout((function(){e.trigger("ready")}),1)}}),jn.register=function(e,t){return Hn[e]=jt.inherits(On,t)},window.Blob&&window.FileReader&&window.DataView&&$t.addRuntime("html5",jn),jn.register("Blob",{slice:function(e,t){var n=this.owner.source;return n=(n.slice||n.webkitSlice||n.mozSlice).call(n,e,t),new on(this.getRuid(),n)}});var qn=jt.$,In="webuploader-dnd-",Mn=(jn.register("DragAndDrop",{init:function(){var e=this.elem=this.options.container;this.dragEnterHandler=jt.bindFn(this._dragEnterHandler,this),this.dragOverHandler=jt.bindFn(this._dragOverHandler,this),this.dragLeaveHandler=jt.bindFn(this._dragLeaveHandler,this),this.dropHandler=jt.bindFn(this._dropHandler,this),this.dndOver=!1,e.on("dragenter",this.dragEnterHandler),e.on("dragover",this.dragOverHandler),e.on("dragleave",this.dragLeaveHandler),e.on("drop",this.dropHandler),this.options.disableGlobalDnd&&(qn(document).on("dragover",this.dragOverHandler),qn(document).on("drop",this.dropHandler))},_dragEnterHandler:function(e){var t,n=this,r=n._denied||!1;return e=e.originalEvent||e,n.dndOver||(n.dndOver=!0,(t=e.dataTransfer.items)&&t.length&&(n._denied=r=!n.trigger("accept",t)),n.elem.addClass(In+"over"),n.elem[r?"addClass":"removeClass"](In+"denied"),n.trigger("dragOver")),e.dataTransfer.dropEffect=r?"none":"copy",!1},_dragOverHandler:function(e){var t=this.elem.parent().get(0);return t&&!qn.contains(t,e.currentTarget)||(clearTimeout(this._leaveTimer),this._dragEnterHandler.call(this,e)),!1},_dragLeaveHandler:function(){var e,t=this;return e=function(){t.dndOver=!1,t.elem.removeClass(In+"over "+In+"denied")},t.trigger("dragLeave"),clearTimeout(t._leaveTimer),t._leaveTimer=setTimeout(e,100),!1},_dropHandler:function(e){var t,n,r=this,i=r.getRuid(),o=r.elem.parent().get(0);if(o&&!qn.contains(o,e.currentTarget))return!1;t=(e=e.originalEvent||e).dataTransfer;try{n=t.getData("text/html")}catch(e){}return r.dndOver=!1,r.elem.removeClass(In+"over"),t&&!n?(r._getTansferFiles(t,(function(e){r.trigger("drop",qn.map(e,(function(e){return new un(i,e)})))})),!1):void 0},_getTansferFiles:function(e,t){var n,r,i,o,a,s,u,c=[],l=[];for(n=e.items,r=e.files,u=!(!n||!n[0].webkitGetAsEntry),a=0,s=r.length;a<s;a++)i=r[a],o=n&&n[a],u&&o.webkitGetAsEntry().isDirectory?l.push(this._traverseDirectoryTree(o.webkitGetAsEntry(),c)):c.push(i);jt.when.apply(jt,l).done((function(){c.length&&t(c)}))},_traverseDirectoryTree:function(e,t){var n=jt.Deferred(),r=this;return e.isFile?e.file((function(e){t.push(e),n.resolve()})):e.isDirectory&&e.createReader().readEntries((function(e){var i,o=e.length,a=[],s=[];for(i=0;i<o;i++)a.push(r._traverseDirectoryTree(e[i],s));jt.when.apply(jt,a).then((function(){t.push.apply(t,s),n.resolve()}),n.reject)})),n.promise()},destroy:function(){var e=this.elem;e&&(e.off("dragenter",this.dragEnterHandler),e.off("dragover",this.dragOverHandler),e.off("dragleave",this.dragLeaveHandler),e.off("drop",this.dropHandler),this.options.disableGlobalDnd&&(qn(document).off("dragover",this.dragOverHandler),qn(document).off("drop",this.dropHandler)))}}),jn.register("FilePaste",{init:function(){var e,t,n,r,i=this.options,o=this.elem=i.container,a=".*";if(i.accept){for(e=[],t=0,n=i.accept.length;t<n;t++)(r=i.accept[t].mimeTypes)&&e.push(r);e.length&&(a=(a=e.join(",")).replace(/,/g,"|").replace(/\*/g,".*"))}this.accept=a=new RegExp(a,"i"),this.hander=jt.bindFn(this._pasteHander,this),o.on("paste",this.hander)},_pasteHander:function(e){var t,n,r,i,o,a=[],s=this.getRuid();for(i=0,o=(t=(e=e.originalEvent||e).clipboardData.items).length;i<o;i++)"file"===(n=t[i]).kind&&(r=n.getAsFile())&&a.push(new un(s,r));a.length&&(e.preventDefault(),e.stopPropagation(),this.trigger("paste",a))},destroy:function(){this.elem.off("paste",this.hander)}}),jt.$),Bn=(jn.register("FilePicker",{init:function(){var e,t,n,r,i,o=this.getRuntime().getContainer(),a=this,s=a.owner,u=a.options,c=this.label=Mn(document.createElement("label")),l=this.input=Mn(document.createElement("input"));if(l.attr("type","file"),l.attr("capture","camera"),l.attr("name",u.name),l.addClass("webuploader-element-invisible"),c.on("click",(function(e){l.trigger("click"),e.stopPropagation(),s.trigger("dialogopen")})),c.css({opacity:0,width:"100%",height:"100%",display:"block",cursor:"pointer",background:"#ffffff"}),u.multiple&&l.attr("multiple","multiple"),u.accept&&u.accept.length>0){for(e=[],t=0,n=u.accept.length;t<n;t++)e.push(u.accept[t].mimeTypes);l.attr("accept",e.join(","))}o.append(l),o.append(c),r=function(e){s.trigger(e.type)},i=function(e){var t;if(0===e.target.files.length)return!1;a.files=e.target.files,(t=this.cloneNode(!0)).value=null,this.parentNode.replaceChild(t,this),l.off(),l=Mn(t).on("change",i).on("mouseenter mouseleave",r),s.trigger("change")},l.on("change",i),c.on("mouseenter mouseleave",r)},getFiles:function(){return this.files},destroy:function(){this.input.off(),this.label.off()}}),window.createObjectURL&&window||window.URL&&URL.revokeObjectURL&&URL||window.webkitURL),Pn=jt.noop,Un=Pn;Bn&&(Pn=function(){return Bn.createObjectURL.apply(Bn,arguments)},Un=function(){return Bn.revokeObjectURL.apply(Bn,arguments)});var zn,$n={createObjectURL:Pn,revokeObjectURL:Un,dataURL2Blob:function(e){var t,n,r,i,o,a;for(t=~(a=e.split(","))[0].indexOf("base64")?atob(a[1]):decodeURIComponent(a[1]),r=new ArrayBuffer(t.length),n=new Uint8Array(r),i=0;i<t.length;i++)n[i]=t.charCodeAt(i);return o=a[0].split(":")[1].split(";")[0],this.arrayBufferToBlob(r,o)},dataURL2ArrayBuffer:function(e){var t,n,r,i;for(t=~(i=e.split(","))[0].indexOf("base64")?atob(i[1]):decodeURIComponent(i[1]),n=new Uint8Array(t.length),r=0;r<t.length;r++)n[r]=t.charCodeAt(r);return n.buffer},arrayBufferToBlob:function(e,t){var n,r=window.BlobBuilder||window.WebKitBlobBuilder;return r?((n=new r).append(e),n.getBlob(t)):new Blob([e],t?{type:t}:{})},canvasToDataUrl:function(e,t,n){return e.toDataURL(t,n/100)},parseMeta:function(e,t){t(!1,{})},updateImageHead:function(e){return e}};zn={parsers:{65505:[]},maxMetaDataSize:262144,parse:function(e,t){var n=this,r=new FileReader;r.onload=function(){t(!1,n._parse(this.result)),r=r.onload=r.onerror=null},r.onerror=function(e){t(e.message),r=r.onload=r.onerror=null},e=e.slice(0,n.maxMetaDataSize),r.readAsArrayBuffer(e.getSource())},_parse:function(e,t){if(!(e.byteLength<6)){var n,r,i,o,a=new DataView(e),s=2,u=a.byteLength-4,c=s,l={};if(65496===a.getUint16(0)){for(;s<u&&((n=a.getUint16(s))>=65504&&n<=65519||65534===n)&&!(s+(r=a.getUint16(s+2)+2)>a.byteLength);){if(i=zn.parsers[n],!t&&i)for(o=0;o<i.length;o+=1)i[o].call(zn,a,s,r,l);c=s+=r}c>6&&(e.slice?l.imageHead=e.slice(2,c):l.imageHead=new Uint8Array(e).subarray(2,c))}return l}},updateImageHead:function(e,t){var n,r,i,o=this._parse(e,!0);return i=2,o.imageHead&&(i=2+o.imageHead.byteLength),r=e.slice?e.slice(i):new Uint8Array(e).subarray(i),(n=new Uint8Array(t.byteLength+2+r.byteLength))[0]=255,n[1]=216,n.set(new Uint8Array(t),2),n.set(new Uint8Array(r),t.byteLength+2),n.buffer}},$n.parseMeta=function(){return zn.parse.apply(zn,arguments)},$n.updateImageHead=function(){return zn.updateImageHead.apply(zn,arguments)};var Wn=zn,Vn={ExifMap:function(){return this}};function Xn(e){Math.round;var t,n,r,i,o,a=Math.floor,s=new Array(64),u=new Array(64),c=new Array(64),l=new Array(64),f=new Array(65535),d=new Array(65535),p=new Array(64),h=new Array(64),g=[],m=0,v=7,y=new Array(64),b=new Array(64),x=new Array(64),w=new Array(256),_=new Array(2048),T=[0,1,5,6,14,15,27,28,2,4,7,13,16,26,29,42,3,8,12,17,25,30,41,43,9,11,18,24,31,40,44,53,10,19,23,32,39,45,52,54,20,22,33,38,46,51,55,60,21,34,37,47,50,56,59,61,35,36,48,49,57,58,62,63],k=[0,0,1,5,1,1,1,1,1,1,0,0,0,0,0,0,0],E=[0,1,2,3,4,5,6,7,8,9,10,11],S=[0,0,2,1,3,3,2,4,3,5,5,4,4,0,0,1,125],C=[1,2,3,0,4,17,5,18,33,49,65,6,19,81,97,7,34,113,20,50,129,145,161,8,35,66,177,193,21,82,209,240,36,51,98,114,130,9,10,22,23,24,25,26,37,38,39,40,41,42,52,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,225,226,227,228,229,230,231,232,233,234,241,242,243,244,245,246,247,248,249,250],A=[0,0,3,1,1,1,1,1,1,1,1,1,0,0,0,0,0],N=[0,1,2,3,4,5,6,7,8,9,10,11],R=[0,0,2,1,2,4,4,3,4,7,5,4,4,0,1,2,119],D=[0,1,2,3,17,4,5,33,49,6,18,65,81,7,97,113,19,34,50,129,8,20,66,145,161,177,193,9,35,51,82,240,21,98,114,209,10,22,36,52,225,37,241,23,24,25,26,38,39,40,41,42,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,130,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,226,227,228,229,230,231,232,233,234,242,243,244,245,246,247,248,249,250];function F(e,t){for(var n=0,r=0,i=new Array,o=1;o<=16;o++){for(var a=1;a<=e[o];a++)i[t[r]]=[],i[t[r]][0]=n,i[t[r]][1]=o,r++,n++;n*=2}return i}function L(e){for(var t=e[0],n=e[1]-1;n>=0;)t&1<<n&&(m|=1<<v),n--,--v<0&&(255==m?(O(255),O(0)):O(m),v=7,m=0)}function O(e){g.push(w[e])}function H(e){O(e>>8&255),O(255&e)}function j(e,t,n,r,i){for(var o,a=i[0],s=i[240],u=function(e,t){var n,r,i,o,a,s,u,c,l,f,d=0;for(l=0;l<8;++l){n=e[d],r=e[d+1],i=e[d+2],o=e[d+3],a=e[d+4],s=e[d+5],u=e[d+6];var h=n+(c=e[d+7]),g=n-c,m=r+u,v=r-u,y=i+s,b=i-s,x=o+a,w=o-a,_=h+x,T=h-x,k=m+y,E=m-y;e[d]=_+k,e[d+4]=_-k;var S=.707106781*(E+T);e[d+2]=T+S,e[d+6]=T-S;var C=.382683433*((_=w+b)-(E=v+g)),A=.5411961*_+C,N=1.306562965*E+C,R=.707106781*(k=b+v),D=g+R,F=g-R;e[d+5]=F+A,e[d+3]=F-A,e[d+1]=D+N,e[d+7]=D-N,d+=8}for(d=0,l=0;l<8;++l){n=e[d],r=e[d+8],i=e[d+16],o=e[d+24],a=e[d+32],s=e[d+40],u=e[d+48];var L=n+(c=e[d+56]),O=n-c,H=r+u,j=r-u,q=i+s,I=i-s,M=o+a,B=o-a,P=L+M,U=L-M,z=H+q,$=H-q;e[d]=P+z,e[d+32]=P-z;var W=.707106781*($+U);e[d+16]=U+W,e[d+48]=U-W;var V=.382683433*((P=B+I)-($=j+O)),X=.5411961*P+V,Q=1.306562965*$+V,G=.707106781*(z=I+j),J=O+G,Y=O-G;e[d+40]=Y+X,e[d+24]=Y-X,e[d+8]=J+Q,e[d+56]=J-Q,d++}for(l=0;l<64;++l)f=e[l]*t[l],p[l]=f>0?f+.5|0:f-.5|0;return p}(e,t),c=0;c<64;++c)h[T[c]]=u[c];var l=h[0]-n;n=h[0],0==l?L(r[0]):(L(r[d[o=32767+l]]),L(f[o]));for(var g=63;g>0&&0==h[g];g--);if(0==g)return L(a),n;for(var m,v=1;v<=g;){for(var y=v;0==h[v]&&v<=g;++v);var b=v-y;if(b>=16){m=b>>4;for(var x=1;x<=m;++x)L(s);b&=15}o=32767+h[v],L(i[(b<<4)+d[o]]),L(f[o]),v++}return 63!=g&&L(a),n}function q(e){e<=0&&(e=1),e>100&&(e=100),o!=e&&(function(e){for(var t=[16,11,10,16,24,40,51,61,12,12,14,19,26,58,60,55,14,13,16,24,40,57,69,56,14,17,22,29,51,87,80,62,18,22,37,56,68,109,103,77,24,35,55,64,81,104,113,92,49,64,78,87,103,121,120,101,72,92,95,98,112,100,103,99],n=0;n<64;n++){var r=a((t[n]*e+50)/100);r<1?r=1:r>255&&(r=255),s[T[n]]=r}for(var i=[17,18,24,47,99,99,99,99,18,21,26,66,99,99,99,99,24,26,56,99,99,99,99,99,47,66,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99],o=0;o<64;o++){var f=a((i[o]*e+50)/100);f<1?f=1:f>255&&(f=255),u[T[o]]=f}for(var d=[1,1.387039845,1.306562965,1.175875602,1,.785694958,.5411961,.275899379],p=0,h=0;h<8;h++)for(var g=0;g<8;g++)c[p]=1/(s[T[p]]*d[h]*d[g]*8),l[p]=1/(u[T[p]]*d[h]*d[g]*8),p++}(e<50?Math.floor(5e3/e):Math.floor(200-2*e)),o=e)}this.encode=function(e,o){o&&q(o),g=new Array,m=0,v=7,H(65496),H(65504),H(16),O(74),O(70),O(73),O(70),O(0),O(1),O(1),O(0),H(1),H(1),O(0),O(0),function(){H(65499),H(132),O(0);for(var e=0;e<64;e++)O(s[e]);O(1);for(var t=0;t<64;t++)O(u[t])}(),function(e,t){H(65472),H(17),O(8),H(t),H(e),O(3),O(1),O(17),O(0),O(2),O(17),O(1),O(3),O(17),O(1)}(e.width,e.height),function(){H(65476),H(418),O(0);for(var e=0;e<16;e++)O(k[e+1]);for(var t=0;t<=11;t++)O(E[t]);O(16);for(var n=0;n<16;n++)O(S[n+1]);for(var r=0;r<=161;r++)O(C[r]);O(1);for(var i=0;i<16;i++)O(A[i+1]);for(var o=0;o<=11;o++)O(N[o]);O(17);for(var a=0;a<16;a++)O(R[a+1]);for(var s=0;s<=161;s++)O(D[s])}(),H(65498),H(12),O(3),O(1),O(0),O(2),O(17),O(3),O(17),O(0),O(63),O(0);var a=0,f=0,d=0;m=0,v=7,this.encode.displayName="_encode_";for(var p,h,w,T,F,I,M,B,P,U=e.data,z=e.width,$=e.height,W=4*z,V=0;V<$;){for(p=0;p<W;){for(I=F=W*V+p,M=-1,B=0,P=0;P<64;P++)I=F+(B=P>>3)*W+(M=4*(7&P)),V+B>=$&&(I-=W*(V+1+B-$)),p+M>=W&&(I-=p+M-W+4),h=U[I++],w=U[I++],T=U[I++],y[P]=(_[h]+_[w+256>>0]+_[T+512>>0]>>16)-128,b[P]=(_[h+768>>0]+_[w+1024>>0]+_[T+1280>>0]>>16)-128,x[P]=(_[h+1280>>0]+_[w+1536>>0]+_[T+1792>>0]>>16)-128;a=j(y,c,a,t,r),f=j(b,l,f,n,i),d=j(x,l,d,n,i),p+=32}V+=8}if(v>=0){var X=[];X[1]=v+1,X[0]=(1<<v+1)-1,L(X)}H(65497);var Q="data:image/jpeg;base64,"+btoa(g.join(""));return g=[],Q},e||(e=50),function(){for(var e=String.fromCharCode,t=0;t<256;t++)w[t]=e(t)}(),t=F(k,E),n=F(A,N),r=F(S,C),i=F(R,D),function(){for(var e=1,t=2,n=1;n<=15;n++){for(var r=e;r<t;r++)d[32767+r]=n,f[32767+r]=[],f[32767+r][1]=n,f[32767+r][0]=r;for(var i=-(t-1);i<=-e;i++)d[32767+i]=n,f[32767+i]=[],f[32767+i][1]=n,f[32767+i][0]=t-1+i;e<<=1,t<<=1}}(),function(){for(var e=0;e<256;e++)_[e]=19595*e,_[e+256>>0]=38470*e,_[e+512>>0]=7471*e+32768,_[e+768>>0]=-11059*e,_[e+1024>>0]=-21709*e,_[e+1280>>0]=32768*e+8421375,_[e+1536>>0]=-27439*e,_[e+1792>>0]=-5329*e}(),q(e)}Vn.ExifMap.prototype.map={Orientation:274},Vn.ExifMap.prototype.get=function(e){return this[e]||this[this.map[e]]},Vn.exifTagTypes={1:{getValue:function(e,t){return e.getUint8(t)},size:1},2:{getValue:function(e,t){return String.fromCharCode(e.getUint8(t))},size:1,ascii:!0},3:{getValue:function(e,t,n){return e.getUint16(t,n)},size:2},4:{getValue:function(e,t,n){return e.getUint32(t,n)},size:4},5:{getValue:function(e,t,n){return e.getUint32(t,n)/e.getUint32(t+4,n)},size:8},9:{getValue:function(e,t,n){return e.getInt32(t,n)},size:4},10:{getValue:function(e,t,n){return e.getInt32(t,n)/e.getInt32(t+4,n)},size:8}},Vn.exifTagTypes[7]=Vn.exifTagTypes[1],Vn.getExifValue=function(e,t,n,r,i,o){var a,s,u,c,l,f,d=Vn.exifTagTypes[r];if(d){if(!((s=(a=d.size*i)>4?t+e.getUint32(n+8,o):n+8)+a>e.byteLength)){if(1===i)return d.getValue(e,s,o);for(u=[],c=0;c<i;c+=1)u[c]=d.getValue(e,s+c*d.size,o);if(d.ascii){for(l="",c=0;c<u.length&&"\0"!==(f=u[c]);c+=1)l+=f;return l}return u}jt.log("Invalid Exif data: Invalid data offset.")}else jt.log("Invalid Exif data: Invalid tag type.")},Vn.parseExifTag=function(e,t,n,r,i){var o=e.getUint16(n,r);i.exif[o]=Vn.getExifValue(e,t,n,e.getUint16(n+2,r),e.getUint32(n+4,r),r)},Vn.parseExifTags=function(e,t,n,r,i){var o,a,s;if(n+6>e.byteLength)jt.log("Invalid Exif data: Invalid directory offset.");else{if(!((a=n+2+12*(o=e.getUint16(n,r)))+4>e.byteLength)){for(s=0;s<o;s+=1)this.parseExifTag(e,t,n+2+12*s,r,i);return e.getUint32(a,r)}jt.log("Invalid Exif data: Invalid directory size.")}},Vn.parseExifData=function(e,t,n,r){var i,o,a=t+10;if(1165519206===e.getUint32(t+4))if(a+8>e.byteLength)jt.log("Invalid Exif data: Invalid segment size.");else if(0===e.getUint16(t+8)){switch(e.getUint16(a)){case 18761:i=!0;break;case 19789:i=!1;break;default:return void jt.log("Invalid Exif data: Invalid byte alignment marker.")}42===e.getUint16(a+2,i)?(o=e.getUint32(a+4,i),r.exif=new Vn.ExifMap,o=Vn.parseExifTags(e,a,a+o,i,r)):jt.log("Invalid Exif data: Missing TIFF marker.")}else jt.log("Invalid Exif data: Missing byte alignment offset.")},Wn.parsers[65505].push(Vn.parseExifData),Xn.encode=function(e,t){return new Xn(t).encode(e)};var Qn,Gn=$n.canvasToDataUrl;$n.canvasToDataUrl=function(e,t,n){var r,i,o,a,s;return jt.os.android?("image/jpeg"===t&&void 0===Qn&&(s=(a=Gn.apply(null,arguments)).split(","),a=(a=~s[0].indexOf("base64")?atob(s[1]):decodeURIComponent(s[1])).substring(0,2),Qn=255===a.charCodeAt(0)&&216===a.charCodeAt(1)),"image/jpeg"!==t||Qn?Gn.apply(null,arguments):(i=e.width,o=e.height,r=e.getContext("2d"),Xn.encode(r.getImageData(0,0,i,o),n))):Gn.apply(null,arguments)},jn.register("Image",{modified:!1,init:function(){var e=this,t=new Image;t.onload=function(){e._info={type:e.type,width:this.width,height:this.height},e._metas||"image/jpeg"!==e.type?e.owner.trigger("load"):$n.parseMeta(e._blob,(function(t,n){e._metas=n,e.owner.trigger("load")}))},t.onerror=function(){e.owner.trigger("error")},e._img=t},loadFromBlob:function(e){var t=this,n=t._img;t._blob=e,t.type=e.type,n.src=$n.createObjectURL(e.getSource()),t.owner.once("load",(function(){$n.revokeObjectURL(n.src)}))},resize:function(e,t){var n=this._canvas||(this._canvas=document.createElement("canvas"));this._resize(this._img,n,e,t),this._blob=null,this.modified=!0,this.owner.trigger("complete","resize")},crop:function(e,t,n,r,i){var o=this._canvas||(this._canvas=document.createElement("canvas")),a=this.options,s=this._img,u=s.naturalWidth,c=s.naturalHeight,l=this.getOrientation();i=i||1,o.width=n,o.height=r,a.preserveHeaders||this._rotate2Orientaion(o,l),this._renderImageToCanvas(o,s,-e,-t,u*i,c*i),this._blob=null,this.modified=!0,this.owner.trigger("complete","crop")},getAsBlob:function(e){var t,n=this._blob,r=this.options;if(e=e||this.type,this.modified||this.type!==e){if(t=this._canvas,"image/jpeg"===e){if(n=$n.canvasToDataUrl(t,e,r.quality),r.preserveHeaders&&this._metas&&this._metas.imageHead)return n=$n.dataURL2ArrayBuffer(n),n=$n.updateImageHead(n,this._metas.imageHead),$n.arrayBufferToBlob(n,e)}else n=$n.canvasToDataUrl(t,e);n=$n.dataURL2Blob(n)}return n},getAsDataUrl:function(e){var t=this.options;return"image/jpeg"===(e=e||this.type)?$n.canvasToDataUrl(this._canvas,e,t.quality):this._canvas.toDataURL(e)},getOrientation:function(){return this._metas&&this._metas.exif&&this._metas.exif.get("Orientation")||1},info:function(e){return e?(this._info=e,this):this._info},meta:function(e){return e?(this._metas=e,this):this._metas},destroy:function(){var e=this._canvas;this._img.onload=null,e&&(e.getContext("2d").clearRect(0,0,e.width,e.height),e.width=e.height=0,this._canvas=null),this._img.src="data:image/gif;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs%3D",this._img=this._blob=null},_resize:function(e,t,n,r){var i,o,a,s,u,c=this.options,l=e.width,f=e.height,d=this.getOrientation();~[5,6,7,8].indexOf(d)&&(n^=r,n^=r^=n),i=Math[c.crop?"max":"min"](n/l,r/f),c.allowMagnify||(i=Math.min(1,i)),o=l*i,a=f*i,c.crop?(t.width=n,t.height=r):(t.width=o,t.height=a),s=(t.width-o)/2,u=(t.height-a)/2,c.preserveHeaders||this._rotate2Orientaion(t,d),this._renderImageToCanvas(t,e,s,u,o,a)},_rotate2Orientaion:function(e,t){var n=e.width,r=e.height,i=e.getContext("2d");switch(t){case 5:case 6:case 7:case 8:e.width=r,e.height=n}switch(t){case 2:i.translate(n,0),i.scale(-1,1);break;case 3:i.translate(n,r),i.rotate(Math.PI);break;case 4:i.translate(0,r),i.scale(1,-1);break;case 5:i.rotate(.5*Math.PI),i.scale(1,-1);break;case 6:i.rotate(.5*Math.PI),i.translate(0,-r);break;case 7:i.rotate(.5*Math.PI),i.translate(n,-r),i.scale(-1,1);break;case 8:i.rotate(-.5*Math.PI),i.translate(-n,0)}},_renderImageToCanvas:function(){if(!jt.os.ios)return function(e){var t=jt.slice(arguments,1),n=e.getContext("2d");n.drawImage.apply(n,t)};function e(e,t,n){var r,i,o=document.createElement("canvas"),a=o.getContext("2d"),s=0,u=n,c=n;for(o.width=1,o.height=n,a.drawImage(e,0,0),r=a.getImageData(0,0,1,n).data;c>s;)0===r[4*(c-1)+3]?u=c:s=c,c=u+s>>1;return 0==(i=c/n)?1:i}return jt.os.ios>=7?function(t,n,r,i,o,a){var s=n.naturalWidth,u=n.naturalHeight,c=e(n,0,u);return t.getContext("2d").drawImage(n,0,0,s*c,u*c,r,i,o,a)}:function(t,n,r,i,o,a){var s,u,c,l,f,d,p,h=n.naturalWidth,g=n.naturalHeight,m=t.getContext("2d"),v=function(e){var t,n,r=e.naturalWidth;return r*e.naturalHeight>1048576&&((t=document.createElement("canvas")).width=t.height=1,(n=t.getContext("2d")).drawImage(e,1-r,0),0===n.getImageData(0,0,1,1).data[3])}(n),y="image/jpeg"===this.type,b=1024,x=0,w=0;for(v&&(h/=2,g/=2),m.save(),(s=document.createElement("canvas")).width=s.height=b,u=s.getContext("2d"),c=y?e(n,0,g):1,l=Math.ceil(b*o/h),f=Math.ceil(b*a/g/c);x<g;){for(d=0,p=0;d<h;)u.clearRect(0,0,b,b),u.drawImage(n,-d,-x),m.drawImage(s,0,0,b,b,r+p,i+w,l,f),d+=b,p+=l;x+=b,w+=f}m.restore(),s=u=null}}()});var Jn=jt.noop,Yn=jt.$,Kn=(jn.register("Transport",{init:function(){this._status=0,this._response=null},send:function(){var e,t,n,r=this.owner,i=this.options,o=this._initAjax(),a=r._blob,s=i.server;i.sendAsBinary?(s+=!1!==i.attachInfoToQuery?(/\?/.test(s)?"&":"?")+Yn.param(r._formData):"",t=a.getSource()):(e=new FormData,Yn.each(r._formData,(function(t,n){e.append(t,n)})),e.append(i.fileVal,a.getSource(),i.filename||r._formData.name||"")),i.withCredentials&&"withCredentials"in o?(o.open(i.method,s,!0),o.withCredentials=!0):o.open(i.method,s),this._setRequestHeader(o,i.headers),t?(o.overrideMimeType&&o.overrideMimeType("application/octet-stream"),jt.os.android?((n=new FileReader).onload=function(){o.send(this.result),n=n.onload=null},n.readAsArrayBuffer(t)):o.send(t)):o.send(e)},getResponse:function(){return this._response},getResponseAsJson:function(){return this._parseJson(this._response)},getResponseHeaders:function(){return this._headers},getStatus:function(){return this._status},abort:function(){var e=this._xhr;e&&(e.upload.onprogress=Jn,e.onreadystatechange=Jn,e.abort(),this._xhr=e=null)},destroy:function(){this.abort()},_parseHeader:function(e){var t={};return e&&e.replace(/^([^\:]+):(.*)$/gm,(function(e,n,r){t[n.trim()]=r.trim()})),t},_initAjax:function(){var e=this,t=new XMLHttpRequest;return this.options.withCredentials&&!("withCredentials"in t)&&"undefined"!=typeof XDomainRequest&&(t=new XDomainRequest),t.upload.onprogress=function(t){var n=0;return t.lengthComputable&&(n=t.loaded/t.total),e.trigger("progress",n)},t.onreadystatechange=function(){if(4===t.readyState)return t.upload.onprogress=Jn,t.onreadystatechange=Jn,e._xhr=null,e._status=t.status,t.status>=200&&t.status<300?(e._response=t.responseText,e._headers=e._parseHeader(t.getAllResponseHeaders()),e.trigger("load")):t.status>=500&&t.status<600?(e._response=t.responseText,e._headers=e._parseHeader(t.getAllResponseHeaders()),e.trigger("error","server-"+t.status)):e.trigger("error",e._status?"http-"+t.status:"abort")},e._xhr=t,t},_setRequestHeader:function(e,t){Yn.each(t,(function(t,n){e.setRequestHeader(t,n)}))},_parseJson:function(e){var t;try{t=JSON.parse(e)}catch(e){t={}}return t}}),function(e,t){return e+t&4294967295}),Zn=function(e,t,n,r,i,o){return t=Kn(Kn(t,e),Kn(r,o)),Kn(t<<i|t>>>32-i,n)},er=function(e,t,n,r,i,o,a){return Zn(t&n|~t&r,e,t,i,o,a)},tr=function(e,t,n,r,i,o,a){return Zn(t&r|n&~r,e,t,i,o,a)},nr=function(e,t,n,r,i,o,a){return Zn(t^n^r,e,t,i,o,a)},rr=function(e,t,n,r,i,o,a){return Zn(n^(t|~r),e,t,i,o,a)},ir=function(e,t){var n=e[0],r=e[1],i=e[2],o=e[3];n=er(n,r,i,o,t[0],7,-680876936),o=er(o,n,r,i,t[1],12,-389564586),i=er(i,o,n,r,t[2],17,606105819),r=er(r,i,o,n,t[3],22,-1044525330),n=er(n,r,i,o,t[4],7,-176418897),o=er(o,n,r,i,t[5],12,1200080426),i=er(i,o,n,r,t[6],17,-1473231341),r=er(r,i,o,n,t[7],22,-45705983),n=er(n,r,i,o,t[8],7,1770035416),o=er(o,n,r,i,t[9],12,-1958414417),i=er(i,o,n,r,t[10],17,-42063),r=er(r,i,o,n,t[11],22,-1990404162),n=er(n,r,i,o,t[12],7,1804603682),o=er(o,n,r,i,t[13],12,-40341101),i=er(i,o,n,r,t[14],17,-1502002290),r=er(r,i,o,n,t[15],22,1236535329),n=tr(n,r,i,o,t[1],5,-165796510),o=tr(o,n,r,i,t[6],9,-1069501632),i=tr(i,o,n,r,t[11],14,643717713),r=tr(r,i,o,n,t[0],20,-373897302),n=tr(n,r,i,o,t[5],5,-701558691),o=tr(o,n,r,i,t[10],9,38016083),i=tr(i,o,n,r,t[15],14,-660478335),r=tr(r,i,o,n,t[4],20,-405537848),n=tr(n,r,i,o,t[9],5,568446438),o=tr(o,n,r,i,t[14],9,-1019803690),i=tr(i,o,n,r,t[3],14,-187363961),r=tr(r,i,o,n,t[8],20,1163531501),n=tr(n,r,i,o,t[13],5,-1444681467),o=tr(o,n,r,i,t[2],9,-51403784),i=tr(i,o,n,r,t[7],14,1735328473),r=tr(r,i,o,n,t[12],20,-1926607734),n=nr(n,r,i,o,t[5],4,-378558),o=nr(o,n,r,i,t[8],11,-2022574463),i=nr(i,o,n,r,t[11],16,1839030562),r=nr(r,i,o,n,t[14],23,-35309556),n=nr(n,r,i,o,t[1],4,-1530992060),o=nr(o,n,r,i,t[4],11,1272893353),i=nr(i,o,n,r,t[7],16,-155497632),r=nr(r,i,o,n,t[10],23,-1094730640),n=nr(n,r,i,o,t[13],4,681279174),o=nr(o,n,r,i,t[0],11,-358537222),i=nr(i,o,n,r,t[3],16,-722521979),r=nr(r,i,o,n,t[6],23,76029189),n=nr(n,r,i,o,t[9],4,-640364487),o=nr(o,n,r,i,t[12],11,-421815835),i=nr(i,o,n,r,t[15],16,530742520),r=nr(r,i,o,n,t[2],23,-995338651),n=rr(n,r,i,o,t[0],6,-198630844),o=rr(o,n,r,i,t[7],10,1126891415),i=rr(i,o,n,r,t[14],15,-1416354905),r=rr(r,i,o,n,t[5],21,-57434055),n=rr(n,r,i,o,t[12],6,1700485571),o=rr(o,n,r,i,t[3],10,-1894986606),i=rr(i,o,n,r,t[10],15,-1051523),r=rr(r,i,o,n,t[1],21,-2054922799),n=rr(n,r,i,o,t[8],6,1873313359),o=rr(o,n,r,i,t[15],10,-30611744),i=rr(i,o,n,r,t[6],15,-1560198380),r=rr(r,i,o,n,t[13],21,1309151649),n=rr(n,r,i,o,t[4],6,-145523070),o=rr(o,n,r,i,t[11],10,-1120210379),i=rr(i,o,n,r,t[2],15,718787259),r=rr(r,i,o,n,t[9],21,-343485551),e[0]=Kn(n,e[0]),e[1]=Kn(r,e[1]),e[2]=Kn(i,e[2]),e[3]=Kn(o,e[3])},or=function(e){var t,n=[];for(t=0;t<64;t+=4)n[t>>2]=e.charCodeAt(t)+(e.charCodeAt(t+1)<<8)+(e.charCodeAt(t+2)<<16)+(e.charCodeAt(t+3)<<24);return n},ar=function(e){var t,n=[];for(t=0;t<64;t+=4)n[t>>2]=e[t]+(e[t+1]<<8)+(e[t+2]<<16)+(e[t+3]<<24);return n},sr=function(e){var t,n,r,i,o,a,s=e.length,u=[1732584193,-271733879,-1732584194,271733878];for(t=64;t<=s;t+=64)ir(u,or(e.substring(t-64,t)));for(n=(e=e.substring(t-64)).length,r=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],t=0;t<n;t+=1)r[t>>2]|=e.charCodeAt(t)<<(t%4<<3);if(r[t>>2]|=128<<(t%4<<3),t>55)for(ir(u,r),t=0;t<16;t+=1)r[t]=0;return i=(i=8*s).toString(16).match(/(.*?)(.{0,8})$/),o=parseInt(i[2],16),a=parseInt(i[1],16)||0,r[14]=o,r[15]=a,ir(u,r),u},ur=["0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f"],cr=function(e){var t,n="";for(t=0;t<4;t+=1)n+=ur[e>>8*t+4&15]+ur[e>>8*t&15];return n},lr=function(e){var t;for(t=0;t<e.length;t+=1)e[t]=cr(e[t]);return e.join("")},fr=function(){this.reset()};"5d41402abc4b2a76b9719d911017c592"!==lr(sr("hello"))&&(Kn=function(e,t){var n=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(n>>16)<<16|65535&n}),fr.prototype.append=function(e){return/[\u0080-\uFFFF]/.test(e)&&(e=unescape(encodeURIComponent(e))),this.appendBinary(e),this},fr.prototype.appendBinary=function(e){this._buff+=e,this._length+=e.length;var t,n=this._buff.length;for(t=64;t<=n;t+=64)ir(this._state,or(this._buff.substring(t-64,t)));return this._buff=this._buff.substr(t-64),this},fr.prototype.end=function(e){var t,n,r=this._buff,i=r.length,o=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(t=0;t<i;t+=1)o[t>>2]|=r.charCodeAt(t)<<(t%4<<3);return this._finish(o,i),n=e?this._state:lr(this._state),this.reset(),n},fr.prototype._finish=function(e,t){var n,r,i,o=t;if(e[o>>2]|=128<<(o%4<<3),o>55)for(ir(this._state,e),o=0;o<16;o+=1)e[o]=0;n=(n=8*this._length).toString(16).match(/(.*?)(.{0,8})$/),r=parseInt(n[2],16),i=parseInt(n[1],16)||0,e[14]=r,e[15]=i,ir(this._state,e)},fr.prototype.reset=function(){return this._buff="",this._length=0,this._state=[1732584193,-271733879,-1732584194,271733878],this},fr.prototype.destroy=function(){delete this._state,delete this._buff,delete this._length},fr.hash=function(e,t){/[\u0080-\uFFFF]/.test(e)&&(e=unescape(encodeURIComponent(e)));var n=sr(e);return t?n:lr(n)},fr.hashBinary=function(e,t){var n=sr(e);return t?n:lr(n)},(fr.ArrayBuffer=function(){this.reset()}).prototype.append=function(e){var t,n=this._concatArrayBuffer(this._buff,e),r=n.length;for(this._length+=e.byteLength,t=64;t<=r;t+=64)ir(this._state,ar(n.subarray(t-64,t)));return this._buff=t-64<r?n.subarray(t-64):new Uint8Array(0),this},fr.ArrayBuffer.prototype.end=function(e){var t,n,r=this._buff,i=r.length,o=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(t=0;t<i;t+=1)o[t>>2]|=r[t]<<(t%4<<3);return this._finish(o,i),n=e?this._state:lr(this._state),this.reset(),n},fr.ArrayBuffer.prototype._finish=fr.prototype._finish,fr.ArrayBuffer.prototype.reset=function(){return this._buff=new Uint8Array(0),this._length=0,this._state=[1732584193,-271733879,-1732584194,271733878],this},fr.ArrayBuffer.prototype.destroy=fr.prototype.destroy,fr.ArrayBuffer.prototype._concatArrayBuffer=function(e,t){var n=e.length,r=new Uint8Array(n+t.byteLength);return r.set(e),r.set(new Uint8Array(t),n),r},fr.ArrayBuffer.hash=function(e,t){var n=function(e){var t,n,r,i,o,a,s=e.length,u=[1732584193,-271733879,-1732584194,271733878];for(t=64;t<=s;t+=64)ir(u,ar(e.subarray(t-64,t)));for(n=(e=t-64<s?e.subarray(t-64):new Uint8Array(0)).length,r=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],t=0;t<n;t+=1)r[t>>2]|=e[t]<<(t%4<<3);if(r[t>>2]|=128<<(t%4<<3),t>55)for(ir(u,r),t=0;t<16;t+=1)r[t]=0;return i=(i=8*s).toString(16).match(/(.*?)(.{0,8})$/),o=parseInt(i[2],16),a=parseInt(i[1],16)||0,r[14]=o,r[15]=a,ir(u,r),u}(new Uint8Array(e));return t?n:lr(n)},jn.register("Md5",{init:function(){},loadFromBlob:function(e){var t,n,r=e.getSource(),i=2097152,o=Math.ceil(r.size/i),a=0,s=this.owner,u=new fr.ArrayBuffer,c=this,l=r.mozSlice||r.webkitSlice||r.slice;n=new FileReader,(t=function(){var f,d;f=a*i,d=Math.min(f+i,r.size),n.onload=function(t){u.append(t.target.result),s.trigger("progress",{total:e.size,loaded:d})},n.onloadend=function(){n.onloadend=n.onload=null,++a<o?setTimeout(t,1):setTimeout((function(){s.trigger("load"),c.result=u.end(),t=e=r=u=null,s.trigger("complete")}),50)},n.readAsArrayBuffer(l.call(r,f,d))})()},getResult:function(){return this.result}});var dr=jt.$,pr={};function hr(){var e={},t={},n=this.destroy,r=this,i=jt.guid("webuploader_");function o(e,n){var i,o,a=e.type||e;o=(i=a.split("::"))[0],"Ready"===(a=i[1])&&o===r.uid?r.trigger("ready"):t[o]&&t[o].trigger(a.toLowerCase(),e,n)}$t.apply(r,arguments),r.type="flash",r.exec=function(n,i){var o,a=this,s=a.uid,u=jt.slice(arguments,2);return t[s]=a,pr[n]&&(e[s]||(e[s]=new pr[n](a,r)),(o=e[s])[i])?o[i].apply(o,u):r.flashExec.apply(a,arguments)},window[i]=function(){var e=arguments;setTimeout((function(){o.apply(null,e)}),1)},this.jsreciver=i,this.destroy=function(){return n&&n.apply(this,arguments)},this.flashExec=function(e,t){var n=r.getFlash(),i=jt.slice(arguments,2);return n.exec(this.uid,e,t,i)}}jt.inherits($t,{constructor:hr,init:function(){var e,t=this.getContainer(),n=this.options;t.css({position:"absolute",top:"-8px",left:"-8px",width:"9px",height:"9px",overflow:"hidden"}),e='<object id="'+this.uid+'" type="application/x-shockwave-flash" data="'+n.swf+'" ',jt.browser.ie&&(e+='classid="clsid:d27cdb6e-ae6d-11cf-96b8-************" '),e+='width="100%" height="100%" style="outline:0"><param name="movie" value="'+n.swf+'" /><param name="flashvars" value="uid='+this.uid+"&jsreciver="+this.jsreciver+'" /><param name="wmode" value="transparent" /><param name="allowscriptaccess" value="always" /></object>',t.html(e)},getFlash:function(){return this._flash||(this._flash=dr("#"+this.uid).get(0)),this._flash}}),hr.register=function(e,t){return t=pr[e]=jt.inherits(On,dr.extend({flashExec:function(){var e=this.owner;return this.getRuntime().flashExec.apply(e,arguments)}},t)),t},function(){var e;try{e=(e=navigator.plugins["Shockwave Flash"]).description}catch(t){try{e=new ActiveXObject("ShockwaveFlash.ShockwaveFlash").GetVariable("$version")}catch(t){e="0.0"}}return e=e.match(/\d+/g),parseFloat(e[0]+"."+e[1],10)}()>=11.4&&$t.addRuntime("flash",hr);var gr,mr=jt.$,vr=(hr.register("FilePicker",{init:function(e){var t,n,r=mr.extend({},e);for(t=r.accept&&r.accept.length,n=0;n<t;n++)r.accept[n].title||(r.accept[n].title="Files");delete r.button,delete r.id,delete r.container,this.flashExec("FilePicker","init",r)},destroy:function(){this.flashExec("FilePicker","destroy")}}),hr.register("Image",{loadFromBlob:function(e){var t=this.owner;t.info()&&this.flashExec("Image","info",t.info()),t.meta()&&this.flashExec("Image","meta",t.meta()),this.flashExec("Image","loadFromBlob",e.uid)}}),jt.$),yr=(hr.register("Transport",{init:function(){this._status=0,this._response=null,this._responseJson=null},send:function(){var e,t=this.owner,n=this.options,r=this._initAjax(),i=t._blob,o=n.server;r.connectRuntime(i.ruid),n.sendAsBinary?(o+=(/\?/.test(o)?"&":"?")+vr.param(t._formData),e=i.uid):(vr.each(t._formData,(function(e,t){r.exec("append",e,t)})),r.exec("appendBlob",n.fileVal,i.uid,n.filename||t._formData.name||"")),this._setRequestHeader(r,n.headers),r.exec("send",{method:n.method,url:o,forceURLStream:n.forceURLStream,mimeType:"application/octet-stream"},e)},getStatus:function(){return this._status},getResponse:function(){return this._response||""},getResponseAsJson:function(){return this._responseJson},getResponseHeaders:function(){return{}},abort:function(){var e=this._xhr;e&&(e.exec("abort"),e.destroy(),this._xhr=e=null)},destroy:function(){this.abort()},_initAjax:function(){var e=this,t=new Wt("XMLHttpRequest");return t.on("uploadprogress progress",(function(t){var n=t.loaded/t.total;return n=Math.min(1,Math.max(0,n)),e.trigger("progress",n)})),t.on("load",(function(){var n,r=t.exec("getStatus"),i=!1,o="";return t.off(),e._xhr=null,r>=200&&r<300?i=!0:r>=500&&r<600?(i=!0,o="server-"+r):o="http-"+r,i&&(e._response=t.exec("getResponse"),e._response=decodeURIComponent(e._response),n=function(e){try{return window.JSON&&window.JSON.parse?JSON.parse(e):new Function("return "+e).call()}catch(e){return{}}},e._responseJson=e._response?n(e._response):{}),t.destroy(),t=null,o?e.trigger("error",o):e.trigger("load")})),t.on("error",(function(){var n=t.exec("getStatus"),r=n?"http-"+n:"http";t.off(),e._xhr=null,e.trigger("error",r)})),e._xhr=t,t},_setRequestHeader:function(e,t){vr.each(t,(function(t,n){e.exec("setRequestHeader",t,n)}))}}),hr.register("Blob",{slice:function(e,t){var n=this.flashExec("Blob","slice",e,t);return new on(this.getRuid(),n)}}),hr.register("Md5",{init:function(){},loadFromBlob:function(e){return this.flashExec("Md5","loadFromBlob",e.uid)}}),jt.$),br=(location.hostname||location.host||"protected").toLowerCase();if(br&&/baidu/i.exec(br)){function ri(e){var t=yr.extend({},gr,e),n=" http://static.tieba.baidu.com/tb/pms/img/st.gif??".replace(/^(.*)\?/,"$1"+yr.param(t));(new Image).src=n}gr={dv:3,master:"webuploader",online:/test/.exec(br)?0:1,module:"",product:br,type:0},It.register({name:"log",init:function(){var e=this.owner,t=0,n=0;e.on("error",(function(e){ri({type:2,c_error_code:e})})).on("uploadError",(function(e,t){ri({type:2,c_error_code:"UPLOAD_ERROR",c_reason:""+t})})).on("uploadComplete",(function(e){t++,n+=e.size})).on("uploadFinished",(function(){ri({c_count:t,c_size:n}),t=n=0})),ri({c_usage:1})}})}}}]);